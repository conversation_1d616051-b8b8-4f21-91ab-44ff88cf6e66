package com.registrocontrato.infra.jsf.converter;

import javax.faces.component.UIComponent;
import javax.faces.context.FacesContext;
import javax.faces.convert.Converter;
import javax.faces.convert.FacesConverter;

import com.registrocontrato.infra.entity.TemplateValidacao;

@FacesConverter(value = "templateValidacaoConverter")
public class TemplateValidacaoConverter implements Converter {

    @Override
    public Object getAsObject(FacesContext context, UIComponent component, String value) {
        if (value != null && !value.trim().isEmpty()) {
            String[] split = value.replace("TemplateValidacao[", "").replaceAll("]", "").split(",");
            TemplateValidacao entity = new TemplateValidacao();
            entity.setId(Long.parseLong(split[0].trim().equals("null") ? "0" : split[0].trim()));
            entity.setDescricao(split[1].trim());
            entity.setPosicaoAux(Integer.parseInt(split[2].trim().equals("null") ? "0" : split[2].trim()));
            entity.setLinhaInicial(Integer.parseInt(split[3].trim().equals("null") ? "0" : split[3].trim()));
            entity.setLinhaFinal(Integer.parseInt(split[4].trim().equals("null") ? "0" : split[4].trim()));
            return entity;
        }
        return null;
    }

    @Override
    public String getAsString(FacesContext context, UIComponent component, Object value) {
        if (value instanceof TemplateValidacao) {
            TemplateValidacao entity = (TemplateValidacao) value;
            return String.format("TemplateValidacao[%d, %s, %d, %d, %d]",
                    entity.getId(),
                    entity.getDescricao(),
                    entity.getPosicaoAux(),
                    entity.getLinhaInicial(),
                    entity.getLinhaFinal());
        }
        return "";
    }
}
