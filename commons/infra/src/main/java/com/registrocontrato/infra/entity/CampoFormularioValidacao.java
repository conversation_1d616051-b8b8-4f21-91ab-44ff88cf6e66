package com.registrocontrato.infra.entity;

public enum CampoFormularioValidacao {
    UF("Uf"),
    CHASSIS("Chassis"),
    NOME("Nome"),
    NUMERO_CONTRATO("Numero do contrato"),
    IGNORAR("Ignorar");

    private final String descricao;

    CampoFormularioValidacao(String descricao) {
        this.descricao = descricao;
    }

    public String getDescricao() {
        return descricao;
    }
}
