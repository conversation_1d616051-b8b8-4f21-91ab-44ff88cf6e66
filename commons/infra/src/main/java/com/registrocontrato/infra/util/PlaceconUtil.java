package com.registrocontrato.infra.util;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.itextpdf.text.*;
import com.itextpdf.text.pdf.PdfWriter;
import com.registrocontrato.infra.entity.SimNao;
import org.apache.commons.lang3.RandomUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.apache.tomcat.util.codec.binary.Base64;
import org.hibernate.validator.internal.constraintvalidators.hv.EmailValidator;

import javax.xml.bind.JAXBContext;
import javax.xml.bind.JAXBException;
import javax.xml.bind.Marshaller;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import javax.xml.bind.JAXBContext;
import javax.xml.bind.JAXBException;
import javax.xml.bind.Marshaller;
import java.io.*;
import java.io.StringWriter;
import java.math.BigDecimal;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.text.*;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.List;
import java.util.*;
import java.util.function.Predicate;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public abstract class PlaceconUtil {

    public static final String DATAYYYYMMDD = "yyyy-MM-dd";

    protected static final Log logger = LogFactory.getLog(PlaceconUtil.class);

    public static String limparString(String original) {
        return original.replaceAll("[^A-Za-z0-9_.-]", "_");
    }

    @Deprecated
    public static String encode(String parametro) {
        try {
            byte[] salt = new byte[8];
            Random rand = new Random((new Date()).getTime());
            rand.nextBytes(salt);
            return URLEncoder.encode(new String(Base64.encodeBase64(salt), "UTF-8") + new String(Base64.encodeBase64(parametro.getBytes()), "UTF-8"));
        } catch (Exception e) {
            return null;
        }
    }

    public static String encodeUTF8(String parametro) {
        try {
            byte[] salt = new byte[8];
            Random rand = new Random((new Date()).getTime());
            rand.nextBytes(salt);
            return URLEncoder.encode(new String(Base64.encodeBase64(salt)) + new String(Base64.encodeBase64(parametro.getBytes())), "UTF-8");
        } catch (Exception e) {
            return null;
        }
    }

    public static String decode(String parametro) {
        try {
            parametro = URLDecoder.decode(parametro, "UTF-8");
            String cipher = parametro.substring(12);
            return new String(Base64.decodeBase64(cipher.getBytes()), "UTF-8");
        } catch (Exception e) {
            return null;
        }
    }


    /**
     * Calcula a diferença de duas datas em dias <br>
     * <b>Importante:</b> Quando realiza a diferença em dias entre duas datas, este
     * método considera as horas restantes e as converte em fração de dias.
     *
     * @param dataInicial
     * @param dataFinal
     * @return quantidade de dias existentes entre a dataInicial e dataFinal.
     */
    public static int diferencaEmDias(Date dataInicial, Date dataFinal) {
        double result = 0;
        long diferenca = dataFinal.getTime() - dataInicial.getTime();
        double diferencaEmDias = (diferenca / 1000) / 60 / 60 / 24; // resultado é diferença entre as datas em dias
        long horasRestantes = (diferenca / 1000) / 60 / 60 % 24; // calcula as horas restantes
        result = diferencaEmDias + (horasRestantes / 24d); // transforma as horas restantes em fração de dias
        return (int) result;
    }

    public static String getColorGraph() {
        int red = RandomUtils.nextInt(1, 256);
        int green = RandomUtils.nextInt(1, 256);
        int blue = RandomUtils.nextInt(1, 256);
        return String.format("rgb(%d, %d, %d)", red, green, blue);
    }

    public static String getMesReferencia(Calendar c) {
        return leftPad(String.valueOf(c.get(Calendar.MONTH) + 1), 2, "0") + "/" + c.get(Calendar.YEAR);
    }

    public static String getMesExtensoCobranca(Date data) {
        Locale local = new Locale("pt", "BR");
        return new SimpleDateFormat("MMMM", local).format(data);
    }

    public static String getMesReferencia(Date data) {
        Date aux = new Date(data.getTime());
        LocalDate l = aux.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        return leftPad(String.valueOf(l.getMonthValue()), 2, "0") + "/" + l.getYear();
    }

    public static String deAccent(String str) {
        if (str == null)
            return null;

        String nfdNormalizedString = Normalizer.normalize(str, Normalizer.Form.NFD);
        Pattern pattern = Pattern.compile("\\p{InCombiningDiacriticalMarks}+");
        return pattern.matcher(nfdNormalizedString).replaceAll("");
    }

    public static String getDataHoraFormatada(Date data) {
        Calendar c = Calendar.getInstance();
        c.setTime(data);
        return formatarDataPadrao(data) + " às " + leftPad(String.valueOf(c.get(Calendar.HOUR_OF_DAY)), 2, "0") + ":" + leftPad(String.valueOf(c.get(Calendar.MINUTE)), 2, "0");
    }

    public static String getCepFormatado(String cep) {
        if (cep == null) return null;
        if (cep.length() != 8) return cep;
        return cep.substring(0, 5) + "-" + cep.substring(5);
    }

    public static String getCepSemFormatacao(String cep) {
        if (cep == null) return null;
        if (cep.length() == 8) return cep;
        return cep.replace("-","");
    }

    /**
     * formata um calendar no padrao desejado
     * padroes: dd-MM-yyyy
     *
     * @param c
     * @param format
     * @return
     */
    public static String getDataFormatada(Calendar c, String format) {
        return getDataFormatada(c.getTime(), format);
    }

    public static String getDataHojeFormatada(String format) {
        Calendar c = (Calendar) Calendar.getInstance().clone();
        return getDataFormatada(c.getTime(), format);
    }

    public static String getDataHojeFormatadaPadrao() {
        Calendar c = (Calendar) Calendar.getInstance().clone();
        return formatarDataPadrao(c.getTime());
    }

    public static Date getComecoMesAtual() {
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.DAY_OF_MONTH, 1);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        return calendar.getTime();
    }

    public static String getDataFormatada(Date c, String format) {
        final DateFormat df = new SimpleDateFormat(format, new Locale("pt", "BR"));
        return df.format(c);
    }

    public static LocalDate subtractBusinessDays(LocalDate localDate, int days, Optional<List<LocalDate>> holidays) {
        if (localDate == null || days <= 0 || holidays == null) {
            throw new IllegalArgumentException("Invalid method argument(s) "
                    + "to subtractBusinessDays(" + localDate + "," + days + "," + holidays + ")");
        }
        Predicate<LocalDate> isHoliday =
                date -> holidays.isPresent() ? holidays.get().contains(date) : false;

        Predicate<LocalDate> isWeekend =
                date -> date.getDayOfWeek() == DayOfWeek.SATURDAY
                        || date.getDayOfWeek() == DayOfWeek.SUNDAY;

        LocalDate result = localDate;
        while (days > 0) {
            result = result.minusDays(1);
            if (isHoliday.or(isWeekend).negate().test(result)) {
                days--;
            }
        }
        return result;
    }

    public static Date converteLocalDateEmDate(LocalDate localDate) {
        return Date.from(localDate.atStartOfDay().atZone(ZoneId.systemDefault()).toInstant());
    }

    public static LocalDate convertToLocalDateViaInstant(Date date) {
        return date.toInstant()
                .atZone(ZoneId.systemDefault())
                .toLocalDate();
    }

    public static Date oneMonthBeforeSameDay(Date original) {
        LocalDate local = original.toInstant()
                .atZone(ZoneId.systemDefault())
                .toLocalDate();

        LocalDate priorMonth = local.minusMonths(1);

        int diaOriginal = local.getDayOfMonth();
        int ultimoDiaDoMes = priorMonth.getMonth()
                .length(priorMonth.isLeapYear());
        int diaValido = Math.min(diaOriginal, ultimoDiaDoMes);
        LocalDate finalDate = priorMonth.withDayOfMonth(diaValido);

        return Date.from(finalDate.atStartOfDay(ZoneId.systemDefault()).toInstant());
    }

    public static String valorPorExtenso(double vlr) {
        if (vlr == 0)
            return ("zero");

        long inteiro = (long) Math.abs(vlr); // parte inteira do valor
        double resto = vlr - inteiro;       // parte fracionária do valor

        String vlrS = String.valueOf(inteiro);
        if (vlrS.length() > 15)
            return ("Erro: valor superior a 999 trilhões.");

        String s = "", saux, vlrP;
        String centavos = String.valueOf((int) Math.round(resto * 100));

        String[] unidade = {"", "um", "dois", "três", "quatro", "cinco",
                "seis", "sete", "oito", "nove", "dez", "onze",
                "doze", "treze", "quatorze", "quinze", "dezesseis",
                "dezessete", "dezoito", "dezenove"};
        String[] centena = {"", "cento", "duzentos", "trezentos",
                "quatrocentos", "quinhentos", "seiscentos",
                "setecentos", "oitocentos", "novecentos"};
        String[] dezena = {"", "", "vinte", "trinta", "quarenta", "cinquenta",
                "sessenta", "setenta", "oitenta", "noventa"};
        String[] qualificaS = {"", "mil", "milhão", "bilhão", "trilhão"};
        String[] qualificaP = {"", "mil", "milhões", "bilhões", "trilhões"};

        // definindo o extenso da parte inteira do valor
        int n, unid, dez, cent, tam, i = 0;
        boolean umReal = false, tem = false;
        while (!vlrS.equals("0")) {
            tam = vlrS.length();
            // retira do valor a 1a. parte, 2a. parte, por exemplo, para 123456789:
            // 1a. parte = 789 (centena)
            // 2a. parte = 456 (mil)
            // 3a. parte = 123 (milhões)
            if (tam > 3) {
                vlrP = vlrS.substring(tam - 3, tam);
                vlrS = vlrS.substring(0, tam - 3);
            } else { // última parte do valor
                vlrP = vlrS;
                vlrS = "0";
            }
            if (!vlrP.equals("000")) {
                saux = "";
                if (vlrP.equals("100"))
                    saux = "cem";
                else {
                    n = Integer.parseInt(vlrP, 10);  // para n = 371, tem-se:
                    cent = n / 100;                  // cent = 3 (centena trezentos)
                    dez = (n % 100) / 10;            // dez  = 7 (dezena setenta)
                    unid = (n % 100) % 10;           // unid = 1 (unidade um)
                    if (cent != 0)
                        saux = centena[cent];
                    if ((n % 100) <= 19) {
                        if (saux.length() != 0)
                            saux = saux + " e " + unidade[n % 100];
                        else saux = unidade[n % 100];
                    } else {
                        if (saux.length() != 0)
                            saux = saux + " e " + dezena[dez];
                        else saux = dezena[dez];
                        if (unid != 0) {
                            if (saux.length() != 0)
                                saux = saux + " e " + unidade[unid];
                            else saux = unidade[unid];
                        }
                    }
                }
                if (vlrP.equals("1") || vlrP.equals("001")) {
                    if (i == 0) // 1a. parte do valor (um real)
                        umReal = true;
                    else saux = saux + " " + qualificaS[i];
                } else if (i != 0)
                    saux = saux + " " + qualificaP[i];
                if (s.length() != 0)
                    s = saux + ", " + s;
                else s = saux;
            }
            if (((i == 0) || (i == 1)) && s.length() != 0)
                tem = true; // tem centena ou mil no valor
            i = i + 1; // próximo qualificador: 1- mil, 2- milhão, 3- bilhão, ...
        }

        if (s.length() != 0) {
            if (umReal)
                s = s + " real";
            else if (tem)
                s = s + " reais";
            else s = s + " de reais";
        }

        // definindo o extenso dos centavos do valor
        if (!centavos.equals("0")) { // valor com centavos
            if (s.length() != 0) // se não é valor somente com centavos
                s = s + " e ";
            if (centavos.equals("1"))
                s = s + "um centavo";
            else {
                n = Integer.parseInt(centavos, 10);
                if (n <= 19)
                    s = s + unidade[n];
                else {             // para n = 37, tem-se:
                    unid = n % 10;   // unid = 37 % 10 = 7 (unidade sete)
                    dez = n / 10;    // dez  = 37 / 10 = 3 (dezena trinta)
                    s = s + dezena[dez];
                    if (unid != 0)
                        s = s + " e " + unidade[unid];
                }
                s = s + " centavos";
            }
        }
        return (s);
    }


    /**
     * @param data - java.util.Date a ser formatado
     * @return data no formato dd/MM/yyyy
     */
    public static String formatarDataPadrao(Date data) {
        if (data == null) {
            return null;
        }
        return getDataFormatada(data, "dd/MM/yyyy");
    }

    public static String formatarDataHora(Date data) {
        return getDataFormatada(data, "dd/MM/yyyy HH:mm:ss");
    }

    public static Date getDateStringYYYYMMDD(String valor) {
        if (valor != null) {
            try {
                return DateUtils.parseDate(valor, "yyyyMMdd");
            } catch (ParseException e) {
                logger.error("DATA CADASTRADA NO FORMATO ERRADO - " + e);
            }
        }
        return null;
    }

    public static String formataData(Date data) {
        if (data == null) {
            return null;
        }
        return DateFormatUtils.format(data, "yyyyMMdd");
    }

    public static String formataData(Date data, String formato) {
        if (data == null) {
            return null;
        }
        return DateFormatUtils.format(data, formato);
    }

    public static Date stringToDateDataPadrao(String data) {
        try {
            SimpleDateFormat df = new SimpleDateFormat("dd/MM/yyyy");
            return df.parse(data);
        } catch (Exception e) {
            return null;
        }
    }

    public static String formataData(String data) {
        if (data != null) {
            try {
                Date date = DateUtils.parseDate(data, "yyyyMMdd");
                return DateFormatUtils.format(date, "dd/MM/yyyy");
            } catch (ParseException e) {
            }
        }
        return null;
    }


    public static String formataHora(Date data) {
        if (data == null) {
            return null;
        }
        return DateFormatUtils.format(data, "HHmmss");
    }

    /**
     * verifica se a lista eh nula ou vazia
     *
     * @param lista
     * @return
     */
    public static Boolean isListaVaziaOuNula(Collection<?> lista) {
        return lista == null || lista.size() == 0;
    }

    public static Integer getValorInteger(String valor) {
        valor = retiraFormatacao(valor);
        return StringUtils.isNumeric(valor) ? Integer.parseInt(valor) : null;
    }

    public static Long getValorLong(String valor) {
        return StringUtils.isNumeric(valor) ? Long.parseLong(valor) : null;
    }

    public static BigDecimal getValorBigDecimal(String valor) {
        return !StringUtils.isBlank(valor) ? new BigDecimal(valor) : null;
    }

    public static BigDecimal getNotNull(BigDecimal valor) {
        return valor != null ? valor : BigDecimal.ZERO;
    }

    public static Boolean isSomenteNumero(String valor) {
        Pattern padrao = Pattern.compile("[0-9]*");
        // compara a string com o padrao
        Matcher m = padrao.matcher(valor);
        // verifica se a string confere com o padrao
        if (!m.matches()) {
            return Boolean.FALSE;
        }
        return Boolean.TRUE;
    }

    public static String formataTaxa(BigDecimal valor) {
        if (valor != null) {
            DecimalFormat formatTaxa = new DecimalFormat("###.000");
            return leftPad(formatTaxa.format(valor).replace(".", ","), 7, "0");
        }
        return null;
    }

    public static String formataTaxa6(BigDecimal valor) {
        if (valor != null) {
            DecimalFormat formatTaxa = new DecimalFormat("######.000");
            return leftPad(formatTaxa.format(valor).replace(".", ","), 10, "0");
        }
        return null;
    }

    public static String formataTaxa6(BigDecimal valor, int tamanho) {
        if (valor != null) {
            DecimalFormat formatTaxa = new DecimalFormat("######.000");
            return leftPad(formatTaxa.format(valor).replace(".", ","), tamanho, "0");
        }
        return null;
    }

    public static String formataValor(BigDecimal valor) {
        if (valor != null) {
            DecimalFormat formatValor = new DecimalFormat("#######.00");
            return leftPad(formatValor.format(valor).replace(".", ","), 10, "0");
        }
        return null;
    }

    public static String formataValorPadraoZero(BigDecimal valor, boolean taxa6) {
        if (valor != null) {
            return taxa6 ? formataTaxa6(valor) : formataValor(valor);
        }
        return taxa6 ? "0,000" : "0,00";
    }

    public static String formataValorPadraoZero(BigDecimal valor, boolean taxa6, int tamanho) {
        if (valor != null) {
            return taxa6 ? formataTaxa6(valor, tamanho) : formataValor(valor, tamanho);
        }
        return taxa6 ? "0,000" : "0,00";
    }

    public static String formataValor(BigDecimal valor, int tamanho) {
        if (valor != null) {
            DecimalFormat formatValor = new DecimalFormat("#######.00");
            return leftPad(formatValor.format(valor).replace(".", ","), tamanho, "0");
        }
        return null;
    }

    public static String bigDecimalToString(BigDecimal valor, int decimal) {
        if (valor != null) {
            return String.format("%." + decimal + "f", valor);
        }
        return null;
    }

    public static String formataValorMonetario(BigDecimal valor) {
        if (valor != null) {
            NumberFormat numberFormat = NumberFormat.getCurrencyInstance(new Locale("pt", "BR"));
            return numberFormat.format(valor.doubleValue());
        }
        return null;
    }

    public static String formataValorMonetario(Double valor) {
        return formataValorMonetario(new BigDecimal(valor));
    }

    public static String naoNull(String valor) {
        return valor == null ? "" : valor;
    }

    public static String objectToStringNotNull(Object valor) {
        String retorno = objectToString(valor);
        return retorno == null ? "" : retorno;
    }

    public static String objectToString(Object object) {
        if (object != null) {
            return object.toString();
        }
        return null;
    }

    public static boolean simNaoToBoolean(SimNao simNao) {
        if (simNao != null) {
            return simNao.equals(SimNao.S);
        }
        return false;
    }

    public static String toSimNao(Boolean object) {
        if (object != null) {
            if (object)
                return "Sim";
            return "Não";
        }
        return null;
    }

    public static String toSimNaoUpper(Boolean object) {
        if (object != null) {
            if (object)
                return "SIM";
            return "NAO";
        }
        return null;
    }

    public static String toSNUpper(Boolean object) {
        if (object != null) {
            if (object)
                return "SIM";
            return "NÃO";
        }
        return null;
    }

    public static String retiraFormatacao(String string) {
        if (string != null) {
            return string.replaceAll("-", "").replaceAll("\\.", "").replaceAll("/", "").replaceAll(",", "").trim();
        }
        return null;
    }

    public static String formatarCPF(String cpf) {

        if (!StringUtils.isBlank(cpf) && cpf.length() == 11) {
            return cpf.replaceAll("[\\.\\-/]", "");
        }
        return cpf;
    }

    public static String formatarCNPJ(String cnpj) {

        if (!StringUtils.isBlank(cnpj) && cnpj.length() == 14) {
            return cnpj.replaceAll("[\\.\\-/]", "");
        }
        return cnpj;
    }

    public static String stringMax(String descricao, int tamanho) {
        if (descricao == null) {
            return null;
        }
        return descricao.length() <= tamanho ? descricao : descricao.substring(0, tamanho);
    }

    public static int diferencaEmMeses(Date dateStart, Date dateEnd) {
        int count = 0;
        if (dateStart != null && dateEnd != null && dateStart.before(dateEnd)) {
            Calendar clStart = Calendar.getInstance();
            clStart.setTime(dateStart);
            Calendar clEnd = Calendar.getInstance();
            clEnd.setTime(dateEnd);
            while (clStart.get(Calendar.MONTH) != clEnd.get(Calendar.MONTH) || clStart.get(Calendar.YEAR) != clEnd.get(Calendar.YEAR)) {
                clStart.add(Calendar.MONTH, 1);
                count++;
            }
        }
        return count;
    }

    /**
     * Valida CNPJ do usuário.
     *
     * @param cnpj String valor com 14 dígitos
     */
    public static boolean validaCNPJ(String cnpj) {
        if ((cnpj == null || cnpj.length() != 14) || (cnpj.equals("00000000000000")))
            return false;

        try {
            Long.parseLong(cnpj);
        } catch (NumberFormatException e) { // CNPJ não possui somente números
            return false;
        }

        int soma = 0;
        String cnpj_calc = cnpj.substring(0, 12);

        char chr_cnpj[] = cnpj.toCharArray();
        for (int i = 0; i < 4; i++)
            if (chr_cnpj[i] - 48 >= 0 && chr_cnpj[i] - 48 <= 9)
                soma += (chr_cnpj[i] - 48) * (6 - (i + 1));

        for (int i = 0; i < 8; i++)
            if (chr_cnpj[i + 4] - 48 >= 0 && chr_cnpj[i + 4] - 48 <= 9)
                soma += (chr_cnpj[i + 4] - 48) * (10 - (i + 1));

        int dig = 11 - soma % 11;
        cnpj_calc = (new StringBuilder(String.valueOf(cnpj_calc))).append(dig != 10 && dig != 11 ? Integer.toString(dig) : "0").toString();
        soma = 0;
        for (int i = 0; i < 5; i++)
            if (chr_cnpj[i] - 48 >= 0 && chr_cnpj[i] - 48 <= 9)
                soma += (chr_cnpj[i] - 48) * (7 - (i + 1));

        for (int i = 0; i < 8; i++)
            if (chr_cnpj[i + 5] - 48 >= 0 && chr_cnpj[i + 5] - 48 <= 9)
                soma += (chr_cnpj[i + 5] - 48) * (10 - (i + 1));

        dig = 11 - soma % 11;
        cnpj_calc = (new StringBuilder(String.valueOf(cnpj_calc))).append(dig != 10 && dig != 11 ? Integer.toString(dig) : "0").toString();

        return cnpj.equals(cnpj_calc);
    }

    public static String getDDD(String telefone) {
        try {
            return telefone.split(" ")[0].replaceAll("\\(", "").replaceAll("\\)", "");
        } catch (Exception e) {
            return null;
        }
    }

    public static String getNumeroTelefone(String telefone) {
        try {
            return telefone.split(" ")[1].replaceAll("-", "");
        } catch (Exception e) {
            return null;
        }
    }

    public static Date minDateTime(Date date) {
        if (date != null) {
            date = DateUtils.truncate(date, Calendar.DAY_OF_MONTH);
        }
        return date;
    }

    public static Date maxDateTime(Date date) {
        if (date != null) {
            date = DateUtils.truncate(date, Calendar.DAY_OF_MONTH);
            date = DateUtils.addDays(date, 1);
            date = DateUtils.addMilliseconds(date, -1);
        }
        return date;
    }

    public static Object getXml(Object object) {
        try {
            StringWriter writer = new StringWriter();
            JAXBContext context = JAXBContext.newInstance(object.getClass());
            Marshaller m = context.createMarshaller();
            m.marshal(object, writer);
            return writer.toString();
        } catch (JAXBException e) {
            logger.error(e.getMessage(), e);
        }
        return null;
    }

    public static String toJson(Object object) {
        try {
            return new ObjectMapper().writeValueAsString(object);
        } catch (JsonProcessingException e) {
            logger.error(e.getMessage(), e);
        }
        return "";
    }

    public static Object jsonToObject(String json, Class<?> clazz) {
        try {
            return new ObjectMapper().readValue(json, clazz);
        } catch (IOException e) {
            e.printStackTrace();
        }
        return "";
    }

    public static <T> Optional<T> jsonParaObject(String json, Class<T> classe) {
        ObjectMapper mapper = new ObjectMapper();
        try {
            mapper.disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES);
            return Optional.of(mapper.readValue(json, classe));
        } catch (IOException e) {
            logger.error("Falha na conversão de Json para Objecto");
        }
        return Optional.empty();
    }

    public static String rightPad(String string, int tamanho) {
        return rightPad(string, tamanho, " ");
    }

    public static String leftPad(String string, int tamanho) {
        return leftPad(string, tamanho, " ");
    }

    public static String rightPad(String string, int tamanho, String complemento) {
        String rightPad = StringUtils.rightPad(StringUtils.defaultIfBlank(string, complemento), tamanho, complemento);
        if (rightPad.length() > tamanho) {
            return rightPad.substring(0, tamanho);
        }
        return rightPad;
    }

    public static String getDateStringFromPattern(Date data, String pattern) {
        SimpleDateFormat s = new SimpleDateFormat(pattern);
        return s.format(data);
    }

    public static String leftPad(String string, int tamanho, String complemento) {
        if (string == null) {
            return null;
        }
        String leftPad = StringUtils.leftPad(StringUtils.defaultIfBlank(string, complemento), tamanho, complemento);
        if (leftPad.length() > tamanho) {
            return leftPad.substring(0, tamanho);
        }
        return leftPad;
    }

    public static String stripAcentsToUpperCaseNotNull(String string) {
        return string != null ? StringUtils.stripAccents(string).toUpperCase() : "";
    }

    public static boolean validaEmail(String email) {
        EmailValidator emailValidator = new EmailValidator();
        return emailValidator.isValid(email, null);
    }

    /**
     * Valida CPF do usuário. Não aceita CPF's padrões como 11111111111 ou
     * 22222222222
     *
     * @param cpf String valor com 11 dígitos
     */
    public static boolean validaCPF(String cpf) {
        if (cpf == null || cpf.length() != 11 || isCPFPadrao(cpf))
            return false;
        return calcDigVerif(cpf.substring(0, 9)).equals(cpf.substring(9, 11));
    }

    /**
     * @param cpf String valor a ser testado
     * @return boolean indicando se o usuário entrou com um CPF padrão
     */
    private static boolean isCPFPadrao(String cpf) {
        if (cpf.equals("11111111111") || cpf.equals("22222222222") || cpf.equals("33333333333") || cpf.equals("44444444444") || cpf.equals("55555555555") || cpf.equals("66666666666") || cpf.equals("77777777777")
                || cpf.equals("88888888888") || cpf.equals("99999999999") || cpf.equals("00000000000")) {

            return true;
        }

        return false;
    }

    private static String calcDigVerif(String num) {
        Integer primDig, segDig;
        int soma = 0, peso = 10;
        for (int i = 0; i < num.length(); i++)
            soma += Integer.parseInt(num.substring(i, i + 1)) * peso--;

        if (soma % 11 == 0 || soma % 11 == 1)
            primDig = new Integer(0);
        else
            primDig = new Integer(11 - (soma % 11));

        soma = 0;
        peso = 11;
        for (int i = 0; i < num.length(); i++)
            soma += Integer.parseInt(num.substring(i, i + 1)) * peso--;

        soma += primDig.intValue() * 2;
        if (soma % 11 == 0 || soma % 11 == 1)
            segDig = new Integer(0);
        else
            segDig = new Integer(11 - (soma % 11));

        return primDig.toString() + segDig.toString();
    }

    public static String addRetToNameFile(String nameFile) {
        String name = nameFile.replace("RET", "");
        name = name.substring(0, 2) + "_RET" + name.substring(2, name.length());
        return name;
    }

    public static Date buscaDiaUtil(Date date) {
        Calendar c = Calendar.getInstance();
        c.setTime(date);
        c.add(Calendar.DAY_OF_MONTH, -3);

        int diaSemana = c.get(Calendar.DAY_OF_WEEK);
        if (diaSemana == Calendar.SUNDAY) {
            c.add(Calendar.DAY_OF_YEAR, 1);
            return c.getTime();
        }
        if (diaSemana == Calendar.SATURDAY) {
            c.add(Calendar.DAY_OF_YEAR, 2);
            return c.getTime();
        }
        return date;
    }

    public static File convertJpgToPdf(String fileName, String imagePath) throws IOException, DocumentException {
        File filePdf = new File(fileName);
        if (filePdf.exists()) {
            filePdf.delete();
            filePdf = new File(fileName);
        }

        Document document = new Document(PageSize.A4, 20, 20, 20, 20);
        PdfWriter.getInstance(document, new FileOutputStream(filePdf));
        document.open();

        Image image = Image.getInstance(imagePath);
        image.scaleToFit(new Rectangle(595, 842));

        document.add(image);
        document.close();
        return filePdf;
    }

    public static <E extends Enum<E>> String getEnumName(E enumValue) {
        return enumValue != null ? enumValue.name() : null;
    }

    public static String removerZerosAEsquerda(String numeroStr) {
        if (numeroStr == null || numeroStr.isEmpty()) {
            throw new IllegalArgumentException("A string não pode ser nula ou vazia");
        }

        if (numeroStr.contains(".")) {
            String[] partes = numeroStr.split("\\.");
            String parteInteira = partes[0].replaceFirst("^0+", "");

            if (parteInteira.isEmpty())
                parteInteira = "0";

            return parteInteira + "." + partes[1];
        }

        String numeroSemZeros = numeroStr.replaceFirst("^0+", "");

        if (numeroSemZeros.isEmpty())
            numeroSemZeros = "0";

        return numeroSemZeros;
    }

}
