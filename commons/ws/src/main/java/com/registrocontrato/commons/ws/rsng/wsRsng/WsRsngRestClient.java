package com.registrocontrato.commons.ws.rsng.wsRsng;

import com.registrocontrato.commons.ws.rsng.mapper.ContratoRsngMapper;
import com.registrocontrato.commons.ws.rsng.wsRsng.dto.apontamento.UltimoApontamentorRespRsng;
import com.registrocontrato.commons.ws.rsng.wsRsng.dto.baixaOuCancelamento.BaixaCancApontamentoReqRsng;
import com.registrocontrato.commons.ws.rsng.wsRsng.dto.baixaOuCancelamento.BaixaCancApontamentoRespRsng;
import com.registrocontrato.commons.ws.rsng.wsRsng.dto.inclusao.InclusaoApontamentoReqRsng;
import com.registrocontrato.commons.ws.rsng.wsRsng.dto.inclusao.InclusaoApontamentoRespRsng;
import com.registrocontrato.infra.entity.Financeira;
import com.registrocontrato.infra.exception.ServiceException;
import com.registrocontrato.registro.entity.Veiculo;
import com.registrocontrato.registro.entity.VeiculoModel;
import com.registrocontrato.registro.entity.VeiculoRsng;
import com.registrocontrato.registro.repository.ContratoRsngRepository;
import com.registrocontrato.registro.service.RegistroEnvioService;
import com.registrocontrato.seguranca.service.AcessoSenhaService;
import com.registrocontrato.seguranca.service.UsuarioService;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Component;
import org.springframework.web.util.UriComponentsBuilder;

import java.util.Optional;

import static com.registrocontrato.infra.util.PlaceconUtil.toJson;

@Component
public class WsRsngRestClient extends WsRsngDefault {

    @Value("${b3.url.ultimoApontamento:null}")
    private String urlUltimoApontamento;

    @Value("${b3.url.inclusaoApontamento:null}")
    private String urlInclusaoApontamento;

    @Value("${b3.url.baixaApontamento:null}")
    private String urlBaixaApontamento;

    @Value("${b3.url.cancelarApontamento:null}")
    private String urlCancelarApontamento;

    @Value("${b3.url.cancelarTransferenciaApontamento:null}")
    private String urlCancelarTransferenciaApontamento;

    private final AcessoSenhaService acessoSenhaService;

    private final ContratoRsngMapper mapper;

    public WsRsngRestClient(AcessoSenhaService acessoSenhaService,
                            ContratoRsngMapper mapper,
                            RegistroEnvioService registroEnvioService,
                            UsuarioService usuarioService) {
        super(registroEnvioService, usuarioService);
        this.acessoSenhaService = acessoSenhaService;
        this.mapper = mapper;
    }

    public UltimoApontamentorRespRsng consultarUltimoApontamento(Financeira fin, String chassi) throws RsngException {
        Optional<String> token = autenticar(fin);

        if (token.isPresent()) {
            String url = urlBase + urlUltimoApontamento;
            String uri = UriComponentsBuilder.fromHttpUrl(url)
                    .queryParam("numChassi", chassi)
                    .build().toString();

            return makeRequest(null, uri, HttpMethod.GET, null, token.get(), fin, UltimoApontamentorRespRsng.class);
        }
        throw new ServiceException("Falha na autenticação com o serviço RSNG.");
    }

    public InclusaoApontamentoRespRsng incluirApontamento(Financeira fin, VeiculoRsng veiculo) throws RsngException {

        Optional<String> token = autenticar(fin);

        if (token.isPresent()) {
            String url = urlBase + urlInclusaoApontamento;
            InclusaoApontamentoReqRsng request = mapper.entityToInclusaoApontamento(veiculo);
            String json = toJson(request);
            log.info(json);

            return makeRequest(veiculo.getContratoRsng(), url, HttpMethod.POST, request, token.get(), fin, InclusaoApontamentoRespRsng.class);
        }

        throw new RsngException("Falha na autenticação com o serviço RSNG.");
    }

    public BaixaCancApontamentoRespRsng cancelarApontamento(Financeira fin, VeiculoModel veiculo) throws RsngException {
        String url = urlBase + urlCancelarApontamento;
        return baixarCancelarApontamento(fin, veiculo, url);
    }

    public BaixaCancApontamentoRespRsng baixarApontamento(Financeira fin, VeiculoModel veiculo) throws RsngException {
        String url = urlBase + urlBaixaApontamento;
        return baixarCancelarApontamento(fin, veiculo, url);
    }

//    public boolean cancelarTransferenciaApontamento(Financeira fin, Veiculo veiculo) throws RsngException {
//        String url = urlBase + urlCancelarTransferenciaApontamento;
//        return baixarCancelarApontamento(fin, veiculo, url);
//    }

    private BaixaCancApontamentoRespRsng baixarCancelarApontamento(Financeira fin, VeiculoModel veiculoModel, String url) throws RsngException {
        Optional<String> token = autenticar(fin);

        if (token.isPresent()) {

            if (veiculoModel instanceof VeiculoRsng) {
                VeiculoRsng veiculo = (VeiculoRsng) veiculoModel;
                BaixaCancApontamentoReqRsng request = mapper.veiculoToBaixaApontamento(veiculo);

                return makeRequest(veiculo.getContratoRsng(), url, HttpMethod.POST, request, token.get(), fin, BaixaCancApontamentoRespRsng.class);
            } else if (veiculoModel instanceof Veiculo) {
                Veiculo veiculo = (Veiculo) veiculoModel;
                BaixaCancApontamentoReqRsng request = mapper.veiculoToBaixaApontamento(veiculo);

                return makeRequest(veiculo.getContrato(), url, HttpMethod.POST, request, token.get(), fin, BaixaCancApontamentoRespRsng.class);
            }


        }

        throw new ServiceException("Falha na autenticação com o serviço RSNG.");

    }

    @Override
    AcessoSenhaService getAcessoSenhaService() {
        return acessoSenhaService;
    }
}
