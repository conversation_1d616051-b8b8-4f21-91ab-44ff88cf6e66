package com.registrocontrato.commons.ws.rsng.wsRsng;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.registrocontrato.commons.ws.rsng.wsRsng.dto.AuthTokenRsngDTO;
import com.registrocontrato.commons.ws.rsng.wsRsng.dto.ErrosRsngDTO;
import com.registrocontrato.infra.entity.Financeira;
import com.registrocontrato.infra.exception.AcessoSenhaException;
import com.registrocontrato.infra.exception.ServiceException;
import com.registrocontrato.infra.util.PlaceconUtil;
import com.registrocontrato.registro.entity.*;
import com.registrocontrato.registro.service.RegistroEnvioService;
import com.registrocontrato.seguranca.service.AcessoSenhaService;
import com.registrocontrato.seguranca.service.UsuarioService;
import com.registrocontrato.seguranca.service.dto.AcessoSenhaB3DTO;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.apache.http.client.HttpClient;
import org.apache.http.conn.ssl.NoopHostnameVerifier;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.ssl.SSLContextBuilder;
import org.apache.http.ssl.TrustStrategy;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.http.client.ClientHttpRequestFactory;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.net.ssl.SSLContext;
import javax.servlet.http.HttpServletRequest;
import javax.xml.bind.JAXBContext;
import javax.xml.bind.JAXBException;
import javax.xml.bind.Marshaller;
import javax.xml.bind.annotation.XmlAccessorType;
import java.io.File;
import java.io.IOException;
import java.io.StringWriter;
import java.nio.charset.StandardCharsets;
import java.security.cert.X509Certificate;
import java.util.Collections;
import java.util.Date;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

import static com.registrocontrato.infra.util.PlaceconUtil.*;

public abstract class WsRsngDefault {

    protected final Log log = LogFactory.getLog(getClass());

    private AcessoSenhaB3DTO credenciais;

    protected ClientHttpRequestFactory client;

    @Value("${b3.url.base:null}")
    protected String urlBase;

    @Value("${b3.url.auth:null}")
    private String urlAuth;

    @Value("${detran.certificados:null}")
    private String CERT_DIR;

    @Value("${detran.connectionTimeout:20000}")
    private Integer CONNECTION_TIMEOUT;

    protected final RegistroEnvioService registroEnvioService;

    protected final UsuarioService usuarioService;

    protected WsRsngDefault(RegistroEnvioService registroEnvioService, UsuarioService usuarioService) {
        this.registroEnvioService = registroEnvioService;
        this.usuarioService = usuarioService;
    }

    abstract AcessoSenhaService getAcessoSenhaService();

    private ClientHttpRequestFactory getClientHttpRequestFactoryWithSSL(Financeira fin) throws AcessoSenhaException {
        log.info("Buscando credenciais B3");
        this.credenciais = getAcessoSenhaService().recuperarAcessoApiB3Financeira(fin);
        String password = credenciais.getSenhaCertificadoB3();

        try {
            log.info("Recuperando certificado B3");
            File certKeyStore = new File(CERT_DIR + credenciais.getReferenciaArquivo() + credenciais.getNomeCertificadoPFXB3());
            TrustStrategy acceptingTrustStrategy = (X509Certificate[] chain, String authType) -> true;
            log.info("SSL");
            SSLContext sslContext = SSLContextBuilder.create()
                    .loadKeyMaterial(certKeyStore, password.toCharArray(), password.toCharArray())
                    .loadTrustMaterial(acceptingTrustStrategy).build();

            log.info("Http");
            HttpClient client = HttpClients.custom().setSSLContext(sslContext).setSSLHostnameVerifier(NoopHostnameVerifier.INSTANCE).build();
            HttpComponentsClientHttpRequestFactory requestFactory = new HttpComponentsClientHttpRequestFactory(client);
            requestFactory.setConnectTimeout(CONNECTION_TIMEOUT);
            requestFactory.setReadTimeout(CONNECTION_TIMEOUT);
            return requestFactory;
        } catch (Exception e) {
            log.error("Falha na obteção do certificado da B3" + e.getMessage());
            throw new AcessoSenhaException("Falha na assinatura do certificado da B3");
        }
    }

    private HttpHeaders getHttpHeadersToAuth() {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
        headers.setAccept(Collections.singletonList(MediaType.ALL));
        headers.set("Chave", this.credenciais.getChaveDeAcesso());
        return headers;
    }

    protected HttpHeaders getHeaders(String chaveAcesso) {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.setAccept(Collections.singletonList(MediaType.ALL));
        headers.set("Authorization", "Bearer " + chaveAcesso);
        return headers;
    }

    private String[] buscarClientIdSecret() {
        String input = this.credenciais.getClientIdSecretB3();
        String[] lines = input.split("\n");

        String clientId = null;
        String clientSecret = null;

        for (String line : lines) {
            String[] keyValue = line.split(":");

            if (keyValue[0].trim().equals("CLIENT_ID"))
                clientId = keyValue[1].trim();
            if (keyValue[0].trim().equals("SECRET"))
                clientSecret = keyValue[1].trim();
        }

        String[] result = {clientId, clientSecret};
        return result;
    }

    protected Optional<String> autenticar(Financeira fin) throws RsngException {
        try {
            log.info("Autenticando no RSNG");
            this.client = getClientHttpRequestFactoryWithSSL(fin);

            log.info("Montando corpo da requisição");
            MultiValueMap<String, String> map = new LinkedMultiValueMap<>();
            map.add("grant_type", "client_credentials");
            map.add("client_id", buscarClientIdSecret()[0]);
            map.add("client_secret", buscarClientIdSecret()[1]);

            HttpEntity<MultiValueMap<String, String>> request = new HttpEntity<>(map, getHttpHeadersToAuth());

            RestTemplate restTemplate = new RestTemplate(client);
            ResponseEntity<String> responseEntity = restTemplate.postForEntity(urlBase + urlAuth, request, String.class);
            log.info("Autenticado com sucesso - " + responseEntity.getBody());
            Optional<AuthTokenRsngDTO> authTokenRsngDTO = jsonParaObject(responseEntity.getBody(), AuthTokenRsngDTO.class);

            return authTokenRsngDTO.map(AuthTokenRsngDTO::getToken);

        } catch (HttpClientErrorException e) {
            log.error("Falha ao solicitar o token para o RSNG" + e);
            throw new RsngException("Falha na autenticação");
        } catch (AcessoSenhaException e) {
            throw new RsngException("Chaves de autenticação não cadastradas no Placecon");
        }
    }

    protected <T> T makeRequest(ContratoModel contratoModel, String url, HttpMethod httpMethod, Object request, String token, Financeira fin, Class<T> responseType) throws ServiceException, RsngException {
        long start = System.currentTimeMillis();
        ResponseEntity<String> response = null;

        HttpEntity httpEntity = new HttpEntity<>(getHeaders(token));
        if (request != null) {
            httpEntity = new HttpEntity<>(toJson(request), getHeaders(token));
        }

        try {
            this.client = getClientHttpRequestFactoryWithSSL(fin);
            response = new RestTemplate(this.client).exchange(
                    url,
                    httpMethod,
                    httpEntity,
                    String.class
            );

            return new ObjectMapper().readValue(response.getBody(), responseType);

        } catch (HttpClientErrorException e) {
            int codErro = e.getStatusCode().value();
            String mensagemErro = new String(e.getResponseBodyAsByteArray(), StandardCharsets.UTF_8);
            response = new ResponseEntity<>(mensagemErro, HttpStatus.valueOf(codErro));
            log.error("FALHA NA REQUISIÇÃO RSNG: " + codErro);
            log.error("ERRO: " + mensagemErro);

            if (codErro == 422) {
                ErrosRsngDTO erroRsng = (ErrosRsngDTO) PlaceconUtil.jsonToObject(mensagemErro, ErrosRsngDTO.class);
                throw new RsngException("Erro código: " + mensagemErro, erroRsng);
            }
            throw new RsngException(mensagemErro);
        } catch (Exception e) {
            log.error("FALHA NA CONVERSÃO DOS DADOS DO RSNG" + e);
            throw new ServiceException(e);
        } finally {
            if (contratoModel != null)
                registrarLog(contratoModel, start, request, response.getBody());
        }
    }

    protected void registrarLog(ContratoModel contrato, long start, Object request, Object response) {
        long end = System.nanoTime();
        String execution = TimeUnit.NANOSECONDS.toMillis(end - start) + " se";
        String username = "BATCH";
        if (SecurityContextHolder.getContext().getAuthentication() != null) {
            username = SecurityContextHolder.getContext().getAuthentication().getName();
        }
        String multiLineAuditString = recorder(request, response, execution, username);
        log.info(multiLineAuditString);

        registroEnvioService.save(new RegistroEnvio(contrato, username, multiLineAuditString));
    }

    private String recorder(Object request, Object response, String execution, String username) {
        String email = usuarioService.buscarEmailByCpf(username);

        String ipClient = null;
        String localAddr = null;
        if (RequestContextHolder.getRequestAttributes() != null) {
            HttpServletRequest httpRequest = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
            ipClient = httpRequest.getHeader("X-Forwarded-For");
            localAddr = httpRequest.getLocalAddr();
        }

        final StringBuilder builder = new StringBuilder();
        builder.append("\nAuditoria -> Geração de Apontamento no RSNG" + "\n");
        builder.append("=============================================================\n");
        builder.append("Quem: ");
        builder.append(username);
        builder.append("\n");
        builder.append("Email: ");
        builder.append(email);
        builder.append("\n");
        builder.append("Ação: ");
        builder.append("Envio transação para DETRAN");
        builder.append("\n");
        builder.append("Quando: ");
        builder.append(DateFormatUtils.format(new Date(), "dd/MM/yyyy HH:mm:ss"));
        builder.append("\n");
        builder.append("Tempo de Execução: ");
        builder.append(execution);
        builder.append("\n");
        builder.append("Requisiçao: ");
        builder.append(gravarRequestResponse(request));
        builder.append("\n");
        builder.append("Retorno: ");
        builder.append(response != null ? gravarRequestResponse(response) : response);
        builder.append("\n");
        builder.append("IP Cliente: ");
        builder.append(ipClient);
        builder.append("\n");
        builder.append("IP Servidor: ");
        builder.append(localAddr);
        builder.append("\n");
        builder.append("=============================================================");

        return builder.toString();
    }

    private Object gravarRequestResponse(Object obj) {
        Class<?> xml = obj.getClass();
        if (xml.isAnnotationPresent(XmlAccessorType.class))
            return getXml(obj);
        return stringMax(String.valueOf(toJson(obj)), 2048);
    }

}
