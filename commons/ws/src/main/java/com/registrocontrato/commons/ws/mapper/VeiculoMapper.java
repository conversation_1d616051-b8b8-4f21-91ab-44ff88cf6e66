package com.registrocontrato.commons.ws.mapper;

import com.registrocontrato.commons.ws.dto.robot.VeiculoRobotResponseDTO;
import com.registrocontrato.infra.entity.Uf;
import com.registrocontrato.registro.entity.Contrato;
import com.registrocontrato.registro.enums.TipoVeiculo;
import com.registrocontrato.registro.entity.Veiculo;
import com.registrocontrato.registro.service.ContratoService;
import com.registrocontrato.registro.service.MarcaService;
import com.registrocontrato.registro.service.MensagemRetornoService;
import com.registrocontrato.registro.service.ModeloService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;

@Component
public class VeiculoMapper {

    @Autowired
    private MarcaService marcaService;

    @Autowired
    private ModeloService modeloService;

    @Autowired
    private MensagemRetornoService mensagemRetornoService;

    public VeiculoRobotResponseDTO convertEntityToRobotResponseDTO(Veiculo veiculo) {
        VeiculoRobotResponseDTO responseDTO = new VeiculoRobotResponseDTO();
        BeanUtils.copyProperties(veiculo, responseDTO, "cnpjAgenteFinanceiro", "ufRegistro");
        responseDTO.setCnpjAgenteFinanceiro(veiculo.getContrato().getFinanceira().getDocumento());
        responseDTO.setUfRegistro(veiculo.getContrato().getUfRegistro());
        return responseDTO;
    }

    public Veiculo mapperFromAuditoriaToVeiculo(Object[] objRecovery, List<String> colunas, Contrato contrato) {
        Veiculo veiculo = new Veiculo();

        for (String coluna : colunas) {
            if (objRecovery[colunas.indexOf(coluna)] != null) {
                if (coluna.equals("id")) {
                    veiculo.setId(Long.parseLong(objRecovery[colunas.indexOf(coluna)].toString(), 10));
                    continue;
                }
                if (coluna.equals("numero_chassi")) {
                    veiculo.setNumeroChassi((String) objRecovery[colunas.indexOf(coluna)]);
                    continue;
                }
                if (coluna.equals("placa")) {
                    veiculo.setPlaca((String) objRecovery[colunas.indexOf(coluna)]);
                    continue;
                }
                if (coluna.equals("uf")) {
                    veiculo.setUf(Uf.valueOf(objRecovery[colunas.indexOf(coluna)].toString()));
                    continue;
                }
                if (coluna.equals("numero_renavam")) {
                    veiculo.setNumeroRenavam((String) objRecovery[colunas.indexOf(coluna)]);
                    continue;
                }
                if (coluna.equals("numero_gravame")) {
                    veiculo.setNumeroGravame((String) objRecovery[colunas.indexOf(coluna)]);
                    continue;
                }
                if (coluna.equals("numero_registro_detran")) {
                    veiculo.setNumeroRegistroDetran((String) objRecovery[colunas.indexOf(coluna)]);
                    continue;
                }
                if (coluna.equals("contador_transacao")) {
                    veiculo.setContadorTransacao((Integer) objRecovery[colunas.indexOf(coluna)]);
                    continue;
                }
                if (coluna.equals("ano_fabricacao")) {
                    veiculo.setAnoFabricacao((Integer) objRecovery[colunas.indexOf(coluna)]);
                    continue;
                }
                if (coluna.equals("ano_modelo")) {
                    veiculo.setAnoModelo((Integer) objRecovery[colunas.indexOf(coluna)]);
                    continue;
                }
                if (coluna.equals("tipo")) {
                    veiculo.setTipo(TipoVeiculo.valueOf(objRecovery[colunas.indexOf(coluna)].toString()));
                    continue;
                }
                if (coluna.equals("chassi_remarcado")) {
                    veiculo.setChassiRemarcado((Boolean) objRecovery[colunas.indexOf(coluna)]);
                    continue;
                }
                if (coluna.equals("veiculo0km")) {
                    veiculo.setVeiculo0Km((Boolean) objRecovery[colunas.indexOf(coluna)]);
                    continue;
                }
                if (coluna.equals("cor")) {
                    veiculo.setCor((String) objRecovery[colunas.indexOf(coluna)]);
                    continue;
                }
                if (coluna.equals("data_transmissaodetran")) {
                    veiculo.setDataTransmissaoDETRAN((Date) objRecovery[colunas.indexOf(coluna)]);
                    continue;
                }
                if (coluna.equals("mensagem_retorno_detalhada")) {
                    veiculo.setMensagemRetornoDetalhada((String) objRecovery[colunas.indexOf(coluna)]);
                    continue;
                }
                if (coluna.equals("documento_arrecadacao")) {
                    veiculo.setDocumentoArrecadacao((String) objRecovery[colunas.indexOf(coluna)]);
                    continue;
                }

                if (coluna.equals("marca_id")) {
                    Long marcaId = Long.parseLong(objRecovery[colunas.indexOf(coluna)].toString());
                    try {
                        veiculo.setMarca(marcaService.findByMarcaId(marcaId));
                        continue;
                    } catch (NullPointerException e) {
                        System.out.println("Aconteceu um erro " + e + " na configuração da marca");
                        continue;
                    }
                }
                if (coluna.equals("modelo_id")) {
                    Long modeloId = Long.parseLong(objRecovery[colunas.indexOf(coluna)].toString());
                    try {
                        veiculo.setModelo(modeloService.findByMarcaId(modeloId));
                        continue;
                    } catch (NullPointerException e) {
                        System.out.println("Aconteceu um erro " + e + " na configuração da marca");
                        continue;
                    }
                }
                if (coluna.equals("mensagem_retorno_id")) {
                    Long mensagemRetornoId = Long.parseLong(objRecovery[colunas.indexOf(coluna)].toString());
                    try {
                        veiculo.setMensagemRetorno(mensagemRetornoService.findByMarcaId(mensagemRetornoId));
                        continue;
                    } catch (NullPointerException e) {
                        System.out.println("Aconteceu um erro " + e + " na configuração da marca");
                        continue;
                    }
                }
                if (coluna.equals("contrato_id")) {
                    veiculo.setContrato(contrato);
                }
            }
        }
        return veiculo;
    }

}
