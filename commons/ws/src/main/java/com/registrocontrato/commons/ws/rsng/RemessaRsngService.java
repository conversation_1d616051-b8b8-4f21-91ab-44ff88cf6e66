package com.registrocontrato.commons.ws.rsng;

import com.registrocontrato.commons.ws.rsng.mapper.ContratoRsngMapper;
import com.registrocontrato.commons.ws.rsng.wsRsng.WsRsngRestClient;
import com.registrocontrato.commons.ws.rsng.wsRsng.dto.apontamento.UltimoApontamentorRespRsng;
import com.registrocontrato.infra.entity.Financeira;
import com.registrocontrato.infra.entity.StatusProcessamento;
import com.registrocontrato.infra.exception.ServiceException;
import com.registrocontrato.infra.service.BaseService;
import com.registrocontrato.infra.util.PlaceconUtil;
import com.registrocontrato.registro.dto.RemessaB3Filter;
import com.registrocontrato.registro.entity.Contrato;
import com.registrocontrato.registro.entity.ItensRemessaB3;
import com.registrocontrato.registro.entity.RemessaB3;
import com.registrocontrato.registro.repository.ArquivoRemessaRsngRepository;
import com.registrocontrato.registro.repository.RemessaRsngRepository;
import com.registrocontrato.registro.service.ContratoService;
import com.registrocontrato.seguranca.entity.Perfil;
import com.registrocontrato.seguranca.entity.Usuario;
import com.registrocontrato.seguranca.service.UsuarioService;
import org.apache.commons.io.FileUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.data.repository.PagingAndSortingRepository;
import org.springframework.stereotype.Service;

import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import java.io.*;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class RemessaRsngService extends BaseService<RemessaB3, RemessaB3Filter> {

    @Value("${file.remessa.b3.dir:null}")
    private String FILE_DIR;

    private final RemessaRsngRepository remessaRsngRepository;

    private final UsuarioService usuarioService;

    private final WsRsngRestClient wsB3RestClient;

    private final ContratoRsngMapper contratoRsngMapper;

    private final ContratoService contratoService;

    public RemessaRsngService(RemessaRsngRepository remessaRsngRepository, UsuarioService usuarioService, WsRsngRestClient wsB3RestClient, ContratoRsngMapper contratoRsngMapper, ContratoService contratoService) {
        this.remessaRsngRepository = remessaRsngRepository;
        this.usuarioService = usuarioService;
        this.wsB3RestClient = wsB3RestClient;
        this.contratoRsngMapper = contratoRsngMapper;
        this.contratoService = contratoService;
    }

    public RemessaB3 processarRemessaComInputs(List<ItensRemessaB3> itens, String usuario, Financeira financeira) {
        RemessaB3 entity = new RemessaB3();
        entity.setDataTransacao(new Date());
        entity.setUsuario(usuario);
        entity.setFinanceira(financeira);

        return save(itens, entity);
    }

    public RemessaB3 processarRemessaComArquivo(InputStream input, String fileName, String usuario, Financeira financeira) {
        String referenciaArquivo = gravarArquivoUpload(input, fileName, financeira.getDocumento());
        List<ItensRemessaB3> itens = buscarListaDeItensDoArquivo(referenciaArquivo);

        RemessaB3 entity = new RemessaB3();
        entity.setDataTransacao(new Date());
        entity.setUsuario(usuario);
        entity.setFinanceira(financeira);
        entity.setReferenciaArquivo(referenciaArquivo);

        return save(itens, entity);
    }

    public RemessaB3 save(List<ItensRemessaB3> itens, RemessaB3 remessa) {
        remessa.setItensRemessaB3(itens);
        save(remessa);

        Map<ItensRemessaB3, UltimoApontamentorRespRsng> consulta = consultarDadosNaB3(remessa);
        List<ItensRemessaB3> itensProcessados = transmitirContratos(consulta, remessa.getFinanceira(), remessa.getUsuario());
        itensProcessados.forEach(i -> i.setRemessaB3(remessa));
        save(remessa);
        return remessa;
    }

    private String gravarArquivoUpload(InputStream inputStream, String fileName, String cnpjFinanceira) throws ServiceException {
        String referenciaArquivo = cnpjFinanceira + "_" + PlaceconUtil.formataData(new Date()) + "_" + fileName;
        try {
            File targetFile = new File(FILE_DIR, referenciaArquivo);
            FileUtils.copyInputStreamToFile(inputStream, targetFile);
        } catch (IOException e) {
            throw new ServiceException(e.getMessage());
        }
        return referenciaArquivo;
    }

    public RemessaB3 buscarRemessaComItens(Long id) {
        return remessaRsngRepository.findByIdWithItens(id);
    }

    private List<ItensRemessaB3> transmitirContratos(Map<ItensRemessaB3, UltimoApontamentorRespRsng> consulta, Financeira financeira, String usuario) {
        List<ItensRemessaB3> itensProcessados = new ArrayList<>();

        for (Map.Entry<ItensRemessaB3, UltimoApontamentorRespRsng> entry : consulta.entrySet()) {
            ItensRemessaB3 item = entry.getKey();
            UltimoApontamentorRespRsng apontamentos = entry.getValue();

            if (!apontamentos.getDadosDoApontamento().getMensagemNegocio().getCodigoRetorno().equals(30)) {
                item.setStatus(StatusProcessamento.PROCESSADO_ERRO);
                item.setMensagemRetorno("Falha na obtenção dos dados da B3");
                itensProcessados.add(item);
                continue;
            }

            try {
                Contrato contrato = contratoRsngMapper.dtoToEntity(apontamentos, financeira);
                contratoService.transmitir(contrato, usuario);
                item.setStatus(StatusProcessamento.PROCESSADO);
                item.setContrato(contrato);
                item.setMensagemRetorno("Sucesso");
                itensProcessados.add(item);
            } catch (Exception e) {
                logger.error(e.getMessage());
                item.setStatus(StatusProcessamento.PROCESSADO_ERRO);
                item.setMensagemRetorno(e.getMessage());
                itensProcessados.add(item);
            }
        }

        return itensProcessados;
    }

    private Map<ItensRemessaB3, UltimoApontamentorRespRsng> consultarDadosNaB3(RemessaB3 entity) {
        Map<ItensRemessaB3, UltimoApontamentorRespRsng> processamentos = new HashMap<>();

        for (ItensRemessaB3 item : entity.getItensRemessaB3()) {
            try {
                UltimoApontamentorRespRsng apontamentoB3 = wsB3RestClient.consultarUltimoApontamento(entity.getFinanceira(), item.getChassi());
                item.setStatus(StatusProcessamento.EM_PROCESSAMENTO);
                processamentos.put(item, apontamentoB3);
            } catch (Exception e) {
                item.setStatus(StatusProcessamento.PROCESSADO_ERRO);
                processamentos.put(item, null);
            }
        }
        return processamentos;
    }

    private List<ItensRemessaB3> buscarListaDeItensDoArquivo(String referencia) {
        try (InputStream inputStream = Files.newInputStream(Paths.get(FILE_DIR, referencia))) {
            BufferedReader br = new BufferedReader(new InputStreamReader(inputStream));
            List<String> chassis = br.lines()
                    .map(s -> new String(s.getBytes()))
                    .collect(Collectors.toList());

            List<ItensRemessaB3> itensRemessaB3 = new ArrayList<>();
            chassis.forEach(chassi -> {
                ItensRemessaB3 item = new ItensRemessaB3();
                item.setChassi(chassi);
                item.setStatus(StatusProcessamento.EM_PROCESSAMENTO);
                itensRemessaB3.add(item);
            });

            return itensRemessaB3;
        } catch (IOException e) {
            throw new ServiceException("Falha na leitura do arquivo de chassis");
        }
    }

    @Override
    public Page<RemessaB3> findAll(int first, int pageSize, RemessaB3Filter filter) {
        Specification<RemessaB3> specification = new Specification<RemessaB3>() {
            @Override
            public Predicate toPredicate(Root<RemessaB3> root, CriteriaQuery<?> cq, CriteriaBuilder cb) {
                List<Predicate> predicates = new ArrayList<>();

                if (filter.getDataInicio() != null)
                    predicates.add(cb.greaterThanOrEqualTo(root.<Date>get("dataTransacao"), PlaceconUtil.minDateTime(filter.getDataInicio())));

                if (filter.getDataFim() != null)
                    predicates.add(cb.lessThanOrEqualTo(root.<Date>get("dataTransacao"), PlaceconUtil.maxDateTime(filter.getDataFim())));

                if (filter.getFinanceira() != null)
                    predicates.add(cb.equal(root.get("financeira"), filter.getFinanceira()));

                // se o usuario eh financeira filtrar somente os usuario da financeira
                Usuario usuarioLogado = usuarioService.findByCpfFinanceiras(filter.getUsuario());
                if (usuarioLogado.getPerfil() == Perfil.FINANCEIRA || !usuarioLogado.getFinanceiras().isEmpty()) {
                    List<Financeira> values = usuarioLogado.getFinanceiras();
                    predicates.add(root.get("financeira").in(values));
                }

                return andTogether(predicates, cb);
            }

            private Predicate andTogether(List<Predicate> predicates, CriteriaBuilder cb) {
                return cb.and(predicates.toArray(new Predicate[0]));
            }
        };
        return remessaRsngRepository.findAll(specification, new PageRequest(first / pageSize, pageSize, new Sort(Sort.Direction.DESC, "id")));
    }

    @Override
    protected PagingAndSortingRepository<RemessaB3, Long> getRepository() {
        return remessaRsngRepository;
    }
}
