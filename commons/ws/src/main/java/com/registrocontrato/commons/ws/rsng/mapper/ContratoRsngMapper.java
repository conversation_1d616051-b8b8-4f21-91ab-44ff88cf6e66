package com.registrocontrato.commons.ws.rsng.mapper;

import com.registrocontrato.commons.ws.dto.ContratoDTO;
import com.registrocontrato.commons.ws.dto.VeiculoDTO;
import com.registrocontrato.commons.ws.dto.rsng.ContratoRsngRequest;
import com.registrocontrato.commons.ws.dto.rsng.VeiculoRsngRequest;
import com.registrocontrato.commons.ws.rsng.wsRsng.dto.ContratoRsngDTO;
import com.registrocontrato.commons.ws.rsng.wsRsng.dto.DevedorRsng;
import com.registrocontrato.commons.ws.rsng.wsRsng.dto.FinanceiraRsng;
import com.registrocontrato.commons.ws.rsng.wsRsng.dto.VeiculoRsngDTO;
import com.registrocontrato.commons.ws.rsng.wsRsng.dto.apontamento.Apontamento;
import com.registrocontrato.commons.ws.rsng.wsRsng.dto.apontamento.UltimoApontamentorRespRsng;
import com.registrocontrato.commons.ws.rsng.wsRsng.dto.baixaOuCancelamento.BaixaCancApontamentoReqRsng;
import com.registrocontrato.commons.ws.rsng.wsRsng.dto.inclusao.InclusaoApontamentoReqRsng;
import com.registrocontrato.infra.entity.Financeira;
import com.registrocontrato.infra.entity.Municipio;
import com.registrocontrato.infra.entity.Uf;
import com.registrocontrato.infra.util.PlaceconUtil;
import com.registrocontrato.registro.entity.*;
import com.registrocontrato.registro.enums.IndiceFinanceiro;
import com.registrocontrato.registro.enums.TipoContrato;
import com.registrocontrato.registro.enums.TipoRestricao;
import com.registrocontrato.registro.enums.TipoVeiculo;
import com.registrocontrato.registro.service.MunicipioService;
import com.registrocontrato.seguranca.service.FinanceiraService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.Objects;

import static com.registrocontrato.infra.util.PlaceconUtil.*;

@Component
public class ContratoRsngMapper {

    private static final Logger log = LoggerFactory.getLogger(ContratoRsngMapper.class);
    private final MunicipioService municipioService;

    private final FinanceiraService financeiraService;

    public ContratoRsngMapper(MunicipioService municipioService, FinanceiraService financeiraService) {
        this.municipioService = municipioService;
        this.financeiraService = financeiraService;
    }

    public Financeira converterDocumentoToEntity(String documento) {
        return financeiraService.findByDocumento(documento);
    }

    public ContratoRsng convertToEntity(ContratoRsngRequest contrato) {
        ContratoRsng entity = new ContratoRsng();
        BeanUtils.copyProperties(contrato, entity, "veiculos");

        String codMunicipioDevedor = PlaceconUtil.leftPad(contrato.getCodigoMunicipioDevedor(), 4, "0");
        Municipio municipioDevedor = municipioService.findByCodigoDenatran(codMunicipioDevedor);

        String codMunicipioLibCred = PlaceconUtil.leftPad(contrato.getCodigoMunicipioLiberacaoCredito(), 4, "0");
        Municipio municipioLiberacao = municipioService.findByCodigoDenatran(codMunicipioLibCred);

        Uf ufEnderecoDevedor = Uf.valueOf(contrato.getUfDevedor().toUpperCase());
        Uf ufLiberacaoCredito = Uf.valueOf(contrato.getUfLiberacaoCredito().toUpperCase());
        Uf ufRegistro = Uf.valueOf(contrato.getUfRegistro().toUpperCase());
        TipoRestricao tipoRestricao = TipoRestricao.getByCodigo(contrato.getTipoRestricao());

        TipoContrato tipoContrato = TipoContrato.getByCodigo(contrato.getTipoContrato());
        IndiceFinanceiro siglaIndiceFinaceiro = IndiceFinanceiro.getByCodigo(contrato.getSiglaIndiceFinaceiro());
        Financeira financeira = financeiraService.findByDocumento(contrato.getCnpjAgenteFinanceiro());

        entity.setTipoContrato(tipoContrato);
        entity.setTipoRestricao(tipoRestricao);
        entity.setSiglaIndiceFinaceiro(siglaIndiceFinaceiro);
        entity.setMunicipioLiberacao(municipioLiberacao);
        entity.setMunicipioDevedor(municipioDevedor);
        entity.setUfEnderecoDevedor(ufEnderecoDevedor);
        entity.setUfLiberacaoCredito(ufLiberacaoCredito);
        entity.setUfRegistro(ufRegistro);
        entity.setFinanceira(financeira);

        if (StringUtils.isNotEmpty(entity.getNumeroGrupoConsorcio())
            && !entity.getNumeroGrupoConsorcio().equals("0"))
            entity.setTipoRestricao(TipoRestricao.ALIENACAO_FIDUCIARIA_CONS);

        if (contrato.getIntegradora() != null)
            entity.setIntegradora(contrato.getIntegradora());

        VeiculoRsng veiculo;
        for (VeiculoRsngRequest v : contrato.getVeiculos()) {
            veiculo = new VeiculoRsng();
            BeanUtils.copyProperties(v, veiculo);

            veiculo.setContratoRsng(entity);
            veiculo.setUf(v.getUfPlacaAtual() == null ? null : Uf.valueOf(v.getUfPlacaAtual().toUpperCase()));
            veiculo.setChassiRemarcado(v.getIdentificadorRemarcacao() == 1);
            veiculo.setTipo(TipoVeiculo.getByCodigo(v.getCodTipo()));
            veiculo.setVeiculo0Km(StringUtils.isBlank(v.getPlaca()));
            entity.getVeiculos().add(veiculo);
        }

        return entity;
    }

    public Contrato dtoToEntity(UltimoApontamentorRespRsng apontamentoB3DTO, Financeira financeira) {
        Apontamento apontamento = apontamentoB3DTO.getDadosDoApontamento().getApontamento();
        ContratoRsngDTO contratoRsngDTO = apontamento.getContratoB3();
        DevedorRsng devedorRsng = apontamento.getDevedorB3();
        VeiculoRsngDTO veiculoRsngDTO = apontamento.getVeiculoB3();

        Veiculo veiculo = new Veiculo();
        BeanUtils.copyProperties(veiculoRsngDTO, veiculo);
        veiculo.setNumeroChassi(Objects.nonNull(veiculoRsngDTO.getNumChassi()) ? veiculoRsngDTO.getNumChassi() : null);
        veiculo.setUf(Objects.nonNull(veiculoRsngDTO.getUfLicenciamento()) ? Uf.getEnum(veiculoRsngDTO.getUfLicenciamento()) : null);
        veiculo.setUf((Objects.nonNull(veiculoRsngDTO.getUfPlaca()) && Objects.nonNull(veiculo.getUf())) ? Uf.getEnum(veiculoRsngDTO.getUfPlaca()) : null);
        veiculo.setNumeroRenavam((Objects.nonNull(veiculoRsngDTO.getRenavam()) && !veiculoRsngDTO.getRenavam().equals("00000000000")) ? veiculoRsngDTO.getRenavam() : null);
        veiculo.setNumeroGravame(Objects.nonNull(apontamento.getGravame()) ? apontamento.getGravame() : null);
        veiculo.setChassiRemarcado(Objects.nonNull(veiculoRsngDTO.getIndRemarcacao()) && veiculoRsngDTO.getIndRemarcacao().equals(1));
        veiculo.setVeiculo0Km(veiculoRsngDTO.getIndicador0Km() == null || veiculoRsngDTO.getIndicador0Km().equals(1));

        Contrato contrato = new Contrato();
        contrato.setDataContrato(apontamento.getDataInclusao());
        contrato.setFinanceira(financeira);
        contrato.getVeiculos().add(veiculo);
        veiculo.setContrato(contrato);

        BeanUtils.copyProperties(contratoRsngDTO.getContratoPrincipal(), contrato);
        contrato.setIndicadorTaxaMulta(contratoRsngDTO.getContratoPrincipal().getIndicadorMulta() == null || contratoRsngDTO.getContratoPrincipal().getIndicadorMulta().equals(1));
        contrato.setIndicadorTaxaMoraDia(contratoRsngDTO.getContratoPrincipal().getIndicadorTaxaMoraDia() == null || contratoRsngDTO.getContratoPrincipal().getIndicadorTaxaMoraDia().equals(1));
        contrato.setIndicadorPenalidade(contratoRsngDTO.getContratoPrincipal().getIndicadorPenalidade() == null || contratoRsngDTO.getContratoPrincipal().getIndicadorPenalidade().equals(1));
        contrato.setIndicadorComissao(contratoRsngDTO.getContratoPrincipal().getIndicadorComissao() == null || contratoRsngDTO.getContratoPrincipal().getIndicadorComissao().equals(1));

        TipoRestricao tipoRestricao = null;
        if (("0" + apontamento.getCodigoTipo()).equals("03"))
            tipoRestricao = (contratoRsngDTO.getContratoPrincipal().getNumeroCotaConsorcio() == null || contratoRsngDTO.getContratoPrincipal().getNumeroCotaConsorcio() == 0L) ? TipoRestricao.ALIENACAO_FIDUCIARIA : TipoRestricao.ALIENACAO_FIDUCIARIA_CONS;
        contrato.setTipoRestricao(tipoRestricao);
        contrato.setSiglaIndiceFinaceiro(contratoRsngDTO.getContratoPrincipal().getIndiceFinanceiro() != null ? IndiceFinanceiro.getByCodigo(PlaceconUtil.stripAcentsToUpperCaseNotNull(contratoRsngDTO.getContratoPrincipal().getIndiceFinanceiro())) : null);

        contrato.setUfRegistro(Uf.getEnum(apontamento.getUfLicenciamento()));
        contrato.setUfLiberacaoCredito(contratoRsngDTO.getContratoPrincipal().getUfLiberacaoCredito() != null ? Uf.getEnum(contratoRsngDTO.getContratoPrincipal().getUfLiberacaoCredito()) : null);
        Municipio municipioLiberacaoCredito = municipioService.findByUfAndDescricao(
                contrato.getUfLiberacaoCredito(),
                contratoRsngDTO.getContratoPrincipal().getMunicipioLiberacaoCredito()
        );
        contrato.setMunicipioLiberacao(Objects.nonNull(municipioLiberacaoCredito) ? municipioLiberacaoCredito : null);
        contrato.setValorCredito(contratoRsngDTO.getContratoPrincipal().getValorTotalDivida());

        contrato.setNomeDevedorFinanciado(devedorRsng.getNome());
        contrato.setCpfCnpjDevedorFinanciado(definirCpfCnpj(devedorRsng.getCpfCnpjDevedor()));
        contrato.setCepDevedor(devedorRsng.getCep());
        contrato.setEnderecoDevedor(devedorRsng.getEndereco());
        contrato.setNumeroEnderecoDevedor(devedorRsng.getNumEndereco());
        contrato.setComplementoEnderecoDevedor(devedorRsng.getComplementoEndereco());
        contrato.setUfEnderecoDevedor(Uf.getEnum(devedorRsng.getUf()));
        contrato.setBairroDevedor(devedorRsng.getBairro());
        Municipio municipioDevedor = null;
        if (devedorRsng.getCodMunicipio() != null)
            municipioDevedor = municipioService.findByCodigoDenatran(String.valueOf(devedorRsng.getCodMunicipio()));
        contrato.setMunicipioDevedor(municipioDevedor);
        contrato.setDddDevedor(Integer.valueOf(devedorRsng.getDdd()));
        contrato.setTelefoneDevedor(Integer.valueOf(devedorRsng.getTelefone()));

        contrato.setTipoContrato(TipoContrato.CONTRATO_PRINCIPAL);
        if (Objects.nonNull(contratoRsngDTO.getContratoAditivo()) && contratoRsngDTO.getContratoAditivo().getNumAditivo() != null) {
            contrato.setTipoContrato(TipoContrato.CESSAO_DIREITO_DEVEDOR);
            contrato.setDataAditivoContrato(contratoRsngDTO.getContratoAditivo().getDataAditivo());
            contrato.setNumeroAditivoContrato(contratoRsngDTO.getContratoAditivo().getNumAditivo() != null ? String.valueOf(contratoRsngDTO.getContratoAditivo().getNumAditivo()) : null);
        }
        contrato.setComentario(apontamento.getTxtComentario());

        return contrato;
    }

    private String definirCpfCnpj(String documento) {
        log.info("Definir documento: " + documento);
        if (PlaceconUtil.validaCNPJ(documento))
            return documento;
        return formatarCPF(documento);
    }

    public BaixaCancApontamentoReqRsng veiculoToBaixaApontamento(VeiculoModel veiculoModel) {
        BaixaCancApontamentoReqRsng.DadosDaBaixa.DadosValidacao validacao = new BaixaCancApontamentoReqRsng.DadosDaBaixa.DadosValidacao();
        validacao.setChassi(veiculoModel.getNumeroChassi());

        if (veiculoModel instanceof VeiculoRsng) {
            VeiculoRsng veiculo = (VeiculoRsng) veiculoModel;
            validacao.setGravame(veiculo.getApontamento());
            validacao.setCpfCnpjDevedor(veiculo.getContratoRsng().getCpfCnpjDevedorFinanciado());
        } else if (veiculoModel instanceof Veiculo) {
            Veiculo veiculo = (Veiculo) veiculoModel;
            validacao.setGravame(veiculo.getNumeroGravame());
            validacao.setCpfCnpjDevedor(veiculo.getContrato().getCpfCnpjDevedorFinanciado());
        }

        return new BaixaCancApontamentoReqRsng(new BaixaCancApontamentoReqRsng.DadosDaBaixa(validacao));
    }

    public InclusaoApontamentoReqRsng entityToInclusaoApontamento(VeiculoRsng veiculo) {
        InclusaoApontamentoReqRsng.DadosDaInclusao dadosDaInclusao = new InclusaoApontamentoReqRsng.DadosDaInclusao();

        dadosDaInclusao.setVeiculoRsng(entityToVeiculoRsng(veiculo));
        dadosDaInclusao.setFinanceiraRsng(entityToFinanceiraRsng(veiculo.getContratoRsng()));
        dadosDaInclusao.setDevedorRsng(entityToDevedorRsng(veiculo.getContratoRsng()));
        dadosDaInclusao.setContratoPrincipalRsng(entityToContratoPrincipalRsng(veiculo.getContratoRsng()));

        return new InclusaoApontamentoReqRsng(dadosDaInclusao);
    }

    public ContratoDTO ultimoApontamentoToContratoDTO(UltimoApontamentorRespRsng.DadosDoApontamento ultimoApontamento) {
        Apontamento apontamento = ultimoApontamento.getApontamento();
        DevedorRsng devedor = apontamento.getDevedorB3();
        FinanceiraRsng financeiraRsng = apontamento.getFinanceiraRsng();
        ContratoRsngDTO.ContratoPrincipal contrato = apontamento.getContratoB3().getContratoPrincipal();
        ContratoRsngDTO.ContratoAditivo aditivo = apontamento.getContratoB3().getContratoAditivo();

        ContratoDTO dto = new ContratoDTO();
        dto.setVeiculos(Arrays.asList(veiculoRsngToVeiculoDTO(apontamento.getVeiculoB3(), apontamento.getGravame())));
        dto.setDataContrato(apontamento.getDataInclusao());

        dto.setNomeDevedorFinanciado(devedor.getNome());
        dto.setCpfCnpjDevedorFinanciado(devedor.getCpfCnpjDevedor());
        dto.setEnderecoDevedor(devedor.getEndereco());
        dto.setNumeroEnderecoDevedor(devedor.getNumEndereco());
        dto.setComplementoEnderecoDevedor(devedor.getComplementoEndereco());
        dto.setBairroDevedor(devedor.getBairro());
        dto.setUfDevedor(devedor.getUf());
        Municipio municipioDevedor = municipioService.findByCodigoDenatran(devedor.getCodMunicipio().toString());
        dto.setMunicipioDevedor(Objects.nonNull(municipioDevedor) ? municipioDevedor.getDescricao() : null);
        dto.setCodigoMunicipioDevedor(Objects.nonNull(municipioDevedor) ? municipioDevedor.getCodigoDenatran() : null);
        dto.setCepDevedor(devedor.getCep());
        dto.setDddDevedor(Integer.valueOf(devedor.getDdd()));
        dto.setTelefoneDevedor(Integer.valueOf(devedor.getTelefone()));

        dto.setCnpjAgenteFinanceiro(financeiraRsng.getDocumento());

        if (("0" + apontamento.getCodigoTipo()).equals("03")) {
            TipoRestricao tipoRestricao = (contrato.getNumeroCotaConsorcio() == null || contrato.getNumeroCotaConsorcio() == 0L) ? TipoRestricao.ALIENACAO_FIDUCIARIA : TipoRestricao.ALIENACAO_FIDUCIARIA_CONS;
            dto.setTipoRestricao(tipoRestricao.getDescricao());
        }
        dto.setValorCredito(contrato.getValorTotalDivida());
        dto.setUfRegistro(apontamento.getUfLicenciamento());
        dto.setNumeroContrato(contrato.getNumeroContrato());
        dto.setDataContrato(contrato.getDataContrato());
        dto.setQuantidadeMeses(contrato.getQuantidadeMeses());
        dto.setTipoContrato(contrato.getTipoApontamento().toString());
        dto.setValorTotalDivida(contrato.getValorTotalDivida());
        dto.setDataLiberacaoCredito(contrato.getDataLiberacaoCredito());
        dto.setUfLiberacaoCredito(contrato.getUfLiberacaoCredito());
        dto.setMunicipioLiberacaoCredito(contrato.getMunicipioLiberacaoCredito());
        dto.setDataVencimentoPrimeiraParcela(contrato.getDataVencimentoPrimeiraParcela());
        dto.setDataVencimentoUltimaParcela(contrato.getDataVencimentoUltimaParcela());
        dto.setValorParcela(contrato.getValorParcela());
        dto.setSiglaIndiceFinaceiro(contrato.getIndiceFinanceiro());
        dto.setValorTaxaContrato(contrato.getValorTaxaContrato());
        dto.setValorTaxaContrato(contrato.getValorTaxaContrato());
        dto.setValorIOF(contrato.getValorIOF());
        dto.setIndicadorTaxaMulta(contrato.getIndicadorMulta() == 1);
        dto.setValorTaxaMulta(contrato.getValorTaxaMulta());
        dto.setValorTaxaJurosMes(contrato.getValorTaxaJurosMes());
        dto.setValorTaxaJurosAno(contrato.getValorTaxaJurosAno());
        dto.setIndicadorTaxaMoraDia(contrato.getIndicadorTaxaMoraDia() == 1);
        dto.setValorTaxaMoraDia(contrato.getValorTaxaMoraDia());
        dto.setIndicadorPenalidade(contrato.getIndicadorPenalidade() == 1);
        dto.setDescricaoPenalidade(contrato.getDescricaoPenalidade());
        dto.setPercentualComissao(contrato.getPercentualComissao());
        dto.setNumeroCotaConsorcio(contrato.getNumeroCotaConsorcio());
        dto.setNumeroCotaConsorcio(contrato.getNumeroCotaConsorcio());
        dto.setIndicadorComissao(contrato.getIndicadorComissao() == null || contrato.getIndicadorComissao().equals(1));
        dto.setComentario(contrato.getComentario());

        dto.setNumeroAditivoContrato(Objects.nonNull(aditivo.getNumAditivo()) ? aditivo.getNumAditivo().toString() : null);
        dto.setDataAditivoContrato(aditivo.getDataAditivo());

        return dto;
    }

    private VeiculoDTO veiculoRsngToVeiculoDTO(VeiculoRsngDTO veiculoRsngDTO, String gravame) {
        VeiculoDTO veiculoDto = new VeiculoDTO();
        veiculoDto.setNumeroGravame(gravame);
        veiculoDto.setNumeroChassi(veiculoRsngDTO.getNumChassi());
        veiculoDto.setIdentificadorRemarcacao(veiculoRsngDTO.getIndRemarcacao());
        veiculoDto.setPlaca(veiculoRsngDTO.getPlaca());
        veiculoDto.setNumeroRenavam(veiculoRsngDTO.getRenavam());
        veiculoDto.setAnoFabricacao(veiculoRsngDTO.getAnoFabricacao());
        veiculoDto.setAnoModelo(veiculoRsngDTO.getAnoModelo());
        veiculoDto.setUfPlacaAtual(veiculoRsngDTO.getUfPlaca());
        return veiculoDto;
    }

    private VeiculoRsngDTO entityToVeiculoRsng(VeiculoRsng veiculo) {
        VeiculoRsngDTO dto = new VeiculoRsngDTO();
        dto.setNumChassi(veiculo.getNumeroChassi());
        dto.setIndRemarcacao(veiculo.getChassiRemarcado() ? 1 : 0);
        dto.setAnoFabricacao(veiculo.getAnoFabricacao());
        dto.setAnoModelo(veiculo.getAnoModelo());
        dto.setIndicador0Km(0);
        dto.setUfPlaca(Objects.nonNull(veiculo.getUf()) ? veiculo.getUf().name() : null);
        dto.setPlaca(veiculo.getPlaca());
        dto.setRenavam(veiculo.getNumeroRenavam());
        dto.setUfLicenciamento(veiculo.getContratoRsng().getUfRegistro().name());

        if (veiculo.getVeiculo0Km()) {
            dto.setIndicador0Km(1);
            dto.setPlaca(null);
            dto.setUfPlaca(null);
            dto.setRenavam(null);
        }

        return dto;
    }

    private FinanceiraRsng entityToFinanceiraRsng(ContratoRsng contrato) {
        Financeira financeira = financeiraService.findOne(contrato.getFinanceira().getId());

        FinanceiraRsng dto = new FinanceiraRsng();
        dto.setNome(financeira.getNome());
        dto.setDocumento(financeira.getDocumento());
        dto.setEndereco(PlaceconUtil.deAccent(financeira.getEndereco()));
        dto.setNumeroEndereco(financeira.getNumero());
        dto.setComplemento(financeira.getComplemento());
        dto.setBairro(deAccent(financeira.getBairro()));
        dto.setUf(financeira.getUfEndereco().name());
        dto.setCodMunicipioCredor(Objects.nonNull(financeira.getMunicipio()) ? financeira.getMunicipio().getCodigoDenatran() : null);
        dto.setCep(getCepSemFormatacao(financeira.getCep()));
        dto.setDdd(getDDD(financeira.getTelefoneComercialRepresentante()));
        dto.setTelefone(getNumeroTelefone(financeira.getTelefoneComercialRepresentante()));

        return dto;
    }

    private DevedorRsng entityToDevedorRsng(ContratoRsng contrato) {
        DevedorRsng dto = new DevedorRsng();
        dto.setNome(contrato.getNomeDevedorFinanciado());
        dto.setIndicadorTipoDocumento(contrato.getCpfCnpjDevedorFinanciado().length() == 11 ? 1 : 2);
        dto.setCpfCnpjDevedor(contrato.getCpfCnpjDevedorFinanciado());
        dto.setEndereco(deAccent(contrato.getEnderecoDevedor()));
        dto.setNumEndereco(contrato.getNumeroEnderecoDevedor());
        dto.setComplementoEndereco(contrato.getComplementoEnderecoDevedor());
        dto.setBairro(contrato.getBairroDevedor());
        dto.setUf(Objects.nonNull(contrato.getUfEnderecoDevedor()) ? contrato.getUfEnderecoDevedor().name() : null);
        dto.setCodMunicipio(Objects.nonNull(contrato.getMunicipioDevedor()) ? Integer.valueOf(contrato.getMunicipioDevedor().getCodigoDenatran()) : null);
        dto.setCep(getCepSemFormatacao(contrato.getCepDevedor()));
        dto.setDdd(Objects.nonNull(contrato.getDddDevedor()) ? contrato.getDddDevedor().toString() : "");
        dto.setTelefone(Objects.nonNull(contrato.getTelefoneDevedor()) ? contrato.getTelefoneDevedor().toString() : "");

        return dto;
    }

    private ContratoRsngDTO.ContratoPrincipal entityToContratoPrincipalRsng(ContratoRsng contrato) {
        ContratoRsngDTO.ContratoPrincipal dto = new ContratoRsngDTO.ContratoPrincipal();
        dto.setNumeroContrato(contrato.getNumeroContrato());
        dto.setDataContrato(contrato.getDataContrato());
        dto.setQuantidadeMeses(contrato.getQuantidadeMeses());
        dto.setTipoApontamento(Integer.valueOf(contrato.getTipoRestricao().getCodigo().substring(1)));
        dto.setValorTotalDivida(contrato.getValorTotalDivida());
        dto.setDataLiberacaoCredito(contrato.getDataLiberacaoCredito());
        dto.setUfLiberacaoCredito(contrato.getUfLiberacaoCredito().name());
        dto.setMunicipioLiberacaoCredito(contrato.getMunicipioLiberacao().getDescricao());
        dto.setDataVencimentoPrimeiraParcela(contrato.getDataVencimentoPrimeiraParcela());
        dto.setDataVencimentoUltimaParcela(contrato.getDataVencimentoUltimaParcela());
        dto.setValorParcela(contrato.getValorParcela());
        dto.setIndiceFinanceiro(contrato.getSiglaIndiceFinaceiro().name());
        dto.setValorTaxaContrato(contrato.getValorTaxaContrato());
        dto.setValorIOF(contrato.getValorIOF());
        dto.setIndicadorMulta(contrato.getIndicadorTaxaMulta() ? 1 : 0);
        dto.setValorTaxaMulta(contrato.getValorTaxaMulta());
        dto.setValorTaxaJurosMes(contrato.getValorTaxaJurosMes());
        dto.setValorTaxaJurosAno(contrato.getValorTaxaJurosAno());
        dto.setIndicadorTaxaMoraDia(contrato.getIndicadorTaxaMoraDia() ? 1 : 0);
        dto.setValorTaxaMoraDia(contrato.getValorTaxaMoraDia());
        dto.setIndicadorPenalidade(contrato.getIndicadorPenalidade() ? 1 : 0);
        dto.setDescricaoPenalidade(contrato.getDescricaoPenalidade());
        dto.setIndicadorComissao(contrato.getIndicadorComissao() ? 1 : 0);
        dto.setPercentualComissao(contrato.getPercentualComissao());
        dto.setIndicadorTipoDocumento(contrato.getFinanceira().getDocumento().length() == 11 ? 1 : 2);
        dto.setCpfCnpjCredor(contrato.getFinanceira().getDocumento());
        dto.setNumeroGrupoConsorcio(contrato.getNumeroGrupoConsorcio());
        dto.setNumeroCotaConsorcio(contrato.getNumeroCotaConsorcio());
        dto.setComentario(retiraFormatacao(contrato.getComentario()));

        return dto;
    }

}
