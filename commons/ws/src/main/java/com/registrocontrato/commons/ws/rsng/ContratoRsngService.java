package com.registrocontrato.commons.ws.rsng;

import com.google.common.base.Strings;
import com.registrocontrato.commons.ws.dto.rsng.BaixarApontamentoRequest;
import com.registrocontrato.commons.ws.dto.rsng.CancelamentoBaixaApontamentoResponse;
import com.registrocontrato.commons.ws.dto.rsng.CancelarApontamentoRequest;
import com.registrocontrato.commons.ws.dto.rsng.CancelarBaixaApontamentoRequest;
import com.registrocontrato.commons.ws.rsng.mapper.ContratoRsngMapper;
import com.registrocontrato.commons.ws.rsng.wsRsng.RsngException;
import com.registrocontrato.commons.ws.rsng.wsRsng.WsRsngRestClient;
import com.registrocontrato.commons.ws.rsng.wsRsng.dto.apontamento.UltimoApontamentorRespRsng;
import com.registrocontrato.commons.ws.rsng.wsRsng.dto.baixaOuCancelamento.BaixaCancApontamentoRespRsng;
import com.registrocontrato.commons.ws.rsng.wsRsng.dto.inclusao.InclusaoApontamentoRespRsng;
import com.registrocontrato.infra.entity.Agente;
import com.registrocontrato.infra.entity.Financeira;
import com.registrocontrato.infra.entity.Uf;
import com.registrocontrato.infra.exception.ServiceException;
import com.registrocontrato.infra.service.BaseService;
import com.registrocontrato.infra.util.PlaceconUtil;
import com.registrocontrato.registro.entity.*;
import com.registrocontrato.registro.enums.*;
import com.registrocontrato.registro.repository.ContratoRepository;
import com.registrocontrato.registro.repository.ContratoRsngRepository;
import com.registrocontrato.registro.service.ContratoService;
import com.registrocontrato.registro.service.dto.ContratoRsngFilter;
import com.registrocontrato.registro.service.validation.ContratoValidation;
import com.registrocontrato.seguranca.entity.Perfil;
import com.registrocontrato.seguranca.entity.Usuario;
import com.registrocontrato.seguranca.service.UsuarioService;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.Hibernate;
import org.springframework.core.env.Environment;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.data.repository.PagingAndSortingRepository;
import org.springframework.stereotype.Service;

import javax.persistence.criteria.*;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class ContratoRsngService extends BaseService<ContratoRsng, ContratoRsngFilter> {

    private final ContratoRsngRepository contratoRsngRepository;

    private final ContratoRepository contratoRepository;

    private final ContratoService contratoService;

    private final WsRsngRestClient wsRsng;

    private final UsuarioService usuarioService;

    private final ContratoRsngMapper mapper;

    private final ContratoValidation validator;

    private final Environment env;

    private static final int MAX_REGISTROS = 200000;

    public ContratoRsngService(
            ContratoRsngRepository contratoRsngRepository, ContratoRepository contratoRepository, ContratoService contratoService,
            WsRsngRestClient wsRsng,
            UsuarioService usuarioService,
            ContratoRsngMapper mapper,
            ContratoValidation validator, Environment env) {
        this.contratoRsngRepository = contratoRsngRepository;
        this.contratoRepository = contratoRepository;
        this.contratoService = contratoService;
        this.wsRsng = wsRsng;
        this.usuarioService = usuarioService;
        this.mapper = mapper;
        this.validator = validator;
        this.env = env;
    }

    public ContratoRsng findByChassiAndNumeroContrato(String chassi, String numeroContrato) {
        return contratoRsngRepository.findTop1ByChassiAndNumeroContrato(chassi, numeroContrato);
    }

    public Long gerarProtocolo(ContratoRsng entity) {
        return (Calendar.getInstance().get(Calendar.YEAR) * 10000000000L) + entity.getId();
    }

    public ContratoRsng gerarNovoApontamento(ContratoRsng contrato, String currenteUser) {
        validator.validarGeracaoApontamento(contrato, currenteUser);
        logger.info("Contrato validado para gerar apontamento");
        contrato.setCpfCnpjUsuarioResponsavel(currenteUser);
        contrato.setDataCadastroRsng(new Date());
        save(contrato);

        contrato.setSituacao(SituacaoRsng.PENDENTE_PROCESSAMENTO);
        contrato.setProtocoloPlaceconRsng(gerarProtocolo(contrato));

        contrato.getVeiculos().forEach(veiculo -> {
            try {
                if (Arrays.asList(env.getActiveProfiles()).contains("prod")) {
                    InclusaoApontamentoRespRsng responseRsng = wsRsng.incluirApontamento(contrato.getFinanceira(), veiculo);
                    veiculo.setApontamento(responseRsng.getResponseData().getNumApontamento());
                    passarContratoParaPendenteEnvio(veiculo);
                    registrarApontamentoNoDetran(contrato.getId(), currenteUser);
                }
            } catch (RsngException e) {
                logger.error(e.getMessage());
                contrato.setSituacao(SituacaoRsng.ERRO);
                iterarMensagensErro(e, veiculo);
            } catch (Exception e) {
                logger.error(e);
                contrato.setSituacao(SituacaoRsng.ERRO);
            }
        });

        contrato.setSituacaoFinanceira(SituacaoFinanceira.NAO_PAGO);

        save(contrato);
        return contrato;
    }

    public List<ContratoRsng> buscarContratosPendentesEnvio() {
        return contratoRsngRepository.findPendentesEnvio();
    }

    public ContratoRsng registrarApontamentoNoDetran(Long id, String currentUser) {
        ContratoRsng entity = findOne(id);

        Contrato contrato = contratoService.parseContratoRsngToContrato(entity);
        contrato.setTipoContrato(TipoContrato.CONTRATO_PRINCIPAL);
        contrato.setContratoRsng(entity);
        contratoService.transmitir(contrato, currentUser);
        entity.setContrato(contrato);
        entity.setSituacao(SituacaoRsng.ENVIADO);

        return entity;
    }

    public Contrato registrarNoDetran(Long id, String currentUser) {
        ContratoRsng entity = findOne(id);

        Contrato contrato = contratoService.parseContratoRsngToContrato(entity);
        Contrato contratoDetran = contratoService.transmitir(contrato, currentUser);
        entity.setContrato(contrato);
        entity.setSituacao(SituacaoRsng.ENVIADO);
        contrato.setSituacaoFinanceira(SituacaoFinanceira.NAO_PAGO);
        save(entity);

        return contratoDetran;
    }

    private void passarContratoParaPendenteEnvio(VeiculoRsng veiculo) {
        veiculo.getContratoRsng().setSituacao(SituacaoRsng.PENDENTE_ENVIO);
        veiculo.getContratoRsng().setDataConclusaoRsng(new Date());
        if (!Strings.isNullOrEmpty(veiculo.getMensagemErro()))
            veiculo.setMensagemErro(null);
    }

    private void iterarMensagensErro(RsngException e, VeiculoRsng veiculo) {

        if (Objects.nonNull(e.getErrosRsngDTO())) {
            if (veiculo.getMensagemErro() != null)
                veiculo.setMensagemErro(null);

            e.getErrosRsngDTO().getErros().forEach(erroRsng -> {
                String erroJaAtribuido = veiculo.getMensagemErro() == null ? "" : veiculo.getMensagemErro();
                veiculo.setMensagemErro(erroJaAtribuido + " " + erroRsng.getTitulo() + " - " + erroRsng.getDetalhe() + "\n");
            });

        } else {
            veiculo.setMensagemErro("Falha na geração do número do gravame");
        }
    }

    public com.registrocontrato.commons.ws.dto.ContratoDTO consultarUltimoApontamento(Financeira financeira, String chassi) throws RsngException {
        UltimoApontamentorRespRsng apontamentoB3 = wsRsng.consultarUltimoApontamento(financeira, chassi);
        return mapper.ultimoApontamentoToContratoDTO(apontamentoB3.getDadosDoApontamento());
    }

    public List<CancelamentoBaixaApontamentoResponse> cancelarBaixarApontamento(CancelarBaixaApontamentoRequest dto) {
        return contratoRsngRepository.findByChassiGravame(dto.getChassi(), dto.getGravame())
                .map(contrato ->
                        contrato.getVeiculos().stream()
                                .map(veiculo -> cancelamentoBaixa(veiculo, dto))
                                .collect(Collectors.toList())
                )
                .orElseThrow(() -> new ServiceException("Não foi possível encontrar o contrato"));
    }

    public List<CancelamentoBaixaApontamentoResponse> cancelarBaixarApontamentoContrato(CancelarBaixaApontamentoRequest dto) {
        Contrato contrato = contratoRepository.findByChassiGravame(dto.getChassi(), dto.getGravame());
        if (contrato == null) {
            throw new ServiceException("Não foi possível encontrar o contrato");
        }
        return contrato.getVeiculos().stream()
                .map(veiculo -> cancelamentoBaixa(veiculo, dto))
                .collect(Collectors.toList());
    }

    public ContratoRsng cancelarApontamento(ContratoRsng contratoRsng, String currenteUser) {
        validator.validarCancelamentoApontamento(contratoRsng, currenteUser);

        List<VeiculoRsng> veiculos = contratoRsng.getVeiculos().stream().map(veiculo -> {
            try {
                BaixaCancApontamentoRespRsng.DadosDaBaixaResp dadosDaBaixa = wsRsng.cancelarApontamento(veiculo.getContratoRsng().getFinanceira(), veiculo).getDadosDaBaixa();

                if (dadosDaBaixa.getCodigoRetorno() == 30) {
                    veiculo.getContratoRsng().setSituacao(SituacaoRsng.CANCELADO);
                }
            } catch (RsngException e) {
                veiculo.getContratoRsng().setSituacao(SituacaoRsng.ERRO);
                veiculo.setMensagemErro("Falha no cancelamento do gravame no SNG");
                iterarMensagensErro(e, veiculo);
            }
            return veiculo;
        }).collect(Collectors.toList());

        contratoRsng.setVeiculos(veiculos);
        save(contratoRsng);
        return contratoRsng;
    }

    private CancelamentoBaixaApontamentoResponse cancelamentoBaixa(VeiculoModel veiculoModel, CancelarBaixaApontamentoRequest dto) {
        CancelamentoBaixaApontamentoResponse cancelamento = new CancelamentoBaixaApontamentoResponse();
        try {
            BaixaCancApontamentoRespRsng.DadosDaBaixaResp dadosDaBaixa = definirAcao(veiculoModel, dto);

            Date hoje = new Date();
            if (veiculoModel instanceof VeiculoRsng) {
                VeiculoRsng veiculo = (VeiculoRsng) veiculoModel;
                if (dadosDaBaixa.getCodigoRetorno() == 30) {
                    if (dto instanceof BaixarApontamentoRequest) {
                        veiculo.getContratoRsng().setSituacaoBaixaB3(SituacaoBaixaB3.BAIXADO);
                        veiculo.getContratoRsng().setDataBaixaB3(hoje);
                    } else if (dto instanceof CancelarApontamentoRequest) {
                        veiculo.getContratoRsng().setSituacaoBaixaB3(SituacaoBaixaB3.CANCELADO);
                        veiculo.getContratoRsng().setDataCancelamentoBaixaB3(hoje);
                    }

                    cancelamento.setChassi(dadosDaBaixa.getMensagemRetorno());
                } else {
                    veiculo.getContratoRsng().setSituacaoBaixaB3(SituacaoBaixaB3.ERRO);
                    veiculo.setMensagemErro("Falha no cancelamento do gravame no SNG");
                    cancelamento.setChassi(dadosDaBaixa.getMensagemRetorno());
                }

            } else if (veiculoModel instanceof Veiculo) {
                Veiculo veiculo = (Veiculo) veiculoModel;

                if (dadosDaBaixa.getCodigoRetorno() == 30) {
                    if (dto instanceof BaixarApontamentoRequest) {
                        veiculo.getContrato().setSituacaoBaixaB3(SituacaoBaixaB3.BAIXADO);
                        veiculo.getContrato().setTipoBaixaContrato(TipoBaixaContrato.GRAVAME_BAIXADO);
                        veiculo.getContrato().setDataBaixaB3(hoje);
                    } else if (dto instanceof CancelarApontamentoRequest) {
                        veiculo.getContrato().setSituacaoBaixaB3(SituacaoBaixaB3.CANCELADO);
                        veiculo.getContrato().setTipoBaixaContrato(TipoBaixaContrato.GRAVAME_CANCELADO);
                        veiculo.getContrato().setDataCancelamentoBaixaB3(hoje);
                    }
                    contratoService.baixar(veiculo.getContrato());
                    cancelamento.setChassi(dadosDaBaixa.getMensagemRetorno());
                } else {
                    veiculo.getContrato().setSituacaoBaixaB3(SituacaoBaixaB3.ERRO);
                    veiculo.setMensagemRetornoDetalhada("Falha no cancelamento do gravame no SNG");
                    cancelamento.setChassi(dadosDaBaixa.getMensagemRetorno());
                }
            }
        } catch (RsngException e) {
            cancelamento.setMensagemSng(e.getMessage());
        }
        return cancelamento;
    }

    private BaixaCancApontamentoRespRsng.DadosDaBaixaResp definirAcao(VeiculoModel veiculo, CancelarBaixaApontamentoRequest dto) throws RsngException {
        ContratoModel model = null;
        if (veiculo instanceof VeiculoRsng) {
            model = ((VeiculoRsng) veiculo).getContratoRsng();
        } else if (veiculo instanceof Veiculo) {
            model = ((Veiculo) veiculo).getContrato();
        }

        if (model == null)
            throw new ServiceException("Não foi possível encontrar o contrato");


        if (dto instanceof CancelarApontamentoRequest) {

            return wsRsng.cancelarApontamento(model.getFinanceira(), veiculo).getDadosDaBaixa();
        }
        if (dto instanceof BaixarApontamentoRequest) {

            return wsRsng.baixarApontamento(model.getFinanceira(), veiculo).getDadosDaBaixa();
        }

        throw new ServiceException("Falha na requisição de cancelamento/baixa");
    }

    public ContratoRsng buscarPeloProtocoloPlaceconRsng(Long protocolo) throws ServiceException {
        return contratoRsngRepository.findByProtocoloPlaceconRsng(protocolo)
                .orElseThrow(() -> new ServiceException("Contrato RSNG não encontrado"));
    }

    @Override
    public ContratoRsng findOne(Long id) {
        ContratoRsng contrato = super.findOne(id);
        if (contrato != null) {
            Hibernate.initialize(contrato.getMunicipioDevedor());
            Hibernate.initialize(contrato.getMunicipioLiberacao());
            Hibernate.initialize(contrato.getVeiculos());
            Hibernate.initialize(contrato.getEnvios());
            for (VeiculoRsng v : contrato.getVeiculos()) {
                Hibernate.initialize(v.getMarca());
                Hibernate.initialize(v.getModelo());
            }
            return contrato;
        }
        return null;
    }

    @Override
    protected PagingAndSortingRepository<ContratoRsng, Long> getRepository() {
        return contratoRsngRepository;
    }

    @Override
    public Page<ContratoRsng> findAll(int first, int pageSize, ContratoRsngFilter filter) {
        Specification<ContratoRsng> contratoSpec = new Specification<ContratoRsng>() {

            @Override
            public Predicate toPredicate(Root<ContratoRsng> root, CriteriaQuery<?> cq, CriteriaBuilder cb) {
                cq.distinct(true);
                List<Predicate> predicates = new ArrayList<>();
                Join<ContratoRsng, VeiculoRsng> joinVeiculos = root.join("veiculos", JoinType.LEFT);

                if (filter.getProtocoloPlaceconRsng() != null && filter.getProtocoloPlaceconRsng() != 0) {
                    predicates.add(cb.equal(root.<Long>get("numeroRegistroEletronico"), filter.getProtocoloPlaceconRsng()));
                }
                if (StringUtils.isNotEmpty(filter.getNumeroContrato())) {
                    predicates.add(cb.like(cb.lower(root.<String>get("numeroContrato")), "%" + filter.getNumeroContrato().toLowerCase() + "%"));
                }
                if (filter.getUfRegistro() != null) {
                    predicates.add(cb.equal(root.<Uf>get("ufRegistro"), filter.getUfRegistro()));
                }
                if (StringUtils.isNotEmpty(filter.getCpfCnpjDevedorFinanciado())) {
                    String string = PlaceconUtil.retiraFormatacao(filter.getCpfCnpjDevedorFinanciado()).toLowerCase();
                    predicates.add(cb.like(cb.lower(root.<String>get(StringUtils.isNumeric(string) ? "cpfCnpjDevedorFinanciado" : "nomeDevedorFinanciado")), "%" + string + "%"));
                }
                if (filter.getUfEnderecoDevedor() != null) {
                    predicates.add(cb.equal(root.<Uf>get("ufEnderecoDevedor"), filter.getUfEnderecoDevedor()));
                }
                if (filter.getSituacao() != null) {
                    predicates.add(cb.equal(root.<SituacaoRsng>get("situacao"), filter.getSituacao()));
                }
                if (filter.getTipoContrato() != null) {
                    predicates.add(cb.equal(root.<TipoContrato>get("tipoContrato"), filter.getTipoContrato()));
                }
                if (filter.getDataCadastro() != null) {
                    LocalDate localDate = filter.getDataCadastro().toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
                    Date dataInicio = Date.from(localDate.atStartOfDay(ZoneId.systemDefault()).toInstant());
                    localDate = localDate.plusDays(1);
                    Date dataFim = Date.from(localDate.atStartOfDay(ZoneId.systemDefault()).toInstant());
                    predicates.add(cb.between(root.<Date>get("dataCadastro"), dataInicio, dataFim));
                }

                if (StringUtils.isNotEmpty(filter.getChassi())) {
                    predicates.add(cb.like(cb.lower(joinVeiculos.<String>get("numeroChassi")), "%" + filter.getChassi().toLowerCase() + "%"));
                }

                if (filter.getNumeroRenavam() != null) {
                    predicates.add(cb.like(cb.lower(joinVeiculos.<String>get("numeroRenavam")), "%" + filter.getNumeroRenavam() + "%"));
                }

                if (filter.getFinanceira() != null || !filter.getFinanceiras().isEmpty()) {
                    predicates.add(cb.equal(root.<Financeira>get("financeira"), filter.getFinanceira()));
                }

                if (filter.getSituacaoBaixaB3() != null) {
                    predicates.add(cb.equal(root.<SituacaoBaixaB3>get("situacaoBaixa"), filter.getSituacaoBaixaB3()));
                }

                if (filter.getFindBaixa() != null && filter.getFindBaixa()) {
                    predicates.add(
                            cb.or(
                                    cb.not(root.<SituacaoBaixaB3>get("situacaoBaixa").in(SituacaoBaixaB3.BAIXADO, SituacaoBaixaB3.CANCELADO)),
                                    cb.isNull(root.<SituacaoBaixaB3>get("situacaoBaixa"))
                            )
                    );
                }

                // se o usuario eh financeira filtrar somente os usuario da financeira
                Usuario usuarioLogado = usuarioService.findByCpfFinanceiras(filter.getUsuario());
                if (usuarioLogado.getPerfil() == Perfil.FINANCEIRA || !usuarioLogado.getFinanceiras().isEmpty()) {
                    List<Financeira> values = usuarioLogado.getFinanceiras();
                    predicates.add(root.<Financeira>get("financeira").in(values));

                    if (usuarioLogado.getAgente() != null) {
                        predicates.add(cb.equal(root.<Agente>get("agente"), usuarioLogado.getAgente()));
                    }
                }
//                if (usuarioLogado.getPerfil() == Perfil.DETRAN) {
//                    predicates.add(cb.isNotNull(root.<Long>get("numeroRegistroEletronico")));
//                }
                if (usuarioLogado.getUf() != null && !usuarioLogado.isPerfilAdministrador()) {
                    predicates.add(cb.equal(root.<Uf>get("ufRegistro"), usuarioLogado.getUf()));
                }
                return andTogether(predicates, cb);
            }

            private Predicate andTogether(List<Predicate> predicates, CriteriaBuilder cb) {
                return cb.and(predicates.toArray(new Predicate[0]));
            }
        };

        if (pageSize == 0) {
            Page<ContratoRsng> list = contratoRsngRepository.findAll(contratoSpec, new PageRequest(0, MAX_REGISTROS, new Sort(Sort.Direction.ASC, "id")));
            list.forEach(l -> l.getVeiculos().forEach(v -> Hibernate.initialize(v.getMarca())));
            return list;
        }

        Sort.Direction direction = filter.getDirection() == null ? Sort.Direction.DESC : filter.getDirection();
        Page<ContratoRsng> contratos = contratoRsngRepository.findAll(contratoSpec, new PageRequest(first / pageSize, pageSize, new Sort(direction, "id")));
        contratos.forEach(c -> Hibernate.initialize(c.getVeiculos()));
        return contratos;
    }
}
