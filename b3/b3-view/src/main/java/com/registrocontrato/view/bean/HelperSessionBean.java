package com.registrocontrato.view.bean;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.util.Collections;
import java.util.List;

import org.apache.commons.io.IOUtils;
import org.primefaces.model.DefaultStreamedContent;
import org.primefaces.model.StreamedContent;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.web.context.annotation.SessionScope;

import com.registrocontrato.infra.bean.BaseBean;
import com.registrocontrato.infra.entity.Financeira;
import com.registrocontrato.seguranca.entity.Perfil;
import com.registrocontrato.seguranca.entity.Usuario;
import com.registrocontrato.seguranca.service.FinanceiraService;
import com.registrocontrato.seguranca.service.UsuarioService;

import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;
import io.jsonwebtoken.security.Keys;

@Controller
@SessionScope
public class HelperSessionBean extends BaseBean {

	private static final long serialVersionUID = 1L;

	private Usuario usuario;

	private StreamedContent logo;

	private StreamedContent foto;

	@Value("${METABASE_SITE_URL}")
	private String METABASE_SITE_URL;

	@Value("${METABASE_SECRET_KEY}")
	private String METABASE_SECRET_KEY;

	private List<Financeira> financeiras;

	@Autowired
	private UsuarioService usuarioService;

	@Autowired
	private FinanceiraService financeiraService;
	
	public String getSourceDashboard() {
		String payload = "{\"resource\": { \"dashboard\": 10 },\"params\": {}, \"exp\": " + ((System.currentTimeMillis() / 1000) + (15 * 60)) + " }";
		String token = Jwts
						.builder()
						.setPayload(payload)
						.signWith(Keys.hmacShaKeyFor(METABASE_SECRET_KEY.getBytes(StandardCharsets.UTF_8)), SignatureAlgorithm.HS256)
						.compact();
		
		return METABASE_SITE_URL + "/embed/dashboard/" + token + "#bordered=false&titled=true";
	}

	public Usuario getUsuario() {
		if (usuario == null) {
			usuario = usuarioService.findByCpfFinanceiras(getUsername());
		}
		return usuario;
	}

	public List<Financeira> getFinanceiras() {
		if (financeiras == null && getUsername() != null) {
			Usuario usuario = getUsuario();
			if (usuario.getPerfil() == Perfil.FINANCEIRA || !usuario.getFinanceiras().isEmpty()) {
				financeiras = usuario.getFinanceiras();
			} else if (usuario.getPerfil() == Perfil.DETRAN) {
				financeiras = Collections.emptyList();
			} else {
				financeiras = financeiraService.findAtivos();
			}
		}
		return financeiras;
	}
	
	public StreamedContent getLogo() {
		if (logo == null) {
			if (getUsuario() != null && getUsuario().getPerfil() == Perfil.DETRAN) {
				InputStream stream = getExternalContext().getResourceAsStream("/templates/assets/images/"+getUsuario().getUf()+"_sm.png");
				if(stream != null) {
					logo = DefaultStreamedContent.builder()
							.stream(()-> {
								try {
									return new ByteArrayInputStream(IOUtils.toByteArray(stream));
								} catch (IOException e) {
									logger.error("DETRAN sem logo configurada", e);
								}
								return null;
							}).build();
				}
			} else if (getUsuario() != null && getUsuario().getPerfil() == Perfil.FINANCEIRA) {
				byte [] logoFinanceira = null;

				if (getFinanceiras() != null) {
					Financeira financeira = financeiraService.findOne(getFinanceiras().get(0).getId());
					if (financeira.getGrupoFinanceira() != null) {
						logoFinanceira = financeira.getGrupoFinanceira().getLogoFinanceira();
					} else {
						logoFinanceira = financeira.getLogoFinanceira();
					}
				}
				
				if(logoFinanceira != null) {
					byte[] finalLogoFinanceira = logoFinanceira;
					logo = DefaultStreamedContent.builder()
							.stream(() -> new ByteArrayInputStream(finalLogoFinanceira)).build();
				}
			}
		}
		return logo;
	}

	public StreamedContent getFoto() {
		if (foto == null) {
			if (getUsuario() != null && getUsuario().getFoto() != null) {
				foto = DefaultStreamedContent.builder()
						.stream(()-> new ByteArrayInputStream(getUsuario().getFoto())).build();
			}
		}
		return foto;
	}
	
	public void setFoto(StreamedContent foto) {
		this.foto = foto;
	}

}
