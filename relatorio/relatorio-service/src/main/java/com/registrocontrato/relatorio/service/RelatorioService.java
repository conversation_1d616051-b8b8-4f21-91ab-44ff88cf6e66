package com.registrocontrato.relatorio.service;

import com.registrocontrato.infra.entity.Financeira;
import com.registrocontrato.infra.entity.SimNao;
import com.registrocontrato.infra.entity.Uf;
import com.registrocontrato.infra.service.BaseService;
import com.registrocontrato.registro.entity.*;
import com.registrocontrato.registro.enums.Situacao;
import com.registrocontrato.registro.enums.SituacaoFinanceira;
import com.registrocontrato.registro.enums.TipoContrato;
import com.registrocontrato.registro.enums.TipoRestricao;
import com.registrocontrato.relatorio.repository.RelatorioRepository;
import com.registrocontrato.relatorio.service.dto.*;
import com.registrocontrato.seguranca.entity.Perfil;
import com.registrocontrato.seguranca.entity.Usuario;
import com.registrocontrato.seguranca.service.FinanceiraService;
import com.registrocontrato.seguranca.service.UsuarioService;
import org.apache.commons.lang3.time.DateUtils;
import org.hibernate.Hibernate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.data.domain.Sort.Direction;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.data.repository.PagingAndSortingRepository;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.criteria.*;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.temporal.TemporalAdjusters;
import java.util.*;
import java.util.stream.Collectors;

import static com.registrocontrato.infra.util.PlaceconUtil.convertToLocalDateViaInstant;
import static com.registrocontrato.infra.util.PlaceconUtil.oneMonthBeforeSameDay;

@Service
@Transactional
public class RelatorioService extends BaseService<Contrato, RelatorioDTO> {

    private static final int MAX_REGISTRO = 200000;

    private static final long serialVersionUID = 1L;

    @Autowired
    private UsuarioService usuarioService;

    @Autowired
    private RelatorioRepository relatorioRepository;

    @Autowired
    private FinanceiraService financeiraService;

    public List<PrecoComposto> findPrecosCompostos(Credenciamento credenciamento) {
        return relatorioRepository.findPrecosCompostos(credenciamento);
    }

    @Override
    public Page<Contrato> findAll(int first, int pageSize, RelatorioDTO filter) {
        Specification<Contrato> contratoSpec = new Specification<Contrato>() {

            @Override
            public Predicate toPredicate(Root<Contrato> root, CriteriaQuery<?> cq, CriteriaBuilder cb) {

                cq.distinct(true);
                if (Long.class != cq.getResultType()) {
                    Fetch<Contrato, Veiculo> joinVeiculos = root.fetch("veiculos", JoinType.LEFT);
                    joinVeiculos.fetch("marca", JoinType.LEFT);
                    joinVeiculos.fetch("modelo", JoinType.LEFT);
                }
                if (pageSize == 0 && Long.class != cq.getResultType()) {
                    root.fetch("cobranca", JoinType.LEFT);
                }

                List<Predicate> predicates = new ArrayList<>();

                Date dataInicio = DateUtils.truncate(filter.getDataInicio(), Calendar.DATE);
                Calendar dataFim = DateUtils.toCalendar(filter.getDataFim());
                dataFim.set(Calendar.HOUR_OF_DAY, dataFim.getActualMaximum(Calendar.HOUR_OF_DAY));
                dataFim.set(Calendar.MINUTE, dataFim.getActualMaximum(Calendar.MINUTE));
                dataFim.set(Calendar.MILLISECOND, dataFim.getActualMaximum(Calendar.MILLISECOND));

                Predicate dataConclusao = cb.between(root.<Date>get("dataConclusaoDETRAN"), dataInicio, dataFim.getTime());

                Predicate dataCadastro = cb.between(root.<Date>get("dataCadastro"), dataInicio, dataFim.getTime());
                Predicate situacaoCadastro = cb.isNull(root.<Long>get("dataConclusaoDETRAN"));

                predicates.add(cb.or(dataConclusao, cb.and(dataCadastro, situacaoCadastro)));

                if (filter.getSituacao() != null) {
                    predicates.add(cb.equal(root.<Situacao>get("situacao"), filter.getSituacao()));
                }
                if (filter.getSituacaoFinanceira() != null) {
                    predicates.add(cb.equal(root.<SituacaoFinanceira>get("situacaoFinanceira"), filter.getSituacaoFinanceira()));
                }
                if (filter.getTipoContrato() != null) {
                    predicates.add(cb.equal(root.<TipoContrato>get("tipoContrato"), filter.getTipoContrato()));
                }
                if (filter.getTipoRestricao() != null) {
                    predicates.add(cb.equal(root.<TipoRestricao>get("tipoRestricao"), filter.getTipoRestricao()));
                }
                if (filter.getUf() != null) {
                    predicates.add(cb.equal(root.<Uf>get("ufRegistro"), filter.getUf()));
                }
                if (filter.getFinanceira() != null) {
                    predicates.add(cb.equal(root.<Financeira>get("financeira"), filter.getFinanceira()));
                }
                if (filter.getMensagemRetorno() != null) {
                    predicates.add(cb.equal(root.join("veiculos").<Long>get("mensagemRetorno"), filter.getMensagemRetorno()));
                }
                if (filter.getAnexo() != null) {
                    Join<Contrato, Anexo> joinAnexo = root.join("anexos", JoinType.LEFT);
                    if (filter.getAnexo() == SimNao.N) {
                        predicates.add(cb.isNull(joinAnexo));
                    } else {
                        predicates.add(cb.isNotNull(joinAnexo));
                    }
                }

                // se o usuario eh financeira filtrar somente os usuario da financeira
                Usuario usuarioLogado = usuarioService.findByCpfFinanceiras(filter.getUsuario());
                if (usuarioLogado.getPerfil() == Perfil.FINANCEIRA || !usuarioLogado.getFinanceiras().isEmpty()) {
                    List<Financeira> values = usuarioLogado.getFinanceiras();
                    predicates.add(root.<Financeira>get("financeira").in(values));
                }
                if (usuarioLogado.getUf() != null) {
                    predicates.add(cb.equal(root.<Uf>get("ufRegistro"), usuarioLogado.getUf()));
                }

                if (filter.getDataConclusaoDetran() != null) {
                    LocalDate lInicio = new Date(filter.getDataConclusaoDetran().getTime()).toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
                    LocalDate lFim = lInicio.plusDays(1);
                    predicates.add(cb.between(root.<Date>get("dataConclusaoDETRAN"), java.sql.Date.valueOf(lInicio), java.sql.Date.valueOf(lFim)));
                }

                return andTogether(predicates, cb);
            }

            private Predicate andTogether(List<Predicate> predicates, CriteriaBuilder cb) {
                return cb.and(predicates.toArray(new Predicate[0]));
            }
        };

        if (pageSize == 0) {
            Page<Contrato> list = relatorioRepository.findAll(contratoSpec, new PageRequest(0, MAX_REGISTRO, new Sort(Direction.ASC, "id")));
            return list;
        }

        return relatorioRepository.findAll(contratoSpec, new PageRequest(first / pageSize, pageSize, new Sort(Direction.ASC, "id")));
    }


    public List<Contrato> findConcluidosDetran(Date data, Uf uf) {
        LocalDate lInicio = new Date(data.getTime()).toInstant().atZone(ZoneId.systemDefault()).toLocalDate().atStartOfDay().toLocalDate();
        LocalDate lFim = lInicio.plusDays(1);
        return relatorioRepository.findByDataConclusaoDetran(java.sql.Date.valueOf(lInicio), java.sql.Date.valueOf(lFim), uf);
    }

    public Credenciamento findByUfAndPeriodo(Uf uf, Date dataInicio, Date dataFim) {
        return relatorioRepository.findByUfAndPeriodo(uf, dataInicio, dataFim);
    }

    public List<ResultadoGrupoDTO> findRelatorioFaturamentobyGrupo(RelatorioDTO filter) {
        List<Financeira> financeiras = null;
        Usuario usuarioLogado = usuarioService.findByCpfFinanceiras(filter.getUsuario());
        if (usuarioLogado.getPerfil() == Perfil.FINANCEIRA) {
            financeiras = usuarioLogado.getFinanceiras();
        }

        if (filter.getFinanceira() != null) {
            if (financeiras == null) {
                financeiras = new ArrayList<>();
                financeiras.add(filter.getFinanceira());
            } else {
                financeiras.retainAll(Arrays.asList(filter.getFinanceira()));
            }
        }

        if (!filter.getFinanceiras().isEmpty()) {
            if (financeiras == null) {
                financeiras = filter.getFinanceiras();
            } else {
                financeiras.retainAll(filter.getFinanceiras());
            }
        }

        if (!filter.getGruposFinanceiras().isEmpty()) {
            if (financeiras == null) {
                financeiras = filter.getGruposFinanceiras().stream().flatMap(grupo -> financeiraService.buscarFinanceirasDoGrupo(grupo).stream()).collect(Collectors.toList());
            } else {
                financeiras.retainAll(filter.getGruposFinanceiras().stream().flatMap(grupo -> financeiraService.buscarFinanceirasDoGrupo(grupo).stream()).collect(Collectors.toList()));
            }
        }

        Date dataInicio = setDataInicio(filter).getTime();
        Date dataFim = setDataFim(filter).getTime();

        boolean isProximoMes = isNextMonth(convertToLocalDateViaInstant(dataInicio));
        List<Object[]> resultadoPrevisao = null;
        // gerar previsão de cobrança
        if (financeiras != null && !financeiras.isEmpty() && isProximoMes) {
            resultadoPrevisao = relatorioRepository.findDashboardNative(oneMonthBeforeSameDay(dataInicio), financeiras.stream().map(Financeira::getId).collect(Collectors.toList()));
        } else if (isProximoMes) {
            resultadoPrevisao = relatorioRepository.findDashboardNative(oneMonthBeforeSameDay(dataInicio));
        }
        List<ResultadoGrupoDTO> dtos = new ArrayList<>();
        if (resultadoPrevisao != null) {
            for (Object[] row : resultadoPrevisao) {
                String ufRegistro = (String) row[0];
                Long quantidade = ((Number) row[1]).longValue();
                BigDecimal valorCredenciada = (BigDecimal) row[2];
//                BigDecimal valorDetran = (BigDecimal) row[3];
                BigDecimal percentualDesconto = (BigDecimal) row[4];
                String financeira = (String) row[6];
                BigDecimal valorPrevisto = valorCredenciada.multiply(new BigDecimal(quantidade));
//                BigDecimal valorPrevistoDetran = valorDetran.multiply(new BigDecimal(quantidade));
                BigDecimal percentual = percentualDesconto.divide(BigDecimal.valueOf(100), 4, RoundingMode.HALF_UP);
                BigDecimal valorDesconto = valorCredenciada.multiply(new BigDecimal(quantidade)).multiply(percentual);

                BigDecimal valorFinal = valorPrevisto.subtract(valorDesconto);

                ResultadoGrupoDTO dto = new ResultadoGrupoDTO();
                dto.setFinanceira(financeira);
                dto.setGrupo(ufRegistro);
                dto.setQuantidade(quantidade);
                dto.setValor(valorFinal);
                dto.setCobrancaUnificada(false);
                dto.setQuantidadeVeiculos(0L);
                dto.setData(dataInicio);
                dto.setTipo(Enum.valueOf(Uf.class, ufRegistro));

                dtos.add(dto);
            }
            return dtos;
        }


        if (financeiras != null) {
            return financeiras.stream().flatMap(financeira -> {
                List<ResultadoGrupoDTO> resultado = relatorioRepository.countFinanceiro(Collections.singletonList(financeira), dataInicio, dataFim);
                resultado.forEach(r -> {
                    r.setFinanceira(financeira.getNome());
                    r.setCobrancaUnificada(financeira.getUnificaCobrancas() != null && financeira.getUnificaCobrancas().equals(SimNao.S));
                });
                return resultado.stream();
            }).collect(Collectors.toList());
        }
        return relatorioRepository.countFinanceiroByUf(usuarioLogado.isPerfilAdministrador()? null :usuarioLogado.getUf() ,dataInicio, dataFim).stream().peek(r -> r.setCobrancaUnificada(false)).collect(Collectors.toList());
    }

    public boolean isNextMonth(LocalDate data) {
        LocalDate hoje = LocalDate.now();
        LocalDate primeiro = hoje.with(TemporalAdjusters.firstDayOfNextMonth());
        LocalDate ultimo = primeiro.with(TemporalAdjusters.lastDayOfMonth());

        return !data.isBefore(primeiro) && !data.isAfter(ultimo);
    }

    public List<ResultadoGrupoDTO> findByMesAnoGrupo(RelatorioDTO filter) {
        List<Financeira> financeiras = null;
        Usuario usuarioLogado = usuarioService.findByCpfFinanceiras(filter.getUsuario());
        if (usuarioLogado.getPerfil() == Perfil.FINANCEIRA || !usuarioLogado.getFinanceiras().isEmpty()) {
            financeiras = usuarioLogado.getFinanceiras();
        }

        if (filter.getFinanceira() != null) {
            if (financeiras == null) {
                financeiras = new ArrayList<>();
                financeiras.add(filter.getFinanceira());
            } else {
                financeiras.retainAll(Arrays.asList(filter.getFinanceira()));
            }
        }

        if (!filter.getFinanceiras().isEmpty()) {
            if (financeiras == null) {
                financeiras = filter.getFinanceiras();
            } else {
                financeiras.retainAll(filter.getFinanceiras());
            }
        }

        if (!filter.getGruposFinanceiras().isEmpty()) {
            if (financeiras == null) {
                financeiras = filter.getGruposFinanceiras().stream().flatMap(grupo -> financeiraService.buscarFinanceirasDoGrupo(grupo).stream()).collect(Collectors.toList());
            } else {
                financeiras.retainAll(filter.getGruposFinanceiras().stream().flatMap(grupo -> financeiraService.buscarFinanceirasDoGrupo(grupo).stream()).collect(Collectors.toList()));
            }
        }

        Date dataInicio = setDataInicio(filter).getTime();
        Date dataFim = setDataFim(filter).getTime();

        Boolean integraB3 = null;

        Uf  uf = usuarioLogado.isPerfilAdministrador()? null :usuarioLogado.getUf();

        if (filter.getGrupo().equals("S")) {
            if (financeiras != null) {
                return financeiras.stream().flatMap(financeira -> {
                            List<ResultadoGrupoDTO> resultado = relatorioRepository.countBySituacao(Collections.singletonList(financeira), uf, dataInicio, dataFim, integraB3);
                            resultado.forEach(r -> r.setFinanceira(financeira.getNome()));
                            return resultado.stream();
                        }).collect(Collectors.toList());
            }
            return relatorioRepository.countBySituacao(uf, dataInicio, dataFim, integraB3);
        } else if (filter.getGrupo().equals("R")) {
            if (financeiras != null) {
                return financeiras.stream().flatMap(financeira -> {
                    List<ResultadoGrupoDTO> resultado = relatorioRepository.countByRestricao(Collections.singletonList(financeira), uf, dataInicio, dataFim, integraB3);
                    resultado.forEach(r -> r.setFinanceira(financeira.getNome()));
                    return resultado.stream();
                }).collect(Collectors.toList());
            }
            return relatorioRepository.countByRestricao(uf, dataInicio, dataFim, integraB3);
        } else if (filter.getGrupo().equals("U")) {
            if (financeiras != null) {
                return financeiras.stream().flatMap(financeira -> {
                    List<ResultadoGrupoDTO> resultado = relatorioRepository.countByUf(Collections.singletonList(financeira), uf, dataInicio, dataFim, integraB3);
                    resultado.forEach(r -> r.setFinanceira(financeira.getNome()));
                    return resultado.stream();
                }).collect(Collectors.toList());
            }
            return relatorioRepository.countByUf(uf, dataInicio, dataFim, integraB3);
        } else if (filter.getGrupo().equals("T")) {
            if (financeiras != null) {
                return financeiras.stream().flatMap(financeira -> {
                    List<ResultadoGrupoDTO> resultado = relatorioRepository.countByTipoContrato(Collections.singletonList(financeira), uf, dataInicio, dataFim, integraB3);
                    resultado.forEach(r -> r.setFinanceira(financeira.getNome()));
                    return resultado.stream();
                }).collect(Collectors.toList());
            }
            return relatorioRepository.countByTipoContrato(uf, dataInicio, dataFim, integraB3);
        }
        return Collections.emptyList();
    }

    public List<ResultadoDiarioDTO> findByDia(RelatorioDTO filter) {
        List<Financeira> financeiras = null;
        Usuario usuarioLogado = usuarioService.findByCpfFinanceiras(filter.getUsuario());
        if (usuarioLogado.getPerfil() == Perfil.FINANCEIRA || !usuarioLogado.getFinanceiras().isEmpty()) {
            financeiras = usuarioLogado.getFinanceiras();
        }

        if (filter.getFinanceira() != null) {
            if (financeiras == null) {
                financeiras = new ArrayList<>();
                financeiras.add(filter.getFinanceira());
            } else {
                financeiras.retainAll(Arrays.asList(filter.getFinanceira()));
            }
        }

        Date dataInicio = setDataInicio(filter).getTime();
        Date dataFim = setDataFim(filter).getTime();

        if (financeiras != null) {
            return relatorioRepository.countByDia(financeiras, usuarioLogado.getUf(), filter.getSituacao(), filter.getTipoContrato(), filter.getTipoRestricao(), dataInicio, dataFim);
        }

        return relatorioRepository.countByDia(usuarioLogado.getUf(), filter.getSituacao(), filter.getTipoContrato(), filter.getTipoRestricao(), dataInicio, dataFim);
    }

    private Calendar setDataFim(RelatorioDTO filter) {
        Calendar dataFim = Calendar.getInstance();
        dataFim.set(Calendar.MONTH, filter.getMes() - 1);
        dataFim.set(Calendar.YEAR, filter.getAno());
        dataFim.set(Calendar.DAY_OF_MONTH, dataFim.getActualMaximum(Calendar.DAY_OF_MONTH));
        dataFim.set(Calendar.HOUR_OF_DAY, dataFim.getActualMaximum(Calendar.HOUR_OF_DAY));
        dataFim.set(Calendar.MINUTE, dataFim.getActualMaximum(Calendar.MINUTE));
        dataFim.set(Calendar.MILLISECOND, dataFim.getActualMaximum(Calendar.MILLISECOND));
        return dataFim;
    }

    private Calendar setDataInicio(RelatorioDTO filter) {
        Calendar dataInicio = Calendar.getInstance();
        dataInicio.set(Calendar.DAY_OF_MONTH, 1);
        dataInicio.set(Calendar.MONTH, filter.getMes() - 1);
        dataInicio.set(Calendar.YEAR, filter.getAno());
        dataInicio = DateUtils.truncate(dataInicio, Calendar.DATE);
        return dataInicio;
    }

    public List<ResultadoDiarioUsuarioDTO> findByDiaUsuario(RelatorioDTO filter) {
        List<Long> financeiras = new ArrayList<>();
        Usuario usuarioLogado = usuarioService.findByCpfFinanceiras(filter.getUsuario());
        if (usuarioLogado.getPerfil() == Perfil.FINANCEIRA || !usuarioLogado.getFinanceiras().isEmpty()) {
            financeiras = usuarioLogado.getFinanceiras().stream().map(Financeira::getId).collect(Collectors.toList());
        }
        financeiras.add(0L);

        Date dataInicio = setDataInicio(filter).getTime();
        Date dataFim = setDataFim(filter).getTime();

        List<Object[]> list = relatorioRepository.findByDiaUsuario(financeiras, financeiras.size(), usuarioLogado.getUf() == null ? "" : usuarioLogado.getUf().toString(), dataInicio, dataFim);
        List<ResultadoDiarioUsuarioDTO> retorno = new ArrayList<>();
        for (Object[] l : list) {
            retorno.add(new ResultadoDiarioUsuarioDTO(Uf.valueOf((String) l[0]), (String) l[1], (String) l[2], (Date) l[3], (BigInteger) l[4], (BigInteger) l[5], (BigInteger) l[6]));
        }

        return retorno;
    }

    public List<DashboardDTO> findDashboard(Date data, String usuario) {
        Usuario usuarioLogado = usuarioService.findByCpfFinanceiras(usuario);
        List<Long> financeiras = usuarioLogado.getFinanceiras().stream().map(Financeira::getId).collect(Collectors.toList());
        List<Object[]> list = new ArrayList<>();
        Uf uf = usuarioLogado.isPerfilAdministrador() ? null : usuarioLogado.getUf();
        if (financeiras.isEmpty()) {
            list =  relatorioRepository.findDashboardNative(data);
        } else {
            list =  relatorioRepository.findDashboardNative(data, financeiras);
        }
        List<DashboardDTO> retorno = new ArrayList<>();
        for (Object[] l : list) {
            if (l[1] == null || l[1].equals(0)) {
                continue;
            }
            retorno.add(new DashboardDTO(Uf.valueOf((String) l[0]), (BigInteger) l[1], (BigDecimal) l[2], (BigDecimal) l[3], (BigDecimal) l[4], (BigDecimal) l[5], (String) l[6]));
        }
        return retorno;
    }

    @Override
    protected PagingAndSortingRepository<Contrato, Long> getRepository() {
        return relatorioRepository;
    }

}
