package com.registrocontrato.relatorio.service;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.Join;
import javax.persistence.criteria.JoinType;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import javax.transaction.Transactional;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.data.domain.Sort.Direction;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.data.repository.PagingAndSortingRepository;
import org.springframework.stereotype.Service;

import com.registrocontrato.infra.entity.Financeira;
import com.registrocontrato.infra.entity.Uf;
import com.registrocontrato.infra.service.BaseService;
import com.registrocontrato.registro.entity.ArquivoRemessa;
import com.registrocontrato.registro.entity.ItemArquivoRemessa;
import com.registrocontrato.relatorio.repository.RelatorioRemessaRepository;
import com.registrocontrato.relatorio.service.dto.RelatorioArquivoRemessaDTO;
import com.registrocontrato.seguranca.entity.Perfil;
import com.registrocontrato.seguranca.entity.Usuario;
import com.registrocontrato.seguranca.service.UsuarioService;

@Service
@Transactional
public class RelatorioRemessaService extends BaseService<ArquivoRemessa, RelatorioArquivoRemessaDTO> {
	
	private static final long serialVersionUID = 1L;

	@Autowired
	private UsuarioService usuarioService;
	
	@Autowired
	private RelatorioRemessaRepository repository;
	
	
	@Override
	public Page<ArquivoRemessa> findAll(int first, int pageSize, RelatorioArquivoRemessaDTO filter) {
		Specification<ArquivoRemessa> contratoSpec = new Specification<ArquivoRemessa>() {

			@Override
			public Predicate toPredicate(Root<ArquivoRemessa> root, CriteriaQuery<?> cq, CriteriaBuilder cb) {
				Join<ArquivoRemessa, ItemArquivoRemessa> join = root.join("registros", JoinType.LEFT);
				cq.distinct(true);
				List<Predicate> predicates = new ArrayList<>();

				if (StringUtils.isNotEmpty(filter.getChassi())) {
					predicates.add(cb.like(cb.lower(join.get("chassi")), "%" + filter.getChassi().toLowerCase() + "%"));
				}
				
				if (StringUtils.isNotEmpty(filter.getNumeroContrato())) {
					predicates.add(cb.like(cb.lower(join.get("numeroContrato")), "%" + filter.getNumeroContrato().toLowerCase() + "%"));
				}
				
				if (StringUtils.isNotEmpty(filter.getNomeArquivo())) {
					predicates.add(cb.like(cb.lower(root.<String>get("nome")), "%" + filter.getNomeArquivo().toLowerCase() + "%"));
				}
				if (filter.getDataInicio() != null){
					Calendar c = getDateHoraMinima(filter.getDataInicio());
					predicates.add( cb.greaterThanOrEqualTo(root.<Date>get("dataTransacao"), c.getTime()));
				}
				if (filter.getDataFim() != null){
					Calendar c = getDateHoraMaxima(filter.getDataFim());
					predicates.add( cb.lessThanOrEqualTo(root.<Date>get("dataTransacao"), c.getTime()));
				}
				
				// se o usuario eh financeira filtrar somente os usuario da financeira
				Usuario usuarioLogado = usuarioService.findByCpfFinanceiras(filter.getUsuario());
				if (usuarioLogado.getPerfil() == Perfil.FINANCEIRA || !usuarioLogado.getFinanceiras().isEmpty()) {
					List<Financeira> values = usuarioLogado.getFinanceiras();
					predicates.add(join.get("financeira").in(values));
				}
				if (usuarioLogado.getUf() != null) {
					predicates.add(cb.equal(join.<Uf>get("ufRegistro"), usuarioLogado.getUf()));
				}
				return andTogether(predicates, cb);
			}

			private Calendar getDateHoraMaxima(Date data) {
				Calendar c = Calendar.getInstance();
				c.setTime(data);
				c.set(Calendar.HOUR_OF_DAY, 23);
				c.set(Calendar.MINUTE, 59);
				c.set(Calendar.SECOND, 59);
				c.set(Calendar.MILLISECOND, 999);
				return c;
			}

			private Calendar getDateHoraMinima(Date data) {
				Calendar c = Calendar.getInstance();
				c.setTime(data);
				c.set(Calendar.HOUR_OF_DAY, 0);
				c.set(Calendar.MINUTE, 0);
				c.set(Calendar.SECOND, 0);
				c.set(Calendar.MILLISECOND, 0);
				return c;
			}

			private Predicate andTogether(List<Predicate> predicates, CriteriaBuilder cb) {
				return cb.and(predicates.toArray(new Predicate[0]));
			}
		};
		Page<ArquivoRemessa> p = repository.findAll(contratoSpec, new PageRequest(first / pageSize, pageSize, new Sort(Direction.DESC, "id")));
		if (p.getContent() !=null) {
			for (ArquivoRemessa a : p.getContent()) {
				a.getRegistros().forEach(i->i.getId());
			}
		}
		return p;
	}

	@Override
	protected PagingAndSortingRepository<ArquivoRemessa, Long> getRepository() {
		return repository;
	}
}
