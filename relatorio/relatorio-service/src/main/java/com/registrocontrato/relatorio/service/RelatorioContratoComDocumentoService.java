package com.registrocontrato.relatorio.service;

import com.registrocontrato.infra.entity.Financeira;
import com.registrocontrato.infra.entity.Uf;
import com.registrocontrato.infra.service.BaseService;
import com.registrocontrato.infra.util.PlaceconUtil;
import com.registrocontrato.registro.entity.Anexo;
import com.registrocontrato.registro.entity.Contrato;
import com.registrocontrato.registro.entity.Veiculo;
import com.registrocontrato.registro.repository.AnexoRepository;
import com.registrocontrato.relatorio.repository.RelatorioContratoComDocumentoRepository;
import com.registrocontrato.relatorio.service.dto.RelatorioContratoComDocumentoDTO;
import com.registrocontrato.seguranca.entity.Perfil;
import com.registrocontrato.seguranca.entity.Usuario;
import com.registrocontrato.seguranca.service.UsuarioService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.data.domain.Sort.Direction;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.data.repository.PagingAndSortingRepository;
import org.springframework.stereotype.Service;

import javax.persistence.criteria.*;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Service

public class RelatorioContratoComDocumentoService extends BaseService<Veiculo, RelatorioContratoComDocumentoDTO> {

    private static final long serialVersionUID = 1L;

    @Autowired
    private UsuarioService usuarioService;

    @Autowired
    private AnexoRepository anexoRepository;

    @Autowired
    private RelatorioContratoComDocumentoRepository repository;

    private static final int MAX_REGISTROS = 200000;

    @Override
    public Page<Veiculo> findAll(int first, int pageSize, RelatorioContratoComDocumentoDTO filter) {

        Specification<Veiculo> contratoSpec = new Specification<Veiculo>() {
            @Override
            public Predicate toPredicate(Root<Veiculo> root, CriteriaQuery<?> cq, CriteriaBuilder cb) {
//				cq.distinct(true);
                List<Predicate> predicates = new ArrayList<>();
                if (Long.class != cq.getResultType()) {
                    root.fetch("contrato");
                }
                Join<Contrato, Anexo> joinContrato = root.join("contrato", JoinType.INNER);
                Join<Contrato, Anexo> joinAnexo = joinContrato.join("anexos", JoinType.INNER);

                if (filter.getUf() != null) {
                    predicates.add(cb.equal(joinContrato.<Uf>get("ufRegistro"), filter.getUf()));
                }
                if (filter.getDataInicio() != null) {
                    predicates.add(cb.greaterThanOrEqualTo(joinContrato.<Date>get("dataCadastro"), filter.getDataInicio()));
                }
                if (filter.getDataFim() != null) {
                    Date date = PlaceconUtil.maxDateTime(filter.getDataFim());
                    predicates.add(cb.lessThanOrEqualTo(joinContrato.<Date>get("dataCadastro"), date));
                }

//                predicates.add(cb.isTrue(joinAnexo.get("enviado")));

                if (filter.getFinanceira() != null) {
                    predicates.add(cb.equal(joinContrato.<Financeira>get("financeira"), filter.getFinanceira()));
                }

                // se o usuario eh financeira filtrar somente os usuario da financeira
                Usuario usuarioLogado = usuarioService.findByCpfFinanceiras(filter.getUsuario());
                if (usuarioLogado.getPerfil() == Perfil.FINANCEIRA || !usuarioLogado.getFinanceiras().isEmpty()) {
                    List<Financeira> values = usuarioLogado.getFinanceiras();
                    predicates.add(joinContrato.<Financeira>get("financeira").in(values));
                }
                if (usuarioLogado.getPerfil() == Perfil.DETRAN) {
                    predicates.add(cb.isNotNull(joinContrato.<Long>get("numeroRegistroEletronico")));
                }
                if (usuarioLogado.getUf() != null) {
                    predicates.add(cb.equal(joinContrato.<Uf>get("ufRegistro"), usuarioLogado.getUf()));
                }

                cq.orderBy(
                        cb.asc(joinContrato.<Financeira>get("financeira")),
                        cb.asc(joinContrato.<Uf>get("ufRegistro")),
                        cb.asc(joinContrato.<Date>get("dataCadastro"))
                );

                return andTogether(predicates, cb);
            }

            private Predicate andTogether(List<Predicate> predicates, CriteriaBuilder cb) {
                return cb.and(predicates.toArray(new Predicate[0]));
            }
        };

        Page<Veiculo> list = null;
        if (pageSize == 0) {
            list = repository.findAll(contratoSpec, new PageRequest(0, MAX_REGISTROS,
                    new Sort(Direction.ASC, "contrato.financeira").and(new Sort(Direction.ASC, "contrato.ufRegistro")).and(new Sort(Direction.ASC, "contrato.dataCadastro"))));
        } else {
            Direction direction = filter.getDirection() == null ? Direction.DESC : filter.getDirection();
            list = repository.findAll(contratoSpec, new PageRequest(first / pageSize, pageSize,
                    new Sort(direction, "contrato.financeira")
                            .and(new Sort(direction, "contrato.ufRegistro"))
                            .and(new Sort(direction, "contrato.dataCadastro"))));
        }
        return list;

    }

    @Override
    protected PagingAndSortingRepository<Veiculo, Long> getRepository() {
        return repository;
    }

    public List<Anexo> findAnexos(Contrato contrato) {

        return anexoRepository.findByContrato(contrato);

    }

}
