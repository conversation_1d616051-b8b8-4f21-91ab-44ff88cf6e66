package com.registrocontrato.relatorio.service.dto;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import com.registrocontrato.infra.entity.Financeira;
import com.registrocontrato.infra.entity.GrupoFinanceira;
import com.registrocontrato.infra.entity.SimNao;
import com.registrocontrato.infra.entity.Uf;
import com.registrocontrato.infra.service.BaseDTO;
import com.registrocontrato.registro.entity.Credenciamento;
import com.registrocontrato.registro.entity.CupomDesconto;
import com.registrocontrato.registro.enums.SituacaoCobranca;

public class RelatorioCobrancaDTO extends BaseDTO{


	private static final long serialVersionUID = 1L;

	private Long id;

	private Uf estado;

	private Financeira financeira;

	private List<Financeira> financeiras;

	private List<GrupoFinanceira> gruposFinanceiras;

	private Date dataInicio;

	private Date dataFim;

	private Date dataGeracao;

	private SituacaoCobranca situacaoCobranca;

	private List<SituacaoCobranca> situacoesCobranca = new ArrayList<>();

	private Long quantidadeRegistros;

	private BigDecimal valorCobranca;

	private Credenciamento credenciamento;

	private CupomDesconto cupomDesconto;

	private String usuario;
	
	private SimNao integraMais;
	
	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public List<Financeira> getFinanceiras() {
		return financeiras;
	}

	public void setFinanceiras(List<Financeira> financeiras) {
		this.financeiras = financeiras;
	}

	public List<GrupoFinanceira> getGruposFinanceiras() {
		return gruposFinanceiras;
	}

	public void setGruposFinanceiras(List<GrupoFinanceira> gruposFinanceiras) {
		this.gruposFinanceiras = gruposFinanceiras;
	}

	public Date getDataInicio() {
		return dataInicio;
	}

	public void setDataInicio(Date dataInicio) {
		this.dataInicio = dataInicio;
	}

	public Date getDataFim() {
		return dataFim;
	}

	public void setDataFim(Date dataFim) {
		this.dataFim = dataFim;
	}

	public Date getDataGeracao() {
		return dataGeracao;
	}

	public void setDataGeracao(Date dataGeracao) {
		this.dataGeracao = dataGeracao;
	}

	public Uf getEstado() {
		return estado;
	}

	public void setEstado(Uf estado) {
		this.estado = estado;
	}

	public Financeira getFinanceira() {
		return financeira;
	}

	public void setFinanceira(Financeira financeira) {
		this.financeira = financeira;
	}

	public SituacaoCobranca getSituacaoCobranca() {
		return situacaoCobranca;
	}

	public void setSituacaoCobranca(SituacaoCobranca situacaoCobranca) {
		this.situacaoCobranca = situacaoCobranca;
	}

	public CupomDesconto getCupomDesconto() {
		return cupomDesconto;
	}

	public void setCupomDesconto(CupomDesconto cupomDesconto) {
		this.cupomDesconto = cupomDesconto;
	}

	public Long getQuantidadeRegistros() {
		return quantidadeRegistros;
	}

	public void setQuantidadeRegistros(Long quantidadeRegistros) {
		this.quantidadeRegistros = quantidadeRegistros;
	}

	public BigDecimal getValorCobranca() {
		return valorCobranca;
	}

	public void setValorCobranca(BigDecimal valorCobranca) {
		this.valorCobranca = valorCobranca;
	}

	public Credenciamento getCredenciamento() {
		return credenciamento;
	}

	public void setCredenciamento(Credenciamento credenciamento) {
		this.credenciamento = credenciamento;
	}

	public String getUsuario() {
		return usuario;
	}

	public void setUsuario(String usuario) {
		this.usuario = usuario;
	}

	public List<SituacaoCobranca> getSituacoesCobranca() {
		return situacoesCobranca;
	}

	public void setSituacoesCobranca(List<SituacaoCobranca> situacoesCobranca) {
		this.situacoesCobranca = situacoesCobranca;
	}


	public SimNao getIntegraMais() {
		return integraMais;
	}
	
	public void setIntegraMais(SimNao integraMais) {
		this.integraMais = integraMais;
	}
}
