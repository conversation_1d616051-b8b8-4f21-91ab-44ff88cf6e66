package com.registrocontrato.relatorio.service;

import com.registrocontrato.finaceiro.entity.Imposto;
import com.registrocontrato.finaceiro.entity.dto.ImpostoDTO;
import com.registrocontrato.financeiro.service.ImpostoService;
import com.registrocontrato.infra.entity.Financeira;
import com.registrocontrato.infra.entity.Uf;
import com.registrocontrato.infra.service.BaseService;
import com.registrocontrato.registro.entity.Cobranca;
import com.registrocontrato.registro.enums.SituacaoCobranca;
import com.registrocontrato.relatorio.repository.RelatorioCobrancaRepository;
import com.registrocontrato.relatorio.service.dto.FaturamentoDTO;
import com.registrocontrato.relatorio.service.dto.RelatorioCobrancaDTO;
import com.registrocontrato.seguranca.entity.Perfil;
import com.registrocontrato.seguranca.entity.Usuario;
import com.registrocontrato.seguranca.service.GrupoFinanceiraService;
import com.registrocontrato.seguranca.service.UsuarioService;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.data.domain.Sort.Direction;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.data.repository.PagingAndSortingRepository;
import org.springframework.stereotype.Service;

import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Service
public class RelatorioCobrancaService extends BaseService<Cobranca, RelatorioCobrancaDTO> {

    private final UsuarioService usuarioService;

    private final ImpostoService impostoService;

    private final RelatorioCobrancaRepository repository;

    private final GrupoFinanceiraService grupoFinanceiraService;

    public RelatorioCobrancaService(UsuarioService usuarioService, ImpostoService impostoService, RelatorioCobrancaRepository repository, GrupoFinanceiraService grupoFinanceiraService) {
        this.usuarioService = usuarioService;
        this.impostoService = impostoService;
        this.repository = repository;
        this.grupoFinanceiraService = grupoFinanceiraService;
    }

    private FaturamentoDTO buscarFaturamentoCobranca(Cobranca cobranca) {
        FaturamentoDTO fat = new FaturamentoDTO();
        fat.setDataGeracaoCobranca(cobranca.getDataGeracao());
        fat.setDocumento(cobranca.getFinanceira().getDocumento());
        fat.setNomeFinanceira(cobranca.getFinanceira().getNome());
        fat.setUfCobranca(cobranca.getEstado());
        fat.setSituacaoCobranca(cobranca.getSituacaoCobranca());
        fat.setNumeroNotaFiscal(cobranca.getNumeroNotaFiscal());
        fat.setNumeroRegistros(cobranca.getQuantidadeRegistros());
        fat.setValorCredenciada(cobranca.getValorCredenciada());
        fat.setValorDesconto(cobranca.getValorDesconto());
        fat.setReceitaBruta(cobranca.getValorCredenciada().subtract(cobranca.getValorDesconto()).add(cobranca.getValorIntegraMais()));

        return fat;
    }

    public Page<FaturamentoDTO> buscarFaturamentoLiquido(int first, int pageSize, RelatorioCobrancaDTO filter) {
        filter.setSituacaoCobranca(SituacaoCobranca.PAGA);
        Page<Cobranca> cobrancaPage = findAll(first, pageSize, filter);

        return cobrancaPage.map(cobranca -> {
            FaturamentoDTO faturamento = buscarFaturamentoCobranca(cobranca);

            List<Imposto> impostos = impostoService
                    .findAll(0, 10, new ImpostoDTO(cobranca.getDataGeracao()))
                    .getContent();

            BigDecimal impostoSobreVenda = impostos.stream().map(i ->
                            i.getPercentualImposto()
                                    .multiply(faturamento.getReceitaBruta())
                                    .divide(new BigDecimal("100")))
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            faturamento.setImpostoSobreVenda(impostoSobreVenda);
            faturamento.setReceitaLiquida(faturamento.getReceitaBruta().subtract(impostoSobreVenda));

            return faturamento;
        });
    }

    @Override
    public Page<Cobranca> findAll(int first, int pageSize, RelatorioCobrancaDTO filter) {

        Specification<Cobranca> cobrancaSpecification = new Specification<Cobranca>() {

            @Override
            public Predicate toPredicate(Root<Cobranca> root, CriteriaQuery<?> cq, CriteriaBuilder cb) {
                cq.distinct(true);
                List<Predicate> predicates = new ArrayList<>();

                if (filter.getEstado() != null) {
                    predicates.add(cb.equal(root.<Uf>get("estado"), filter.getEstado()));
                }

                if (filter.getSituacaoCobranca() != null) {
                    predicates.add(cb.equal(root.<SituacaoCobranca>get("situacaoCobranca"), filter.getSituacaoCobranca()));
                }

                if (!filter.getSituacoesCobranca().isEmpty()) {
                    predicates.add(root.<SituacaoCobranca>get("situacaoCobranca").in(filter.getSituacoesCobranca()));
                }

                if (filter.getDataInicio() != null) {
                    LocalDateTime l = filter.getDataInicio().toInstant().atZone(ZoneId.systemDefault()).toLocalDate().atStartOfDay();
                    predicates.add(cb.greaterThanOrEqualTo(root.<Date>get("dataInicio"), java.sql.Date.valueOf(l.toLocalDate())));
                }

                if (filter.getDataFim() != null) {
                    LocalDateTime l = filter.getDataFim().toInstant().atZone(ZoneId.systemDefault()).toLocalDate().plusDays(1).atStartOfDay();
                    predicates.add(cb.lessThan(root.<Date>get("dataFim"), java.sql.Date.valueOf(l.toLocalDate())));
                }



                // se o usuario eh financeira filtrar somente os usuario da financeira
                Usuario usuarioLogado = usuarioService.findByCpfFinanceiras(filter.getUsuario());
                if (usuarioLogado.getPerfil() == Perfil.FINANCEIRA || !usuarioLogado.getFinanceiras().isEmpty()) {
                    List<Financeira> values = usuarioLogado.getFinanceiras();
                    predicates.add(root.<Financeira>get("financeira").in(values));
                } else {
                    List<Financeira> values = new ArrayList<>();
                    if (filter.getFinanceiras() != null && !filter.getFinanceiras().isEmpty()) {
                        values.addAll(filter.getFinanceiras());
                    }

                    if (filter.getGruposFinanceiras() != null && !filter.getGruposFinanceiras().isEmpty()) {
                        filter.getGruposFinanceiras().forEach((grupo -> values.addAll(grupoFinanceiraService.findOne(grupo.getId()).getFinanceiras())));
                    }

                    if (!values.isEmpty()) {
                        predicates.add(root.<Financeira>get("financeira").in(values));
                    }

                    if (filter.getFinanceira() != null) {
                        predicates.add(cb.equal(root.<Financeira>get("financeira"), filter.getFinanceira()));
                    }
                }

                if (usuarioLogado.getUf() != null && !usuarioLogado.isPerfilAdministrador()) {
                    predicates.add(cb.equal(root.<Uf>get("estado"), usuarioLogado.getUf()));
                }
                return andTogether(predicates, cb);
            }

            private Predicate andTogether(List<Predicate> predicates, CriteriaBuilder cb) {
                return cb.and(predicates.toArray(new Predicate[0]));
            }
        };
        return repository.findAll(cobrancaSpecification, new PageRequest(first / pageSize, pageSize, new Sort(Direction.DESC, "id")));
    }


    @Override
    protected PagingAndSortingRepository<Cobranca, Long> getRepository() {
        return repository;
    }

}
