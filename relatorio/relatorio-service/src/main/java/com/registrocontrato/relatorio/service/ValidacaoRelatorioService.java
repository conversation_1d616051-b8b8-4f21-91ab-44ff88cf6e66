package com.registrocontrato.relatorio.service;

import com.registrocontrato.infra.entity.CampoFormularioValidacao;
import com.registrocontrato.infra.entity.CampoTemplateValidacao;
import com.registrocontrato.infra.entity.TemplateValidacao;
import com.registrocontrato.infra.entity.Uf;
import com.registrocontrato.infra.exception.ServiceException;
import com.registrocontrato.infra.relatorio.RelatorioData;
import com.registrocontrato.infra.relatorio.RelatorioDefault;
import com.registrocontrato.infra.service.BaseService;
import com.registrocontrato.infra.util.PlaceconUtil;
import com.registrocontrato.registro.entity.Credenciamento;
import com.registrocontrato.registro.entity.BilhetagemGravame;
import com.registrocontrato.relatorio.repository.ValidacaoRelatorioRepository;
import com.registrocontrato.relatorio.service.dto.ValidacaoRelatorioDTO;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.text.PDFTextStripper;
import org.apache.poi.openxml4j.exceptions.InvalidFormatException;
import org.apache.poi.ss.usermodel.*;
import org.primefaces.model.file.UploadedFile;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.repository.PagingAndSortingRepository;
import org.springframework.stereotype.Service;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

import static com.registrocontrato.infra.util.PlaceconUtil.formataValorMonetario;

@Service
public class ValidacaoRelatorioService extends BaseService {

    @Autowired
    private ValidacaoRelatorioRepository validacaoRelatorioRepository;

    @Autowired
    private RelatorioDefault relatorioDefault;

    public String validarRelatorioExterno(UploadedFile externo, UploadedFile relatorio, TemplateValidacao templateValidacao, Date dataInicio, Date dataFim) {
        List<String> relatorioP = processarArquivo(relatorio);
        List<Map<String, String>> externoP = processarCsv(externo, templateValidacao);
        List<RelatorioData> abas = processarLinhasExterno(relatorioP, externoP, dataInicio, dataFim);

        String nomeArquivo = "Relatorio_externo_comparacao-" +
                PlaceconUtil.formataData(new Date()) +
                ".xlsx";

        return relatorioDefault.excelComVariasAbas(nomeArquivo, abas);
    }

    public List<RelatorioData> processarLinhasExterno(List<String> linhas, List<Map<String, String>> externoP, Date dataInicio, Date dataFim) {
        List<String[]> relatorio = Collections.synchronizedList(new ArrayList<>());
        List<String[]> relatorioD = Collections.synchronizedList(new ArrayList<>());
        Map<String, BigDecimal> somatoriosPorUfGravame = new ConcurrentHashMap<>();
        Map<String, BigDecimal> somatoriosPorUfCrednciamento = new ConcurrentHashMap<>();

        Map<String, Map<String, String>> externoPorChassi = externoP.stream()
                .collect(Collectors.toMap(
                        map -> map.get("CHASSIS"),
                        map -> map,
                        (v1, v2) -> v1
                ));

        linhas.parallelStream().forEach(linha -> {
            String[] linhaSplit = linha.split(" ");
            linhaSplit = Arrays.stream(linhaSplit)
                    .filter(s -> !s.isEmpty())
                    .toArray(String[]::new);
            if (!new HashSet<>(Arrays.asList(linhaSplit)).containsAll(Arrays.asList("INCLUSAO", "GRAVAME"))) {
                return;
            }
            String uf = linhaSplit[0];
            String chassi = linhaSplit[1];
            StringBuilder nome = new StringBuilder(linhaSplit[2]);
            int index = 3;
            while (index < linhaSplit.length && linhaSplit[index].chars().allMatch(Character::isLetter)) {
                nome.append(" ").append(linhaSplit[index]);
                index++;
            }
            if (index >= linhaSplit.length) {
                relatorioD.add(new String[]{uf, nome.toString(), "", chassi});
                return;
            }
            String numero = linhaSplit[index];

            try {
                Map<String, String> externoLinha = externoPorChassi.get(chassi);

                if (externoLinha == null) {
                    relatorioD.add(new String[]{uf, nome.toString(), numero, chassi, "Chassi não encontrado no relatório externo"});
                    return;
                }

                String ufExterno = externoLinha.get("UF");
                if (!uf.equals(ufExterno) && externoLinha.get("UF") != null) {
                    relatorioD.add(new String[]{uf, nome.toString(), numero, chassi, "UF diferente no relatório externo"});
                    return;
                }
                String nomeExterno = externoLinha.get("NOME");
                if (!nome.toString().equals(nomeExterno) && externoLinha.get("NOME") != null) {
                    relatorioD.add(new String[]{uf, nome.toString(), numero, chassi, "Nome do devedor diferente no relatório externo"});
                    return;
                }
                String numeroExterno = externoLinha.get("NUMERO_CONTRATO");
                if (!numero.equals(numeroExterno) && externoLinha.get("NUMERO_CONTRATO") != null) {
                    relatorioD.add(new String[]{uf, nome.toString(), numero, chassi, "Numero do contrato diferente no relatório externo"});
                    return;
                }

                Optional<BilhetagemGravame> bilhetagemGravameOpt = validacaoRelatorioRepository.findGravame(Uf.getEnum(uf), dataInicio, dataFim);
                Optional<Credenciamento> credenciamentoOpt = validacaoRelatorioRepository.findCredenciamento(Uf.getEnum(uf), dataInicio, dataFim);

                if (!bilhetagemGravameOpt.isPresent() && !credenciamentoOpt.isPresent()) {
                    relatorioD.add(new String[]{uf, nome.toString(), numero, chassi, "Não existe bilhetagem e credenciamento para o estado"});
                    return;
                }

                relatorio.add(new String[]{
                        uf,
                        nome.toString(),
                        numero,
                        chassi,
                        bilhetagemGravameOpt.isPresent() ? formataValorMonetario(bilhetagemGravameOpt.get().getValorTotal()) : "Não cadastrado",
                        credenciamentoOpt.isPresent() ? formataValorMonetario(credenciamentoOpt.get().getValorTotal()) : "Não cadastrado"
                });

                if (bilhetagemGravameOpt.isPresent()) {
                    somatoriosPorUfGravame.compute(uf, (key, oldValue) ->
                            oldValue == null ? bilhetagemGravameOpt.get().getValorTotal() : oldValue.add(bilhetagemGravameOpt.get().getValorTotal()));
                }

                if (credenciamentoOpt.isPresent()) {
                    somatoriosPorUfCrednciamento.compute(uf, (key, oldValue) ->
                            oldValue == null ? credenciamentoOpt.get().getValorTotalComposto() : oldValue.add(credenciamentoOpt.get().getValorTotalComposto()));
                }

            } catch (RuntimeException e) {
            }
        });

        RelatorioData resumo = getResumoData(somatoriosPorUfGravame, somatoriosPorUfCrednciamento);
        RelatorioData consolidacao = getConsolidacaoData(relatorio);
        RelatorioData divergenciasData = getDivergenciasData(relatorioD);
        return Arrays.asList(consolidacao, divergenciasData, resumo);
    }


    private List<Map<String, String>> processarCsv(UploadedFile arquivo, TemplateValidacao templateValidacao) {
        List<Map<String, String>> resultado = new ArrayList<>();

        try {
            byte[] cachedContent = arquivo.getContent();
            if (cachedContent == null || cachedContent.length == 0) {
                throw new IllegalArgumentException("O arquivo está vazio ou não foi carregado corretamente.");
            }

            try (ByteArrayInputStream bais = new ByteArrayInputStream(cachedContent)) {
                Workbook workbook = WorkbookFactory.create(bais);
                Sheet sheet = workbook.getSheetAt(0);

                List<CampoTemplateValidacao> campos = templateValidacao.getCampos()
                        .stream()
                        .sorted(Comparator.comparing(CampoTemplateValidacao::getOrdem))
                        .collect(Collectors.toList());

                int linhaAtual = 0;

                for (Row row : sheet) {
                    linhaAtual++;

                    if (linhaAtual < templateValidacao.getLinhaInicial()) {
                        continue;
                    }

                    if (templateValidacao.getLinhaFinal() != null && linhaAtual > templateValidacao.getLinhaFinal()) {
                        break;
                    }

                    Map<String, String> linhaMap = new LinkedHashMap<>();

                    for (CampoTemplateValidacao campo : campos) {
                        if (campo.getDescricao().equals(CampoFormularioValidacao.IGNORAR)) {
                            continue;
                        }
                        int indexColuna = campo.getOrdem() - 1;
                        String chave = campo.getDescricao().name();

                        Cell cell = row.getCell(indexColuna, Row.MissingCellPolicy.CREATE_NULL_AS_BLANK);
                        String valor = cell != null ? cell.toString().trim() : "";
                        linhaMap.put(chave, valor);
                    }
                    resultado.add(linhaMap);
                }

                workbook.close();
            } catch (InvalidFormatException | IOException e) {
                throw new IOException("Erro ao ler arquivo Excel", e);
            }
        } catch (IOException e) {
            throw new RuntimeException("Erro ao processar arquivo Excel", e);
        }

        return resultado;
    }

    public String validarRelatorioB3Local(UploadedFile pdfFile, Date dataInicio, Date dataFim, Long id, String documento) throws IOException {
        List<RelatorioData> abas = processarLinhas(processarArquivo(pdfFile), dataInicio, dataFim, id);
        StringBuilder nomeArquivo = new StringBuilder();
        nomeArquivo.append("Relatorio_gravame-");
        nomeArquivo.append(documento);
        nomeArquivo.append("-");
        nomeArquivo.append(PlaceconUtil.formataData(new Date()));
        nomeArquivo.append(".xlsx");

        return relatorioDefault.excelComVariasAbas(nomeArquivo.toString(), abas);
    }

    public List<RelatorioData> processarLinhas(List<String> linhas, Date dataInicio, Date dataFim, Long id) {
        List<String[]> relatorio = Collections.synchronizedList(new ArrayList<>());
        List<String[]> relatorioD = Collections.synchronizedList(new ArrayList<>());
        Map<String, BigDecimal> somatoriosPorUfGravame = new ConcurrentHashMap<>();
        Map<String, BigDecimal> somatoriosPorUfCrednciamento = new ConcurrentHashMap<>();

        linhas.forEach(linha -> {

            String[] linhaSplit = linha.split(" ");
            linhaSplit = Arrays.stream(linhaSplit)
                    .filter(s -> !s.isEmpty())
                    .toArray(String[]::new);
            if (!new HashSet<>(Arrays.asList(linhaSplit)).containsAll(Arrays.asList("INCLUSAO", "GRAVAME"))) {
                return;
            }
            if (linhaSplit.length < 3) {
                return;
            }
            String uf = linhaSplit[0];
            String chassi = linhaSplit[1];
            StringBuilder nome = new StringBuilder(linhaSplit[2]);
            int index = 3;
            while (index < linhaSplit.length && linhaSplit[index].chars().allMatch(Character::isLetter)) {
                nome.append(" ").append(linhaSplit[index]);
                index++;
            }
            if (index >= linhaSplit.length) {
                relatorioD.add(new String[]{uf, nome.toString(), "", chassi, "Linha inválida"});
                return;
            }
            String numero = linhaSplit[index];
            String valor = linhaSplit[linhaSplit.length - 2];

            try {

                logger.info(String.format("Validando numero contrato: %s | UF: %s | %s", numero, Uf.getEnum(uf), chassi));
                List<ValidacaoRelatorioDTO> val = validacaoRelatorioRepository.validar(numero, Uf.getEnum(uf), chassi);
                if (val.isEmpty()) {
                    relatorioD.add(new String[]{uf, nome.toString(), numero, chassi, "Não encontrado dentro do Placecon"});
                    return;
                }

                logger.info("Validando bilhetagem");
                Optional<BilhetagemGravame> bilhetagemGravameOpt = validacaoRelatorioRepository.findGravame(Uf.getEnum(uf), dataInicio, dataFim);

                logger.info(String.format("Validando credenciamento - UF: %s | data inicio: %s | data fim: %s", Uf.getEnum(uf), dataInicio, dataFim));
                Optional<Credenciamento> credenciamentoOpt = validacaoRelatorioRepository.findCredenciamento(Uf.getEnum(uf), dataInicio, dataFim);

                if (!bilhetagemGravameOpt.isPresent() && !credenciamentoOpt.isPresent()) {
                    relatorioD.add(new String[]{uf, nome.toString(), numero, chassi, "Não existe bilhetagem e credenciamento para o estado"});
                    return;
                }

                AtomicReference<String> mensagemC = new AtomicReference<>("");
                AtomicReference<String> mensagemF = new AtomicReference<>("");
                bilhetagemGravameOpt.ifPresent(bilhetagemGravame -> {
                    if (bilhetagemGravame.getValorCetip().compareTo(new BigDecimal(valor)) != 0) {
                        mensagemC.set("Valor Cetip divergente");
                    } else {
                        mensagemC.set("Valor Cetip congruente");
                    }
                    if (bilhetagemGravame.getValorFenaseg().compareTo(new BigDecimal(valor)) != 0) {
                        mensagemF.set("Valor Fenaseg divergente ");
                    } else {
                        mensagemF.set("Valor Fenaseg congruente ");
                    }
                });

                if (mensagemC.get().isEmpty() && mensagemF.get().isEmpty()) {
                    relatorioD.add(new String[]{uf, nome.toString(), numero, chassi, "Valor Cetip e Fenaseg divergentes"});
                    return;
                }

                boolean jaExiste = relatorio.stream().anyMatch(item ->
                        item[0].equals(uf) &&
                                item[1].contentEquals(nome) &&
                                item[2].equals(numero) &&
                                item[3].equals(chassi)
                );

                relatorio.add(new String[]{
                        uf,
                        nome.toString(),
                        numero,
                        chassi,
                        bilhetagemGravameOpt.isPresent() ? formataValorMonetario(bilhetagemGravameOpt.get().getValorTotal()) : "Não cadastrado",
                        credenciamentoOpt.isPresent() ? formataValorMonetario(credenciamentoOpt.get().getValorTotal()) : "Não cadastrado",
                        (jaExiste ? "Contrato duplicado " : "") + mensagemC.get() + mensagemF.get()
                });

                bilhetagemGravameOpt.ifPresent(bilhetagemGravame -> somatoriosPorUfGravame.compute(uf, (key, oldValue) ->
                        oldValue == null ? bilhetagemGravame.getValorTotal() : oldValue.add(bilhetagemGravame.getValorTotal())));

                credenciamentoOpt.ifPresent(credenciamento -> somatoriosPorUfCrednciamento.compute(uf, (key, oldValue) ->
                        oldValue == null ? credenciamento.getValorTotalComposto() : oldValue.add(credenciamento.getValorTotalComposto())));

            } catch (ServiceException e) {
                logger.warn("Divergência - " + e.getMessage());
            }

        });

        RelatorioData resumo = getResumoData(somatoriosPorUfGravame, somatoriosPorUfCrednciamento);
        RelatorioData consolidacao = getConsolidacaoData(relatorio);
        RelatorioData divergenciasData = getDivergenciasData(relatorioD);
        return Arrays.asList(consolidacao, divergenciasData, resumo);
    }

    private RelatorioData getResumoData(Map<String, BigDecimal> gravame, Map<String, BigDecimal> credenciamento) {
        List<String[]> relatorio = new ArrayList<>();

        Set<String> keys = new TreeSet<>(gravame.keySet());
        keys.addAll(credenciamento.keySet());
        keys.forEach(k -> {
            relatorio.add(new String[]{
                    k,
                    gravame.get(k) != null ? formataValorMonetario(Double.valueOf(gravame.get(k).toString())) : "Não cadastrado",
                    credenciamento.get(k) != null ? formataValorMonetario(Double.valueOf(String.valueOf(credenciamento.get(k)))) : "Não cadastrado"
            });
        });
        Map<String, Integer> headerNames = new LinkedHashMap<>();
        headerNames.put("UF", 15);
        headerNames.put("Valor Gravame", 25);
        headerNames.put("Valor Credenciamento", 25);
        RelatorioData resumo = new RelatorioData(
                headerNames,
                relatorio,
                "Resumo",
                null
        );
        return resumo;
    }

    private RelatorioData getConsolidacaoData(List<String[]> relatorio) {
        Map<String, Integer> headerNames = new LinkedHashMap<>();
        headerNames.put("UF do registro", 15);
        headerNames.put("Nome do Devedor", 45);
        headerNames.put("Número do Contrato", 20);
        headerNames.put("Chassi", 20);
        headerNames.put("Valor de registro de Gravame", 30);
        headerNames.put("Valor Credenciada", 22);
        headerNames.put("Mensagem", 40);

        RelatorioData consolidacao = new RelatorioData(
                headerNames,
                relatorio,
                "Consolidação",
                null
        );
        return consolidacao;
    }

    private RelatorioData getDivergenciasData(List<String[]> relatorioD) {
        Map<String, Integer> headerNamesD = new LinkedHashMap<>();
        headerNamesD.put("UF do registro", 15);
        headerNamesD.put("Nome do Devedor", 45);
        headerNamesD.put("Número do Contrato", 20);
        headerNamesD.put("Chassi", 20);
        headerNamesD.put("Mensagem", 40);

        return new RelatorioData(
                headerNamesD,
                relatorioD,
                "Divergências",
                null
        );
    }

    private List<String> processarArquivo(UploadedFile pdfFile) {
        try {
            byte[] cachedContent = pdfFile.getContent();

            try (ByteArrayInputStream bais = new ByteArrayInputStream(cachedContent); PDDocument document = PDDocument.load(bais)) {
                PDFTextStripper stripper = new PDFTextStripper();
                String textoExtraido = stripper.getText(document);
                String[] linhas = textoExtraido.split("\n");
                List<String> linhasFiltradas = new ArrayList<>();
                for (String linha : linhas) {
                    if (linha.trim().length() < 2) continue;
                    String prefix = linha.substring(1, 3);
                    String prefix2 = linha.substring(0, 2);
                    if (Uf.getEnum(prefix) != null || Uf.getEnum(prefix2) != null) {
                        linhasFiltradas.add(linha);
                    }
                }
                return linhasFiltradas;
            }
        } catch (IOException e) {
            throw new ServiceException(e);
        }
    }

    @Override
    public Page findAll(int first, int pageSize, Object filter) {
        return null;
    }

    @Override
    protected PagingAndSortingRepository getRepository() {
        return null;
    }
}
