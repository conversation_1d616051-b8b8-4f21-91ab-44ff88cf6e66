package com.registrocontrato.relatorio.service.dto;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

public class ResultadoGrupoDTO implements Serializable {

	private static final long serialVersionUID = 1L;

	private String financeira;

	private Date data;

	private Enum<?> tipo;

	private String grupo;

	private Long quantidade;
	
	private Long quantidadeVeiculos;

	private BigDecimal valor;

	private Boolean cobrancaUnificada;

	public ResultadoGrupoDTO() {
	}
	
	public ResultadoGrupoDTO(Date data, Long quantidade) {
		super();
		this.data = data;
		this.quantidade = quantidade;
	}

	public ResultadoGrupoDTO(Enum<?> tipo, Long quantidade, Long quantidadeVeiculos) {
		super();
		this.tipo = tipo;
		this.quantidade = quantidade;
		this.quantidadeVeiculos = quantidadeVeiculos;
	}

	public ResultadoGrupoDTO(Enum<?> tipo, Long quantidade, BigDecimal valor) {
		super();
		this.tipo = tipo;
		this.quantidade = quantidade;
		this.valor = valor;
	}


	public ResultadoGrupoDTO(String financeira,Enum<?> tipo, Long quantidade, Long quantidadeVeiculos) {
		super();
		this.financeira = financeira;
		this.tipo = tipo;
		this.quantidade = quantidade;
		this.quantidadeVeiculos = quantidadeVeiculos;
	}

	public ResultadoGrupoDTO(Enum<?> tipo, Long quantidade) {
		super();
		this.tipo = tipo;
		this.quantidade = quantidade;
	}

	public ResultadoGrupoDTO(Enum<?> tipo, BigDecimal valor) {
		super();
		this.tipo = tipo;
		this.valor = valor;
	}

	public ResultadoGrupoDTO(String grupo, BigDecimal valor) {
		super();
		this.grupo = grupo;
		this.valor = valor;
	}

	public ResultadoGrupoDTO(String grupo, Long quantidade) {
		super();
		this.grupo = grupo;
		this.quantidade = quantidade;
	}

	public Boolean getCobrancaUnificada() {
		return cobrancaUnificada;
	}

	public void setCobrancaUnificada(Boolean cobrancaUnificada) {
		this.cobrancaUnificada = cobrancaUnificada;
	}

	public String getFinanceira() {
		return financeira;
	}

	public void setFinanceira(String financeira) {
		this.financeira = financeira;
	}

	public String getGrupo() {
		return grupo;
	}

	public void setGrupo(String grupo) {
		this.grupo = grupo;
	}

	public Long getQuantidade() {
		return quantidade;
	}

	public void setQuantidade(Long quantidade) {
		this.quantidade = quantidade;
	}

	public BigDecimal getValor() {
		return valor;
	}

	public void setValor(BigDecimal valor) {
		this.valor = valor;
	}

	public Enum<?> getTipo() {
		return tipo;
	}

	public void setTipo(Enum<?> tipo) {
		this.tipo = tipo;
	}

	public Date getData() {
		return data;
	}

	public void setData(Date data) {
		this.data = data;
	}

	public Long getQuantidadeVeiculos() {
		return quantidadeVeiculos;
	}

	public void setQuantidadeVeiculos(Long quantidadeVeiculos) {
		this.quantidadeVeiculos = quantidadeVeiculos;
	}
	
}
