package com.registrocontrato.relatorio.repository;

import com.registrocontrato.infra.entity.Uf;
import com.registrocontrato.infra.service.BaseRepository;
import com.registrocontrato.registro.entity.Contrato;
import com.registrocontrato.registro.entity.Credenciamento;
import com.registrocontrato.registro.entity.BilhetagemGravame;
import com.registrocontrato.relatorio.service.dto.ValidacaoRelatorioDTO;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;
import java.util.Optional;

@Repository
public interface ValidacaoRelatorioRepository extends BaseRepository<Contrato> {

    @Query("SELECT new com.registrocontrato.relatorio.service.dto.ValidacaoRelatorioDTO(v.numeroChassi, v.numeroGravame, co.ufRegistro, co.nomeDevedorFinanciado) " +
            "from Cobranca c left join c.contratos co left join co.veiculos v left join co.financeira f " +
            "where co.numeroContrato = :numero and (c.estado = :uf or c.estado = 'BR') and v.numeroChassi = :chassi")
    List<ValidacaoRelatorioDTO> validar(@Param("numero") String numero, @Param("uf") Uf uf, @Param("chassi") String chassi);

    @Query("SELECT g from BilhetagemGravame g where g.uf = :uf and g.dataFim > :dataFim and g.dataInicio < :dataInicio")
    Optional<BilhetagemGravame> findGravame(@Param("uf") Uf uf, @Param("dataInicio") Date dataInicio, @Param("dataFim") Date dataFim);

    @Query("SELECT g from Credenciamento g where g.uf = :uf and g.dataFim > :dataFim and g.dataInicio < :dataInicio")
    Optional<Credenciamento> findCredenciamento(@Param("uf") Uf uf, @Param("dataInicio") Date dataInicio, @Param("dataFim") Date dataFim);
}
