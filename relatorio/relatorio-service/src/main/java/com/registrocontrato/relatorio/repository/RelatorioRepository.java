package com.registrocontrato.relatorio.repository;

import java.util.Date;
import java.util.List;

import org.springframework.data.jpa.domain.Specification;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import com.registrocontrato.infra.entity.Financeira;
import com.registrocontrato.infra.entity.Uf;
import com.registrocontrato.infra.service.BaseRepository;
import com.registrocontrato.registro.entity.Contrato;
import com.registrocontrato.registro.entity.Credenciamento;
import com.registrocontrato.registro.entity.PrecoComposto;
import com.registrocontrato.registro.enums.Situacao;
import com.registrocontrato.registro.enums.TipoContrato;
import com.registrocontrato.registro.enums.TipoRestricao;
import com.registrocontrato.relatorio.service.dto.ResultadoDiarioDTO;
import com.registrocontrato.relatorio.service.dto.ResultadoGrupoDTO;

@Repository
public interface RelatorioRepository extends BaseRepository<Contrato> {

	@Query("select new com.registrocontrato.relatorio.service.dto.ResultadoGrupoDTO(c.situacao, count(distinct c.id), count(v.id)) "
			+ "from Contrato c "
			+ "inner join c.veiculos v "
			+ "where c.financeira in (:financeiras) "
			+ "and (:integra is null or ((:integra = true and c.idProcessoB3 is not null) or (:integra = false and c.idProcessoB3 is null))) "
			+ "and (:uf is null or c.ufRegistro = :uf) "
			+ "and c.dataConclusaoDETRAN between :dataInicio and :dataFim "
			+ "group by c.situacao")
	List<ResultadoGrupoDTO> countBySituacao(@Param("financeiras")List<Financeira> financeiras, @Param("uf")Uf uf, @Param("dataInicio")Date dataInicio, @Param("dataFim")Date dataFim,  @Param("integra") Boolean integra);

	@Query("select new com.registrocontrato.relatorio.service.dto.ResultadoGrupoDTO(c.situacao, count(distinct c.id), count(v.id)) "
			+ "from Contrato c "
			+ "inner join c.veiculos v "
			+ "where (:uf is null or c.ufRegistro = :uf) "
			+ "and (:integra is null or ((:integra = true and c.idProcessoB3 is not null) or (:integra = false and c.idProcessoB3 is null))) "
			+ "and c.dataConclusaoDETRAN between :dataInicio and :dataFim "
			+ "group by c.situacao")
	List<ResultadoGrupoDTO> countBySituacao(@Param("uf")Uf uf, @Param("dataInicio")Date dataInicio, @Param("dataFim")Date dataFim,  @Param("integra") Boolean integra);

	@Query("select new com.registrocontrato.relatorio.service.dto.ResultadoGrupoDTO(c.tipoRestricao, count(distinct c.id), count(v.id)) "
			+ "from Contrato c "
			+ "inner join c.veiculos v "
			+ "where c.financeira in (:financeiras) "
			+ "and (:integra is null or ((:integra = true and c.idProcessoB3 is not null) or (:integra = false and c.idProcessoB3 is null))) "
			+ "and (:uf is null or c.ufRegistro = :uf) "
			+ "and c.dataConclusaoDETRAN between :dataInicio and :dataFim "
			+ "group by c.tipoRestricao")
	List<ResultadoGrupoDTO> countByRestricao(@Param("financeiras")List<Financeira> financeiras, @Param("uf")Uf uf, @Param("dataInicio")Date dataInicio, @Param("dataFim")Date dataFim,  @Param("integra") Boolean integra);

	@Query("select new com.registrocontrato.relatorio.service.dto.ResultadoGrupoDTO(c.tipoRestricao, count(distinct c.id), count(v.id)) "
			+ "from Contrato c "
			+ "inner join c.veiculos v "
			+ "where (:uf is null or c.ufRegistro = :uf) "
			+ "and (:integra is null or ((:integra = true and c.idProcessoB3 is not null) or (:integra = false and c.idProcessoB3 is null))) "
			+ "and c.dataConclusaoDETRAN between :dataInicio and :dataFim "
			+ "group by c.tipoRestricao")
	List<ResultadoGrupoDTO> countByRestricao(@Param("uf")Uf uf, @Param("dataInicio")Date dataInicio, @Param("dataFim")Date dataFim,  @Param("integra") Boolean integra);

	@Query("from Contrato c where c.dataConclusaoDETRAN between :dataInicio and :dataFim "
			+ "and (:uf is null or c.ufRegistro = :uf) ")
	List<Contrato>findByDataConclusaoDetran(@Param("dataInicio")Date dataInicio, @Param("dataFim")Date dataFim, @Param("uf")Uf uf);

	@Query("from Credenciamento c where c.uf = :uf "
	        + "and c.dataInicio between :dataInicio and :dataFim ")
	Credenciamento findByUfAndPeriodo(@Param("uf")Uf uf, @Param("dataInicio")Date dataInicio, @Param("dataFim")Date dataFim);

	@Query("from PrecoComposto pc where pc.credenciamento = :credenciamento")
	List<PrecoComposto>findPrecosCompostos(@Param("credenciamento") Credenciamento credenciamento);

	@Query("select new com.registrocontrato.relatorio.service.dto.ResultadoGrupoDTO(c.ufRegistro, count(distinct c.id), count(v.id)) "
			+ "from Contrato c "
			+ "inner join c.veiculos v "
			+ "where c.financeira in (:financeiras) "
			+ "and (:integra is null or ((:integra = true and c.idProcessoB3 is not null) or (:integra = false and c.idProcessoB3 is null))) "
			+ "and (:uf is null or c.ufRegistro = :uf) "
			+ "and c.dataConclusaoDETRAN between :dataInicio and :dataFim "
			+ "group by c.ufRegistro")
	List<ResultadoGrupoDTO> countByUf(@Param("financeiras")List<Financeira> financeiras, @Param("uf")Uf uf, @Param("dataInicio")Date dataInicio, @Param("dataFim")Date dataFim,  @Param("integra") Boolean integra);

	@Query("select new com.registrocontrato.relatorio.service.dto.ResultadoGrupoDTO(c.ufRegistro, count(distinct c.id), count(v.id)) "
			+ "from Contrato c "
			+ "inner join c.veiculos v "
			+ "where (:uf is null or c.ufRegistro = :uf) "
			+ "and (:integra is null or ((:integra = true and c.idProcessoB3 is not null) or (:integra = false and c.idProcessoB3 is null))) "
			+ "and c.dataConclusaoDETRAN between :dataInicio and :dataFim "
			+ "group by c.ufRegistro")
	List<ResultadoGrupoDTO> countByUf(@Param("uf")Uf uf, @Param("dataInicio")Date dataInicio, @Param("dataFim")Date dataFim, @Param("integra") Boolean integra);

	@Query("select new com.registrocontrato.relatorio.service.dto.ResultadoGrupoDTO(c.tipoContrato, count(distinct c.id), count(v.id)) "
			+ "from Contrato c "
			+ "inner join c.veiculos v "
			+ "where c.financeira in (:financeiras) "
			+ "and (:integra is null or ((:integra = true and c.idProcessoB3 is not null) or (:integra = false and c.idProcessoB3 is null))) "
			+ "and (:uf is null or c.ufRegistro = :uf) "
			+ "and c.dataConclusaoDETRAN between :dataInicio and :dataFim "
			+ "group by c.tipoContrato")
	List<ResultadoGrupoDTO> countByTipoContrato(@Param("financeiras")List<Financeira> financeiras, @Param("uf")Uf uf, @Param("dataInicio")Date dataInicio, @Param("dataFim")Date dataFim,  @Param("integra") Boolean integra);

	@Query("select new com.registrocontrato.relatorio.service.dto.ResultadoGrupoDTO(c.tipoContrato, count(distinct c.id), count(v.id)) "
			+ "from Contrato c "
			+ "inner join c.veiculos v "
			+ "where (:uf is null or c.ufRegistro = :uf) "
			+ "and c.dataConclusaoDETRAN between :dataInicio and :dataFim "
			+ "and (:integra is null or ((:integra = true and c.idProcessoB3 is not null) or (:integra = false and c.idProcessoB3 is null))) "
			+ "group by c.tipoContrato")
	List<ResultadoGrupoDTO> countByTipoContrato(@Param("uf")Uf uf, @Param("dataInicio")Date dataInicio, @Param("dataFim")Date dataFim,  @Param("integra") Boolean integra);

	List<ResultadoDiarioDTO> findAll(Specification<Contrato> specification);

	@Query("select new com.registrocontrato.relatorio.service.dto.ResultadoDiarioDTO("
			+ "c.ufRegistro, c.financeira, date_trunc('day', c.dataCadastro), count(distinct c.id), count(v.id)) "
			+ "from Contrato c "
			+ "inner join c.veiculos v "
			+ "where c.financeira in (:financeiras) "
			+ "and (:uf is null or c.ufRegistro = :uf) "
			+ "and (:situacao is null or c.situacao = :situacao) "
			+ "and (:tipoContrato is null or c.tipoContrato = :tipoContrato) "
			+ "and (:tipoRestricao is null or c.tipoRestricao = :tipoRestricao) "
			+ "and c.dataCadastro between :dataInicio and :dataFim "
			+ "group by c.ufRegistro, c.financeira, date_trunc('day', c.dataCadastro) "
			+ "order by c.ufRegistro, c.financeira, date_trunc('day', c.dataCadastro)")
	List<ResultadoDiarioDTO> countByDia(@Param("financeiras")List<Financeira> financeiras,
			@Param("uf")Uf uf,
			@Param("situacao")Situacao situacao,
			@Param("tipoContrato")TipoContrato tipoContrato,
			@Param("tipoRestricao")TipoRestricao tipoRestricao,
			@Param("dataInicio")Date dataInicio,
			@Param("dataFim")Date dataFim);

	@Query("select new com.registrocontrato.relatorio.service.dto.ResultadoDiarioDTO("
			+ "c.ufRegistro, c.financeira, date_trunc('day', c.dataCadastro), count(distinct c.id), count(v.id)) "
			+ "from Contrato c "
			+ "inner join c.veiculos v "
			+ "where (:uf is null or c.ufRegistro = :uf) "
			+ "and (:situacao is null or c.situacao = :situacao) "
			+ "and (:tipoContrato is null or c.tipoContrato = :tipoContrato) "
			+ "and (:tipoRestricao is null or c.tipoRestricao = :tipoRestricao) "
			+ "and c.dataCadastro between :dataInicio and :dataFim "
			+ "group by c.ufRegistro, c.financeira, date_trunc('day', c.dataCadastro) "
			+ "order by c.ufRegistro, c.financeira, date_trunc('day', c.dataCadastro)")
	List<ResultadoDiarioDTO> countByDia(@Param("uf")Uf uf,
			@Param("situacao")Situacao situacao,
			@Param("tipoContrato")TipoContrato tipoContrato,
			@Param("tipoRestricao")TipoRestricao tipoRestricao,
			@Param("dataInicio")Date dataInicio,
			@Param("dataFim")Date dataFim);

	@Query(nativeQuery = true,
			value = "select t.uf_registro uf, f.documento ||' - '|| f.nome as financeira, t.username, t.data_log, " +
					"(select count(*) from registro.contrato_aud cc " +
					" where cc.financeira_id = f.id and cc.revtype = 0 " +
					" and date_trunc('day', cc.data_conclusaodetran) = t.data_log) as cadastros," +
					"(select count(*) from registro.contrato_aud cc " +
					" where cc.financeira_id = f.id and cc.revtype = 1 " +
					" and cc.data_conclusaodetran is not null " +
					" and date_trunc('day', cc.data_conclusaodetran) = t.data_log) as transmissoes, " +
					" (select count(*) from registro.contrato_aud cc inner join registro.revision_entity r on cc.rev = r.id " +
					" where cc.financeira_id = f.id and cc.revtype = 2 " +
					" and (TO_DATE('01/01/1970 00:00:00','DD/MM/YYYY HH24:MI:SS') + (r.timestamp /1000/60/60/24 ||' day')\\:\\:interval) = t.data_log) as exclusoes " +
					"from " +
					"(select c.uf_registro, c.financeira_id, r.username as username, " +
					"(TO_DATE('01/01/1970 00:00:00','DD/MM/YYYY HH24:MI:SS') + (r.timestamp /1000/60/60/24 ||' day')\\:\\:interval) as data_log " +
					"from registro.contrato_aud c " +
					"inner join registro.revision_entity r " +
					"on c.rev = r.id " +
					"group by c.uf_registro, c.financeira_id, r.username, (TO_DATE('01/01/1970 00:00:00','DD/MM/YYYY HH24:MI:SS') + (r.timestamp /1000/60/60/24 ||' day')\\:\\:interval)) t " +
					"inner join registro.financeira f " +
					"on f.id = t.financeira_id " +
					"where t.data_log between :dataInicio and :dataFim " +
					"and ( :uf = '' or t.uf_registro = :uf ) " +
					"and ( :financeirasSize = 1 or f.id in :financeiras ) " +
					"group by t.uf_registro, f.documento, f.nome, f.id, t.username, t.data_log " +
					"order by t.data_log, t.username,  t.uf_registro, f.id")
	List<Object[]> findByDiaUsuario(
				@Param("financeiras")List<Long> financeiras,
				@Param("financeirasSize")Integer financeirasSize,
				@Param("uf")String uf,
				@Param("dataInicio")Date dataInicio,
				@Param("dataFim")Date dataFim);

//	TODO : adcionar contagem de operações sng
	@Query("select new com.registrocontrato.relatorio.service.dto.ResultadoGrupoDTO(c.ufRegistro, cb.quantidadeRegistros, cb.valorCobranca) "
			+ "from Cobranca cb "
			+ "inner join cb.contratos c "
			+ "where (:uf is null or c.ufRegistro = :uf) "
			+ "and cb.situacaoCobranca = 'PAGA' "
			+ "and cb.dataGeracao between :dataInicio and :dataFim "
			+ "group by c.ufRegistro, cb.quantidadeRegistros, cb.valorCobranca")
	List<ResultadoGrupoDTO> countFinanceiroByUf(@Param("uf")Uf uf, @Param("dataInicio")Date dataInicio, @Param("dataFim")Date dataFim);

	@Query("select new com.registrocontrato.relatorio.service.dto.ResultadoGrupoDTO(c.ufRegistro, cb.quantidadeRegistros, cb.valorCredenciada) "
			+ "from Cobranca cb "
			+ "inner join cb.contratos c "
			+ "inner join cb.financeira f "
			+ "where f in (:financeiras) "
			+ "and cb.dataGeracao between :dataInicio and :dataFim "
			+ "group by c.ufRegistro, cb.quantidadeRegistros, cb.valorCredenciada")
	List<ResultadoGrupoDTO> countFinanceiro(@Param("financeiras") List<Financeira> financeiras, @Param("dataInicio") Date dataInicio, @Param("dataFim") Date dataFim);

	@Query(value = "SELECT\n" +
			"        c.uf_registro,\n" +
			"        COUNT(*) AS quantidade_contratos,\n" +
			"        COALESCE(cr.valor_credenciada, 0) as valor_credenciada,\n" +
			"        COALESCE(cr.valordetran, 0) AS valor_detran,\n" +
			"        COALESCE(fd.percentual, 0) as percentual,\n" +
			"        COALESCE(fd.valor_final, 0) as valor_final,\n" +
			"        COALESCE(f.nome, '') as financeira\n" +
			"    FROM registro.contrato c\n" +
			"             LEFT JOIN registro.credenciamento cr\n" +
			"                       ON cr.uf = c.uf_registro AND cr.data_fim > :data\n" +
			"             LEFT JOIN financeiro.cupom_desconto cd\n" +
			"                       ON cd.uf = c.uf_registro AND cd.financeira_id = c.financeira_id\n" +
			"                           AND :data BETWEEN cd.data_inicio_vigencia AND cd.data_fim_vigencia\n" +
			"             LEFT JOIN financeiro.faixa_desconto fd\n" +
			"                       ON fd.cupom_id = cd.id\n" +
			"             LEFT JOIN registro.financeira f\n" +
			"                       ON f.id = c.financeira_id\n" +
			"    WHERE c.financeira_id IN (:financeiras)\n" +
			"      AND c.data_conclusaodetran > :data\n" +
			"      AND c.cobranca_id IS NULL\n" +
			"      AND c.situacao != 'ERRO'\n" +
			"      AND c.situacao_financeira = 'NAO_PAGO'\n" +
			"    GROUP BY c.uf_registro, cr.valor_credenciada, cr.valordetran, fd.percentual, fd.valor_final, f.nome", nativeQuery = true)
	List<Object[]> findDashboardNative(@Param("data") Date data, @Param("financeiras") List<Long> financeiras);

	@Query(value = "SELECT\n" +
			"        c.uf_registro,\n" +
			"        COUNT(*) AS quantidade_contratos,\n" +
			"        COALESCE(cr.valor_credenciada, 0) as valor_credenciada,\n" +
			"        COALESCE(cr.valordetran, 0) AS valor_detran,\n" +
			"        COALESCE(fd.percentual, 0) as percentual,\n" +
			"        COALESCE(fd.valor_final, 0) as valor_final,\n" +
			"        COALESCE(f.nome, '') as financeira\n" +
			"    FROM registro.contrato c\n" +
			"             LEFT JOIN registro.credenciamento cr\n" +
			"                       ON cr.uf = c.uf_registro AND cr.data_fim > :data\n" +
			"             LEFT JOIN financeiro.cupom_desconto cd\n" +
			"                       ON cd.uf = c.uf_registro AND cd.financeira_id = c.financeira_id\n" +
			"                           AND :data BETWEEN cd.data_inicio_vigencia AND cd.data_fim_vigencia\n" +
			"             LEFT JOIN financeiro.faixa_desconto fd\n" +
			"                       ON fd.cupom_id = cd.id\n" +
			"             LEFT JOIN registro.financeira f\n" +
			"                       ON f.id = c.financeira_id\n" +
			"    WHERE " +
			"      c.data_conclusaodetran >= :data\n" +
			"      AND c.cobranca_id IS NULL\n" +
		    "      AND c.situacao != 'ERRO'\n" +
			"      AND c.situacao_financeira = 'NAO_PAGO'\n" +
			"    GROUP BY c.uf_registro, cr.valor_credenciada, cr.valordetran, fd.percentual, fd.valor_final, f.nome", nativeQuery = true)
	List<Object[]> findDashboardNative(@Param("data") Date data);
}
