package com.registrocontrato.relatorio.service.dto;

import com.registrocontrato.infra.entity.Financeira;
import com.registrocontrato.infra.entity.GrupoFinanceira;
import com.registrocontrato.infra.entity.SimNao;
import com.registrocontrato.infra.entity.Uf;
import com.registrocontrato.infra.util.PlaceconUtil;
import com.registrocontrato.registro.entity.*;
import com.registrocontrato.registro.enums.Situacao;
import com.registrocontrato.registro.enums.SituacaoFinanceira;
import com.registrocontrato.registro.enums.TipoContrato;
import com.registrocontrato.registro.enums.TipoRestricao;
import org.primefaces.model.charts.bar.BarChartDataSet;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

public class RelatorioDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    private Date dataInicio;

    private Date dataFim;

    private Integer mes;

    private Integer ano;

    private Boolean comCobranca;

    private Situacao situacao;

    private TipoContrato tipoContrato;

    private TipoRestricao tipoRestricao;

    private String usuario;

    private Uf uf;

    private String grupo;

    private SituacaoFinanceira situacaoFinanceira;

    private Financeira financeira;

    private List<Financeira> financeiras;

    private List<GrupoFinanceira> gruposFinanceiras;

    private SimNao anexo;

    private Date dataConclusaoDetran;

    private MensagemRetorno mensagemRetorno;

    public List<GrupoFinanceira> getGruposFinanceiras() {
        return gruposFinanceiras;
    }

    public void setGruposFinanceiras(List<GrupoFinanceira> gruposFinanceiras) {
        this.gruposFinanceiras = gruposFinanceiras;
    }

    public List<Financeira> getFinanceiras() {
        return financeiras;
    }

    public void setFinanceiras(List<Financeira> financeiras) {
        this.financeiras = financeiras;
    }

    public Integer getMes() {
        return mes;
    }

    public void setMes(Integer mes) {
        this.mes = mes;
    }

    public Integer getAno() {
        return ano;
    }

    public void setAno(Integer ano) {
        this.ano = ano;
    }

    public Situacao getSituacao() {
        return situacao;
    }

    public void setSituacao(Situacao situacao) {
        this.situacao = situacao;
    }

    public TipoContrato getTipoContrato() {
        return tipoContrato;
    }

    public void setTipoContrato(TipoContrato tipoContrato) {
        this.tipoContrato = tipoContrato;
    }

    public TipoRestricao getTipoRestricao() {
        return tipoRestricao;
    }

    public void setTipoRestricao(TipoRestricao tipoRestricao) {
        this.tipoRestricao = tipoRestricao;
    }

    public Uf getUf() {
        return uf;
    }

    public void setUf(Uf uf) {
        this.uf = uf;
    }

    public Boolean getComCobranca() {
        return comCobranca;
    }

    public void setComCobranca(Boolean comCobranca) {
        this.comCobranca = comCobranca;
    }

    public String getUsuario() {
        return usuario;
    }

    public void setUsuario(String usuario) {
        this.usuario = usuario;
    }

    public String getGrupo() {
        return grupo;
    }

    public void setGrupo(String grupo) {
        this.grupo = grupo;
    }

    public Date getDataInicio() {
        return dataInicio;
    }

    public void setDataInicio(Date dataInicio) {
        this.dataInicio = dataInicio;
    }

    public String getDataInicioFormatada() {
        return dataInicio == null ? "" : PlaceconUtil.formatarDataPadrao(dataInicio);
    }

    public String getDataFimFormatada() {
        return dataFim == null ? "" : PlaceconUtil.formatarDataPadrao(dataFim);
    }

    public Date getDataFim() {
        return dataFim;
    }

    public void setDataFim(Date dataFim) {
        this.dataFim = dataFim;
    }

    public SituacaoFinanceira getSituacaoFinanceira() {
        return situacaoFinanceira;
    }

    public void setSituacaoFinanceira(SituacaoFinanceira situacaoFinanceira) {
        this.situacaoFinanceira = situacaoFinanceira;
    }

    public Financeira getFinanceira() {
        return financeira;
    }

    public void setFinanceira(Financeira financeira) {
        this.financeira = financeira;
    }

    public Date getDataConclusaoDetran() {
        return dataConclusaoDetran;
    }

    public void setDataConclusaoDetran(Date dataConclusaoDetran) {
        this.dataConclusaoDetran = dataConclusaoDetran;
    }

    public SimNao getAnexo() {
        return anexo;
    }

    public void setAnexo(SimNao anexo) {
        this.anexo = anexo;
    }

    public MensagemRetorno getMensagemRetorno() {
        return mensagemRetorno;
    }

    public void setMensagemRetorno(MensagemRetorno mensagemRetorno) {
        this.mensagemRetorno = mensagemRetorno;
    }
}
