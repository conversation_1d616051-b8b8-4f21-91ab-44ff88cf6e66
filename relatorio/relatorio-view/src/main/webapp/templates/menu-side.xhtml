<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
                xmlns:h="http://xmlns.jcp.org/jsf/html"
                xmlns:p="http://primefaces.org/ui"
                xmlns:jsf="http://xmlns.jcp.org/jsf"
                xmlns:sec="http://www.springframework.org/security/tags">

    <!-- ========== Left Sidebar Start ========== -->
    <div class="left side-menu">
        <div class="user-details">
            <div class="pull-left">
                <img src="#{request.contextPath}/templates/assets/images/users/avatar-1.png"
                     alt="Foto do usuário default" class="rounded-circle"
                     jsf:rendered="#{helperSessionBean.foto == null}"></img>
                <p:graphicImage value="#{helperSessionBean.logo}" stream="false" cache="false"
                                alt="Foto do usuário customizada" styleClass="rounded-circle"
                                rendered="#{helperSessionBean.logo != null}"/>
            </div>
            <div class="user-info">
                <a href="#">#{helperSessionBean.usuario.nome.split(' ')[0]}</a>
                <p class="text-muted m-0">#{helperSessionBean.usuario.perfil.descricao}</p>
                <p class="text-muted m-0">#{helperSessionBean.usuario.agente.nome}</p>
            </div>
        </div>

                <!--- Sidemenu -->
                <div id="sidebar-menu">
                    <!-- Left Menu Start -->
                    <ul class="metismenu" id="side-menu">
                        <li class="menu-title">Relatórios</li>
                        <li>
                            <a href="#{request.contextPath}">
                                <i class="ti-home"></i><span> Início </span>
                            </a>
                        </li>
                       	<sec:authorize ifAnyGranted="RELATORIO_SEM_ANEXO">
							<li>
								<a href="javascript:void(0)">
									<i class="mdi mdi-file-check"></i>
									<span> Contratos </span>
									<span class="menu-arrow"></span>
								</a>
								<ul class="nav-second-level">
									<li>
										<a href="#{request.contextPath}/comanexo/list-com-documento.xhtml">
											Contratos com Anexos
										</a>
									</li>
									<li>
										<a href="#{request.contextPath}/semanexo/list-sem-documento.xhtml">
											Contratos sem Anexos
										</a>
									</li>
								</ul>
							</li>
						</sec:authorize>

						<sec:authorize ifAnyGranted="RELATORIO_COBRANCA">
						<li>
							<a href="javascript:void(0)">
								<i class="mdi mdi-file-document-box"></i>
								<span>Faturamento</span>
								<span class="menu-arrow"></span>
							</a>
							<ul class="nav-second-level">

								<sec:authorize ifAnyGranted="RELATORIO_COBRANCA">
									<li>
										<a href="#{request.contextPath}/cobranca/list_receita_bruta.xhtml">
											Receita Bruta
										</a>
									</li>
								</sec:authorize>

								<sec:authorize ifAnyGranted="RELATORIO_RECEITA_LIQUIDA">
									<li>
										<a href="#{request.contextPath}/cobranca/list_receita_liquida.xhtml">
											Receita Líquida
										</a>
									</li>
								</sec:authorize>

								<sec:authorize ifAnyGranted="RELATORIO_RECEITA_GRUPO_FINANCEIRAS">
									<li>
										<a href="#{request.contextPath}/cobranca/list_group.xhtml">
											Grupo de financeiras
										</a>
									</li>
								</sec:authorize>


							</ul>
						</li>
						</sec:authorize>

						<sec:authorize ifAnyGranted="PROJECAO_MENSAL">
							<li>
								<a href="#{request.contextPath}/cobranca/imposto.xhtml">
									<i class="mdi mdi-file-document-box"></i><span>Impostos</span>
								</a>
							</li>
						</sec:authorize>
						<sec:authorize ifAnyGranted="RELATORIO_ADMINISTRACAO,RELATORIO_DETRAN,RELATORIO_FINANCEIRA,RELATORIO_SEM_ANEXO">
							<li>
								<a href="#{request.contextPath}/geral/list-group.xhtml">
									<i class=" mdi mdi-chart-pie"></i><span>Consolidado Mensal</span>
								</a>
							</li>
							<li>
								<a href="#{request.contextPath}/geral/list.xhtml">
									<i class=" mdi mdi-chart-histogram"></i><span>Detalhado Mensal</span>
								</a>
							</li>
							<li>
								<a href="#{request.contextPath}/geral/list-day.xhtml">
									<i class=" mdi mdi-chart-line"></i><span>Evolução Diária</span>
								</a>
							</li>
				         </sec:authorize>
				         <sec:authorize ifAnyGranted="RELATORIO_REGISTROS_EFETUADOS">
					         <li>
								<a href="#{request.contextPath}/financeira/registros-efetuados.xhtml">
									<i class=" mdi mdi-chart-histogram"></i><span>Registros Efetuados</span>
								</a>
							</li>
				         </sec:authorize>
				         <sec:authorize ifAnyGranted="RELATORIO_REMESSA">
					         <li>
								<a href="#{request.contextPath}/remessa/list.xhtml">
									<i class="fa fa-stack-overflow"></i><span>Remessas</span>
								</a>
							</li>
				         </sec:authorize>
				         <sec:authorize ifAnyGranted="RELATORIO_ADMINISTRACAO,RELATORIO_FINANCEIRA">
					         <li>
								<a href="#{request.contextPath}/financeira/list-day.xhtml">
									<i class=" mdi mdi-human-greeting"></i><span>Usuário</span>
								</a>
							</li>
				         </sec:authorize>
						<sec:authorize ifAnyGranted="RELATORIO_USUARIO_POR_FINANCEIRA">
							<li>
								<a href="#{request.contextPath}/usuariofinanceira/list.xhtml">
									<i class=" mdi mdi-view-list"></i><span>Usuario por Financeira</span>
								</a>
							</li>
						</sec:authorize>
				        <sec:authorize ifAnyGranted="RELATORIO_MARCAS">
	                        <li>
								<a href="#{request.contextPath}/marca/list.xhtml">
									<i class="mdi mdi-car"></i><span>Marcas</span>
								</a>
							</li>
						</sec:authorize>
				        <sec:authorize ifAnyGranted="ARQUIVOS_DETRAN_MG">
	                        <li>
								<a href="#{request.contextPath}/arquivosdetran/list.xhtml">
									<i class="mdi mdi-car"></i><span>Arquivos DETRAN MG</span>
								</a>
							</li>
						</sec:authorize>
						<sec:authorize ifAnyGranted="CONSULTAR_RELATORIO_PRODUCAO_FINANCEIRAS">
							<li>
								<a href="#{request.contextPath}/relatoriofinanceiro/relatorio-list.xhtml">
									<i class=" mdi mdi-file-document"></i><span>Relatório de Produção</span>
								</a>
							</li>
						</sec:authorize>
						<sec:authorize ifAnyGranted="CONSULTAR_RELATORIO_DOCUMENTO_ARRECADACAO">
							<li>
								<a href="#{request.contextPath}/relatoriodocumentoarrecadacao/relatorio-arrecadacao-list.xhtml">
									<i class=" mdi mdi-chart-pie"></i><span>Relatório de Boletos</span>
								</a>
							</li>
						</sec:authorize>
						<sec:authorize ifAnyGranted="CONSULTAR_FINANCEIRAS_REEMBOLSO">
							<li>
								<a href="#{request.contextPath}/relatorioreembolso/relatorio-reembolso-list.xhtml">
									<i class="mdi mdi-cash-multiple"></i><span>Relatório: Reembolso</span>
								</a>
							</li>
						</sec:authorize>
						<sec:authorize ifAnyGranted="RELATORIO_CONSULTA_FINANCEIRA">
							<li>
								<a href="#{request.contextPath}/relatoriofinanceira/relatorio-financeira-list.xhtml">
									<i class="mdi mdi-bank"></i><span>Relatório: Financeira</span>
								</a>
							</li>
						</sec:authorize>
                        <sec:authorize ifAnyGranted="VALIDAR_RELATORIO_INTERNO,VALIDAR_RELATORIO_EXTERNO">
                            <li>
                                <a href="javascript: void(0);">
                                    <i class="fa fa-money" aria-hidden="true"></i>
                                    <span>Validação</span>
                                    <span class="menu-arrow"></span>
                                </a>
                                <ul class="nav-second-level">
                                    <sec:authorize ifAnyGranted="VALIDAR_RELATORIO_INTERNO">
                                        <li><a href="#{request.contextPath}/relatorio/menu.xhtml">Relatório</a>
                                        </li>
                                    </sec:authorize>
                                    <sec:authorize ifAnyGranted="VALIDAR_RELATORIO_EXTERNO">
                                        <li>
                                            <a href="#{request.contextPath}/templaterelatorio/list.xhtml">Templates</a>
                                        </li>
                                        <li>
                                            <a href="#{request.contextPath}/templaterelatorio/form-add.xhtml">Novo Template</a>
                                        </li>
                                    </sec:authorize>
                                </ul>
                            </li>
                        </sec:authorize>
						<li>
                            <a href="#{request.contextPath}/logout/cas">
                                <i class="ti-power-off"></i><span> Sair </span>
                            </a>
                        </li>
                    </ul>

        </div>
        <!-- Sidebar -->
        <div class="clearfix"></div>

    </div>
    <!-- Left Sidebar End -->

</ui:composition>
