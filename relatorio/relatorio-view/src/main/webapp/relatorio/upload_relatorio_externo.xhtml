<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:h="http://xmlns.jcp.org/jsf/html"
                xmlns:f="http://xmlns.jcp.org/jsf/core"
                xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
                xmlns:p="http://primefaces.org/ui"
                xmlns:jsf="http://xmlns.jcp.org/jsf"
                xmlns:sec="http://www.springframework.org/security/tags"
                template="/templates/blank.xhtml">

    <ui:define name="content">
        <style>
            .gap-3 {
                gap: 1rem;
            }

            .custom-links h4 {
                margin: 0;
            }

            .custom-links a {
                text-decoration: none;
                color: inherit;
                transition: color 0.3s ease;
            }

            .custom-links a:hover {
                color: #007bff;
            }
        </style>

        <div class="content">
            <div class="container-fluid">
                <div class="row">
                    <div class="col-sm-12">
                        <div class="d-flex align-items-center gap-3 custom-links">
                            <h4>
                                Relatório externo
                            </h4>
                        </div>
                    </div>
                    <h:messages styleClass="col-lg-12" id="messages" warnClass="alert alert-warning alert-dismissable"
                                infoClass="alert alert-success alert-dismissable"
                                errorClass="alert alert-danger alert-dismissable"/>
                    <form jsf:id="form" jsf:prependId="false" class="form w-100" enctype="multipart/form-data">
                        <div class="row w-100">
                            <div class="col-lg-12">
                                <div class="row">
                                    <div class="col-lg-6">
                                        <div class="m-b-20">
                                            <h6 class="font-14 mt-4">Upload do Relatório Externo (csv|xls|xlsx)</h6>
                                            <p:fileUpload
                                                    listener="#{validacaoRelatorioExternoBean.handleFileUploadExterno}"
                                                    class="required"
                                                    mode="advanced" dragDropSupport="true"
                                                    update="btnConfirmar uploadedFilesExterno messages"
                                                    sizeLimit="10000000" allowTypes="/(\.|\/)(csv|xls|xlsx)$/"
                                                    auto="true"
                                                    cancelLabel="Cancelar" uploadLabel="Upload"
                                                    label="Selecionar Arquivo"
                                                    fileLimit="1" multiple="false"
                                                    fileLimitMessage="Selecione no máximo 1 arquivo."
                                                    invalidFileMessage="Envie um arquivo CSV"
                                                    invalidSizeMessage="Tamanho máximo 10M"
                                                    required="true"/>
                                            <input type="hidden" name="#{_csrf.parameterName}" value="#{_csrf.token}"/>
                                        </div>
                                        <div class="col-lg-12 p-0 mb-3" jsf:id="uploadedFilesExterno">
                                            <div class="table-responsive"
                                                 jsf:rendered="#{not empty validacaoRelatorioExternoBean.externo}">
                                                <p:dataTable value="#{validacaoRelatorioExternoBean.externo}" var="file"
                                                             emptyMessage="Nenhuma Linha Processada"
                                                             tableStyleClass="table table-hover m-0">
                                                    <p:column headerText="Relatório B3">
                                                        #{file.fileName}
                                                    </p:column>
                                                    <p:column headerText="Ações" style="width:60px; text-align:center;">
                                                        <p:button icon="pi pi-times"
                                                                  action="#{validacaoRelatorioExternoBean.removerExterno}"
                                                                  update="uploadedFilesExterno messages"
                                                                  style="padding:2px; margin:0; font-size:0.8em;"/>
                                                    </p:column>
                                                </p:dataTable>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-lg-6">
                                        <div class="m-b-20">
                                            <h6 class="font-14 mt-4">Upload do Relatório (PDF)</h6>
                                            <p:fileUpload
                                                    listener="#{validacaoRelatorioExternoBean.handleFileUploadRelatorio}"
                                                    class="required"
                                                    mode="advanced" dragDropSupport="true"
                                                    update="btnConfirmar uploadedFilesRelatorio messages"
                                                    sizeLimit="10000000" allowTypes="/(\.|\/)(pdf)$/"
                                                    auto="true"
                                                    cancelLabel="Cancelar" uploadLabel="Upload"
                                                    label="Selecionar Arquivo"
                                                    fileLimit="1" multiple="false"
                                                    fileLimitMessage="Selecione no máximo 1 arquivo."
                                                    invalidFileMessage="Envie um arquivo PDF"
                                                    invalidSizeMessage="Tamanho máximo 10M"
                                                    required="true"/>
                                            <input type="hidden" name="#{_csrf.parameterName}" value="#{_csrf.token}"/>
                                        </div>
                                        <div class="col-lg-12 p-0 mb-3" jsf:id="uploadedFilesRelatorio">
                                            <div class="table-responsive"
                                                 jsf:rendered="#{not empty validacaoRelatorioExternoBean.relatorio}">
                                                <p:dataTable value="#{validacaoRelatorioExternoBean.relatorio}"
                                                             var="file"
                                                             emptyMessage="Nenhuma Linha Processada"
                                                             tableStyleClass="table table-hover m-0">
                                                    <p:column headerText="Relatório B3">
                                                        #{file.fileName}
                                                    </p:column>
                                                    <p:column headerText="Ações" style="width:60px; text-align:center;">
                                                        <p:button icon="pi pi-times"
                                                                  action="#{validacaoRelatorioExternoBean.removerRelatorio}"
                                                                  update="uploadedFilesRelatorio messages"
                                                                  style="padding:2px; margin:0; font-size:0.8em;"/>
                                                    </p:column>
                                                </p:dataTable>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-lg-8">
                                <div class="form-group form-group form-group-default required">
                                    <label>Template</label>
                                    <select jsf:id="template"
                                            jsf:value="#{validacaoRelatorioExternoBean.selectedTemplateValidacao}"
                                            class="form-control full-width select2"
                                            jsf:label="Template"
                                            jsf:required="true" required="required"
                                            jsf:disabled="#{disabled eq 'disabled'}">
                                        <f:selectItems value="#{validacaoRelatorioExternoBean.templatesValidacao}"
                                                       var="i" itemValue="#{i}"
                                                       itemLabel="#{i.descricao}"/>
                                        <f:converter converterId="templateValidacaoConverter"/>
                                    </select>
                                </div>
                            </div>
                            <div class="col-lg-2">
                                <div class="form-group form-group-default required">
                                    <label>Data Início</label>
                                    <p:calendar id="dataInicio"
                                                styleClass="form-control" locale="pt_BR"
                                                navigator="true" yearRange="c-10:c+10" mask="true"
                                                label="Data Início"
                                                value="#{validacaoRelatorioExternoBean.dataInicio}"
                                                pattern="dd/MM/yyyy" required="true"/>
                                </div>
                            </div>
                            <div class="col-lg-2">
                                <div class="form-group form-group-default required">
                                    <label>Data Fim</label>
                                    <p:calendar id="dataFim"
                                                styleClass="form-control" locale="pt_BR"
                                                navigator="true" yearRange="c-10:c+10" mask="true"
                                                label="Data Fim"
                                                value="#{validacaoRelatorioExternoBean.dataFim}"
                                                pattern="dd/MM/yyyy" required="true"/>
                                </div>
                            </div>
                        </div>

                        <div class="row" jsf:id="btnConfirmar">
                            <div class="col-lg-12 text-center">
                                <hr class="buttons"/>
                                <p:commandButton value="Validar"
                                                 action="#{validacaoRelatorioExternoBean.confirmar}"
                                                 ajax="false"
                                                 update="messages btn-download"
                                                 style="color: white"
                                                 process="@form"/>
                                <p:commandLink id="btn-download"
                                               value="Baixar Relatório"
                                               style="margin-left: 1rem; margin-right: 1rem;"
                                               ajax="false"
                                               actionListener="#{validacaoRelatorioExternoBean.getFile()}"
                                               onclick="PrimeFaces.monitorDownload(loading, closeLoading);"
                                               rendered="#{not empty validacaoRelatorioExternoBean.relatorioPath}">
                                    <p:fileDownload value="#{validacaoRelatorioExternoBean.file}"/>
                                </p:commandLink>
                                <p:button style="color: white" value="Voltar" href="#{request.contextPath}/relatorio/menu.xhtml"/>
                                <input type="hidden" name="${_csrf.parameterName}" value="${_csrf.token}"/>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </ui:define>
</ui:composition>
