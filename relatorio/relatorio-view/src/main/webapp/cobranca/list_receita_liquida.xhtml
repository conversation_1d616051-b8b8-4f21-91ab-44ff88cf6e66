<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:h="http://xmlns.jcp.org/jsf/html"
                xmlns:f="http://xmlns.jcp.org/jsf/core"
                xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
                xmlns:p="http://primefaces.org/ui"
                xmlns:jsf="http://xmlns.jcp.org/jsf"
                template="/templates/blank.xhtml">

    <ui:define name="content">
        <!-- Start content -->
        <div class="content">
            <div class="container-fluid">

                <div class="row">
                    <div class="col-sm-12">
                        <h4 class="header-title">Relatório de Faturamento Líquido</h4>
                        <div class="alert alert-info alert-dismissable">
                            <span>O filtro de datas foram limitados em 90 dias.</span>
                        </div>
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="m-b-20">
                                    <h6 class="font-14 mt-4">Pesquisar</h6>
                                    <form jsf:id="form" jsf:prependId="false">
                                        <h:messages id="messages" warnClass="alert alert-warning alert-dismissable"
                                                    infoClass="alert alert-success alert-dismissable"
                                                    errorClass="alert alert-danger alert-dismissable"/>
                                        <div class="row">
                                            <div class="col-lg-2">
                                                <div class="form-group form-group-default form-group-default-select2">
                                                    <label>UF</label>
                                                    <select jsf:id="uf"
                                                            jsf:value="#{relatorioCobrancaBean.filter.estado}"
                                                            class="form-control full-width select2" size="1"
                                                            disabled="#{disabled}">
                                                        <f:selectItem itemLabel="Selecione"/>
                                                        <f:selectItems value="#{helperBean.ufs}" var="i"
                                                                       itemLabel="#{i}"/>
                                                    </select>
                                                </div>
                                            </div>
                                            <div class="col-lg-2">
                                                <div class="form-group form-group-default form-group-default-select2">
                                                    <label>Situação</label>
                                                    <select jsf:id="situacaoFinanceira"
                                                            jsf:value="#{relatorioCobrancaBean.filter.situacaoCobranca}"
                                                            class="form-control full-width select2" size="1"
                                                            data-init-plugin="select2" disabled="#{disabled}">
                                                        <f:selectItem itemLabel="Selecione"/>
                                                        <f:selectItems value="#{helperBean.situacoesCobranca}" var="i"
                                                                       itemLabel="#{i}"/>
                                                    </select>
                                                </div>
                                            </div>
                                            <div class="col-lg-4">
                                                <div class="form-group form-group-default form-group-default-select2 required">
                                                    <label>Data Início</label>
                                                    <p:calendar id="dataInicio" styleClass="form-control" locale="pt_BR"
                                                                navigator="true" yearRange="c-5:c+5"
                                                                value="#{relatorioCobrancaBean.filter.dataInicio}"
                                                                pattern="dd/MM/yyyy" required="true" mask="true"
                                                    />
                                                </div>
                                            </div>
                                            <div class="col-lg-4">
                                                <div class="form-group form-group-default form-group-default-select2 required">
                                                    <label>Data Fim</label>
                                                    <p:calendar id="dataFim" styleClass="form-control" locale="pt_BR"
                                                                navigator="true" yearRange="c-5:c+5"
                                                                value="#{relatorioCobrancaBean.filter.dataFim}"
                                                                pattern="dd/MM/yyyy" required="true" mask="true"
                                                    />
                                                </div>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-lg-6">
                                                <div class="form-group form-group-default form-group-default-select2">
                                                    <label>Financeiras</label>
                                                    <select jsf:id="financeira"
                                                            jsf:value="#{relatorioCobrancaBean.filter.financeiras}"
                                                            class="form-control full-width select2" size="1"
                                                            data-init-plugin="select2" multiple="multiple"
                                                            disabled="#{disabled}">
                                                        <f:selectItems value="#{helperSessionBean.financeiras}" var="i"
                                                                       itemLabel="#{i.nome}" itemValue="#{i}"/>
                                                        <f:converter converterId="financeiraConverter"></f:converter>
                                                    </select>
                                                </div>
                                            </div>
                                            <div class="col-lg-6">
                                                <div class="form-group form-group-default form-group-default-select2">
                                                    <label>Grupos de Financeiras</label>
                                                    <select jsf:id="gfinanceira"
                                                            jsf:value="#{relatorioCobrancaBean.filter.gruposFinanceiras}"
                                                            class="form-control full-width select2" size="1"
                                                            data-init-plugin="select2" multiple="multiple"
                                                            disabled="#{disabled}">
                                                        <f:selectItems
                                                                value="#{relatorioCobrancaBean.gruposFinanceiras}"
                                                                var="i" itemLabel="#{i.nome}" itemValue="#{i}"/>
                                                        <f:converter
                                                                converterId="grupoFinanceiraConverter"></f:converter>
                                                    </select>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="row text-center">
                                            <div class="col-lg-12">
                                                <hr class="buttons"/>
                                                <a href="#{request.contextPath}/cobranca/list_receita_liquida.xhtml"
                                                   class="btn btn-default">Limpar</a>
                                                <button type="submit" class="btn btn-primary btn-cons"
                                                        jsf:action="#{relatorioCobrancaBean.searchReceitaLiquida}">Pesquisar
                                                </button>
                                            </div>
                                        </div>
                                        <input type="hidden" name="${_csrf.parameterName}" value="${_csrf.token}"/>
                                    </form>
                                </div>
                            </div>
                        </div>

                        <div class="row" jsf:rendered="#{relatorioCobrancaBean.consultaRealizada}">
                            <div class="table-responsive">
                                <form jsf:id="formDataTable" jsf:prependId="false">
                                    <div style="min-height: 30px;">
                                        <div style="float: right; padding: 0;">
                                            Resultados por página
                                            <select jsf:id="registros" jsf:value="#{relatorioCobrancaBean.size}"
                                                    size="1">
                                                <f:selectItem itemLabel="10" itemValue="10"/>
                                                <f:selectItem itemLabel="25" itemValue="25"/>
                                                <f:selectItem itemLabel="50" itemValue="50"/>
                                                <f:selectItem itemLabel="100" itemValue="100"/>
                                                <f:ajax execute="@this" render="formDataTable"
                                                        listener="#{relatorioCobrancaBean.search}"
                                                        onevent="function(data){$.masks();}"/>
                                            </select>
                                        </div>
                                    </div>
                                    <p:dataTable id="dataTable" var="object"
                                                 value="#{relatorioCobrancaBean.listFaturamento}"
                                                 paginator="true" emptyMessage="Nenhum registro encontrado"
                                                 rows="#{relatorioCobrancaBean.size}" paginatorPosition="bottom"
                                                 currentPageReportTemplate="{currentPage} de {totalPages} - Total Registros: #{relatorioCobrancaBean.list.rowCount}"
                                                 paginatorTemplate="{FirstPageLink} {PreviousPageLink} {CurrentPageReport} {NextPageLink} {LastPageLink}"
                                                 lazy="true" draggableColumns="true" resizableColumns="true"
                                                 tableStyleClass="table table-hover m-0">

                                        <p:column headerText="Cobrança">
                                            <h:outputText value="#{object.dataGeracaoCobranca}">
                                                <f:convertDateTime pattern="dd/MM/yyyy HH:mm"/>
                                            </h:outputText>
                                        </p:column>
                                        <p:column headerText="CNPJ">
                                            <h:outputText value="#{object.documento}"/>
                                        </p:column>
                                        <p:column headerText="Financeira">
                                            <h:outputText value="#{object.nomeFinanceira}"/>
                                        </p:column>
                                        <p:column headerText="UF">
                                            <h:outputText value="#{object.ufCobranca}"/>
                                        </p:column>
                                        <p:column headerText="Situação">
                                            <h:outputText value="#{object.situacaoCobranca}"/>
                                        </p:column>
                                        <p:column headerText="Número NF">
                                            <h:outputText value="#{object.numeroNotaFiscal}"/>
                                        </p:column>
                                        <p:column headerText="Registros">
                                            <h:outputText value="#{object.numeroRegistros}"/>
                                        </p:column>
                                        <p:column headerText="Valor do Serviço">
                                            <h:outputText value="#{object.valorCredenciada}">
                                                <f:convertNumber pattern="#,##0.00" locale="pt_BR"/>
                                            </h:outputText>
                                        </p:column>
                                        <p:column headerText="Valor do Desconto">
                                            <h:outputText value="#{object.valorDesconto}">
                                                <f:convertNumber pattern="#,##0.00" locale="pt_BR"/>
                                            </h:outputText>
                                        </p:column>
                                        <p:column headerText="Receita Bruta">
                                            <h:outputText value="#{object.receitaBruta}">
                                                <f:convertNumber pattern="#,##0.00" locale="pt_BR"/>
                                            </h:outputText>
                                        </p:column>
                                        <p:column headerText="Imposto Sobre a Venda">
                                            <h:outputText value="#{object.impostoSobreVenda}">
                                                <f:convertNumber pattern="#,##0.00" locale="pt_BR"/>
                                            </h:outputText>
                                        </p:column>
                                        <p:column headerText="Receita Líquida">
                                            <h:outputText value="#{object.receitaLiquida}">
                                                <f:convertNumber pattern="#,##0.00" locale="pt_BR"/>
                                            </h:outputText>
                                        </p:column>
                                        <p:columnGroup type="footer">
                                            <p:row>
                                                <p:column
                                                        footerText="Valor total bruto total: " style="text-align:right"
                                                        colspan="9"/>
                                                <p:column>
                                                    <f:facet name="footer" >
                                                        <h:outputText
                                                                value="R$ #{relatorioCobrancaBean.totalValorCobrancaBruto}">
                                                            <f:convertNumber pattern="#,##0.00" locale="pt_BR"/>
                                                        </h:outputText>
                                                    </f:facet>
                                                </p:column>
                                            </p:row>
                                        </p:columnGroup>

                                    </p:dataTable>

                                    <div class="row text-center">
                                        <div class="col-lg-12">
                                            <hr class="buttons"/>
                                            <button type="submit" class="btn btn-primary btn-cons"
                                                    jsf:id="btnExportarCSV">Exportar CSV
                                                <p:dataExporter encoding="iso-8859-1" type="csv" target="dataTable"
                                                                fileName="relatorio-cobranca"/>
                                            </button>

                                            <a href="#" jsf:action="#{relatorioCobrancaBean.excelFaturamentoLiquido}"
                                               class="btn btn-primary btn-cons">Exportar Excel</a>
                                        </div>
                                    </div>
                                    <input type="hidden" name="${_csrf.parameterName}" value="${_csrf.token}"/>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </ui:define>
</ui:composition>
