<ui:composition xmlns="http://www.w3.org/1999/xhtml"
				xmlns:h="http://xmlns.jcp.org/jsf/html"
				xmlns:f="http://xmlns.jcp.org/jsf/core"
				xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
				xmlns:p="http://primefaces.org/ui"
				xmlns:jsf="http://xmlns.jcp.org/jsf"
				xmlns:sec="http://www.springframework.org/security/tags"
				template="/templates/print.xhtml">

	<ui:define name="header">
		<style>
			.page.landscape {
				/* só funciona em navegadores e impressoras que respeitam @page */
				@page {
					size: A4 landscape;
					margin: 1cm;
				}
			}

			.page.portrait {
				@page {
					size: A4 portrait;
					margin: 2cm;
				}
			}

			canvas {
				width: 100% !important;
				height: auto !important;
				display: block;
			}

			body {
				-webkit-print-color-adjust: exact;
			}
		</style>

		<script>
			function customExtender() {
				this.cfg.grid = {
					background: '#FFF',
					gridLineColor: '#f5f5f5'
				};
			}
		</script>
	</ui:define>

	<ui:define name="content">
		<f:metadata>
			<f:viewAction action="#{relatorioGeralGrupoBean.loadPrint()}"/>
		</f:metadata>
		<h4 class="header-title">
			<img height="30" src="#{request.contextPath}/templates/assets/images/logo.png"/>
		</h4>
		<div class="row">

			<div class="col-lg-12">
				<div class="m-b-20">
					<h5>Relatório Cobrança por Grupo de Financeiras Mensal</h5>
					<form jsf:id="form" jsf:prependId="false">
						<div class="row">
							<div class="col-lg-6">
								<div class="form-group form-group-default required">
									<label>Mês</label>
									<input jsf:id="numeroRegistroEletronico" type="text"
										   jsf:value="#{relatorioGeralGrupoBean.filter.mes}"
										   class="form-control integer"/>
								</div>
							</div>
							<div class="col-lg-6">
								<div class="form-group form-group-default required">
									<label>Ano</label>
									<input jsf:id="ano" type="text" jsf:value="#{relatorioGeralGrupoBean.filter.ano}"
										   class="form-control integer"/>
								</div>
							</div>
						</div>
					</form>
				</div>
			</div>
		</div>
		<div class="page landscape">
			<p:barChart model="#{relatorioGeralGrupoBean.barModel}"
						responsive="true"
						animate="true"
						extender="customExtender"
						rendered="#{relatorioGeralGrupoBean.barModel != null}"/>
		</div>
		<div class="page portrait">
			<p:dataTable value="#{relatorioGeralGrupoBean.list}" var="object" emptyMessage="Nenhum registro">
				<p:column headerText="Grupo">
					#{object.tipo.descricao}
				</p:column>
				<p:column headerText="Quantidade">
					#{object.quantidade}
				</p:column>
				<p:column headerText="Veículos">
					#{object.quantidadeVeiculos}
				</p:column>
			</p:dataTable>
		</div>
	</ui:define>
</ui:composition>
