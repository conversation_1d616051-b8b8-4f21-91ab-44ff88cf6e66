<ui:composition xmlns="http://www.w3.org/1999/xhtml"
				xmlns:h="http://xmlns.jcp.org/jsf/html"
				xmlns:f="http://xmlns.jcp.org/jsf/core"
				xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
				xmlns:p="http://primefaces.org/ui"
				xmlns:jsf="http://xmlns.jcp.org/jsf"
				template="/templates/blank.xhtml">

    <ui:define name="header">
        <script>
            function customExtender() {
                this.cfg.grid = {
                    background: '#FFF',
                    gridLineColor: '#f5f5f5'
                };
            }
        </script>
    </ui:define>

    <ui:define name="content">
        <!-- Start content -->
        <div class="content">
            <div class="container-fluid">

                <div class="row">
                    <div class="col-sm-12">
                        <h4 class="header-title">Relatório Consolidado Mensal</h4>

                        <div class="row">
                            <div class="col-lg-12">
                                <div class="m-b-20">
                                    <h6 class="font-14 mt-4">Pesquisar</h6>
                                    <h:messages id="messages" warnClass="alert alert-warning alert-dismissable"
                                                infoClass="alert alert-success alert-dismissable"
                                                errorClass="alert alert-danger alert-dismissable"/>

                                    <form jsf:id="form" jsf:prependId="false">
                                        <div class="row">
                                            <div class="col-lg-4">
                                                <div class="form-group form-group-default required">
                                                    <label>Mês</label>
                                                    <input jsf:id="numeroRegistroEletronico" type="text" maxlength="4"
                                                           required="required"
                                                           jsf:value="#{relatorioGeralGrupoBean.filter.mes}"
                                                           class="form-control integer"/>
                                                </div>
                                            </div>
                                            <div class="col-lg-4">
                                                <div class="form-group form-group-default required">
                                                    <label>Ano</label>
                                                    <input jsf:id="ano" type="text" maxlength="4" required="required"
                                                           jsf:value="#{relatorioGeralGrupoBean.filter.ano}"
                                                           class="form-control integer"/>
                                                </div>
                                            </div>
                                            <div class="col-lg-4">
                                                <div class="form-group form-group-default form-group-default-select2 required">
                                                    <label>Agrupar por</label>
                                                    <select jsf:id="grupo"
                                                            jsf:value="#{relatorioGeralGrupoBean.filter.grupo}"
                                                            class="form-control full-width" size="1"
                                                            data-init-plugin="select2" required="required">
                                                        <f:selectItem itemLabel="Selecione"/>
                                                        <f:selectItem itemLabel="Situação" itemValue="S"/>
                                                        <f:selectItem itemLabel="Restrição" itemValue="R"/>
                                                        <f:selectItem itemLabel="UF" itemValue="U"/>
                                                        <f:selectItem itemLabel="Tipo de Contrato" itemValue="T"/>
                                                    </select>
                                                </div>
                                            </div>
                                            <div class="col-lg-12">
                                                <div class="form-group form-group-default form-group-default-select2">
                                                    <label>Financeiras</label>
                                                    <select jsf:id="financeira"
                                                            multiple="multiple"
                                                            jsf:value="#{relatorioGeralGrupoBean.filter.financeiras}"
                                                            class="form-control full-width select2" size="1">
                                                        <f:selectItems value="#{helperSessionBean.financeiras}" var="f"
                                                                       itemValue="#{f}" itemLabel="#{f.nome}"/>
                                                        <f:converter converterId="financeiraConverter"/>
                                                    </select>
                                                </div>
                                            </div>
                                            <div class="col-lg-12">
                                                <div class="form-group form-group-default form-group-default-select2">
                                                    <label>Grupos de Financeiras</label>
                                                    <select jsf:id="grupoFinanceira"
                                                            multiple="multiple"
                                                            jsf:value="#{relatorioGeralGrupoBean.filter.gruposFinanceiras}"
                                                            class="form-control full-width select2" size="1">
                                                        <f:selectItems value="#{relatorioGeralGrupoBean.gruposFinanceiras}" var="f"
                                                                       itemValue="#{f}" itemLabel="#{f.nome}"/>
                                                        <f:converter converterId="grupoFinanceiraConverter"/>
                                                    </select>
                                                </div>
                                            </div>

                                        </div>
                                        <div class="row text-center">
                                            <div class="col-lg-12">
                                                <hr class="buttons"/>
                                                <a href="#{request.contextPath}/geral/list-group.xhtml"
                                                   class="btn btn-default">Limpar</a>
                                                <button type="submit" class="btn btn-primary btn-cons"
                                                        jsf:action="#{relatorioGeralGrupoBean.search}">Pesquisar
                                                </button>
                                                <a href="#" jsf:action="#{relatorioGeralGrupoBean.print}"
                                                   jsf:rendered="#{not empty relatorioGeralGrupoBean.list}"
                                                   target="_blank" class="btn btn-primary btn-cons">Imprimir</a>
                                            </div>
                                        </div>
                                        <input type="hidden" name="${_csrf.parameterName}" value="${_csrf.token}"/>
                                    </form>
                                </div>
                            </div>
                        </div>

                        <div jsf:rendered="#{relatorioGeralGrupoBean.list != null and relatorioGeralGrupoBean.list.size() > 0}">
                            <div class="row">
                                <div class="col-lg-12">
                                    <p:barChart model="#{relatorioGeralGrupoBean.barModel}"
                                                rendered="#{relatorioGeralGrupoBean.barModel != null}"
                                                responsive="true" animate="true"/>
                                </div>
                            </div>
                            <div class="row">
                                <div class="table-responsive">
                                    <p:dataTable id="dataTable" var="object" value="#{relatorioGeralGrupoBean.list}"
                                                 emptyMessage="Nenhum registro encontrado"
                                                 tableStyleClass="table table-hover m-0">
                                        <p:column headerText="Grupo">
                                            #{object.financeira} - #{object.tipo.descricao}
                                        </p:column>
                                        <p:column headerText="Contratos">
                                            #{object.quantidade}
                                        </p:column>
                                        <p:column headerText="Veículos">
                                            #{object.quantidadeVeiculos}
                                        </p:column>

                                        <f:facet name="footer">
                                            <p:column>
                                                Quantidade de contratos: #{relatorioGeralGrupoBean.totalContratos}
                                            </p:column>
                                            <p:column headerText="Veículos">
                                                Quantidade de Veículos: #{relatorioGeralGrupoBean.totalVeiculos}
                                            </p:column>
                                        </f:facet>
                                    </p:dataTable>
                                </div>
                            </div>

                        </div>
                    </div>
                </div>
            </div>
        </div>
    </ui:define>
</ui:composition>
