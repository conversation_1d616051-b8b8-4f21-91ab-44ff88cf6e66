package com.registrocontrato;

import com.registrocontrato.commons.seguranca.CustomUserDetailsService;
import com.registrocontrato.infra.application.InvalidSession;
import org.jasig.cas.client.session.SingleSignOutFilter;
import org.jasig.cas.client.validation.Cas20ServiceTicketValidator;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.cas.ServiceProperties;
import org.springframework.security.cas.authentication.CasAssertionAuthenticationToken;
import org.springframework.security.cas.authentication.CasAuthenticationProvider;
import org.springframework.security.cas.web.CasAuthenticationEntryPoint;
import org.springframework.security.cas.web.CasAuthenticationFilter;
import org.springframework.security.config.annotation.authentication.builders.AuthenticationManagerBuilder;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.builders.WebSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.annotation.web.configuration.WebSecurityConfigurerAdapter;
import org.springframework.security.core.userdetails.AuthenticationUserDetailsService;
import org.springframework.security.web.authentication.logout.LogoutFilter;
import org.springframework.security.web.authentication.logout.SecurityContextLogoutHandler;

@Configuration
@EnableWebSecurity
public class WebSecurityApplication extends WebSecurityConfigurerAdapter {

    @Value("${cas.server}")
    private String CAS;

    @Value("${cas.local.login}")
    private String LOGIN_CAS;

    private static final String ERROR_EXPIRED_XHTML = "/error/unauthorized.xhtml";

    @Bean
    public ServiceProperties serviceProperties() {
        ServiceProperties serviceProperties = new ServiceProperties();
        serviceProperties.setService(LOGIN_CAS);
        serviceProperties.setSendRenew(false);
        return serviceProperties;
    }

    @Bean
    public Cas20ServiceTicketValidator cas20ServiceTicketValidator() {
        Cas20ServiceTicketValidator cas20ServiceTicketValidator = new Cas20ServiceTicketValidator(CAS);
        return cas20ServiceTicketValidator;
    }

    @Bean
    public CasAuthenticationProvider casAuthenticationProvider() {
        CasAuthenticationProvider casAuthenticationProvider = new CasAuthenticationProvider();
        casAuthenticationProvider.setServiceProperties(serviceProperties());
        casAuthenticationProvider.setTicketValidator(cas20ServiceTicketValidator());
        casAuthenticationProvider.setAuthenticationUserDetailsService(customUserdetailService());
        casAuthenticationProvider.setKey("an_id_for_this_auth_provider_only");
        return casAuthenticationProvider;
    }

    @Bean
    public AuthenticationUserDetailsService<CasAssertionAuthenticationToken> customUserdetailService() {
        CustomUserDetailsService customUserdetailService = new CustomUserDetailsService();
        customUserdetailService.setEnableGroups(true);
        return customUserdetailService;
    }

    @Bean
    public CasAuthenticationFilter casAuthenticationFilter() throws Exception {
        CasAuthenticationFilter casAuthenticationFilter = new CasAuthenticationFilter();
        casAuthenticationFilter.setAuthenticationManager(authenticationManager());
        return casAuthenticationFilter;
    }

    @Bean
    public CasAuthenticationEntryPoint casAuthenticationEntryPoint() {
        CasAuthenticationEntryPoint casAuthenticationEntryPoint = new CasAuthenticationEntryPoint();
        casAuthenticationEntryPoint.setLoginUrl(CAS + "/login");
        casAuthenticationEntryPoint.setServiceProperties(serviceProperties());
        return casAuthenticationEntryPoint;
    }

    @Bean
    public LogoutFilter requestCasGlobalLogoutFilter() {
        LogoutFilter logoutFilter = new LogoutFilter(CAS + "/logout", new SecurityContextLogoutHandler());
        logoutFilter.setFilterProcessesUrl("/logout/cas");
        return logoutFilter;
    }

    @Bean
    public SingleSignOutFilter singleSignOutFilter() {
        SingleSignOutFilter singleSignOutFilter = new SingleSignOutFilter();
        singleSignOutFilter.setIgnoreInitConfiguration(true);
        singleSignOutFilter.setCasServerUrlPrefix(CAS + "/logout");
        return singleSignOutFilter;
    }

    @Override
    protected void configure(HttpSecurity http) throws Exception {
        http.addFilter(casAuthenticationFilter());
        http.exceptionHandling().authenticationEntryPoint(casAuthenticationEntryPoint());
        http.addFilterBefore(singleSignOutFilter(), CasAuthenticationFilter.class);
        http.addFilterBefore(requestCasGlobalLogoutFilter(), LogoutFilter.class);
        http.headers().frameOptions().sameOrigin();

        http.authorizeRequests().antMatchers("/semanexo/**").hasAnyRole("RELATORIO_SEM_ANEXO");
        http.authorizeRequests().antMatchers("/cobranca/**").hasAnyRole("RELATORIO_COBRANCA");
        http.authorizeRequests().antMatchers("/cobranca/list_receita_liquida.xhtml").hasAnyRole("RELATORIO_RECEITA_LIQUIDA");
        http.authorizeRequests().antMatchers("/cobranca/list_group.xhtml").hasAnyRole("RELATORIO_RECEITA_GRUPO_FINANCEIRAS");
        http.authorizeRequests().antMatchers("/cobranca/projecao.xhtml").hasAnyRole("PROJECAO_MENSAL");
        http.authorizeRequests().antMatchers("/marca/**").hasAnyRole("RELATORIO_MARCAS");
        http.authorizeRequests().antMatchers("/geral/**").hasAnyRole("RELATORIO_ADMINISTRACAO", "RELATORIO_FINANCEIRA", "RELATORIO_DETRAN");
        http.authorizeRequests().antMatchers("/financeira/**").hasAnyRole("RELATORIO_FINANCEIRA", "RELATORIO_ADMINISTRACAO", "RELATORIO_REGISTROS_EFETUADOS");
        http.authorizeRequests().antMatchers("/financeira/registros-efetuados.xhtml").hasAnyRole("RELATORIO_REGISTROS_EFETUADOS");
        http.authorizeRequests().antMatchers("/usuariofinanceira/list.xhtml").hasAnyRole("RELATORIO_USUARIO_POR_FINANCEIRA");
        http.authorizeRequests().antMatchers("/arquivosdetran/**").hasRole("ARQUIVOS_DETRAN_MG");

        http.authorizeRequests().anyRequest().authenticated();
        http.exceptionHandling().accessDeniedPage("/error/forbidden.xhtml");

        //estrategia para sessao expirada
        http.sessionManagement().invalidSessionUrl(ERROR_EXPIRED_XHTML);
        http.sessionManagement().invalidSessionStrategy(new InvalidSession(ERROR_EXPIRED_XHTML));
    }

    @Override
    protected void configure(AuthenticationManagerBuilder auth) throws Exception {
        auth.authenticationProvider(casAuthenticationProvider());
    }

    @Override
    public void configure(WebSecurity web) throws Exception {
        web.ignoring()
                .antMatchers("/javax.faces.resource/**")
                .antMatchers("/public/**")
                .antMatchers("/assets/**")
                .antMatchers("/error/**")
                .antMatchers("/templates/**")
                .antMatchers("/logout");
    }

}
