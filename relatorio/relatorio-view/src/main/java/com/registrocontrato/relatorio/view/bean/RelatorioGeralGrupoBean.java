package com.registrocontrato.relatorio.view.bean;

import java.io.IOException;
import java.lang.reflect.InvocationTargetException;
import java.util.*;

import com.registrocontrato.infra.entity.GrupoFinanceira;
import com.registrocontrato.seguranca.service.GrupoFinanceiraService;
import org.primefaces.model.charts.ChartData;
import org.primefaces.model.charts.bar.BarChartDataSet;
import org.primefaces.model.charts.bar.BarChartModel;
import org.primefaces.model.charts.bar.BarChartOptions;
import org.primefaces.model.charts.pie.PieChartDataSet;
import org.primefaces.model.charts.pie.PieChartModel;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;

import com.registrocontrato.infra.bean.BaseBean;
import com.registrocontrato.infra.jsf.ViewScope;
import com.registrocontrato.infra.util.PlaceconUtil;
import com.registrocontrato.relatorio.service.RelatorioService;
import com.registrocontrato.relatorio.service.dto.RelatorioDTO;
import com.registrocontrato.relatorio.service.dto.ResultadoGrupoDTO;

import javax.annotation.PostConstruct;

@Controller
@ViewScope
public class RelatorioGeralGrupoBean extends BaseBean {

    private static final long serialVersionUID = 1L;

    private RelatorioDTO filter = new RelatorioDTO();

    private List<ResultadoGrupoDTO> list;

    private BarChartModel barModel;

    private Long totalContratos;

    private Long totalVeiculos;

    private List<GrupoFinanceira> gruposFinanceiras;

    @Autowired
    private RelatorioService service;

    @Autowired
    private GrupoFinanceiraService grupoFinanceiraService;

    @PostConstruct
    @SuppressWarnings({ "unchecked", "rawtypes" })
    public void postInitialization() {
        setGruposFinanceiras(grupoFinanceiraService.findGruposFinanceiras());
    }

    public void search() {
        filter.setUsuario(getUsername());
        list = service.findByMesAnoGrupo(filter);
        contabilizaContratos();
        buildChart();
    }

    public String print() {
        getFlash().put("RelatorioGeralGrupoBeanFilter", getFilter());
        return "/geral/print-group.xhtml?faces-redirect=true&wait=S";
    }

    public void loadPrint() throws IOException {
        filter = (RelatorioDTO) getFlash().get("RelatorioGeralGrupoBeanFilter");
        if (filter == null || filter.getAno() == null || filter.getMes() == null) {
            getExternalContext().redirect(getExternalContext().getRequestContextPath() + "/error/403.xhtml");
            getCurrentInstance().responseComplete();
        }
        filter.setUsuario(getUsername());
        list = service.findByMesAnoGrupo(filter);
        contabilizaContratos();
        buildChart();
    }

    private void buildChart() {
        barModel = new BarChartModel();
        ChartData data = new ChartData();
        if (list != null && !list.isEmpty()) {
            Map<String, String> tipoToColor = new HashMap<>();
            Map<String, String> tipoToDescricao = new HashMap<>();
            Map<String, Map<String, Long>> tipoFinanceiraQtd = new HashMap<>();

            Set<String> financeirasSet = new LinkedHashSet<>();

            list.forEach(r -> {
                String tipoKey = r.getTipo().toString();
                String financeira = r.getFinanceira() != null ? r.getFinanceira() : "Todas Financeiras";
                Long qtd = r.getQuantidade();
                financeirasSet.add(financeira);

                tipoToColor.computeIfAbsent(tipoKey, k -> PlaceconUtil.getColorGraph());

                try {
                    String descricao = (String) r.getTipo().getClass().getMethod("getDescricao").invoke(r.getTipo());
                    tipoToDescricao.put(tipoKey, descricao);
                } catch (Exception e) {
                    tipoToDescricao.put(tipoKey, "Undefined");
                    logger.error(e);
                }

                tipoFinanceiraQtd.computeIfAbsent(tipoKey, k -> new HashMap<>());
                tipoFinanceiraQtd.get(tipoKey).put(financeira, qtd);
            });

            tipoFinanceiraQtd.forEach((tipoKey, financeiraQtdMap) -> {
                BarChartDataSet dataSet = new BarChartDataSet();
                List<Number> values = new ArrayList<>();

                for (String financeira : financeirasSet) {
                    values.add(financeiraQtdMap.getOrDefault(financeira, 0L));
                }

                dataSet.setData(values);
                dataSet.setLabel(tipoToDescricao.get(tipoKey));
                dataSet.setBackgroundColor(tipoToColor.get(tipoKey));
                data.addChartDataSet(dataSet);
            });


            data.setLabels(new ArrayList<>(financeirasSet));
            barModel.setData(data);
        } else {
            list = new ArrayList<>();
        }
    }


    private void contabilizaContratos() {
        totalContratos = totalVeiculos = 0L;
        list.forEach(resultadoDTO -> {
            totalContratos += resultadoDTO.getQuantidade();
            totalVeiculos += resultadoDTO.getQuantidadeVeiculos();
        });
    }

    public List<GrupoFinanceira> getGruposFinanceiras() {
        return gruposFinanceiras;
    }

    public void setGruposFinanceiras(List<GrupoFinanceira> gruposFinanceiras) {
        this.gruposFinanceiras = gruposFinanceiras;
    }

    public Long getTotalContratos() {

        return totalContratos;
    }

    public Long getTotalVeiculos() {
        return totalVeiculos;
    }


    public List<ResultadoGrupoDTO> getList() {
        return list;
    }

    public void setList(List<ResultadoGrupoDTO> list) {
        this.list = list;
    }

    public RelatorioDTO getFilter() {
        return filter;
    }

    public void setFilter(RelatorioDTO filter) {
        this.filter = filter;
    }

    public BarChartModel getBarModel() {
        return barModel;
    }
}