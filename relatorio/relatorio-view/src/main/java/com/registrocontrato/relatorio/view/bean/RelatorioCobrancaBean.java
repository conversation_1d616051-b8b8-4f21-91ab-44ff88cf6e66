package com.registrocontrato.relatorio.view.bean;

import com.registrocontrato.infra.bean.BaseCrud;
import com.registrocontrato.infra.entity.GrupoFinanceira;
import com.registrocontrato.infra.jsf.ViewScope;
import com.registrocontrato.infra.relatorio.RelatorioData;
import com.registrocontrato.infra.relatorio.RelatorioDefault;
import com.registrocontrato.infra.service.BaseService;
import com.registrocontrato.registro.entity.Cobranca;
import com.registrocontrato.relatorio.service.RelatorioCobrancaService;
import com.registrocontrato.relatorio.service.dto.FaturamentoDTO;
import com.registrocontrato.relatorio.service.dto.RelatorioCobrancaDTO;
import com.registrocontrato.seguranca.service.GrupoFinanceiraService;
import org.apache.commons.lang.time.DateFormatUtils;
import org.primefaces.model.FilterMeta;
import org.primefaces.model.LazyDataModel;
import org.primefaces.model.SortMeta;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Controller;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import static com.registrocontrato.infra.relatorio.RelatorioImpl.nomeRelatorio;
import static com.registrocontrato.infra.util.PlaceconUtil.formataValorMonetario;

@ViewScope
@Controller
public class RelatorioCobrancaBean extends BaseCrud<Cobranca, RelatorioCobrancaDTO> {

    private static final String FLASH_FILTER = "RelatorioCobrancaFilter";

    private static final long serialVersionUID = 1L;

    private Boolean consultaRealizada = false;

    private LazyDataModel<FaturamentoDTO> listFaturamento;

    private final RelatorioCobrancaService service;

    private final RelatorioDefault relatorioDefault;

    private List<GrupoFinanceira> gruposFinanceiras;

    private final GrupoFinanceiraService grupoFinanceiraService;

    public RelatorioCobrancaBean(RelatorioCobrancaService service, RelatorioDefault relatorioDefault, GrupoFinanceiraService grupoFinanceiraService) {
        this.service = service;
        this.relatorioDefault = relatorioDefault;
        this.grupoFinanceiraService = grupoFinanceiraService;
    }

    @Override
    public BaseService<Cobranca, RelatorioCobrancaDTO> getService() {
        return service;
    }

    @Override
    public void postInitialization() {
        super.postInitialization();
        RelatorioCobrancaDTO filter = (RelatorioCobrancaDTO) getFilter();
        filter.setUsuario(getUsername());
        setGruposFinanceiras(grupoFinanceiraService.findGruposFinanceiras());
    }

    @Override
    public void search() {
        if (getFilter().getDataInicio() != null && getFilter().getDataFim() != null) {
            if (getFilter().getDataFim().getTime() - getFilter().getDataInicio().getTime() > 60 * 24 * 60 * 60 * 1000L) {
                addMessageError("Erro: a data não pode ter um intervalo com mais de 02 meses.");
                return;
            } else if (getFilter().getDataInicio().after(getFilter().getDataFim())) {
                addMessageError("Erro: a data de início não pode ser depois da data fim.");
                return;
            }
            consultaRealizada = true;
        }
        super.search();
    }

    public void searchReceitaLiquida() {
        if (getFilter().getDataInicio() != null && getFilter().getDataFim() != null) {

            if (getFilter().getDataFim().getTime() - getFilter().getDataInicio().getTime() > 90 * 24 * 60 * 60 * 1000L) {
                addMessageError("Erro: a data não pode ter um intervalo com mais de 90 dias.");
            } else if (getFilter().getDataInicio().after(getFilter().getDataFim())) {
                addMessageError("Erro: a data de início não pode ser depois da data fim.");
            }

            setListFaturamento(new LazyDataModel<FaturamentoDTO>() {

                private static final long serialVersionUID = 1L;

                @Override
                public int count(Map<String, FilterMeta> filterBy) {
                    return 0;
                }

                @Override
                public List<FaturamentoDTO> load(int first, int pageSize, Map<String, SortMeta> sortBy, Map<String, FilterMeta> filterBy) {
                    Page<FaturamentoDTO> page = service.buscarFaturamentoLiquido(first, pageSize, getFilter());
                    setRowCount((int) page.getTotalElements());
                    setPageSize(getSize());
                    return page.getContent();
                }
            });

            consultaRealizada = true;

        }
    }

    public String print() {
        getFlash().put(FLASH_FILTER, getFilter());
        return "/cobranca/print-cobranca.xhtml?faces-redirect=true";
    }

    public void loadPrint() throws IOException {
        setFilter((RelatorioCobrancaDTO) getFlash().get(FLASH_FILTER));
        if (getFilter() == null) {
            getExternalContext().redirect(getExternalContext().getRequestContextPath() + "/error/403.xhtml");
            getCurrentInstance().responseComplete();
        }
        getFilter().setUsuario(getUsername());
    }

    public List<Cobranca> getListPrint() {
        Page<Cobranca> page = service.findAll(0, 200000, getFilter());
        return page.getContent();
    }

    public Boolean getConsultaRealizada() {
        return consultaRealizada;
    }

    public Long getTotalRegistros() {
        Long total = 0L;
        for (Cobranca c : getList().getWrappedData()) {
            total += c.getQuantidadeRegistros();
        }
        return total;
    }

    public BigDecimal getTotalValorCobranca() {
        BigDecimal total = BigDecimal.ZERO;
        for (Cobranca c : getList().getWrappedData()) {
            total = total.add(c.getValorCredenciada() != null ? c.getValorCredenciada() : BigDecimal.ZERO);
            total = total.add(c.getValorCobrancaSng() != null ? c.getValorCobrancaSng() : BigDecimal.ZERO);
        }
        return total;
    }

    public BigDecimal getTotalValorDetran() {
        BigDecimal total = BigDecimal.ZERO;
        for (Cobranca c : getList().getWrappedData()) {
            total = total.add(c.getValorDetran() != null ? c.getValorDetran() : BigDecimal.ZERO);
        }
        return total;
    }

    public BigDecimal getTotalValorCobrancaBruto() {
        BigDecimal total = BigDecimal.ZERO;
        for (FaturamentoDTO c : getListFaturamento().getWrappedData()) {
            total = total.add(c.getReceitaBruta());
        }
        return total;
    }

    public List<Cobranca> getListForExcel() {
        Page<Cobranca> page = service.findAll(0, 10000, getFilter());
        return page.getContent();
    }

    public void excelFaturamentoLiquido() {
        Map<String, Integer> headerNames = new LinkedHashMap<>();
        headerNames.put("Cobrança", 24);
        headerNames.put("CNPJ Cliente", 15);
        headerNames.put("Financeira", 45);
        headerNames.put("UF", 4);
        headerNames.put("Situação", 10);
        headerNames.put("Número da NF", 16);
        headerNames.put("Registros", 10);
        headerNames.put("Valor do Serviço", 18);
        headerNames.put("Valor do Desconto", 20);
        headerNames.put("Receita Bruta", 20);
        headerNames.put("Imposto Sobre a Venda", 26);
        headerNames.put("Receita Líquida", 20);

        List<String[]> relatorio = new ArrayList<>();
        getListFaturamento().forEach(faturamento -> {
            String[] row = new String[]{
                    DateFormatUtils.format(faturamento.getDataGeracaoCobranca(), "dd/MM/yyyy HH:mm"),
                    faturamento.getDocumento(),
                    faturamento.getNomeFinanceira(),
                    faturamento.getUfCobranca().name(),
                    faturamento.getSituacaoCobranca().getDescricao(),
                    faturamento.getNumeroNotaFiscal(),
                    formataValorMonetario(faturamento.getValorCredenciada()),
                    formataValorMonetario(faturamento.getValorDesconto()),
                    formataValorMonetario(faturamento.getReceitaBruta()),
                    formataValorMonetario(faturamento.getImpostoSobreVenda()),
                    formataValorMonetario(faturamento.getReceitaLiquida())
            };
            relatorio.add(row);
        });

        RelatorioData relatorioData = new RelatorioData(headerNames, relatorio, "Relatório de Faturamento Líquido");
        relatorioDefault.excelDownloadDefault(nomeRelatorio(), relatorioData);

        fecharLoading();
    }

    public void excelFaturamentoBruto() {
        Map<String, Integer> headerNames = new LinkedHashMap<>();
        headerNames.put("Cobrança", 24);
        headerNames.put("CNPJ Cliente", 15);
        headerNames.put("Financeira", 45);
        headerNames.put("UF", 4);
        headerNames.put("Situação", 10);
        headerNames.put("Registros", 10);
        headerNames.put("Valor do Serviço", 18);
        headerNames.put("Valor do Desconto", 20);
        headerNames.put("Valor Total", 18);
        headerNames.put("Número da NF", 16);
        headerNames.put("Data Início", 12);
        headerNames.put("Data Fim", 12);

        List<String[]> relatorio = new ArrayList<>();
        getListForExcel().forEach(cobranca -> {
            String[] row = new String[]{
                    DateFormatUtils.format(cobranca.getDataGeracao(), "dd/MM/yyyy HH:mm"),
                    cobranca.getFinanceira().getDocumento(),
                    cobranca.getFinanceira().getNome(),
                    cobranca.getEstado().name(),
                    cobranca.getSituacaoCobranca().getDescricao(),
                    cobranca.getQuantidadeRegistros().toString(),
                    formataValorMonetario(cobranca.getValorCredenciada()),
                    formataValorMonetario(cobranca.getValorDesconto()),
                    formataValorMonetario(cobranca.getValorCredenciada().subtract(cobranca.getValorDesconto()).add(cobranca.getValorIntegraMais())),
                    cobranca.getNumeroNotaFiscal(),
                    DateFormatUtils.format(cobranca.getDataInicio(), "dd/MM/yyyy"),
                    DateFormatUtils.format(cobranca.getDataFim(), "dd/MM/yyyy")
            };
            relatorio.add(row);
        });

        RelatorioData relatorioData = new RelatorioData(headerNames, relatorio, "Relatório de Faturamento");
        relatorioDefault.excelDownloadDefault(nomeRelatorio(), relatorioData);

        fecharLoading();
    }

    private void fecharLoading() {
        String viewId = getCurrentInstance().getViewRoot().getViewId().replace("/", "_");
        getCurrentInstance().getExternalContext().addResponseCookie(
                org.primefaces.util.Constants.DOWNLOAD_COOKIE + viewId,
                "true",
                Collections.<String, Object>emptyMap()
        );
    }

    public LazyDataModel<FaturamentoDTO> getListFaturamento() {
        return listFaturamento;
    }

    public void setListFaturamento(LazyDataModel<FaturamentoDTO> listFaturamento) {
        this.listFaturamento = listFaturamento;
    }

    public List<GrupoFinanceira> getGruposFinanceiras() {
        return gruposFinanceiras;
    }

    public void setGruposFinanceiras(List<GrupoFinanceira> gruposFinanceiras) {
        this.gruposFinanceiras = gruposFinanceiras;
    }
}
