package com.registrocontrato.relatorio.view.bean;

import com.registrocontrato.infra.bean.BaseBean;
import com.registrocontrato.infra.entity.Financeira;
import com.registrocontrato.infra.jsf.ViewScope;
import com.registrocontrato.relatorio.service.ValidacaoRelatorioService;
import com.registrocontrato.seguranca.entity.Usuario;
import com.registrocontrato.seguranca.service.FinanceiraService;
import com.registrocontrato.seguranca.service.UsuarioService;
import org.primefaces.event.FileUploadEvent;
import org.primefaces.model.DefaultStreamedContent;
import org.primefaces.model.StreamedContent;
import org.primefaces.model.file.UploadedFile;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;

import javax.annotation.PostConstruct;
import java.io.*;
import java.nio.file.Files;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;

@Controller
@ViewScope
public class ValidacaoRelatorioBean extends BaseBean {

    private List<Financeira> financeiras;

    @Autowired
    private FinanceiraService financeiraService;

    @Autowired
    private UsuarioService usuarioService;

    @Autowired
    private ValidacaoRelatorioService validacaoRelatorioService;

    private Financeira selectedFinanceira;
    private Date dataInicio;
    private Date dataFim;
    private List<UploadedFile> lista = new ArrayList<>();
    private String relatorio;

    @PostConstruct
    public void postInitialization() {
        Usuario usuario = usuarioService.findByCpfFinanceiras(getUsername());
        if (usuario.isPerfilFinanceira()) {
            financeiras = usuario.getFinanceiras();
        } else {
            financeiras = financeiraService.findAtivos();
        }

    }

    public void handleFileUpload(FileUploadEvent event) {
        try {
            UploadedFile file = event.getFile();
            validacaoArquivosDefault(file, Collections.singletonList("pdf"));
            lista.add(file);
            addMessageInfo("Arquivo processado com sucesso");
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            addMessageError(e.getMessage() != null ? e.getMessage() : "Houve um erro no sistema. Favor entrar em contato com suporte");
        }
    }

    public void removerArquivo() {
        if (!lista.isEmpty()) {
            logger.debug("Removendo arquivo com sucesso");
            lista.remove(0);
            addMessageInfo("Arquivo removido com sucesso");
        } else {
            addMessageError("Arquivo não encontrado para remoção");
        }
    }

    public void confirmar() {
        if (lista == null || lista.isEmpty()) {
            addMessageError("PDF: Erro de validação: o valor é necessário.");
            return;
        }
        UploadedFile pdfFile = lista.get(0);
        try {
            relatorio = validacaoRelatorioService.validarRelatorioB3Local(pdfFile, dataInicio, dataFim, selectedFinanceira.getId(), selectedFinanceira.getDocumento());
            addMessageInfo("Arquivo processado com sucesso! ");
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            addMessageError("Erro ao criar arquivo");
        }
    }

    public StreamedContent getFile() throws IOException {
        if (relatorio == null || relatorio.isEmpty()) {
            addMessageError("Nenhum arquivo disponível para download.");
            return null;
        }

        File file = new File(relatorio);
        if (!file.exists()) {
            addMessageError("Arquivo não encontrado: " + relatorio);
            return null;
        }

        return DefaultStreamedContent.builder()
                .name(file.getName())
                .contentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
                .stream(() -> {
                    try {
                        return Files.newInputStream(file.toPath());
                    } catch (IOException e) {
                        throw new RuntimeException("Erro ao ler o arquivo: " + relatorio, e);
                    }
                })
                .build();
    }


    public List<Financeira> getFinanceiras() {
        return financeiras;
    }

    public void setFinanceiras(List<Financeira> financeiras) {
        this.financeiras = financeiras;
    }

    public Financeira getSelectedFinanceira() {
        return selectedFinanceira;
    }

    public void setSelectedFinanceira(Financeira selectedFinanceira) {
        this.selectedFinanceira = selectedFinanceira;
    }

    public Date getDataInicio() {
        return dataInicio;
    }

    public void setDataInicio(Date dataInicio) {
        this.dataInicio = dataInicio;
    }

    public Date getDataFim() {
        return dataFim;
    }

    public void setDataFim(Date dataFim) {
        this.dataFim = dataFim;
    }

    public List<UploadedFile> getLista() {
        return lista;
    }

    public void setLista(List<UploadedFile> lista) {
        this.lista = lista;
    }

    public String getRelatorio() {
        return relatorio;
    }

    public void setRelatorio(String relatorio) {
        this.relatorio = relatorio;
    }
}
