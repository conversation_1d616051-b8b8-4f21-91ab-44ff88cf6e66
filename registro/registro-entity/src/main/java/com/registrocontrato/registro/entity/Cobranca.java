package com.registrocontrato.registro.entity;

import com.registrocontrato.infra.entity.BaseEntity;
import com.registrocontrato.infra.entity.Financeira;
import com.registrocontrato.infra.entity.SimNao;
import com.registrocontrato.infra.entity.Uf;
import com.registrocontrato.infra.util.PlaceconUtil;
import com.registrocontrato.registro.enums.PagamentoCobranca;
import com.registrocontrato.registro.enums.SituacaoCobranca;
import com.registrocontrato.registro.enums.TipoCobranca;
import org.hibernate.envers.Audited;

import javax.persistence.*;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Entity
@Audited
@Table(schema = "financeiro")
public class Cobranca extends BaseEntity {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Enumerated(EnumType.STRING)
    private Uf estado;

    @JoinColumn
    @ManyToOne(cascade = {}, fetch = FetchType.EAGER)
    private Financeira financeira;

    @Temporal(TemporalType.TIMESTAMP)
    private Date dataInicio;

    @Temporal(TemporalType.TIMESTAMP)
    private Date dataFim;

    @Temporal(TemporalType.TIMESTAMP)
    private Date dataGeracao;

    @Enumerated(EnumType.STRING)
    private SituacaoCobranca situacaoCobranca;

    @Enumerated(EnumType.STRING)
    private TipoCobranca tipoCobranca;

    @Enumerated(EnumType.STRING)
    private PagamentoCobranca pagamentoCobranca;

    @Enumerated(EnumType.STRING)
    private SimNao cobrancaUnificada;

    private Long quantidadeRegistros = 0L;

    private Long quantidadeAditivo = 0L;

    private Long quantidadePrincipal = 0L;

    private Long quantidadeRegistrosSng = 0L;

    private Long quantidadeBaixaSng = 0L;

    private Long quantidadeCancelamentoBaixaSng = 0L;

    private BigDecimal valorCobrancaSng = BigDecimal.ZERO;

    private BigDecimal valorCobranca = BigDecimal.ZERO;

    private BigDecimal valorDetranAditivo = BigDecimal.ZERO;

    private BigDecimal valorDetranPrincipal = BigDecimal.ZERO;

    private BigDecimal valorDetran = BigDecimal.ZERO;

    private BigDecimal valorDetranReembolsavel = BigDecimal.ZERO;

    private BigDecimal valorCredenciada = BigDecimal.ZERO;

    private BigDecimal valorDesconto = BigDecimal.ZERO;

    private BigDecimal valorIntegraMais = BigDecimal.ZERO;

    private Boolean boletoEmitido;

    private Boolean boletoReembolsoEmitido;

    private Boolean boletoDetranManual;

    private Boolean boletoDetranUnificadaFinanceiroPlace;

    private Boolean boletoDetranUnificadaCliente;

    private Boolean notaRembolsoEmitida;

    private String tokenBoleto;

    @Temporal(TemporalType.DATE)
    private Date dataVencimentoBoleto;

    private String tokenReembolsoBoleto;

    private String tokenSngBoleto;

    @Temporal(TemporalType.DATE)
    private Date dataVencimentoReembolsoBoleto;

    private String numeroNotaFiscal;

    private Date dataNotaFiscal;

    private String tokenNotaReembolso;

    private String notaFiscal;

    private String notaFiascalSng;

    private Boolean boletoSngEmitido;

    private String xmlNotaFiscal;

    private String notaReembolso;

    private Boolean notaAzul;

    private Boolean notaAzulSng;

    private SimNao integraMais;

    @Enumerated(EnumType.STRING)
    private SimNao pagouDetran;

    @Enumerated(EnumType.STRING)
    private SimNao pagouCredenciada;

    @JoinColumn
    @ManyToOne(cascade = {}, fetch = FetchType.EAGER)
    private Credenciamento credenciamento;

    @JoinColumn
    @ManyToOne(cascade = {}, fetch = FetchType.EAGER)
    private BilhetagemGravame gravame;

    @JoinColumn
    @ManyToOne(cascade = {}, fetch = FetchType.EAGER)
    private CupomDesconto cupomDesconto;

    @OneToMany(cascade = {}, fetch = FetchType.LAZY, mappedBy = "cobranca")
    private List<Contrato> contratos = new ArrayList<Contrato>();

    @OneToMany(cascade = {}, fetch = FetchType.LAZY, mappedBy = "cobranca")
    private List<ContratoRsng> contratosRsng = new ArrayList<>();

    private String numeroVenda;

    private String numeroVendaSng;

    private String numeroDocumentoCredenciada;

    private String numeroDocumentoDetran;

    private String tokenArquivoPagamentoDetran;

    private String tokenArquivoPagamentoCredenciada;

    private String tokenRegistroPagamentoDetran;

    private String tokenRegistroPagamentoCredenciada;

    private String numeroPedido;

    @Enumerated(EnumType.STRING)
    private SimNao quatroRodas;

    @Enumerated(EnumType.STRING)
    private SimNao reembolsoLinhaDigitavel;

    @Enumerated(EnumType.STRING)
    private SimNao notificacaoBaixa;

    @Enumerated(EnumType.STRING)
    private SimNao notificacaoVencimento;

    @Transient
    private BigDecimal valorCobrancaInformadoDetran = BigDecimal.ZERO;

    @Temporal(TemporalType.DATE)
    private Date dataRegistroPagamento;

    @Lob
    @Column(length = 500)
    private String observacao;

    public Cobranca(Financeira financeira, Date dataInicio, Date dataFim, SimNao cobrancaUnificada, Uf estado) {
        this.financeira = financeira;
        this.dataInicio = dataInicio;
        this.dataGeracao = new Date();
        this.cobrancaUnificada = cobrancaUnificada;
        this.dataFim = dataFim;
        this.estado = estado;
    }

    public Cobranca(Uf estado, Date dataInicio, Date dataFim) {
        this.estado = estado;
        this.dataInicio = dataInicio;
        this.dataGeracao = new Date();
        this.dataFim = dataFim;
    }

    public Cobranca(Uf estado, Financeira financeira, Date dataInicio, Date dataFim, Credenciamento credenciamento, SimNao quatroRodas) {
        this.estado = estado;
        this.financeira = financeira;
        this.dataInicio = dataInicio;
        this.dataGeracao = new Date();
        this.dataFim = dataFim;
        this.credenciamento = credenciamento;
        this.quatroRodas = quatroRodas;
    }

    public Date getDataRegistroPagamento() {
        return dataRegistroPagamento;
    }

    public void setDataRegistroPagamento(Date dataRegistroPagamento) {
        this.dataRegistroPagamento = dataRegistroPagamento;
    }

    public String getObservacao() {
        return observacao;
    }

    public void setObservacao(String observacao) {
        this.observacao = observacao;
    }

    public Cobranca() {
    }

    public Cobranca(Uf estado, Date dataInicio, Date dataFim, Financeira financeira) {
        this.estado = estado;
        this.financeira = financeira;
        this.dataGeracao = new Date();
        this.dataInicio = dataInicio;
        this.dataFim = dataFim;
        this.situacaoCobranca = SituacaoCobranca.GERADA;
    }

    public Cobranca(Long id, Uf estado, Date dataInicio, Date dataFim, Financeira financeira, SimNao cobrancaUnificada) {
        this.id = id;
        this.estado = estado;
        this.financeira = financeira;
        this.dataGeracao = new Date();
        this.dataInicio = dataInicio;
        this.dataFim = dataFim;
        this.cobrancaUnificada = cobrancaUnificada;
    }



    public Cobranca(Uf estado, Financeira financeira, Date dataInicio, Date dataFim, Credenciamento credenciamento, BilhetagemGravame gravame) {
        this.estado = estado;
        this.financeira = financeira;
        this.dataGeracao = new Date();
        this.dataInicio = dataInicio;
        this.dataFim = dataFim;
        this.credenciamento = credenciamento;
        this.situacaoCobranca = SituacaoCobranca.GERADA;
        this.tipoCobranca = credenciamento.getTipoCobranca();
        this.gravame = gravame;
    }

    public Cobranca(Uf estado, Financeira financeira, Date dataInicio, Date dataFim, Credenciamento credenciamento) {
        this.estado = estado;
        this.financeira = financeira;
        this.dataGeracao = new Date();
        this.dataInicio = dataInicio;
        this.dataFim = dataFim;
        this.credenciamento = credenciamento;
        this.situacaoCobranca = SituacaoCobranca.GERADA;
        if (credenciamento != null) {
            this.tipoCobranca = credenciamento.getTipoCobranca();
        }
    }

    public Long getQuantidadeBaixaSng() {
        return quantidadeBaixaSng;
    }

    public void setQuantidadeBaixaSng(Long quantidadeBaixaSng) {
        this.quantidadeBaixaSng = quantidadeBaixaSng;
    }

    public Long getQuantidadeCancelamentoBaixaSng() {
        return quantidadeCancelamentoBaixaSng;
    }

    public void setQuantidadeCancelamentoBaixaSng(Long quantidadeCancelamentoBaixaSng) {
        this.quantidadeCancelamentoBaixaSng = quantidadeCancelamentoBaixaSng;
    }

    public String getTokenSngBoleto() {
        return tokenSngBoleto;
    }

    public void setTokenSngBoleto(String tokenSngBoleto) {
        this.tokenSngBoleto = tokenSngBoleto;
    }

    public Long getQuantidadeRegistrosSng() {
        return quantidadeRegistrosSng;
    }

    public void setQuantidadeRegistrosSng(Long quantidadeRegistrosSng) {
        this.quantidadeRegistrosSng = quantidadeRegistrosSng;
    }

    public String getNumeroVendaSng() {
        return numeroVendaSng;
    }

    public void setNumeroVendaSng(String numeroVendaSng) {
        this.numeroVendaSng = numeroVendaSng;
    }

    public BigDecimal getValorCredenciadaIntegra() {
        BigDecimal valor = BigDecimal.ZERO;
        if (this.valorIntegraMais != null) {
            valor = valor.add(this.valorIntegraMais);
        }
        if (this.valorCredenciada != null) {
            valor = valor.add(this.valorCredenciada);
        }
        return valor;
    }

    public String getNotaFiascalSng() {
        return notaFiascalSng;
    }

    public void setNotaFiascalSng(String notaFiascalSng) {
        this.notaFiascalSng = notaFiascalSng;
    }

    public Boolean getBoletoSngEmitido() {
        return boletoSngEmitido;
    }

    public void setBoletoSngEmitido(Boolean boletoSngEmitido) {
        this.boletoSngEmitido = boletoSngEmitido;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Date getDataInicio() {
        return dataInicio;
    }

    public void setDataInicio(Date dataInicio) {
        this.dataInicio = dataInicio;
    }

    public Date getDataFim() {
        return dataFim;
    }

    public void setDataFim(Date dataFim) {
        this.dataFim = dataFim;
    }

    public Date getDataGeracao() {
        return dataGeracao;
    }

    public String getDataGeracaoString() {
        return PlaceconUtil.formatarDataPadrao(dataGeracao);
    }

    public void setDataGeracao(Date dataGeracao) {
        this.dataGeracao = dataGeracao;
    }

    public Date getDataVencimentoBoleto() {
        return dataVencimentoBoleto;
    }

    public void setDataVencimentoBoleto(Date dataVencimentoBoleto) {
        this.dataVencimentoBoleto = dataVencimentoBoleto;
    }

    public Date getDataVencimentoReembolsoBoleto() {
        return dataVencimentoReembolsoBoleto;
    }

    public void setDataVencimentoReembolsoBoleto(Date dataVencimentoReembolsoBoleto) {
        this.dataVencimentoReembolsoBoleto = dataVencimentoReembolsoBoleto;
    }

    public BigDecimal getValorCobrancaSng() {
        return valorCobrancaSng;
    }

    public void setValorCobrancaSng(BigDecimal valorCobrancaSng) {
        this.valorCobrancaSng = valorCobrancaSng;
    }

    public BilhetagemGravame getGravame() {
        return gravame;
    }

    public void setGravame(BilhetagemGravame gravame) {
        this.gravame = gravame;
    }

    public SimNao getNotificacaoBaixa() {
        return notificacaoBaixa;
    }

    public void setNotificacaoBaixa(SimNao notificacaoBaixa) {
        this.notificacaoBaixa = notificacaoBaixa;
    }

    public SimNao getNotificacaoVencimento() {
        return notificacaoVencimento;
    }

    public void setNotificacaoVencimento(SimNao notificacaoVencimento) {
        this.notificacaoVencimento = notificacaoVencimento;
    }

    public Boolean getNotaAzulSng() {
        return notaAzulSng;
    }

    public void setNotaAzulSng(Boolean notaAzulSng) {
        this.notaAzulSng = notaAzulSng;
    }

    public Uf getEstado() {
        return estado;
    }

    public void setEstado(Uf estado) {
        this.estado = estado;
    }

    public Financeira getFinanceira() {
        return financeira;
    }

    public void setFinanceira(Financeira financeira) {
        this.financeira = financeira;
    }

    public SituacaoCobranca getSituacaoCobranca() {
        return situacaoCobranca;
    }

    public void setSituacaoCobranca(SituacaoCobranca situacaoCobranca) {
        this.situacaoCobranca = situacaoCobranca;
    }

    public List<Contrato> getContratos() {
        return contratos;
    }

    public void setContratos(List<Contrato> contratos) {
        this.contratos = contratos;
    }

    public List<ContratoRsng> getContratosRsng() {
        return contratosRsng;
    }

    public void setContratosRsng(List<ContratoRsng> contratosRsng) {
        this.contratosRsng = contratosRsng;
    }

    public CupomDesconto getCupomDesconto() {
        return cupomDesconto;
    }

    public void setCupomDesconto(CupomDesconto cupomDesconto) {
        this.cupomDesconto = cupomDesconto;
    }

    public Long getQuantidadeRegistros() {
        return quantidadeRegistros;
    }

    public void setQuantidadeRegistros(Long quantidadeRegistros) {
        this.quantidadeRegistros = quantidadeRegistros;
    }

    public BigDecimal getValorCobranca() {
        return valorCobranca;
    }

    public BigDecimal getValorCredenciada() {
        return valorCredenciada;
    }

    public BigDecimal getValorDesconto() {
        return valorDesconto;
    }

    public void setValorDesconto(BigDecimal valorDesconto) {
        this.valorDesconto = valorDesconto;
    }

    public void setValorCredenciada(BigDecimal valorCredenciada) {
        this.valorCredenciada = valorCredenciada;
    }

    public void setValorCobranca(BigDecimal valorCobranca) {
        this.valorCobranca = valorCobranca;
    }

    public Credenciamento getCredenciamento() {
        return credenciamento;
    }

    public void setCredenciamento(Credenciamento credenciamento) {
        this.credenciamento = credenciamento;
    }

    public TipoCobranca getTipoCobranca() {
        return tipoCobranca;
    }

    public void setTipoCobranca(TipoCobranca tipoCobranca) {
        this.tipoCobranca = tipoCobranca;
    }

    public Boolean isFaturada() {
        return situacaoCobranca == SituacaoCobranca.FATURADA;
    }

    public Boolean isEnviada() {
        return situacaoCobranca == SituacaoCobranca.ENVIADA;
    }

    public Boolean isGerada() {
        return situacaoCobranca == SituacaoCobranca.GERADA;
    }

    public Boolean getNotaRembolsoEmitida() {
        return notaRembolsoEmitida;
    }

    public void setNotaRembolsoEmitida(Boolean notaRembolsoEmitida) {
        this.notaRembolsoEmitida = notaRembolsoEmitida;
    }

    public Boolean isPaga() {
        return situacaoCobranca == SituacaoCobranca.PAGA;
    }

    public Boolean getBoletoEmitido() {
        return boletoEmitido;
    }

    public void setBoletoEmitido(Boolean boletoEmitido) {
        this.boletoEmitido = boletoEmitido;
    }

    public Boolean getBoletoReembolsoEmitido() {
        return boletoReembolsoEmitido;
    }

    public void setBoletoReembolsoEmitido(Boolean boletoReembolsoEmitido) {
        this.boletoReembolsoEmitido = boletoReembolsoEmitido;
    }

    public String getTokenBoleto() {
        return tokenBoleto;
    }

    public void setTokenBoleto(String tokenBoleto) {
        this.tokenBoleto = tokenBoleto;
    }

    public String getTokenReembolsoBoleto() {
        return tokenReembolsoBoleto;
    }

    public void setTokenReembolsoBoleto(String tokenReembolsoBoleto) {
        this.tokenReembolsoBoleto = tokenReembolsoBoleto;
    }

    public String getNotaFiscal() {
        return notaFiscal;
    }

    public void setNotaFiscal(String notaFiscal) {
        this.notaFiscal = notaFiscal;
    }

    public String getXmlNotaFiscal() {
        return xmlNotaFiscal;
    }

    public void setXmlNotaFiscal(String xmlNotaFiscal) {
        this.xmlNotaFiscal = xmlNotaFiscal;
    }

    public String getNotaReembolso() {
        return notaReembolso;
    }

    public void setNotaReembolso(String notaReembolso) {
        this.notaReembolso = notaReembolso;
    }

    public SimNao getIntegraMais() {
        return integraMais;
    }

    public void setIntegraMais(SimNao integraMais) {
        this.integraMais = integraMais;
    }

    public BigDecimal getValorIntegraMais() {
        return valorIntegraMais;
    }

    public void setValorIntegraMais(BigDecimal valorIntegraMais) {
        this.valorIntegraMais = valorIntegraMais;
    }

    public Long getQuantidadeAditivo() {
        return quantidadeAditivo;
    }

    public void setQuantidadeAditivo(Long quantidadeAditivo) {
        this.quantidadeAditivo = quantidadeAditivo;
    }

    public Long getQuantidadePrincipal() {
        return quantidadePrincipal;
    }

    public void setQuantidadePrincipal(Long quantidadePrincipal) {
        this.quantidadePrincipal = quantidadePrincipal;
    }

    public BigDecimal getValorDetranAditivo() {
        return valorDetranAditivo;
    }

    public void setValorDetranAditivo(BigDecimal valorDetranAditivo) {
        this.valorDetranAditivo = valorDetranAditivo;
    }

    public BigDecimal getValorDetranPrincipal() {
        return valorDetranPrincipal;
    }

    public void setValorDetranPrincipal(BigDecimal valorDetranPrincipal) {
        this.valorDetranPrincipal = valorDetranPrincipal;
    }

    public BigDecimal getValorDetran() {
        return valorDetran;
    }

    public void setValorDetran(BigDecimal valorDetran) {
        this.valorDetran = valorDetran;
    }

    public Boolean getNotaAzul() {
        return notaAzul;
    }

    public void setNotaAzul(Boolean notaAzul) {
        this.notaAzul = notaAzul;
    }

    public String getTokenNotaReembolso() {
        return tokenNotaReembolso;
    }

    public void setTokenNotaReembolso(String tokenNotaReembolso) {
        this.tokenNotaReembolso = tokenNotaReembolso;
    }


    public SimNao getPagouDetran() {
        return pagouDetran;
    }

    public void setPagouDetran(SimNao pagouDetran) {
        this.pagouDetran = pagouDetran;
    }

    public SimNao getPagouCredenciada() {
        return pagouCredenciada;
    }

    public void setPagouCredenciada(SimNao pagouCredenciada) {
        this.pagouCredenciada = pagouCredenciada;
    }

    public PagamentoCobranca getPagamentoCobranca() {
        return pagamentoCobranca;
    }

    public void setPagamentoCobranca(PagamentoCobranca pagamentoCobranca) {
        this.pagamentoCobranca = pagamentoCobranca;
    }

    public String getNumeroDocumentoCredenciada() {
        return numeroDocumentoCredenciada;
    }

    public String getNumeroDocumentoDetran() {
        return numeroDocumentoDetran;
    }


    public void setNumeroDocumentoCredenciada(String numeroDocumentoCredenciada) {
        this.numeroDocumentoCredenciada = numeroDocumentoCredenciada;
    }

    public void setNumeroDocumentoDetran(String numeroDocumentoDetran) {
        this.numeroDocumentoDetran = numeroDocumentoDetran;
    }

    public String getTokenArquivoPagamentoCredenciada() {
        return tokenArquivoPagamentoCredenciada;
    }

    public String getTokenArquivoPagamentoDetran() {
        return tokenArquivoPagamentoDetran;
    }

    public void setTokenArquivoPagamentoCredenciada(String tokenArquivoPagamentoCredenciada) {
        this.tokenArquivoPagamentoCredenciada = tokenArquivoPagamentoCredenciada;
    }

    public void setTokenArquivoPagamentoDetran(String tokenArquivoPagamentoDetran) {
        this.tokenArquivoPagamentoDetran = tokenArquivoPagamentoDetran;
    }

    public String getTokenRegistroPagamentoDetran() {
        return tokenRegistroPagamentoDetran;
    }

    public void setTokenRegistroPagamentoDetran(String tokenRegistroPagamentoDetran) {
        this.tokenRegistroPagamentoDetran = tokenRegistroPagamentoDetran;
    }

    public String getTokenRegistroPagamentoCredenciada() {
        return tokenRegistroPagamentoCredenciada;
    }

    public void setTokenRegistroPagamentoCredenciada(String tokenRegistroPagamentoCredenciada) {
        this.tokenRegistroPagamentoCredenciada = tokenRegistroPagamentoCredenciada;
    }


    public Boolean getBoletoDetranManual() {
        return boletoDetranManual;
    }

    public void setBoletoDetranManual(Boolean boletoDetranManual) {
        this.boletoDetranManual = boletoDetranManual;
    }

    public String getNumeroVenda() {
        return numeroVenda;
    }

    public void setNumeroVenda(String numeroVenda) {
        this.numeroVenda = numeroVenda;
    }

    public SimNao getReembolsoLinhaDigitavel() {
        return reembolsoLinhaDigitavel;
    }

    public void setReembolsoLinhaDigitavel(SimNao reembolsoLinhaDigitavel) {
        this.reembolsoLinhaDigitavel = reembolsoLinhaDigitavel;
    }

    public SimNao getQuatroRodas() {
        return quatroRodas;
    }

    public void setQuatroRodas(SimNao quatroRodas) {
        this.quatroRodas = quatroRodas;
    }

    public BigDecimal getValorCobrancaInformadoDetran() {
        return valorCobrancaInformadoDetran;
    }

    public void setValorCobrancaInformadoDetran(BigDecimal valorCobrancaInformadoDetran) {
        this.valorCobrancaInformadoDetran = valorCobrancaInformadoDetran;
    }

    public String getNumeroPedido() {
        return numeroPedido;
    }

    public void setNumeroPedido(String numeroPedido) {
        this.numeroPedido = numeroPedido;
    }

    public String getNumeroNotaFiscal() {
        return numeroNotaFiscal;
    }

    public void setNumeroNotaFiscal(String numeroNotaFiscal) {
        this.numeroNotaFiscal = numeroNotaFiscal;
    }

    public Date getDataNotaFiscal() {
        return dataNotaFiscal;
    }

    public void setDataNotaFiscal(Date dataNotaFiscal) {
        this.dataNotaFiscal = dataNotaFiscal;
    }

    public SimNao getCobrancaUnificada() {
        return cobrancaUnificada;
    }

    public void setCobrancaUnificada(SimNao cobrancaUnificada) {
        this.cobrancaUnificada = cobrancaUnificada;
    }

    public Boolean isUnificada() {
        return getCobrancaUnificada() == SimNao.S;
    }

    public BigDecimal getValorDetranReembolsavel() {
        return valorDetranReembolsavel;
    }

    public void setValorDetranReembolsavel(BigDecimal valorDetranReembolsavel) {
        this.valorDetranReembolsavel = valorDetranReembolsavel;
    }

    public Boolean getBoletoDetranUnificadaFinanceiroPlace() {
        return boletoDetranUnificadaFinanceiroPlace;
    }

    public void setBoletoDetranUnificadaFinanceiroPlace(Boolean boletoDetranUnificadaFinanceiroPlace) {
        this.boletoDetranUnificadaFinanceiroPlace = boletoDetranUnificadaFinanceiroPlace;
    }

    public Boolean getBoletoDetranUnificadaCliente() {
        return boletoDetranUnificadaCliente;
    }

    public void setBoletoDetranUnificadaCliente(Boolean boletoDetranUnificadaCliente) {
        this.boletoDetranUnificadaCliente = boletoDetranUnificadaCliente;
    }
}

