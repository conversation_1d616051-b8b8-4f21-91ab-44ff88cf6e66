package com.registrocontrato.registro.entity;

import com.registrocontrato.infra.entity.*;
import com.registrocontrato.infra.util.PlaceconUtil;
import com.registrocontrato.registro.enums.*;
import org.hibernate.envers.Audited;

import javax.persistence.*;
import java.math.BigDecimal;
import java.util.Date;

@Audited
@Inheritance(strategy = InheritanceType.TABLE_PER_CLASS)
@MappedSuperclass
public abstract class ContratoModel extends BaseEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @JoinColumn
    @ManyToOne(cascade = {}, fetch = FetchType.EAGER)
    private Financeira financeira;

    @JoinColumn
    @ManyToOne(cascade = {}, fetch = FetchType.EAGER)
    private Agente agente;

    @Enumerated(EnumType.STRING)
    private Uf ufRegistro;

    @Enumerated(EnumType.STRING)
    private TipoContrato tipoContrato;

    @Enumerated(EnumType.STRING)
    private TipoRestricao tipoRestricao;

    // aditivo
    private String numeroAditivoContrato;

    @Temporal(TemporalType.DATE)
    private Date dataAditivoContrato;

    // alteracao
    private Boolean alteracao;

    // contrato
    private String numeroContrato;

    // Financeiro
    @Enumerated(EnumType.STRING)
    private SituacaoFinanceira situacaoFinanceira;

    @Temporal(TemporalType.DATE)
    private Date dataContrato;

    @Temporal(TemporalType.DATE)
    private Date dataCompra;

    private Integer quantidadeMeses;

    private BigDecimal valorTaxaJurosMes;

    private BigDecimal valorTaxaJurosAno;

    private Boolean indicadorTaxaMulta;

    private BigDecimal valorTaxaMulta;

    private Boolean indicadorTaxaMoraDia;

    private BigDecimal valorTaxaMoraDia;

    private BigDecimal valorTaxaContrato;

    private BigDecimal valorTotalDivida;

    private BigDecimal valorIOF;

    private BigDecimal valorParcela;

    @Temporal(TemporalType.DATE)
    private Date dataVencimentoPrimeiraParcela;

    @Temporal(TemporalType.DATE)
    private Date dataVencimentoUltimaParcela;

    @Temporal(TemporalType.DATE)
    private Date dataLiberacaoCredito;

    @Enumerated(EnumType.STRING)
    private Uf ufLiberacaoCredito;

    @JoinColumn
    @ManyToOne(cascade = {}, fetch = FetchType.LAZY)
    private Municipio municipioLiberacao;

    private BigDecimal valorCredito;

    @Enumerated(EnumType.STRING)
    private IndiceFinanceiro siglaIndiceFinaceiro;

    private String numeroGrupoConsorcio;

    private Long numeroCotaConsorcio;

    private Boolean indicadorPenalidade;

    private String descricaoPenalidade;

    private Boolean indicadorComissao;

    private BigDecimal percentualComissao;

    // devedor
    private String cpfCnpjDevedorFinanciado;

    private String nomeDevedorFinanciado;

    private String cepDevedor;

    private String enderecoDevedor;

    private String numeroEnderecoDevedor;

    private String complementoEnderecoDevedor;

    private String bairroDevedor;

    @JoinColumn
    @ManyToOne(cascade = {}, fetch = FetchType.LAZY)
    private Municipio municipioDevedor;

    @Enumerated(EnumType.STRING)
    private Uf ufEnderecoDevedor;

    private Integer dddDevedor;

    private Integer telefoneDevedor;

    private String emailDevedor;

    private String comentario;

    @JoinColumn
    @ManyToOne(cascade = {}, fetch = FetchType.LAZY)
    private Integradora integradora;

    // VRG - Valor Real de Garantia
    @Enumerated(EnumType.STRING)
    private TipoVrg tipoVrg;

    private String clausulaPenalVrg;

    private BigDecimal valorVrg;

    // Complemento para Tipo de Contrato
    private String cpfCnpjCessaoDireito;

    private String nomeCessaoDireito;

    private String chassiSubstituicao;

    // garantidor
    @Enumerated(EnumType.STRING)
    private SimNao possuiGarantidor;

    private String cpfCnpjGarantidorFinanciado;

    private String nomeGarantidorFinanciado;

    private String cepGarantidor;

    private String enderecoGarantidor;

    private String numeroEnderecoGarantidor;

    private String complementoEnderecoGarantidor;

    private String bairroGarantidor;

    @JoinColumn
    @ManyToOne(cascade = {}, fetch = FetchType.EAGER)
    private Municipio municipioGarantidor;

    @Enumerated(EnumType.STRING)
    private Uf ufEnderecoGarantidor;

    private Integer dddGarantidor;

    private Integer telefoneGarantidor;

    private String emailGarantidor;

    @Temporal(TemporalType.DATE)
    private Date dataCancelamentoBaixaB3;

    @Temporal(TemporalType.DATE)
    private Date dataBaixaB3;

    @Enumerated(EnumType.STRING)
    private SituacaoBaixaB3 situacaoBaixa;

    @Override
    public Long getId() {
        return id;
    }

    @Override
    public void setId(Long id) {
        this.id = id;
    }

    public Date getDataCancelamentoBaixaB3() {
        return dataCancelamentoBaixaB3;
    }

    public void setDataCancelamentoBaixaB3(Date dataCancelamentoBaixaB3) {
        this.dataCancelamentoBaixaB3 = dataCancelamentoBaixaB3;
    }

    public Date getDataBaixaB3() {
        return dataBaixaB3;
    }

    public void setDataBaixaB3(Date dataBaixaB3) {
        this.dataBaixaB3 = dataBaixaB3;
    }

    public SituacaoBaixaB3 getSituacaoBaixaB3() {
        return situacaoBaixa;
    }

    public void setSituacaoBaixaB3(SituacaoBaixaB3 situacaoBaixaB3) {
        this.situacaoBaixa = situacaoBaixaB3;
    }

    public SituacaoFinanceira getSituacaoFinanceira() {
        return situacaoFinanceira;
    }

    public void setSituacaoFinanceira(SituacaoFinanceira situacaoFinanceira) {
        this.situacaoFinanceira = situacaoFinanceira;
    }

    public Financeira getFinanceira() {
        return financeira;
    }

    public void setFinanceira(Financeira financeira) {
        this.financeira = financeira;
    }

    public Agente getAgente() {
        return agente;
    }

    public void setAgente(Agente agente) {
        this.agente = agente;
    }

    public Uf getUfRegistro() {
        return ufRegistro;
    }

    public void setUfRegistro(Uf ufRegistro) {
        this.ufRegistro = ufRegistro;
    }

    public TipoContrato getTipoContrato() {
        return tipoContrato;
    }

    public void setTipoContrato(TipoContrato tipoContrato) {
        this.tipoContrato = tipoContrato;
    }

    public TipoRestricao getTipoRestricao() {
        return tipoRestricao;
    }

    public void setTipoRestricao(TipoRestricao tipoRestricao) {
        this.tipoRestricao = tipoRestricao;
    }

    public String getNumeroAditivoContrato() {
        return numeroAditivoContrato;
    }

    public void setNumeroAditivoContrato(String numeroAditivoContrato) {
        this.numeroAditivoContrato = numeroAditivoContrato;
    }

    public Date getDataAditivoContrato() {
        return dataAditivoContrato;
    }

    public void setDataAditivoContrato(Date dataAditivoContrato) {
        this.dataAditivoContrato = dataAditivoContrato;
    }

    public Boolean getAlteracao() {
        return alteracao;
    }

    public void setAlteracao(Boolean alteracao) {
        this.alteracao = alteracao;
    }

    public String getNumeroContrato() {
        return numeroContrato;
    }

    public void setNumeroContrato(String numeroContrato) {
        this.numeroContrato = numeroContrato;
    }

    public Date getDataContrato() {
        return dataContrato;
    }

    public String getDataContratoFormatada() {
        return dataContrato == null ? "-" : PlaceconUtil.formatarDataPadrao(dataContrato);
    }

    public void setDataContrato(Date dataContrato) {
        this.dataContrato = dataContrato;
    }

    public Date getDataCompra() {
        return dataCompra;
    }

    public void setDataCompra(Date dataCompra) {
        this.dataCompra = dataCompra;
    }

    public Integer getQuantidadeMeses() {
        return quantidadeMeses;
    }

    public void setQuantidadeMeses(Integer quantidadeMeses) {
        this.quantidadeMeses = quantidadeMeses;
    }

    public BigDecimal getValorTaxaJurosMes() {
        return valorTaxaJurosMes;
    }

    public void setValorTaxaJurosMes(BigDecimal valorTaxaJurosMes) {
        this.valorTaxaJurosMes = valorTaxaJurosMes;
    }

    public BigDecimal getValorTaxaJurosAno() {
        return valorTaxaJurosAno;
    }

    public void setValorTaxaJurosAno(BigDecimal valorTaxaJurosAno) {
        this.valorTaxaJurosAno = valorTaxaJurosAno;
    }

    public Boolean getIndicadorTaxaMulta() {
        return indicadorTaxaMulta;
    }

    public void setIndicadorTaxaMulta(Boolean indicadorTaxaMulta) {
        this.indicadorTaxaMulta = indicadorTaxaMulta;
    }

    public BigDecimal getValorTaxaMulta() {
        return valorTaxaMulta;
    }

    public void setValorTaxaMulta(BigDecimal valorTaxaMulta) {
        this.valorTaxaMulta = valorTaxaMulta;
    }

    public Boolean getIndicadorTaxaMoraDia() {
        return indicadorTaxaMoraDia;
    }

    public void setIndicadorTaxaMoraDia(Boolean indicadorTaxaMoraDia) {
        this.indicadorTaxaMoraDia = indicadorTaxaMoraDia;
    }

    public BigDecimal getValorTaxaMoraDia() {
        return valorTaxaMoraDia;
    }

    public void setValorTaxaMoraDia(BigDecimal valorTaxaMoraDia) {
        this.valorTaxaMoraDia = valorTaxaMoraDia;
    }

    public BigDecimal getValorTaxaContrato() {
        return valorTaxaContrato;
    }

    public void setValorTaxaContrato(BigDecimal valorTaxaContrato) {
        this.valorTaxaContrato = valorTaxaContrato;
    }

    public BigDecimal getValorTotalDivida() {
        return valorTotalDivida;
    }

    public void setValorTotalDivida(BigDecimal valorTotalDivida) {
        this.valorTotalDivida = valorTotalDivida;
    }

    public BigDecimal getValorIOF() {
        return valorIOF;
    }

    public void setValorIOF(BigDecimal valorIOF) {
        this.valorIOF = valorIOF;
    }

    public BigDecimal getValorParcela() {
        return valorParcela;
    }

    public void setValorParcela(BigDecimal valorParcela) {
        this.valorParcela = valorParcela;
    }

    public Date getDataVencimentoPrimeiraParcela() {
        return dataVencimentoPrimeiraParcela;
    }

    public void setDataVencimentoPrimeiraParcela(Date dataVencimentoPrimeiraParcela) {
        this.dataVencimentoPrimeiraParcela = dataVencimentoPrimeiraParcela;
    }

    public Date getDataVencimentoUltimaParcela() {
        return dataVencimentoUltimaParcela;
    }

    public void setDataVencimentoUltimaParcela(Date dataVencimentoUltimaParcela) {
        this.dataVencimentoUltimaParcela = dataVencimentoUltimaParcela;
    }

    public Date getDataLiberacaoCredito() {
        return dataLiberacaoCredito;
    }

    public void setDataLiberacaoCredito(Date dataLiberacaoCredito) {
        this.dataLiberacaoCredito = dataLiberacaoCredito;
    }

    public Uf getUfLiberacaoCredito() {
        return ufLiberacaoCredito;
    }

    public void setUfLiberacaoCredito(Uf ufLiberacaoCredito) {
        this.ufLiberacaoCredito = ufLiberacaoCredito;
    }

    public Municipio getMunicipioLiberacao() {
        return municipioLiberacao;
    }

    public void setMunicipioLiberacao(Municipio municipioLiberacao) {
        this.municipioLiberacao = municipioLiberacao;
    }

    public BigDecimal getValorCredito() {
        return valorCredito;
    }

    public void setValorCredito(BigDecimal valorCredito) {
        this.valorCredito = valorCredito;
    }

    public IndiceFinanceiro getSiglaIndiceFinaceiro() {
        return siglaIndiceFinaceiro;
    }

    public void setSiglaIndiceFinaceiro(IndiceFinanceiro siglaIndiceFinaceiro) {
        this.siglaIndiceFinaceiro = siglaIndiceFinaceiro;
    }

    public String getNumeroGrupoConsorcio() {
        return numeroGrupoConsorcio;
    }

    public void setNumeroGrupoConsorcio(String numeroGrupoConsorcio) {
        this.numeroGrupoConsorcio = numeroGrupoConsorcio;
    }

    public Long getNumeroCotaConsorcio() {
        return numeroCotaConsorcio;
    }

    public void setNumeroCotaConsorcio(Long numeroCotaConsorcio) {
        this.numeroCotaConsorcio = numeroCotaConsorcio;
    }

    public Boolean getIndicadorPenalidade() {
        return indicadorPenalidade;
    }

    public void setIndicadorPenalidade(Boolean indicadorPenalidade) {
        this.indicadorPenalidade = indicadorPenalidade;
    }

    public String getDescricaoPenalidade() {
        return descricaoPenalidade;
    }

    public void setDescricaoPenalidade(String descricaoPenalidade) {
        this.descricaoPenalidade = descricaoPenalidade;
    }

    public Boolean getIndicadorComissao() {
        return indicadorComissao;
    }

    public void setIndicadorComissao(Boolean indicadorComissao) {
        this.indicadorComissao = indicadorComissao;
    }

    public BigDecimal getPercentualComissao() {
        return percentualComissao;
    }

    public void setPercentualComissao(BigDecimal percentualComissao) {
        this.percentualComissao = percentualComissao;
    }

    public String getCpfCnpjDevedorFinanciado() {
        return cpfCnpjDevedorFinanciado;
    }

    public void setCpfCnpjDevedorFinanciado(String cpfCnpjDevedorFinanciado) {
        if (cpfCnpjDevedorFinanciado != null) {
            cpfCnpjDevedorFinanciado = cpfCnpjDevedorFinanciado.replaceAll("\\.", "").replaceAll("\\-", "")
                    .replaceAll("/", "");
        }
        this.cpfCnpjDevedorFinanciado = cpfCnpjDevedorFinanciado;
    }

    public String getNomeDevedorFinanciado() {
        return nomeDevedorFinanciado;
    }

    public void setNomeDevedorFinanciado(String nomeDevedorFinanciado) {
        this.nomeDevedorFinanciado = nomeDevedorFinanciado;
    }

    public String getCepDevedor() {
        return cepDevedor;
    }

    public void setCepDevedor(String cepDevedor) {
        if (cepDevedor != null) {
            cepDevedor = cepDevedor.replaceAll("\\-", "");
        }
        this.cepDevedor = cepDevedor;
    }

    public String getEnderecoDevedor() {
        return enderecoDevedor;
    }

    public void setEnderecoDevedor(String enderecoDevedor) {
        this.enderecoDevedor = enderecoDevedor;
    }

    public String getNumeroEnderecoDevedor() {
        return numeroEnderecoDevedor;
    }

    public void setNumeroEnderecoDevedor(String numeroEnderecoDevedor) {
        this.numeroEnderecoDevedor = numeroEnderecoDevedor;
    }

    public String getComplementoEnderecoDevedor() {
        return complementoEnderecoDevedor;
    }

    public void setComplementoEnderecoDevedor(String complementoEnderecoDevedor) {
        this.complementoEnderecoDevedor = complementoEnderecoDevedor;
    }

    public String getBairroDevedor() {
        return bairroDevedor;
    }

    public void setBairroDevedor(String bairroDevedor) {
        this.bairroDevedor = bairroDevedor;
    }

    public Municipio getMunicipioDevedor() {
        return municipioDevedor;
    }

    public void setMunicipioDevedor(Municipio municipioDevedor) {
        this.municipioDevedor = municipioDevedor;
    }

    public Uf getUfEnderecoDevedor() {
        return ufEnderecoDevedor;
    }

    public void setUfEnderecoDevedor(Uf ufEnderecoDevedor) {
        this.ufEnderecoDevedor = ufEnderecoDevedor;
    }

    public Integer getDddDevedor() {
        return dddDevedor;
    }

    public void setDddDevedor(Integer dddDevedor) {
        this.dddDevedor = dddDevedor;
    }

    public Integer getTelefoneDevedor() {
        return telefoneDevedor;
    }

    public void setTelefoneDevedor(Integer telefoneDevedor) {
        this.telefoneDevedor = telefoneDevedor;
    }

    public String getEmailDevedor() {
        return emailDevedor;
    }

    public void setEmailDevedor(String emailDevedor) {
        this.emailDevedor = emailDevedor;
    }

    public String getComentario() {
        return comentario;
    }

    public void setComentario(String comentario) {
        this.comentario = comentario;
    }

    public Integradora getIntegradora() {
        return integradora;
    }

    public void setIntegradora(Integradora integradora) {
        this.integradora = integradora;
    }

    public String getDescricaoEnderecoDevedor() {
        String aux = enderecoDevedor;
        if (complementoEnderecoDevedor != null) {
            aux += ", " + complementoEnderecoDevedor;
        }
        if (numeroEnderecoDevedor != null) {
            aux += ", " + numeroEnderecoDevedor;
        }
        if (bairroDevedor != null) {
            aux += ", " + bairroDevedor;
        }
        if (municipioDevedor != null) {
            aux += ", " + municipioDevedor.getDescricao();
        }
        if (ufEnderecoDevedor != null) {
            aux += " - " + ufEnderecoDevedor;
        }
        return aux;
    }

    public TipoVrg getTipoVrg() {
        return tipoVrg;
    }

    public void setTipoVrg(TipoVrg tipoVrg) {
        this.tipoVrg = tipoVrg;
    }

    public String getClausulaPenalVrg() {
        return clausulaPenalVrg;
    }

    public void setClausulaPenalVrg(String clausulaPenalVrg) {
        this.clausulaPenalVrg = clausulaPenalVrg;
    }

    public BigDecimal getValorVrg() {
        return valorVrg;
    }

    public void setValorVrg(BigDecimal valorVrg) {
        this.valorVrg = valorVrg;
    }

    public String getCpfCnpjCessaoDireito() {
        return cpfCnpjCessaoDireito;
    }

    public void setCpfCnpjCessaoDireito(String cpfCnpjCessaoDireito) {
        if (cpfCnpjCessaoDireito != null) {
            cpfCnpjCessaoDireito = cpfCnpjCessaoDireito.replaceAll("\\.", "").replaceAll("\\-", "").replaceAll("/", "");
        }
        this.cpfCnpjCessaoDireito = cpfCnpjCessaoDireito;
    }

    public String getNomeCessaoDireito() {
        return nomeCessaoDireito;
    }

    public void setNomeCessaoDireito(String nomeCessaoDireito) {
        this.nomeCessaoDireito = nomeCessaoDireito;
    }

    public String getChassiSubstituicao() {
        return chassiSubstituicao;
    }

    public void setChassiSubstituicao(String chassiSubstituicao) {
        this.chassiSubstituicao = chassiSubstituicao;
    }

    public SimNao getPossuiGarantidor() {
        return possuiGarantidor;
    }

    public void setPossuiGarantidor(SimNao possuiGarantidor) {
        this.possuiGarantidor = possuiGarantidor;
    }

    public String getCpfCnpjGarantidorFinanciado() {
        return cpfCnpjGarantidorFinanciado;
    }

    public void setCpfCnpjGarantidorFinanciado(String cpfCnpjGarantidorFinanciado) {
        this.cpfCnpjGarantidorFinanciado = cpfCnpjGarantidorFinanciado;
    }

    public String getNomeGarantidorFinanciado() {
        return nomeGarantidorFinanciado;
    }

    public void setNomeGarantidorFinanciado(String nomeGarantidorFinanciado) {
        this.nomeGarantidorFinanciado = nomeGarantidorFinanciado;
    }

    public String getCepGarantidor() {
        return cepGarantidor;
    }

    public void setCepGarantidor(String cepGarantidor) {
        this.cepGarantidor = cepGarantidor;
    }

    public String getEnderecoGarantidor() {
        return enderecoGarantidor;
    }

    public void setEnderecoGarantidor(String enderecoGarantidor) {
        this.enderecoGarantidor = enderecoGarantidor;
    }

    public String getNumeroEnderecoGarantidor() {
        return numeroEnderecoGarantidor;
    }

    public void setNumeroEnderecoGarantidor(String numeroEnderecoGarantidor) {
        this.numeroEnderecoGarantidor = numeroEnderecoGarantidor;
    }

    public String getComplementoEnderecoGarantidor() {
        return complementoEnderecoGarantidor;
    }

    public void setComplementoEnderecoGarantidor(String complementoEnderecoGarantidor) {
        this.complementoEnderecoGarantidor = complementoEnderecoGarantidor;
    }

    public String getBairroGarantidor() {
        return bairroGarantidor;
    }

    public void setBairroGarantidor(String bairroGarantidor) {
        this.bairroGarantidor = bairroGarantidor;
    }

    public Municipio getMunicipioGarantidor() {
        return municipioGarantidor;
    }

    public void setMunicipioGarantidor(Municipio municipioGarantidor) {
        this.municipioGarantidor = municipioGarantidor;
    }

    public Uf getUfEnderecoGarantidor() {
        return ufEnderecoGarantidor;
    }

    public void setUfEnderecoGarantidor(Uf ufEnderecoGarantidor) {
        this.ufEnderecoGarantidor = ufEnderecoGarantidor;
    }

    public Integer getDddGarantidor() {
        return dddGarantidor;
    }

    public void setDddGarantidor(Integer dddGarantidor) {
        this.dddGarantidor = dddGarantidor;
    }

    public Integer getTelefoneGarantidor() {
        return telefoneGarantidor;
    }

    public void setTelefoneGarantidor(Integer telefoneGarantidor) {
        this.telefoneGarantidor = telefoneGarantidor;
    }

    public String getEmailGarantidor() {
        return emailGarantidor;
    }

    public void setEmailGarantidor(String emailGarantidor) {
        this.emailGarantidor = emailGarantidor;
    }

    public String getDescricaoEnderecoGarantidor() {
        String aux = enderecoGarantidor;
        if (complementoEnderecoGarantidor != null) {
            aux += ", " + complementoEnderecoGarantidor;
        }
        if (numeroEnderecoGarantidor != null) {
            aux += ", " + numeroEnderecoGarantidor;
        }
        if (bairroGarantidor != null) {
            aux += ", " + bairroGarantidor;
        }
        if (municipioGarantidor != null) {
            aux += ", " + municipioGarantidor.getDescricao();
        }
        if (ufEnderecoGarantidor != null) {
            aux += " - " + ufEnderecoGarantidor;
        }
        return aux;
    }
}
