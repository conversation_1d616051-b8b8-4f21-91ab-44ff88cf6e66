package com.registrocontrato.registro.entity;

import java.util.Date;

import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.Lob;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

import org.hibernate.envers.Audited;

import com.registrocontrato.infra.entity.BaseEntity;
import org.hibernate.envers.NotAudited;

@Entity
@Audited
@Table(schema = "registro")
public class RegistroEnvio extends BaseEntity {

	private static final long serialVersionUID = 1L;

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private Long id;

	@Lob
	@NotAudited
	private String mensagem;

	private String usuario;

	@Temporal(TemporalType.TIMESTAMP)
	private Date data;

	@JoinColumn
	@ManyToOne(cascade = {}, fetch = FetchType.LAZY)
	private Contrato contrato;

	@JoinColumn
	@ManyToOne(cascade = {}, fetch = FetchType.LAZY)
	private ContratoRsng contratoRsng;

	public RegistroEnvio() {
	}

	public RegistroEnvio(Contrato contrato, String username, String multiLineAuditString) {
		this.contrato = contrato;
		this.usuario = username;
		this.mensagem = multiLineAuditString;
		this.data = new Date();
	}

	public RegistroEnvio(ContratoModel contrato, String username, String multiLineAuditString) {
		if (contrato instanceof Contrato) {
			this.contrato = (Contrato) contrato;
		} else {
			this.contratoRsng = (ContratoRsng) contrato;
		}
		this.usuario = username;
		this.mensagem = multiLineAuditString;
		this.data = new Date();
	}

	public RegistroEnvio(ContratoRsng contratoRsng, String username, String multiLineAuditString) {
		this.contratoRsng = contratoRsng;
		this.usuario = username;
		this.mensagem = multiLineAuditString;
		this.data = new Date();
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public String getMensagem() {
		return mensagem;
	}

	public void setMensagem(String mensagem) {
		this.mensagem = mensagem;
	}

	public String getUsuario() {
		return usuario;
	}

	public void setUsuario(String usuario) {
		this.usuario = usuario;
	}

	public Date getData() {
		return data;
	}

	public void setData(Date data) {
		this.data = data;
	}

	public Contrato getContrato() {
		return contrato;
	}

	public void setContrato(Contrato contrato) {
		this.contrato = contrato;
	}

	public ContratoRsng getContratoRsng() {
		return contratoRsng;
	}

	public void setContratoRsng(ContratoRsng contratoRsng) {
		this.contratoRsng = contratoRsng;
	}
}
