package com.registrocontrato.registro.entity;

import com.registrocontrato.registro.enums.SituacaoRsng;
import org.hibernate.envers.Audited;
import org.hibernate.envers.ModificationStore;
import org.hibernate.envers.NotAudited;

import javax.persistence.*;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Entity
@Audited(modStore = ModificationStore.FULL)
@Table(schema = "rsng", name = "contrato")
public class ContratoRsng extends ContratoModel {

    private Long protocoloPlaceconRsng;

    @Temporal(TemporalType.TIMESTAMP)
    private Date dataCadastroRsng;

    @Temporal(TemporalType.TIMESTAMP)
    private Date dataConclusaoRsng;

    @Enumerated(EnumType.STRING)
    private SituacaoRsng situacao;

    @OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY, orphanRemoval = true, mappedBy = "contratoRsng")
    private List<VeiculoRsng> veiculos = new ArrayList<>();

    @OrderBy("id asc")
    @OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY, orphanRemoval = true, mappedBy = "contratoRsng")
    private List<RegistroEnvio> envios = new ArrayList<>();

    @OneToOne(cascade = {}, fetch = FetchType.EAGER)
    private Contrato contrato;

    @JoinColumn
    @ManyToOne(cascade = {}, fetch = FetchType.LAZY)
    private Cobranca cobranca;

    private Boolean registroAutomaticoDetran;

    private String cpfCnpjUsuarioResponsavel;

    public Long getProtocoloPlaceconRsng() {
        return protocoloPlaceconRsng;
    }

    public void setProtocoloPlaceconRsng(Long protocoloPlaceconRsng) {
        this.protocoloPlaceconRsng = protocoloPlaceconRsng;
    }

    public Date getDataCadastroRsng() {
        return dataCadastroRsng;
    }

    public void setDataCadastroRsng(Date dataCadastroRsng) {
        this.dataCadastroRsng = dataCadastroRsng;
    }

    public Date getDataConclusaoRsng() {
        return dataConclusaoRsng;
    }

    public void setDataConclusaoRsng(Date dataConclusaoRsng) {
        this.dataConclusaoRsng = dataConclusaoRsng;
    }

    public SituacaoRsng getSituacao() {
        return situacao;
    }

    public void setSituacao(SituacaoRsng situacao) {
        this.situacao = situacao;
    }

    public List<VeiculoRsng> getVeiculos() {
        return veiculos;
    }

    public List<VeiculoRsng> getVeiculosLimitados() {
        return veiculos.size() > 3 ? veiculos.subList(0, 3) : veiculos;
    }

    public void setVeiculos(List<VeiculoRsng> veiculos) {
        this.veiculos = veiculos;
    }

    public int getVeiculosSize() {
        return getVeiculos().size();
    }

    public List<RegistroEnvio> getEnvios() {
        return envios;
    }

    public void setEnvios(List<RegistroEnvio> envios) {
        this.envios = envios;
    }

    public int getVeiculosComErro() {
        int quantidade = 0;
        for (VeiculoRsng v : getVeiculos()) {
            if (v.getMensagemErro() != null)
                quantidade++;
        }
        return quantidade;
    }

    public Contrato getContrato() {
        return contrato;
    }

    public void setContrato(Contrato contrato) {
        this.contrato = contrato;
    }

    public Boolean getRegistroAutomaticoDetran() {
        return registroAutomaticoDetran;
    }

    public void setRegistroAutomaticoDetran(Boolean registroAutomaticoDetran) {
        this.registroAutomaticoDetran = registroAutomaticoDetran;
    }

    public String getCpfCnpjUsuarioResponsavel() {
        return cpfCnpjUsuarioResponsavel;
    }

    public void setCpfCnpjUsuarioResponsavel(String cpfCnpjUsuarioResponsavel) {
        this.cpfCnpjUsuarioResponsavel = cpfCnpjUsuarioResponsavel;
    }

    public Cobranca getCobranca() {
        return cobranca;
    }

    public void setCobranca(Cobranca cobranca) {
        this.cobranca = cobranca;
    }
}
