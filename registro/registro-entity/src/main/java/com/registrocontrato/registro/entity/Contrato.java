package com.registrocontrato.registro.entity;

import com.registrocontrato.infra.entity.*;
import com.registrocontrato.infra.entity.SimNao;
import com.registrocontrato.registro.enums.Situacao;
import com.registrocontrato.registro.enums.SituacaoFinanceira;
import com.registrocontrato.registro.enums.TipoBaixaContrato;
import com.registrocontrato.registro.enums.TipoContrato;
import org.hibernate.annotations.Formula;
import org.hibernate.envers.Audited;
import org.hibernate.envers.ModificationStore;
import org.hibernate.envers.NotAudited;

import javax.persistence.*;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Entity
@Audited(modStore = ModificationStore.FULL)
@Table(schema = "registro")
public class Contrato extends ContratoModel {

    private static final long serialVersionUID = 1L;

    @Formula("(select c.id from registro.contrato c where c.numero_registro_eletronico = numero_registro_eletronico_origem)")
    @NotAudited
    private Long idRegistroOrigem;

    // registro contrato
    private Long numeroRegistroEletronico;

    private Long numeroRegistroEletronicoOrigem;

    @Column(columnDefinition = "TEXT")
    private String motivoBaixa;

    @Column(columnDefinition = "TEXT")
    private String motivoAnulacao;

    @Enumerated(EnumType.STRING)
    private TipoBaixaContrato tipoBaixaContrato;

    @NotAudited
    @OrderBy("id asc")
    @OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY, orphanRemoval = true, mappedBy = "contrato")
    private List<RegistroEnvio> envios = new ArrayList<>();

    // anexos
    @OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY, orphanRemoval = true, mappedBy = "contrato")
    private List<Anexo> anexos = new ArrayList<>();

    // Situacao do Contrato
    @Enumerated(EnumType.STRING)
    private Situacao situacao;

    // Auditoria
    @Enumerated(EnumType.STRING)
    private SimNao aprovadoAuditoria;

    @OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY, orphanRemoval = true, mappedBy = "contrato")
    private List<RegistroAuditoria> auditorias = new ArrayList<>();

    // Assinatura
    @Enumerated(EnumType.STRING)
    private SimNao assinado;

    @OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY, orphanRemoval = true, mappedBy = "contrato")
    private List<Assinatura> assinaturas = new ArrayList<>();

    // Financeiro
    @Enumerated(EnumType.STRING)
    private SituacaoFinanceira situacaoFinanceira;

	@JoinColumn
	@ManyToOne(cascade = {}, fetch = FetchType.LAZY)
	private Cobranca cobranca;

    @Temporal(TemporalType.TIMESTAMP)
    private Date dataCadastro;

    @Temporal(TemporalType.TIMESTAMP)
    private Date dataConclusaoDETRAN;

    @JoinColumn
    @ManyToOne(cascade = {}, fetch = FetchType.LAZY)
    private ArquivoRemessa arquivoRemessa;

    @JoinColumn
    @ManyToOne(cascade = {}, fetch = FetchType.LAZY)
    private Livro livro;

    private Long idProcessoB3;

    private Long idProcessoSENDB3;

    private Integer idTransacaoSENDB3;

    @OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY, orphanRemoval = true, mappedBy = "contrato")
    private List<Veiculo> veiculos = new ArrayList<>();

    //TODO: ajustar o nome para RSNG
    @OneToOne(cascade = {CascadeType.PERSIST, CascadeType.MERGE}, fetch = FetchType.EAGER, mappedBy = "contrato")
    private ItensRemessaB3 itensRemessaB3;

    @OneToOne(cascade = {CascadeType.PERSIST, CascadeType.MERGE}, fetch = FetchType.EAGER, mappedBy = "contrato")
    private ContratoRsng contratoRsng;

    public int getVeiculosComErro() {
        int quantidade = 0;
        for (Veiculo v : getVeiculos()) {
            if (v.getMensagemRetorno() != null && !v.getMensagemRetorno().getSucesso())
                quantidade++;
        }
        return quantidade;
    }

    public int getVeiculosComSucesso() {
        int quantidade = 0;
        for (Veiculo v : getVeiculos()) {
            if (v.getMensagemRetorno() != null && v.getMensagemRetorno().getSucesso())
                quantidade++;
        }
        return quantidade;
    }

    public int getVeiculosSize() {
        return getVeiculos().size();
    }

    public boolean isCessaoDireito() {
        return getTipoContrato() == TipoContrato.CESSAO_DIREITO_CREDOR || getTipoContrato() == TipoContrato.CESSAO_DIREITO_DEVEDOR;
    }

    public String getMotivoAnulacao() {
        return motivoAnulacao;
    }

    public void setMotivoAnulacao(String motivoAnulacao) {
        this.motivoAnulacao = motivoAnulacao;
    }

    public List<Anexo> getAnexos() {
        return anexos;
    }

    public void setAnexos(List<Anexo> anexos) {
        this.anexos = anexos;
    }

    public Situacao getSituacao() {
        return situacao;
    }

    public void setSituacao(Situacao situacao) {
        this.situacao = situacao;
    }

    public SituacaoFinanceira getSituacaoFinanceira() {
        return situacaoFinanceira;
    }

    public void setSituacaoFinanceira(SituacaoFinanceira situacaoFinanceira) {
        this.situacaoFinanceira = situacaoFinanceira;
    }

    public SimNao getAssinado() {
        return assinado;
    }

    public void setAssinado(SimNao assinado) {
        this.assinado = assinado;
    }

    public SimNao getAprovadoAuditoria() {
        return aprovadoAuditoria;
    }

    public void setAprovadoAuditoria(SimNao aprovadoAuditoria) {
        this.aprovadoAuditoria = aprovadoAuditoria;
    }

    public List<RegistroAuditoria> getAuditorias() {
        return auditorias;
    }

    public void setAuditorias(List<RegistroAuditoria> auditorias) {
        this.auditorias = auditorias;
    }

    public List<Assinatura> getAssinaturas() {
        return assinaturas;
    }

    public void setAssinaturas(List<Assinatura> assinaturas) {
        this.assinaturas = assinaturas;
    }

    public Cobranca getCobranca() {
        return cobranca;
    }

    public void setCobranca(Cobranca cobranca) {
        this.cobranca = cobranca;
    }

    public Date getDataCadastro() {
        return dataCadastro;
    }

    public void setDataCadastro(Date dataCadastro) {
        this.dataCadastro = dataCadastro;
    }

    public Date getDataConclusaoDETRAN() {
        return dataConclusaoDETRAN;
    }

    public void setDataConclusaoDETRAN(Date dataConclusaoDETRAN) {
        this.dataConclusaoDETRAN = dataConclusaoDETRAN;
    }

    public String getTipoTransacao() {
        if (!getAlteracao()) {
            if (getNumeroAditivoContrato() == null) {
                return "1";
            }
            return "3";
        }
        if (getNumeroAditivoContrato() == null) {
            return "2";
        }
        return "4";
    }

    public Boolean getPossuiImagemAnexo() {
        if (anexos != null) {
            for (Anexo a : anexos) {
                if (!a.isPdf()) {
                    return true;
                }
            }
            return false;
        }
        return false;
    }

    public ArquivoRemessa getArquivoRemessa() {
        return arquivoRemessa;
    }

    public void setArquivoRemessa(ArquivoRemessa arquivoRemessa) {
        this.arquivoRemessa = arquivoRemessa;
    }

    public Long getIdRegistroOrigem() {
        return idRegistroOrigem;
    }

    public Livro getLivro() {
        return livro;
    }

    public void setLivro(Livro livro) {
        this.livro = livro;
    }

    public Long getIdProcessoB3() {
        return idProcessoB3;
    }

    public void setIdProcessoB3(Long idProcessoB3) {
        this.idProcessoB3 = idProcessoB3;
    }

    public TipoBaixaContrato getTipoBaixaContrato() {
        return tipoBaixaContrato;
    }

    public void setTipoBaixaContrato(TipoBaixaContrato tipoBaixaContrato) {
        this.tipoBaixaContrato = tipoBaixaContrato;
    }

    public List<RegistroEnvio> getEnvios() {
        return envios;
    }

    public void setEnvios(List<RegistroEnvio> envios) {
        this.envios = envios;
    }

    public Long getIdProcessoSENDB3() {
        return idProcessoSENDB3;
    }

    public void setIdProcessoSENDB3(Long idProcessoSENDB3) {
        this.idProcessoSENDB3 = idProcessoSENDB3;
    }

    public Integer getIdTransacaoSENDB3() {
        return idTransacaoSENDB3;
    }

    public void setIdTransacaoSENDB3(Integer idTransacaoSENDB3) {
        this.idTransacaoSENDB3 = idTransacaoSENDB3;
    }

    public ItensRemessaB3 getItensRemessaB3() {
        return itensRemessaB3;
    }

    public void setItensRemessaB3(ItensRemessaB3 itensRemessaB3) {
        this.itensRemessaB3 = itensRemessaB3;
    }

    public ContratoRsng getContratoRsng() {
        return contratoRsng;
    }

    public void setContratoRsng(ContratoRsng contratoRsng) {
        this.contratoRsng = contratoRsng;
    }

    public void setIdRegistroOrigem(Long idRegistroOrigem) {
        this.idRegistroOrigem = idRegistroOrigem;
    }

    public Long getNumeroRegistroEletronico() {
        return numeroRegistroEletronico;
    }

    public void setNumeroRegistroEletronico(Long numeroRegistroEletronico) {
        this.numeroRegistroEletronico = numeroRegistroEletronico;
    }

    public Long getNumeroRegistroEletronicoOrigem() {
        return numeroRegistroEletronicoOrigem;
    }

    public void setNumeroRegistroEletronicoOrigem(Long numeroRegistroEletronicoOrigem) {
        this.numeroRegistroEletronicoOrigem = numeroRegistroEletronicoOrigem;
    }

    public String getMotivoBaixa() {
        return motivoBaixa;
    }

    public void setMotivoBaixa(String motivoBaixa) {
        this.motivoBaixa = motivoBaixa;
    }

    public List<Veiculo> getVeiculos() {
        return veiculos;
    }

    public List<Veiculo> getVeiculosLimitados() {
        return veiculos.size() > 3 ? veiculos.subList(0, 3) : veiculos;
    }

    public void setVeiculos(List<Veiculo> veiculos) {
        this.veiculos = veiculos;
    }
}
