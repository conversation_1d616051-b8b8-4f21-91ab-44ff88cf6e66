package com.registrocontrato.registro.entity;

import com.registrocontrato.infra.entity.BaseEntity;
import com.registrocontrato.infra.entity.Uf;
import com.registrocontrato.registro.enums.OrigemCobranca;
import com.registrocontrato.registro.enums.TipoCobranca;
import com.registrocontrato.registro.enums.TipoPreco;
import org.hibernate.envers.Audited;

import javax.persistence.*;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Entity
@Audited
@Table(schema = "registro")
public class Credenciamento extends BaseEntity implements ValoresCobranca {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Temporal(TemporalType.DATE)
    private Date dataInicio;

    @Temporal(TemporalType.DATE)
    private Date dataFim;

    @Enumerated(EnumType.STRING)
    private Uf uf;

    private BigDecimal valorDETRAN;

    private BigDecimal valorAditivo;

    private BigDecimal valorCredenciada;

    private BigDecimal valorTotal;

    private BigDecimal valorTotalAditivo;

    private BigDecimal valorCredenciadaIntegraMais;

    private BigDecimal valorTotalIntegraMais;

    private BigDecimal valorTotalIntegraMaisAditivo;

    @Enumerated(EnumType.STRING)
    private OrigemCobranca origemCobranca;

    @Enumerated(EnumType.STRING)
    private TipoCobranca tipoCobranca;

    @Enumerated(EnumType.STRING)
    private TipoPreco tipoPreco;

    @OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY, orphanRemoval = true, mappedBy = "credenciamento")
    private List<PrecoComposto> precosCompostos = new ArrayList<PrecoComposto>();

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Date getDataInicio() {
        return dataInicio;
    }

    public void setDataInicio(Date dataInicio) {
        this.dataInicio = dataInicio;
    }

    public Date getDataFim() {
        return dataFim;
    }

    public void setDataFim(Date dataFim) {
        this.dataFim = dataFim;
    }

    public Uf getUf() {
        return uf;
    }

    public void setUf(Uf uf) {
        this.uf = uf;
    }

    public BigDecimal getValorDETRAN() {
        return valorDETRAN;
    }

    public void setValorDETRAN(BigDecimal valorDETRAN) {
        this.valorDETRAN = valorDETRAN;
    }

    public BigDecimal getValorCredenciada() {
        return valorCredenciada;
    }

    public void setValorCredenciada(BigDecimal valorCredenciada) {
        this.valorCredenciada = valorCredenciada;
    }

    public BigDecimal getValorTotal() {
        return valorTotal;
    }

    public BigDecimal getValorTotalComposto() {
        if (tipoPreco == TipoPreco.COMPOSTO) {
            return precosCompostos.stream().map(PrecoComposto::getValorTotal).reduce(BigDecimal.ZERO, BigDecimal::add);
        }
        return valorTotal;
    }

    public void setValorTotal(BigDecimal valorTotal) {
        this.valorTotal = valorTotal;
    }

    public OrigemCobranca getOrigemCobranca() {
        return origemCobranca;
    }

    public void setOrigemCobranca(OrigemCobranca origemCobranca) {
        this.origemCobranca = origemCobranca;
    }

    public TipoCobranca getTipoCobranca() {
        return tipoCobranca;
    }

    public void setTipoCobranca(TipoCobranca tipoCobranca) {
        this.tipoCobranca = tipoCobranca;
    }

    public TipoPreco getTipoPreco() {
        return tipoPreco;
    }

    public void setTipoPreco(TipoPreco tipoPreco) {
        this.tipoPreco = tipoPreco;
    }

    public List<PrecoComposto> getPrecosCompostos() {
        return precosCompostos;
    }

    public void setPrecosCompostos(List<PrecoComposto> precosCompostos) {
        this.precosCompostos = precosCompostos;
    }

    public BigDecimal getValorCredenciadaIntegraMais() {
        return valorCredenciadaIntegraMais;
    }

    public void setValorCredenciadaIntegraMais(BigDecimal valorCredenciadaIntegraMais) {
        this.valorCredenciadaIntegraMais = valorCredenciadaIntegraMais;
    }

    public BigDecimal getValorTotalIntegraMais() {
        return valorTotalIntegraMais;
    }

    public void setValorTotalIntegraMais(BigDecimal valorTotalIntegraMais) {
        this.valorTotalIntegraMais = valorTotalIntegraMais;
    }

    public boolean isPossivelDesconto() {
        return origemCobranca == OrigemCobranca.CREDENCIADA;
    }

    public BigDecimal getValorAditivo() {
        return valorAditivo;
    }

    public void setValorAditivo(BigDecimal valorAditivo) {
        this.valorAditivo = valorAditivo;
    }

    public BigDecimal getValorTotalAditivo() {
        return valorTotalAditivo;
    }

    public void setValorTotalAditivo(BigDecimal valorTotalAditivo) {
        this.valorTotalAditivo = valorTotalAditivo;
    }

    public BigDecimal getValorTotalIntegraMaisAditivo() {
        return valorTotalIntegraMaisAditivo;
    }

    public void setValorTotalIntegraMaisAditivo(BigDecimal valorTotalIntegraMaisAditivo) {
        this.valorTotalIntegraMaisAditivo = valorTotalIntegraMaisAditivo;
    }

}
