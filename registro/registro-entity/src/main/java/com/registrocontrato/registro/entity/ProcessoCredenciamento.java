package com.registrocontrato.registro.entity;

import com.registrocontrato.infra.entity.BaseEntity;
import com.registrocontrato.infra.entity.Financeira;
import com.registrocontrato.infra.entity.Uf;
import com.registrocontrato.registro.enums.SituacaoProcessoCredenciamento;
import com.registrocontrato.registro.enums.TipoDocumentoCredenciamento;
import org.hibernate.envers.Audited;

import javax.persistence.*;
import java.util.List;

@Entity
@Audited
@Table(schema = "registro")
public class ProcessoCredenciamento extends BaseEntity {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Enumerated(EnumType.STRING)
    private Uf uf;

    @Enumerated(EnumType.STRING)
    private SituacaoProcessoCredenciamento situacaoProcessoCredenciamento;

    @ManyToOne
    @JoinColumn(name = "financeira_id")
    private Financeira financeira;

    @OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY, orphanRemoval = true, mappedBy = "processoCredenciamento")
    private List<DocumentoCredenciamento> documentos;

    public Financeira getFinanceira() {
        return financeira;
    }

    public void setFinanceira(Financeira financeira) {
        this.financeira = financeira;
    }

    public Uf getUf() {
        return uf;
    }

    public void setUf(Uf uf) {
        this.uf = uf;
    }

    public SituacaoProcessoCredenciamento getSituacaoProcessoCredenciamento() {
        return situacaoProcessoCredenciamento;
    }

    public void setSituacaoProcessoCredenciamento(SituacaoProcessoCredenciamento situacaoProcessoCredenciamento) {
        this.situacaoProcessoCredenciamento = situacaoProcessoCredenciamento;
    }

    public List<DocumentoCredenciamento> getDocumentos() {
        return documentos;
    }

    public void setDocumentos(List<DocumentoCredenciamento> documentos) {
        this.documentos = documentos;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getId() {
        return id;
    }
}
