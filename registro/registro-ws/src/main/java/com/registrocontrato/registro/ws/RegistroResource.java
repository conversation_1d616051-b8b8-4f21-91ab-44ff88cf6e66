package com.registrocontrato.registro.ws;

import com.registrocontrato.commons.ws.dto.*;
import com.registrocontrato.commons.ws.mapper.ContratoMapper;
import com.registrocontrato.infra.entity.Financeira;
import com.registrocontrato.infra.exception.ServiceException;
import com.registrocontrato.infra.service.audit.AuditTransaction;
import com.registrocontrato.registro.entity.Anexo;
import com.registrocontrato.registro.entity.ArquivoRemessa;
import com.registrocontrato.registro.entity.Contrato;
import com.registrocontrato.registro.entity.Veiculo;
import com.registrocontrato.registro.enums.TipoContrato;
import com.registrocontrato.registro.enums.TipoVeiculo;
import com.registrocontrato.registro.service.ContratoService;
import com.registrocontrato.commons.ws.rsng.RemessaService;
import com.registrocontrato.registro.service.TemplateRemessaService;
import com.registrocontrato.registro.service.dto.ValidacaoEnum;
import com.registrocontrato.seguranca.entity.Perfil;
import com.registrocontrato.seguranca.entity.Usuario;
import com.registrocontrato.seguranca.repository.FinanceiraRepository;
import com.registrocontrato.seguranca.service.UsuarioService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import org.apache.commons.lang3.RandomStringUtils;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.thymeleaf.TemplateEngine;

import javax.validation.Valid;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.util.*;

@RestController
@RequestMapping(value = "/api/v1/contrato")
public class RegistroResource {

    private static final String COMPROVANTE_COMPLETO = "Buscar Arquivo do Comprovante Completo de Registro";
    private static final String LISTAR_TIPO_DE_CONTRATO = "Listar Tipo de Contrato";
    private static final String CONSULTAR_CONTRATO_AGENTE_FINANCEIRO = "Consultar um Contrato do Agente Financeiro";
    private static final String CONSULTAR_CONTRATO_AGENTE_FINANCEIRO_CHASSI_GRAVAME = "Consultar último Contrato do Agente Financeiro por chassi e gravame";
    private static final String CONSULTAR_CONTRATO_AGENTE_FINANCEIRO_QUERY = "Consultar um Contrato do Agente Financeiro Por Query URL";
    private static final String CONSULTAR_REGISTRO = "Consultar um Registro Eletrônico de Contrato";
    private static final String ALTERAR_ADITIVO = "Alterar Aditivo do Registro Eletrônico de Contrato";
    private static final String ADITIVAR_REGISTRO = "Aditivar Registro Eletrônico de Contrato";
    private static final String ALTERAR_REGISTRO = "Alterar Registro Eletrônico de Contrato";
    private static final String INCLUIR_REGISTRO = "Incluir Registro Eletrônico de Contrato";
    private static final String BAIXAR_REGISTRO = "Baixar Registro Eletrônico de Contrato";
    private static final String UPLOAD = "Enviar arquivo de um Registro Eletrônico de Contrato";
    private static final String UPLOAD_BASE64 = "Enviar arquivo de um Registro Eletrônico de Contrato em Base64";
    private static final String LISTAR_ESPECIES = "Listar Espécies";

    private final ContratoMapper mapper;

    private final ContratoService contratoService;

    private final UsuarioService usuarioService;

    private final RemessaService remessaService;

    private final TemplateRemessaService templateService;

    private final FinanceiraRepository financeiraRepository;

    private final TemplateEngine templateEngine;

    public RegistroResource(ContratoMapper mapper, ContratoService contratoService, UsuarioService usuarioService, RemessaService remessaService, TemplateRemessaService templateService, FinanceiraRepository financeiraRepository, TemplateEngine templateEngine) {
        this.mapper = mapper;
        this.contratoService = contratoService;
        this.usuarioService = usuarioService;
        this.remessaService = remessaService;
        this.templateService = templateService;
        this.financeiraRepository = financeiraRepository;
        this.templateEngine = templateEngine;
    }

    @GetMapping(value = "/comprovanteRegistro/{numeroRegistroEletronico}", produces = MediaType.APPLICATION_PDF_VALUE)
    public ResponseEntity<ByteArrayResource> comprovanteRegistroCompleto(
            @PathVariable("numeroRegistroEletronico") Long numeroRegistroEletronico) throws com.itextpdf.text.DocumentException {

        String currentPrincipal = SecurityContextHolder.getContext().getAuthentication().getName();
        Contrato contrato = contratoService.findByNumeroRegistroEletronico(numeroRegistroEletronico, currentPrincipal);

        Map<String, Object> variables = new HashMap<>();
        variables.put("titulo", "Comprovante de Registro Eletrônico");
        variables.put("contrato", contrato);

        ByteArrayResource pdfResource = contratoService.generatePdfComprovante(variables);

        HttpHeaders headers = new HttpHeaders();
        headers.add(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=comprovante.pdf");

        return ResponseEntity.ok()
                .headers(headers)
                .contentType(MediaType.APPLICATION_PDF)
                .body(pdfResource);
    }

    @AuditTransaction(action = BAIXAR_REGISTRO)
    @Operation(description = BAIXAR_REGISTRO)
    @ApiResponse(content = @Content(mediaType = "application/json", schema = @Schema(implementation = RetornoBaixaDetranRestDTO.class)))
    @PostMapping(value = "/baixar", produces = MediaType.APPLICATION_JSON_UTF8_VALUE, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public RetornoBaixaDetranRestDTO baixarContrato(@Valid @RequestBody BaixaContratoDTO baixa) {
        String currentPrincipal = SecurityContextHolder.getContext().getAuthentication().getName();

        RetornoBaixaDetranRestDTO retornoRest = null;
        Long numeroRegistroEletronico = baixa.getNumeroRegistroEletronico();
        try {
            contratoService.baixar(numeroRegistroEletronico, currentPrincipal);
            retornoRest = new RetornoBaixaDetranRestDTO(numeroRegistroEletronico, BAIXAR_REGISTRO);
            retornoRest.setNumeroRegistroEletronico(numeroRegistroEletronico);
            retornoRest.setCodResposta(ValidacaoEnum.SUCESSO.getCodigo());
            retornoRest.setMsgResposta("Contrato baixado com sucesso");
        } catch (ServiceException e) {
            retornoRest = new RetornoBaixaDetranRestDTO(numeroRegistroEletronico, BAIXAR_REGISTRO);
            retornoRest.setCodResposta(ValidacaoEnum.ERRO.getCodigo());
            retornoRest.setMsgResposta(e.getMessage());
        }
        return retornoRest;
    }

    @AuditTransaction(action = INCLUIR_REGISTRO)
    @Operation(description = INCLUIR_REGISTRO)
    @ApiResponse(content = @Content(mediaType = "application/json", schema = @Schema(implementation = RetornoRestDTO.class)))
    @PostMapping(value = "/incluir", produces = MediaType.APPLICATION_JSON_UTF8_VALUE, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public List<RetornoRestDTO> incluirContrato(@Valid @RequestBody ContratoDTO contrato) {

        String currentPrincipal = SecurityContextHolder.getContext().getAuthentication().getName();

        Financeira financeira = financeiraRepository.findByDocumento(contrato.getCnpjAgenteFinanceiro());
        if (financeira.getIntegradora() != null) {
            IntegradoraDto integradoraDto = IntegradoraDto.fromIntegradora(financeira.getIntegradora());
            contrato.setIntegradora(integradoraDto);
        }

        Contrato entity = mapper.convertToEntity(contrato);

        List<RetornoRestDTO> retorno = new ArrayList<>();
        RetornoRestDTO retornoRest = null;

        try {
            entity = contratoService.transmitir(entity, currentPrincipal);
            for (Veiculo v : entity.getVeiculos()) {
                retornoRest = new RetornoRestDTO(contrato.getCnpjAgenteFinanceiro(), v.getNumeroChassi(), INCLUIR_REGISTRO);
                retornoRest.setCodRespostaDetran((v.getMensagemRetorno() != null && v.getMensagemRetorno().getCodigo() != null) ? v.getMensagemRetorno().getCodigo() : null);
                retornoRest.setMsgRespostaDetran(v.getMensagemRetornoDetalhada() != null ? v.getMensagemRetornoDetalhada() : null);
                retornoRest.setNumeroRegistroEletronico(entity.getNumeroRegistroEletronico());
                retorno.add(retornoRest);
            }
        } catch (ServiceException e) {
            for (Veiculo v : entity.getVeiculos()) {
                retornoRest = new RetornoRestDTO(contrato.getCnpjAgenteFinanceiro(), v.getNumeroChassi(), INCLUIR_REGISTRO);
                retornoRest.setCodResposta(ValidacaoEnum.ERRO.getCodigo());
                retornoRest.setMsgResposta(e.getMessage());
                retorno.add(retornoRest);
            }
        }
        return retorno;
    }

    @AuditTransaction(action = ALTERAR_REGISTRO)
    @Operation(description = ALTERAR_REGISTRO)
    @ApiResponse(content = @Content(mediaType = "application/json", schema = @Schema(implementation = RetornoRestDTO.class)))
    @PostMapping(value = "/alterar", produces = MediaType.APPLICATION_JSON_UTF8_VALUE, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public List<RetornoRestDTO> alterarContrato(@Valid @RequestBody AlteracaoContratoDTO contrato) {
        String currentPrincipal = SecurityContextHolder.getContext().getAuthentication().getName();

        Contrato entity = mapper.convertToEntity(contrato);

        List<RetornoRestDTO> retorno = new ArrayList<>();
        RetornoRestDTO retornoRest = null;

        try {
            entity = contratoService.transmitirAlteracao(entity, currentPrincipal);
            for (Veiculo v : entity.getVeiculos()) {
                retornoRest = new RetornoRestDTO(contrato.getCnpjAgenteFinanceiro(), v.getNumeroChassi(), ALTERAR_REGISTRO);
                retornoRest.setCodRespostaDetran(v.getMensagemRetorno().getCodigo());
                retornoRest.setMsgRespostaDetran(v.getMensagemRetorno().getDescricao());
                retornoRest.setNumeroRegistroEletronico(entity.getNumeroRegistroEletronico());
                retorno.add(retornoRest);
            }
        } catch (ServiceException e) {
            for (Veiculo v : entity.getVeiculos()) {
                retornoRest = new RetornoRestDTO(contrato.getCnpjAgenteFinanceiro(), v.getNumeroChassi(), ALTERAR_REGISTRO);
                retornoRest.setCodResposta(ValidacaoEnum.ERRO.getCodigo());
                retornoRest.setMsgResposta(e.getMessage());
                retorno.add(retornoRest);
            }
        }
        return retorno;
    }

    @AuditTransaction(action = ADITIVAR_REGISTRO)
    @Operation(description = ADITIVAR_REGISTRO)
    @ApiResponse(content = @Content(mediaType = "application/json", schema = @Schema(implementation = RetornoRestDTO.class)))
    @PostMapping(value = "/aditivar", produces = MediaType.APPLICATION_JSON_UTF8_VALUE, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public List<RetornoRestDTO> aditivarContrato(@Valid @RequestBody AditivoContratoDTO contrato) {
        String currentPrincipal = SecurityContextHolder.getContext().getAuthentication().getName();

        Contrato entity = mapper.convertToEntity(contrato);
        List<RetornoRestDTO> retorno = new ArrayList<>();
        RetornoRestDTO retornoRest = null;

        try {
            entity = contratoService.transmitir(entity, currentPrincipal);
            for (Veiculo v : entity.getVeiculos()) {
                retornoRest = new RetornoRestDTO(contrato.getCnpjAgenteFinanceiro(), v.getNumeroChassi(), INCLUIR_REGISTRO);
                retornoRest.setCodRespostaDetran(v.getMensagemRetorno().getCodigo());
                retornoRest.setMsgRespostaDetran(v.getMensagemRetorno().getDescricao());
                retornoRest.setNumeroRegistroEletronico(entity.getNumeroRegistroEletronico());
                retorno.add(retornoRest);
            }
        } catch (ServiceException e) {
            for (Veiculo v : entity.getVeiculos()) {
                retornoRest = new RetornoRestDTO(contrato.getCnpjAgenteFinanceiro(), v.getNumeroChassi(), INCLUIR_REGISTRO);
                retornoRest.setCodResposta(ValidacaoEnum.ERRO.getCodigo());
                retornoRest.setMsgResposta(e.getMessage());
                retorno.add(retornoRest);
            }
        }
        return retorno;
    }

    @AuditTransaction(action = ALTERAR_ADITIVO)
    @Operation(description = ALTERAR_ADITIVO)
    @ApiResponse(content = @Content(mediaType = "application/json", schema = @Schema(implementation = RetornoRestDTO.class)))
    @PostMapping(value = "/alterarAditivo", produces = MediaType.APPLICATION_JSON_UTF8_VALUE, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public List<RetornoRestDTO> alterarAditivoContrato(@Valid @RequestBody AditivoContratoAlteradoDTO contrato) {
        String currentPrincipal = SecurityContextHolder.getContext().getAuthentication().getName();

        Contrato entity = mapper.convertToEntity(contrato);
        List<RetornoRestDTO> retorno = new ArrayList<>();
        RetornoRestDTO retornoRest;

        try {
            entity = contratoService.transmitirAlteracao(entity, currentPrincipal);
            for (Veiculo v : entity.getVeiculos()) {
                retornoRest = new RetornoRestDTO(contrato.getCnpjAgenteFinanceiro(), v.getNumeroChassi(), ALTERAR_REGISTRO);
                retornoRest.setCodRespostaDetran(v.getMensagemRetorno().getCodigo());
                retornoRest.setMsgRespostaDetran(v.getMensagemRetorno().getDescricao());
                retornoRest.setNumeroRegistroEletronico(entity.getNumeroRegistroEletronico());
                retorno.add(retornoRest);
            }
        } catch (ServiceException e) {
            for (Veiculo v : entity.getVeiculos()) {
                retornoRest = new RetornoRestDTO(contrato.getCnpjAgenteFinanceiro(), v.getNumeroChassi(), ALTERAR_REGISTRO);
                retornoRest.setCodResposta(ValidacaoEnum.ERRO.getCodigo());
                retornoRest.setMsgResposta(e.getMessage());
                retorno.add(retornoRest);
            }
        }
        return retorno;
    }

    @AuditTransaction(action = CONSULTAR_REGISTRO)
    @Operation(description = CONSULTAR_REGISTRO)
    @ApiResponse(content = @Content(mediaType = "application/json", schema = @Schema(implementation = ContratoResumoDTO.class)))
    @GetMapping(value = "/consultaRegistro/{numeroRegistroEletronico}", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public ContratoResumoDTO consultarRegistro(@PathVariable(name = "numeroRegistroEletronico") Long numeroRegistroEletronico) throws ServiceException {

        String currentPrincipal = SecurityContextHolder.getContext().getAuthentication().getName();
        Contrato entity = contratoService.findByNumeroRegistroEletronico(numeroRegistroEletronico, currentPrincipal);

        return mapper.convertToDTO(entity);
    }

    @AuditTransaction(action = CONSULTAR_CONTRATO_AGENTE_FINANCEIRO_QUERY)
    @Operation(description = CONSULTAR_CONTRATO_AGENTE_FINANCEIRO_QUERY)
    @ApiResponse(content = @Content(mediaType = "application/json", schema = @Schema(implementation = ContratoResumoDTO.class)))
    @GetMapping(value = "/consultaContrato/query", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public ContratoResumoDTO consultarContratoQuery(@RequestParam(name = "cnpjAgenteFinanceiro") String cnpjAgenteFinanceiro, @RequestParam(required = true, name = "numeroContrato") String numeroContrato)
            throws ServiceException {

        String currentPrincipal = SecurityContextHolder.getContext().getAuthentication().getName();
        Contrato entity = contratoService.findByCnpjAgenteFinanceiroAndNumeroContrato(cnpjAgenteFinanceiro, numeroContrato, currentPrincipal);

        return mapper.convertToDTO(entity);
    }

    @AuditTransaction(action = CONSULTAR_CONTRATO_AGENTE_FINANCEIRO)
    @Operation(description = CONSULTAR_CONTRATO_AGENTE_FINANCEIRO)
    @ApiResponse(content = @Content(mediaType = "application/json", schema = @Schema(implementation = ContratoResumoDTO.class)))
    @GetMapping(value = "/consultaContrato/{cnpjAgenteFinanceiro}/{numeroContrato}", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public ContratoResumoDTO consultarContrato(@PathVariable(name = "cnpjAgenteFinanceiro") String cnpjAgenteFinanceiro, @PathVariable(required = true, name = "numeroContrato") String numeroContrato)
            throws ServiceException {

        String currentPrincipal = SecurityContextHolder.getContext().getAuthentication().getName();
        Contrato entity = contratoService.findByCnpjAgenteFinanceiroAndNumeroContrato(cnpjAgenteFinanceiro, numeroContrato, currentPrincipal);

        return mapper.convertToDTO(entity);
    }

    @AuditTransaction(action = CONSULTAR_CONTRATO_AGENTE_FINANCEIRO_CHASSI_GRAVAME)
    @Operation(description = CONSULTAR_CONTRATO_AGENTE_FINANCEIRO_CHASSI_GRAVAME)
    @ApiResponse(content = @Content(mediaType = "application/json", schema = @Schema(implementation = ContratoResumoDTO.class)))
    @GetMapping(value = "/consultaContrato/{cnpjAgenteFinanceiro}/{chassi}/{gravame}", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public ContratoResumoDTO consultarContratoPorChassiEGravame(@PathVariable(name = "cnpjAgenteFinanceiro") String cnpjAgenteFinanceiro, @PathVariable(name = "chassi") String chassi, @PathVariable(name = "gravame") String gravame)
            throws ServiceException {

        String currentPrincipal = SecurityContextHolder.getContext().getAuthentication().getName();
        Contrato entity = contratoService.findByCnpjAgenteFinanceiroAndChassiAndGravame(cnpjAgenteFinanceiro, chassi, gravame, currentPrincipal);

        return mapper.convertToDTO(entity);
    }

    @Operation(description = LISTAR_ESPECIES)
    @ApiResponse(content = @Content(mediaType = "application/json", schema = @Schema(implementation = CodigoDescricaoDTO.class)))
    @GetMapping(value = "/listar/especie", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public List<CodigoDescricaoDTO> listarEspecie() {
        List<CodigoDescricaoDTO> list = new ArrayList<>();
        for (TipoVeiculo e : TipoVeiculo.values()) {
            list.add(new CodigoDescricaoDTO(e.getCodigo(), e.getDescricao()));
        }
        return list;
    }

    @AuditTransaction(action = LISTAR_TIPO_DE_CONTRATO)
    @Operation(description = LISTAR_TIPO_DE_CONTRATO)
    @ApiResponse(content = @Content(mediaType = "application/json", schema = @Schema(implementation = CodigoDescricaoDTO.class)))
    @GetMapping(value = "/listar/tipo-contrato", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public List<CodigoDescricaoDTO> listarTipoContrato() {
        List<CodigoDescricaoDTO> list = new ArrayList<>();
        for (TipoContrato e : TipoContrato.values()) {
            list.add(new CodigoDescricaoDTO(e.getCodigo(), e.getDescricao()));
        }
        return list;
    }

    @AuditTransaction(action = UPLOAD)
    @Operation(description = UPLOAD)
    @ApiResponse(content = @Content(mediaType = MediaType.APPLICATION_JSON_VALUE, schema = @Schema(implementation = RetornoUploadDTO.class)))
    @PutMapping(value = "/upload/numeroRegistroEletronico/{numeroRegistroEletronico}", produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public ResponseEntity<RetornoUploadDTO> uploadFile(@PathVariable(name = "numeroRegistroEletronico") Long numeroRegistroEletronico,
                                                       @RequestParam(name = "file") MultipartFile uploadfile) {
        String currentPrincipal = SecurityContextHolder.getContext().getAuthentication().getName();
        RetornoUploadDTO retornoRest = new RetornoUploadDTO();
        retornoRest.setCodResposta(ValidacaoEnum.SUCESSO.getCodigo());
        retornoRest.setHashOperacao(RandomStringUtils.randomAlphabetic(20));
        retornoRest.setOperacao(UPLOAD);

        try {
            Contrato contrato = contratoService.findByNumeroRegistroEletronico(numeroRegistroEletronico, currentPrincipal);
            // validar contarto
            if (contrato == null) {
                throw new ServiceException("Contrato Inválido.");
            }

            Usuario usuario = usuarioService.findByCpfFinanceiras(currentPrincipal);

            if (usuario == null) {
                usuario = usuarioService.findByCpfApiFinanceiras(currentPrincipal);
            }

            // validar acesso ao contrato
            if (usuario.getPerfil() != Perfil.ADMINISTRADOR &&
                    !usuario.getFinanceiras().contains(contrato.getFinanceira())) {
                throw new ServiceException("Esse usuário não tem autorização para enviar arquivos para essa financeira.");
            }

            contratoService.salvarDocumento(new Anexo(uploadfile.getOriginalFilename(), contrato, uploadfile.getInputStream()));
            retornoRest.setCnpjAgente(contrato.getFinanceira().getDocumento());
            retornoRest.setMsgResposta("Arquivo enviado com sucesso.");
        } catch (ServiceException e) {
            retornoRest.setCodResposta(ValidacaoEnum.ERRO.getCodigo());
            retornoRest.setMsgResposta(e.getMessage());
        } catch (IOException e) {
            return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
        }
        return new ResponseEntity<>(retornoRest, HttpStatus.OK);
    }

    @AuditTransaction(action = UPLOAD_BASE64)
    @Operation(description = UPLOAD_BASE64)
    @ApiResponse(content = @Content(mediaType = MediaType.APPLICATION_JSON_VALUE, schema = @Schema(implementation = RetornoUploadDTO.class)))
    @PutMapping(value = "/upload/base64/numeroRegistroEletronico/{numeroRegistroEletronico}", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<RetornoUploadDTO> uploadFileBase64(@PathVariable(name = "numeroRegistroEletronico") Long numeroRegistroEletronico,
                                                             @Valid @RequestBody Base64Request request) {
        String currentPrincipal = SecurityContextHolder.getContext().getAuthentication().getName();
        RetornoUploadDTO retornoRest = new RetornoUploadDTO();
        retornoRest.setCodResposta(ValidacaoEnum.SUCESSO.getCodigo());
        retornoRest.setHashOperacao(RandomStringUtils.randomAlphabetic(20));
        retornoRest.setOperacao(UPLOAD_BASE64);

        try {
            Contrato contrato = contratoService.findByNumeroRegistroEletronico(numeroRegistroEletronico, currentPrincipal);
            // validar contarto
            if (contrato == null) {
                throw new ServiceException("Contrato Inválido.");
            }

            Usuario usuario = usuarioService.findByCpfFinanceiras(currentPrincipal);

            if (usuario == null) {
                usuario = usuarioService.findByCpfApiFinanceiras(currentPrincipal);
            }

            // validar acesso ao contrato
            if (usuario.getPerfil() != Perfil.ADMINISTRADOR &&
                    !usuario.getFinanceiras().contains(contrato.getFinanceira())) {
                throw new ServiceException("Esse usuário não tem autorização para enviar arquivos para essa financeira.");
            }
            contratoService.salvarDocumento(new Anexo(request.getFileName() + ".pdf", contrato, new ByteArrayInputStream(Base64.getMimeDecoder().decode(request.getFile()))));
            retornoRest.setCnpjAgente(contrato.getFinanceira().getDocumento());
            retornoRest.setMsgResposta("Arquivo enviado com sucesso.");
        } catch (ServiceException e) {
            retornoRest.setCodResposta(ValidacaoEnum.ERRO.getCodigo());
            retornoRest.setMsgResposta(e.getMessage());
        }
        return new ResponseEntity<>(retornoRest, HttpStatus.OK);
    }

    @Operation(description = UPLOAD)
    @ApiResponse(content = @Content(mediaType = "application/json", schema = @Schema(implementation = RetornoUploadChassiDTO.class)))
    @PutMapping(value = "/upload/chassi/{chassi}", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<RetornoUploadChassiDTO> uploadFile(@PathVariable(required = true, name = "chassi") String chassi,
                                                             @RequestParam(required = true, name = "file") MultipartFile uploadfile) {

        String currentPrincipal = SecurityContextHolder.getContext().getAuthentication().getName();
        RetornoUploadChassiDTO retornoRest = new RetornoUploadChassiDTO();
        retornoRest.setCodResposta(ValidacaoEnum.SUCESSO.getCodigo());
        retornoRest.setChassi(chassi);
        retornoRest.setHashOperacao(RandomStringUtils.randomAlphabetic(20));
        retornoRest.setOperacao(UPLOAD);

        try {
            Contrato contrato = contratoService.findByChassi(chassi);
            // validar contarto
            if (contrato == null) {
                throw new ServiceException("Contrato Inválido.");
            }

            Usuario usuario = usuarioService.findByCpfFinanceiras(currentPrincipal);

            if (usuario == null) {
                usuario = usuarioService.findByCpfApiFinanceiras(currentPrincipal);
            }

            // validar acesso ao contrato
            if (usuario.getPerfil() != Perfil.ADMINISTRADOR &&
                    !usuario.getFinanceiras().contains(contrato.getFinanceira())) {
                throw new ServiceException("Esse usuário não tem autorização para enviar arquivos para essa financeira.");
            }

            contratoService.salvarDocumento(new Anexo(uploadfile.getOriginalFilename(), contrato, uploadfile.getInputStream()));
            retornoRest.setCnpjAgente(contrato.getFinanceira().getDocumento());
            retornoRest.setMsgResposta("Arquivo enviado com sucesso.");
        } catch (ServiceException e) {
            retornoRest.setCodResposta(ValidacaoEnum.ERRO.getCodigo());
            retornoRest.setMsgResposta(e.getMessage());
        } catch (IOException e) {
            return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
        }

        return new ResponseEntity<>(retornoRest, HttpStatus.OK);
    }

    @Operation(description = UPLOAD)
    @ApiResponse(content = @Content(mediaType = "application/json", schema = @Schema(implementation = RetornoUploadChassiDTO.class)))
    @PostMapping(value = "/upload/remessa", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<RetornoUploadChassiDTO> uploadRemessaFile(@RequestParam(required = true, name = "file") MultipartFile uploadfile) {

        String currentPrincipal = SecurityContextHolder.getContext().getAuthentication().getName();
        RetornoUploadChassiDTO retornoRest = new RetornoUploadChassiDTO();
        retornoRest.setCodResposta(ValidacaoEnum.SUCESSO.getCodigo());
        retornoRest.setHashOperacao(RandomStringUtils.randomAlphabetic(20));
        retornoRest.setCnpjAgente(currentPrincipal);
        retornoRest.setOperacao(UPLOAD);

        Long templateId = templateService.findDefault(currentPrincipal);

        try {
            ArquivoRemessa arquivoRemessa = remessaService.save(uploadfile.getName(), uploadfile.getInputStream(), templateId);
            remessaService.save(uploadfile.getInputStream(), templateId, currentPrincipal, arquivoRemessa);
            retornoRest.setCodResposta(ValidacaoEnum.SUCESSO.getCodigo());
            retornoRest.setMsgResposta("Arquivo de Remessa enviado.");
        } catch (IOException e) {
            e.printStackTrace();
            return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
        } catch (Exception e) {
            retornoRest.setCodResposta(ValidacaoEnum.ERRO.getCodigo());
            retornoRest.setMsgResposta(e.getMessage());
        }

        return new ResponseEntity<>(retornoRest, HttpStatus.OK);
    }
}
