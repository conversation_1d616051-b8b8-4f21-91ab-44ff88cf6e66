package com.registrocontrato.registro.ws;

import com.registrocontrato.commons.ws.dto.MunicipioDTO;
import com.registrocontrato.infra.entity.Municipio;
import com.registrocontrato.infra.service.audit.AuditTransaction;
import com.registrocontrato.registro.repository.MunicipioRepository;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;

@RestController
@RequestMapping(value = "/api/v1/enderecamento")
public class EnderecamentoResource {

    private final MunicipioRepository municipioRepository;

    private static final String MUNICIPIO = "Consultar Tabela de Município";

    public EnderecamentoResource(MunicipioRepository municipioRepository) {
        this.municipioRepository = municipioRepository;
    }

//    @AuditTransaction(action = MUNICIPIO)
    @Operation(description = MUNICIPIO)
    @ApiResponse(content = @Content(mediaType = "application/json", schema = @Schema(implementation = MunicipioDTO.class)))
    @GetMapping(value = "/municipios/{uf}", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public List<MunicipioDTO> consultarContrato(@PathVariable(required = true, name = "uf") String uf) {
        List<Municipio> list = municipioRepository.findByUfOrderByDescricao(com.registrocontrato.infra.entity.Uf.valueOf(uf.toUpperCase()));
        List<MunicipioDTO> result = new ArrayList<>();
        MunicipioDTO d = null;
        for (Municipio m : list) {
            d = new MunicipioDTO();
            d.setCodigo(m.getCodigoDenatran());
            d.setNome(m.getDescricao());
            d.setUf(m.getUf().toString());
            result.add(d);
        }
        return result;
    }

}
