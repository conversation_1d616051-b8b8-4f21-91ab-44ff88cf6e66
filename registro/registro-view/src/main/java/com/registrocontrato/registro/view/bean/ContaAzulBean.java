package com.registrocontrato.registro.view.bean;

import com.registrocontrato.infra.bean.BaseCrud;
import com.registrocontrato.infra.entity.SimNao;
import com.registrocontrato.infra.entity.Uf;
import com.registrocontrato.infra.exception.ServiceException;
import com.registrocontrato.infra.jsf.ViewScope;
import com.registrocontrato.infra.service.BaseService;
import com.registrocontrato.registro.entity.Cobranca;
import com.registrocontrato.registro.entity.ContaAzul;
import com.registrocontrato.registro.dto.ClienteContaAzulDTO;
import com.registrocontrato.registro.dto.ServicoContaAzulDTO;
import com.registrocontrato.registro.dto.TokenContaAzulDTO;
import com.registrocontrato.registro.service.ContaAzulService;
import com.registrocontrato.registro.service.cobranca.CobrancaService;
import com.registrocontrato.registro.service.dto.CobrancaDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;

import javax.faces.context.FacesContext;
import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

import static com.registrocontrato.infra.util.PlaceconUtil.formatarDataPadrao;

@Controller
@ViewScope
public class ContaAzulBean extends BaseCrud<Cobranca, CobrancaDTO> {

	private static final long serialVersionUID = 1L;

	private static final String COBRANCA = "Cobranca";
	private static final String TOKEN = "Token";
	private static final String DTO = "CobrancaDTO";

	private List<Uf> estadosComReembolso = Arrays.asList(Uf.SP, Uf.MG, Uf.BA, Uf.RS, Uf.PB, Uf.GO, Uf.CE, Uf.RJ);

	private ServicoContaAzulDTO servico;
	private ClienteContaAzulDTO cliente;
	private Date data;
	private String descricao;

	private TokenContaAzulDTO token;

	private String code;
	private String state;
	private Boolean consultado;
	private BigDecimal valorConferencia;
	private Cobranca cobranca;

	@Autowired
	private CobrancaService service;

	@Autowired
	private ContaAzulService contaAzulService;

	@Override
	public BaseService<Cobranca, CobrancaDTO> getService() {
		return service;
	}

	public String irParaConfirmarVenda(Cobranca cobranca) {
		try {
			putSession(COBRANCA, cobranca);
			putSession(DTO, getFilter());
			if (token == null) {
				FacesContext context = FacesContext.getCurrentInstance();
				String baseURL = context.getExternalContext().getRequestContextPath();
				String encodeURL = context.getExternalContext().encodeResourceURL(baseURL + "/contaazul/authorize");
				context.getExternalContext().redirect(encodeURL);
				return null;
			}

			return  "/contaazul/confirma-venda.xhtml?faces-redirect=true";
		}
		catch(Exception e) {
			removeSession(COBRANCA);
			addMessageError(e.getMessage());
			return null;
		}
	}

	public String irParaConfirmarVendaSng(Cobranca cobranca) {
		try {
			putSession(COBRANCA, cobranca);
			putSession(DTO, getFilter());
			putSession("REDIRECT_TO_SNG", Boolean.TRUE);
			if (token == null) {
				FacesContext context = FacesContext.getCurrentInstance();
				String baseURL = context.getExternalContext().getRequestContextPath();
				String encodeURL = context.getExternalContext().encodeResourceURL(baseURL + "/contaazul/authorize");
				context.getExternalContext().redirect(encodeURL);
				return null;
			}

			return  "/contaazul/confirma-venda-sng.xhtml?faces-redirect=true";
		}
		catch(Exception e) {
			removeSession(COBRANCA);
			removeSession("REDIRECT_TO_SNG");
			addMessageError(e.getMessage());
			return null;
		}
	}

	public void initConfirmaVenda() {
		token = (TokenContaAzulDTO) getSessionValue(TOKEN);

		if (token == null) {
			if (code != null && state != null) {
				try {
					token = contaAzulService.getContaAzulTokens(
			                contaAzulService.getTokenUrl(code)
			        );
					putSession(TOKEN, token);

					// Check if we should redirect to the SNG page
					Boolean redirectToSng = (Boolean) getSessionValue("REDIRECT_TO_SNG");
					if (redirectToSng != null && redirectToSng) {
						removeSession("REDIRECT_TO_SNG"); // Clean up the session attribute
						try {
							FacesContext context = FacesContext.getCurrentInstance();
							context.getExternalContext().redirect(context.getExternalContext().getRequestContextPath()
								+ "/contaazul/confirma-venda-sng.xhtml?faces-redirect=true");
							return;
						} catch (Exception e) {
							addMessageError("Erro ao redirecionar para a página de confirmação SNG: " + e.getMessage());
						}
					}
				}
				catch(Exception e) {
					e.printStackTrace();
					addMessageError("Não foi possível obter o token de autenticação");
					addMessageError(e.getMessage());
					return;
				}
			}
			else {
				addMessageError("Não foi possível obter os dados para autenticação na Conta Azul");
				return;
			}
		}

		cobranca = (Cobranca) getSessionValue(COBRANCA);
		if (cobranca == null) {
			addMessageError("Não foi possível obter os dados da cobrança");
			return;
		}

		ContaAzul conta = contaAzulService.findServico(cobranca.getEstado());
		if (conta == null) {
			addMessageError(String.format("Não há codigo de serviço registrado no sistema para a UF: %s", cobranca.getEstado()));
			return;
		}

		ResponseEntity<ServicoContaAzulDTO[]> response = contaAzulService.listServices(token.getAccessToken(), conta.getCodigo());
		if (response == null || response.getBody() == null) {
			addMessageError(String.format("Código %s não registrado na Conta Azul", conta.getCodigo()));
			return;
		}


		ServicoContaAzulDTO[] servicos = response.getBody();
		if (servicos == null || servicos.length==0) {
			addMessageError(String.format("Código %s não registrado na Conta Azul", conta.getCodigo()));
			return ;
		}
		servico = servicos[0];

		String documentoCobranca = cobranca.getFinanceira().getPossuiMatriz() != SimNao.S ? cobranca.getFinanceira().getDocumento() : cobranca.getFinanceira().getDocumentoMatriz();
		ResponseEntity<ClienteContaAzulDTO[]> responseCliente = contaAzulService.listClients(token.getAccessToken(),
				documentoCobranca);

		if (responseCliente == null || responseCliente.getBody() == null) {
			addMessageError("Não foi possível validar a financeira como sendo um cliente cadastrado na Conta Azul");
			return;
		}

		ClienteContaAzulDTO[] clientes = responseCliente.getBody();
		if (clientes == null || clientes.length==0) {
			addMessageError(String.format("Cliente %s não cadastrado na Conta Azul", cobranca.getFinanceira().getDocumento()));
			return ;
		}

		cliente = Arrays.stream(clientes)
				.filter(c -> c.getDocumento().equals(documentoCobranca))
				.findFirst()
				.orElseGet(() -> {
					addMessageError(String.format("Cliente %s não cadastrado na lista do Conta Azul", documentoCobranca));
					return null;
				});
		if (cliente == null)
			return;

		consultado = Boolean.TRUE;
		valorConferencia = new BigDecimal(Double.parseDouble(servico.getValor()) * cobranca.getQuantidadeRegistros());
		data = new Date();
		descricao = String.format("Registro eletrônico de contratos - Detran/%s \n Competência: %s a %s",
				cobranca.getEstado(), formatarDataPadrao(cobranca.getDataInicio()), formatarDataPadrao(cobranca.getDataFim()));
	}

	public void initConfirmaVendaSng() {
		token = (TokenContaAzulDTO) getSessionValue(TOKEN);

		if (token == null) {
			if (code != null && state != null) {
				try {
					token = contaAzulService.getContaAzulTokens(
							contaAzulService.getTokenUrl(code)
					);
					putSession(TOKEN, token);
				}
				catch(Exception e) {
					e.printStackTrace();
					addMessageError("Não foi possível obter o token de autenticação");
					addMessageError(e.getMessage());
					return;
				}
			}
			else {
				addMessageError("Não foi possível obter os dados para autenticação na Conta Azul");
				return;
			}
		}

		cobranca = (Cobranca) getSessionValue(COBRANCA);
		if (cobranca == null) {
			addMessageError("Não foi possível obter os dados da cobrança");
			return;
		}

		ContaAzul conta = contaAzulService.findServicoSng(cobranca.getEstado());
		if (conta == null) {
			addMessageError(String.format("Não há codigo de serviço registrado no sistema para a UF: %s", cobranca.getEstado()));
			return;
		}

		ResponseEntity<ServicoContaAzulDTO[]> response = contaAzulService.listServices(token.getAccessToken(), conta.getCodigo());
		if (response == null || response.getBody() == null) {
			addMessageError(String.format("Código %s não registrado na Conta Azul", conta.getCodigo()));
			return;
		}


		ServicoContaAzulDTO[] servicos = response.getBody();
		if (servicos == null || servicos.length==0) {
			addMessageError(String.format("Código %s não registrado na Conta Azul", conta.getCodigo()));
			return ;
		}
		servico = servicos[0];

		String documentoCobranca = cobranca.getFinanceira().getPossuiMatriz() != SimNao.S ? cobranca.getFinanceira().getDocumento() : cobranca.getFinanceira().getDocumentoMatriz();
		ResponseEntity<ClienteContaAzulDTO[]> responseCliente = contaAzulService.listClients(token.getAccessToken(),
				documentoCobranca);

		if (responseCliente == null || responseCliente.getBody() == null) {
			addMessageError("Não foi possível validar a financeira como sendo um cliente cadastrado na Conta Azul");
			return;
		}

		ClienteContaAzulDTO[] clientes = responseCliente.getBody();
		if (clientes == null || clientes.length==0) {
			addMessageError(String.format("Cliente %s não cadastrado na Conta Azul", cobranca.getFinanceira().getDocumento()));
			return ;
		}

		cliente = Arrays.stream(clientes)
				.filter(c -> c.getDocumento().equals(cobranca.getFinanceira().getDocumento()))
				.findFirst()
				.orElseGet(() -> {
					addMessageError(String.format("Cliente %s não cadastrado na lista do Conta Azul", documentoCobranca));
					return null;
				});
		if (cliente == null)
			return;

		consultado = Boolean.TRUE;
		valorConferencia = new BigDecimal(Double.parseDouble(servico.getValor()) * cobranca.getQuantidadeRegistrosSng());
		data = new Date();

		descricao = String.format("Registro eletrônico de contratos - Detran/%s \n Competência: %s a %s",
				cobranca.getEstado(), formatarDataPadrao(cobranca.getDataInicio()), formatarDataPadrao(cobranca.getDataFim()));
	}

	public BigDecimal getValorCobranca() {
		BigDecimal valor = cobranca.getValorCredenciada();
		if (cobranca.getValorIntegraMais() != null) {
			valor = valor.add(cobranca.getValorIntegraMais());
		}
		if (cobranca.getValorDesconto() != null) {
			valor = valor.subtract(cobranca.getValorDesconto());
		}
		return valor;
	}

	public BigDecimal getValorCobrancaSng() {
		return cobranca.getValorCobrancaSng();
	}

	public void refreshToken() {
		try {
			if (token != null) {
				token = contaAzulService.getContaAzulTokens(contaAzulService.getRefreshTokenUrl(token.getRefreshToken()));
				putSession(TOKEN, token);
			}
		}
		catch(Exception e) {
			addMessageError("Não foi possível atualizar o token da Conta Azul");
		}
	}

	public void initPesquisa() {
		token = (TokenContaAzulDTO) getSessionValue(TOKEN);

		CobrancaDTO filterSession = (CobrancaDTO) getSessionValue(DTO);
		if(filterSession != null) {
			setFilter(filterSession);
		}
		getFilter().setUsuario(getUsername());
		getFilter().setNotaQuery(true);
//		getFilter().setNotaAzul(false);
	}

	public String createVenda() {
		try {
			contaAzulService.createVenda(servico, cliente, cobranca, token.getAccessToken(), descricao, data);
			addMessageSuccess();
			return "/contaazul/list-disponiveis.xhtml?faces-redirect=true";
		} catch (ServiceException e) {
			addMessageError(e.getMessage());
			return null;
		}
	}

	public String createVendaSng() {
		try {
			contaAzulService.createVendaSng(servico, cliente, cobranca, token.getAccessToken(), descricao, data);
			addMessageSuccess();
			return "/contaazul/list-disponiveis.xhtml?faces-redirect=true";
		} catch (ServiceException e) {
			addMessageError(e.getMessage());
			return null;
		}
	}

	public void setState(String state) {
		this.state = state;
	}

	public String getState() {
		return state;
	}

	public void setCode(String code) {
		this.code = code;
	}

	public String getCode() {
		return code;
	}

	public TokenContaAzulDTO getToken() {
		return token;
	}

	public ServicoContaAzulDTO getServico() {
		return servico;
	}

	public Boolean getConsultado() {
		return consultado;
	}

	public BigDecimal getValorConferencia() {
		return valorConferencia;
	}

	public Cobranca getCobranca() {
		return cobranca;
	}

	public String getDescricao() {
		return descricao;
	}

	public void setDescricao(String descricao) {
		this.descricao = descricao;
	}

	public Date getData() {
		return data;
	}

	public void setData(Date data) {
		this.data = data;
	}
}
