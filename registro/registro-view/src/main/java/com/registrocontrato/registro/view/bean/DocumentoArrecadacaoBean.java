package com.registrocontrato.registro.view.bean;

import com.registrocontrato.infra.bean.BaseCrud;
import com.registrocontrato.infra.entity.Financeira;
import com.registrocontrato.infra.entity.SimNao;
import com.registrocontrato.infra.entity.TipoGadE;
import com.registrocontrato.infra.entity.Uf;
import com.registrocontrato.infra.jsf.ViewScope;
import com.registrocontrato.infra.service.BaseService;
import com.registrocontrato.registro.entity.DocumentoArrecadacao;
import com.registrocontrato.registro.dto.SolicitacaoDudasDTO;
import com.registrocontrato.registro.mensageria.producer.ChamadaDudaProducer;
import com.registrocontrato.registro.service.DocumentoArrecadacaoService;
import com.registrocontrato.registro.service.dto.DocumentoArrecadacaoDTO;
import com.registrocontrato.seguranca.entity.Usuario;
import com.registrocontrato.seguranca.service.FinanceiraService;

import org.primefaces.model.DefaultStreamedContent;
import org.primefaces.model.FilterMeta;
import org.primefaces.model.LazyDataModel;
import org.primefaces.model.SortMeta;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Controller;

import javax.servlet.http.HttpServletRequest;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.util.Base64;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Controller
@ViewScope
public class DocumentoArrecadacaoBean extends BaseCrud<DocumentoArrecadacao, DocumentoArrecadacaoDTO> {

    private static final long serialVersionUID = 1L;
    private static final String PASTA_FORM_INPUTS = "/documentoarrecadacao/estado/";

    private final DocumentoArrecadacaoService service;
    private final FinanceiraService financeiraService;
    private final HelperSessionBean helperSessionBean;
    private final ChamadaDudaProducer chamadaDudaProducer;

    private String numeroChassi;
    private TipoGadE tipoGadESelecionado;
    private Long qtdTarifas;
    private Integer tipoServico;
    private Financeira financeira;
    private String pagamento;
    private final Integer qtdPorSessao = 150;

    @Value("${file-boleto.dir-read:null}")
    private String FILE_DIR_READ;

    private List<Financeira> financeirasCe;

    public DocumentoArrecadacaoBean(DocumentoArrecadacaoService service, FinanceiraService financeiraService, HelperSessionBean helperSessionBean, ChamadaDudaProducer chamadaDudaProducer) {
        this.service = service;
        this.financeiraService = financeiraService;
        this.helperSessionBean = helperSessionBean;
        this.chamadaDudaProducer = chamadaDudaProducer;
    }

    @Override
    public void search() {
        DocumentoArrecadacaoDTO filter = getFilter();
        String chassi = filter.getChassi();

        setList(new LazyDataModel<DocumentoArrecadacao>() {
            @Override
            public int count(Map<String, FilterMeta> map) {
                return 0;
            }

            @Override
            public List<DocumentoArrecadacao> load(int first, int pageSize, Map<String, SortMeta> sortBy, Map<String, FilterMeta> filterBy) {
                Page<DocumentoArrecadacao> page = service.findAll(first, getSize(), filter);
                setRowCount((int) page.getTotalElements());
                setPageSize(getSize());
                return page.getContent();

            }
        });
    }

    public String save() {
        if (entity.getEstado().name().equals("CE") && Objects.isNull(entity.getCodigoBarras())) {
            geraDocumentoCe();
        } else if (entity.getEstado() == Uf.RJ) {
            return geraDocumentoRj();
        }
        return super.save();
    }

    public String getPagamento() {
        if (Objects.nonNull(entity.getDataPagamento())) {
            return pagamento = "Pago";
        }
        return pagamento = "Não Pago";
    }

    public void setPagamento(String pagamento) {
        this.pagamento = pagamento;
    }

    public String recuperarFormUf() {
        Uf estado = getEntity().getEstado();
        if (estado == null) {
            return "";
        }
        return PASTA_FORM_INPUTS + "form-inputs-" + estado + ".xhtml";
    }

    public String geraDocumentoRj() {
        int qtdSolicitada = qtdPorSessao;
        int qtdPorRequisicao = qtdPorSessao;
        if (entity.getRequestId() != null) {
            qtdSolicitada = entity.getRequestId();
            qtdPorRequisicao = qtdSolicitada > qtdPorSessao ? qtdPorSessao : qtdSolicitada;
        }
        int total = 0;
        do {
            SolicitacaoDudasDTO chamada = new SolicitacaoDudasDTO(qtdPorRequisicao);
            chamadaDudaProducer.send(chamada);
            total += qtdPorRequisicao;
            qtdPorRequisicao = qtdSolicitada - qtdPorRequisicao;
        } while (total != qtdSolicitada);

        addMessageInfo("As " + entity.getRequestId() + " dudas serão geradas. Enviaremos um e-mail quando o processamento for finalizado.");
        String uri = ((HttpServletRequest) getExternalContext().getRequest()).getServletPath();
        uri = uri.replace("form-add", "list");
        return uri + "?faces-redirect=true";
    }

    public void geraDocumentoCe() {
        try {
            Usuario usuario = helperSessionBean.getUsuario();
            DocumentoArrecadacao doc = service.gerarDocumentoArrecadacao(usuario.getCpf(), qtdTarifas, tipoServico, financeira);
            entity.setCodigo(doc.getCodigo());
            entity.setCodigoBarras(doc.getCodigoBarras());
            entity.setLinhaDigitavel(doc.getLinhaDigitavel());
            entity.setFinanceira(doc.getFinanceira());
            entity.setEstado(Uf.CE);
            logger.info("NOTIFICAÇÃO DE EMAIL: " + doc.getVeiculo().getNumeroChassi());
            entity.setNotificado(SimNao.S);
            entity.setQtdTarifas(qtdTarifas.intValue());
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public List<Financeira> getFinanceirasCe() {
        this.financeirasCe = financeiraService.findAtivosByUf(Uf.CE);
        return this.financeirasCe;
    }

    public void fileDownload(DocumentoArrecadacao documentoArrecadacao) {
        entity = documentoArrecadacao;
    }

    public DefaultStreamedContent getFile() {
        Financeira financeira = service.findFinanceiraByCodigo(entity.getCodigo());
        String fileName = "boletoce_" + entity.getCodigo() + ".pdf";
        File file = new File(FILE_DIR_READ + fileName);
        if (file.exists()) {
            return retornaArquivoDownload(fileName, file);
        }
        try (FileOutputStream fos = new FileOutputStream(file)) {
            byte[] arquivo = Base64.getDecoder().decode(service.buscarBase64BoletoPdf(entity.getCodigo(), financeira));
            fos.write(arquivo);
            if (file.exists()) {
                return retornaArquivoDownload(fileName, file);
            }
        } catch (Exception e) {
            addMessageError("Boleto não disponível para esse estado.");
        }
        return null;
    }

    private DefaultStreamedContent retornaArquivoDownload(String filename, File file) {
        FileInputStream stream = null;
        return DefaultStreamedContent.builder()
                .contentType("application/pdf")
                .name(filename)
                .stream(() -> {
                    try {
                        return new FileInputStream(file);
                    } catch (FileNotFoundException e) {
                        throw new RuntimeException(e);
                    }
                }).build();
    }

    @Override
    public BaseService<DocumentoArrecadacao, DocumentoArrecadacaoDTO> getService() {
        return service;
    }

    public String getNumeroChassi() {
        return numeroChassi;
    }

    public void setNumeroChassi(String numeroChassi) {
        this.numeroChassi = numeroChassi;
    }

    public TipoGadE getTipoGadESelecionado() {
        return tipoGadESelecionado;
    }

    public void setTipoGadESelecionado(TipoGadE tipoGadESelecionado) {
        this.tipoGadESelecionado = tipoGadESelecionado;
    }

    public Long getQtdTarifas() {
        return qtdTarifas;
    }

    public void setQtdTarifas(Long qtdTarifas) {
        this.qtdTarifas = qtdTarifas;
    }

    public void renovarTaxa(DocumentoArrecadacao documentoArrecadacao) {
        service.renovarTaxa(documentoArrecadacao);
    }

    public Integer getTipoServico() {
        return tipoServico;
    }

    public void setTipoServico(Integer tipoServico) {
        this.tipoServico = tipoServico;
    }

    public Financeira getFinanceira() {
        return financeira;
    }

    public void setFinanceira(Financeira financeira) {
        this.financeira = financeira;
    }
}
