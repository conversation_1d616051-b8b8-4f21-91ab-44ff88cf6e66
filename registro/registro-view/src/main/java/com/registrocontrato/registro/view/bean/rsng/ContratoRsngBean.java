package com.registrocontrato.registro.view.bean.rsng;

import com.registrocontrato.commons.ws.rsng.ContratoRsngService;
import com.registrocontrato.infra.bean.BaseCrud;
import com.registrocontrato.infra.entity.Financeira;
import com.registrocontrato.infra.exception.ServiceException;
import com.registrocontrato.infra.jsf.ViewScope;
import com.registrocontrato.infra.service.BaseService;
import com.registrocontrato.infra.service.audit.AuditContrato;
import com.registrocontrato.infra.util.PlaceconUtil;
import com.registrocontrato.registro.entity.ContratoRsng;
import com.registrocontrato.registro.entity.VeiculoRsng;
import com.registrocontrato.registro.enums.SituacaoRsng;
import com.registrocontrato.registro.service.MunicipioService;
import com.registrocontrato.registro.service.dto.ContratoRsngFilter;
import com.registrocontrato.registro.view.bean.ContratoRsngBaseBean;
import com.registrocontrato.seguranca.entity.Usuario;
import com.registrocontrato.seguranca.service.FinanceiraService;
import com.registrocontrato.seguranca.service.UsuarioService;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.stereotype.Controller;

import java.util.List;

@Controller
@ViewScope
public class ContratoRsngBean extends ContratoRsngBaseBean<ContratoRsng, ContratoRsngFilter> {

    private List<Financeira> financeiras;

    private String localidadeDevedor;

    private final ContratoRsngService contratoService;

    private final MunicipioService municipioService;

    private final UsuarioService usuarioService;

    private final FinanceiraService financeiraService;

    public ContratoRsngBean(ContratoRsngService contratoService,
                            MunicipioService municipioService,
                            UsuarioService usuarioService,
                            FinanceiraService financeiraService) {
        this.contratoService = contratoService;
        this.municipioService = municipioService;
        this.usuarioService = usuarioService;
        this.financeiraService = financeiraService;
    }

    @Override
    public void postInitialization() {
        super.postInitialization();

        ContratoRsngFilter filter = getFilter();
        filter.setUsuario(getUsername());

        VeiculoRsng veiculo = new VeiculoRsng();
        veiculo.setVeiculo0Km(Boolean.TRUE);
        veiculo.setContratoRsng(getEntity());
        getEntity().getVeiculos().add(veiculo);
        getEntity().setIndicadorComissao(Boolean.FALSE);
        getEntity().setIndicadorPenalidade(Boolean.FALSE);
        getEntity().setIndicadorTaxaMoraDia(Boolean.FALSE);
        getEntity().setIndicadorTaxaMulta(Boolean.FALSE);

        Usuario usuario = usuarioService.findByCpfFinanceiras(getUsername());
        if (usuario.isPerfilFinanceira()) {
            financeiras = usuario.getFinanceiras();
            if (financeiras.size() == 1) {
                getEntity().setFinanceira(financeiraService.findOne(financeiras.get(0).getId()));
                getEntity().setMunicipioLiberacao(getEntity().getFinanceira().getMunicipio());
                getEntity().setUfLiberacaoCredito(getEntity().getFinanceira().getUfEndereco());
            }
        } else {
            financeiras = financeiraService.findAtivos();
        }
    }

    @Override
    public void loadDetails() {
        super.loadDetails();
        Usuario usuarioLogado = usuarioService.findByCpfFinanceiras(getUsername());
        if (usuarioLogado.isPerfilFinanceira()) {
            if (!usuarioLogado.getFinanceiras().contains(getEntity().getFinanceira())) {
                unauthorized();
            }
        }
        if (usuarioLogado.getUf() != null && !usuarioLogado.getUf().equals(getEntity().getUfRegistro()) && !usuarioLogado.isPerfilAdministrador()) {
            unauthorized();
        }
    }

    public void definirLiberacaoCredito() {
        if (getEntity().getFinanceira() != null) {
            Financeira financeira = financeiraService.findOne(getEntity().getFinanceira().getId());
            getEntity().setFinanceira(financeira);
            getEntity().setMunicipioLiberacao(financeira.getMunicipio());
            getEntity().setUfLiberacaoCredito(financeira.getUfEndereco());
        }
    }

    @AuditContrato(action = "Gerar Apontamento no RSNG")
    public String gerarApontamento() {
        try {
            contratoService.gerarNovoApontamento(getEntity(), getUsername());
            if (this.entity.getRegistroAutomaticoDetran() && this.entity.getSituacao().equals(SituacaoRsng.PENDENTE_ENVIO)) {
                this.entity.setCpfCnpjUsuarioResponsavel(getUsername());
                enviarParaDetran(this.entity.getId());
            }

            if (entity.getSituacao() == SituacaoRsng.PENDENTE_ENVIO) {
                addMessageInfo("Contrato Enviado com sucesso. Aguarde a confirmação do DETRAN.");
                return "/contratorsng/form-detail.xhtml?faces-redirect=true&id=" + getEntity().getId();
            } else {
                addMessageWarn("Contrato salvo! Houve algum erro que impediu a finalização do processo");
                return "/contratorsng/form-update.xhtml?faces-redirect=true&id=" + getEntity().getId();
            }
        } catch (ServiceException e) {
            addMessageError(e.getMessage());
        } catch (Exception e) {
            logger.error(e);
            addMessageError(ExceptionUtils.getStackTrace(e));

            if (getEntity().getId() != null) {
                return "/contratorsng/form-update.xhtml?faces-redirect=true&id=" + getEntity().getId();
            }
        }
        return null;
    }

    @AuditContrato(action = "Cancelar Apontamento no RSNG")
    public String cancelarApontamento() {
        try {
            contratoService.cancelarApontamento(getEntity(), getUsername());

            if (entity.getSituacao() == SituacaoRsng.CANCELADO) {
                addMessageInfo("Contrato Cancelado com sucesso.");
                return "/contratorsng/form-detail.xhtml?faces-redirect=true&id=" + getEntity().getId();
            } else {
                addMessageWarn("Não foi possível cancelar o apontamento!");
                return "/contratorsng/form-update.xhtml?faces-redirect=true&id=" + getEntity().getId();
            }
        } catch (ServiceException e) {
            addMessageError(e.getMessage());
        } catch (Exception e) {
            logger.error(e);
            addMessageError(ExceptionUtils.getStackTrace(e));

            if (getEntity().getId() != null) {
                return "/contratorsng/form-update.xhtml?faces-redirect=true&id=" + getEntity().getId();
            }
        }
        return null;
    }

    public String enviarParaDetran(Long contratoRsngId) {
        try {
            ContratoRsng contratoRsng = contratoService.registrarApontamentoNoDetran(contratoRsngId, getUsername());

            if (contratoRsng.getSituacao() == SituacaoRsng.ENVIADO) {
                addMessageInfo("Contrato Enviado com sucesso. Aguarde a confirmação do DETRAN.");
                return "/contrato/form-detail.xhtml?faces-redirect=true&id=" + contratoRsng.getContrato().getId();
            } else {
                addMessageWarn("Contrato salvo! Houve algum erro que impediu a finalização do processo");
                return "/contratorsng/form-update.xhtml?faces-redirect=true&id=" + contratoRsng.getId();
            }
        } catch (ServiceException e) {
            addMessageError(e.getMessage());
        } catch (Exception e) {
            logger.error(e);
            addMessageError(ExceptionUtils.getStackTrace(e));

            if (getEntity().getId() != null) {
                return "/contratorsng/form-update.xhtml?faces-redirect=true&id=" + getEntity().getId();
            }
        }
        return null;
    }

    public List<Financeira> getFinanceiras() {
        return financeiras;
    }

    public void setFinanceiras(List<Financeira> financeiras) {
        this.financeiras = financeiras;
    }

    public String getLocalidadeDevedor() {
        return localidadeDevedor;
    }

    public void setLocalidadeDevedor(String localidade) {
        if (!StringUtils.isBlank(localidade)) {
            this.localidadeDevedor = PlaceconUtil.deAccent(localidade.toUpperCase());
        } else {
            this.localidadeDevedor = null;
        }
    }

    public void findMunicipioDevedor() {
        if (localidadeDevedor != null && entity.getUfEnderecoDevedor() != null) {
            entity.setMunicipioDevedor(municipioService.findByUfAndDescricao(entity.getUfEnderecoDevedor(), localidadeDevedor));
        }
    }
}
