package com.registrocontrato.registro.view.bean;

import com.registrocontrato.infra.bean.BaseBean;
import com.registrocontrato.infra.bean.BaseCrud;
import com.registrocontrato.infra.exception.ServiceException;
import com.registrocontrato.infra.jsf.ViewScope;
import com.registrocontrato.infra.service.BaseService;
import com.registrocontrato.registro.dto.ProcessoCredenciamentoDTO;
import com.registrocontrato.registro.entity.ProcessoCredenciamento;
import com.registrocontrato.registro.service.AcompanhamentoObservatorioService;
import org.primefaces.event.FileUploadEvent;
import org.primefaces.model.file.UploadedFile;
import org.springframework.stereotype.Controller;

import javax.faces.application.FacesMessage;
import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

@Controller
@ViewScope
public class AcompanhamentoObservatorioBean extends BaseCrud<ProcessoCredenciamento, ProcessoCredenciamentoDTO> {

    private final AcompanhamentoObservatorioService service;

    private List<UploadedFile> lista = new ArrayList<>();

    public AcompanhamentoObservatorioBean(AcompanhamentoObservatorioService service) {
        this.service = service;
    }

    public void handleFileUpload(FileUploadEvent event) {
        try {
            UploadedFile file = event.getFile();
            validacaoArquivosDefault(file, Collections.singletonList("pdf"));
            lista.add(file);
            addMessageInfo("Arquivo processado com sucesso");
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            addMessageError(e.getMessage() != null ? e.getMessage() : "Houve um erro no sistema. Favor entrar em contato com suporte");
        }
    }

    public void removerArquivo() {
        if (!lista.isEmpty()) {
            logger.debug("Removendo arquivo com sucesso");
            lista.remove(0);
            addMessageInfo("Arquivo removido com sucesso");
        } else {
            addMessageError("Arquivo não encontrado para remoção");
        }
    }

    public String save() {
        getExternalContext().getFlash().setKeepMessages(true);
        try {
            getService().save(entity);

            getCurrentInstance().addMessage("", new FacesMessage(FacesMessage.SEVERITY_INFO, "Salvo com sucesso", ""));
            String uri = ((HttpServletRequest) getExternalContext().getRequest()).getServletPath();
            uri = uri.replace("form-add", "acompanhamento-place");
            uri = uri.replace("form-update", "acompanhamento-place");
            return uri + "?faces-redirect=true";
        } catch (ServiceException e) {
            logger.error(e.getStackTrace());
            getCurrentInstance().addMessage("", new FacesMessage(FacesMessage.SEVERITY_ERROR, e.getMessage(), ""));
            logger.error(e);
        } catch (Exception e) {
            logger.error(e);
            getCurrentInstance().addMessage("", new FacesMessage(FacesMessage.SEVERITY_ERROR, "Erro ao salvar", ""));
        }
        return null;
    }

    @Override
    public AcompanhamentoObservatorioService getService() {
        return service;
    }

    public List<UploadedFile> getLista() {
        return lista;
    }

    public void setLista(List<UploadedFile> lista) {
        this.lista = lista;
    }
}
