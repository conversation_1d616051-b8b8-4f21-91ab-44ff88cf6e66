package com.registrocontrato.registro.view.bean;

import com.registrocontrato.infra.entity.*;
import com.registrocontrato.registro.entity.*;
import com.registrocontrato.registro.enums.*;
import com.registrocontrato.registro.repository.MarcaRepository;
import com.registrocontrato.registro.repository.ModeloRepository;
import com.registrocontrato.registro.repository.MunicipioRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.context.annotation.ApplicationScope;

import javax.faces.context.FacesContext;
import javax.servlet.http.HttpServletRequest;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

@Controller
@ApplicationScope
public class HelperBean implements Serializable {

    public static final List<Uf> UFS_CONSULTA_GRAVAME = Arrays.asList(Uf.MG, Uf.SP, Uf.RJ, Uf.CE, Uf.SE);

    public static final List<Uf> UFS_DOCUMENTO_ARRECADACAO = Arrays.asList(Uf.BA, Uf.RJ, Uf.RS, Uf.CE);

    public static final List<Situacao> SITUACOES_ALTERACAO = Arrays.asList(Situacao.ERRO, Situacao.PENDENTE, Situacao.PENDENTE_PAGAMENTO, Situacao.BAIXADO);

    private static final long serialVersionUID = 1L;

    @Autowired
    private MunicipioRepository municipioRepository;

    @Autowired
    private MarcaRepository marcaRepository;

    @Autowired
    private ModeloRepository modeloRepository;

    public PagamentoCobranca[] getPagamentoCobrancas() {
        return PagamentoCobranca.values();
    }

    public SimNao[] getSimNao() {
        return SimNao.values();
    }

    public TipoGadE[] getTipoGadE() {
        return TipoGadE.values();
    }

    public Sistema[] getSistemas() {
        return Sistema.values();
    }

    public TipoMensagem[] getTiposMensagem() {
        return TipoMensagem.values();
    }

    public TipoContrato[] getTiposContrato() {
        return TipoContrato.values();
    }

    public List<TipoContrato> getTiposContratoAditivo() {
        return Arrays.asList(TipoContrato.CESSAO_DIREITO_DEVEDOR, TipoContrato.CESSAO_DIREITO_CREDOR, TipoContrato.SUBSTITUICAO_GARANTIA);
    }

    public TipoCobranca[] getTiposCobranca() {
        return TipoCobranca.values();
    }

    public TipoRestricao[] getTiposRestricao() {
        return TipoRestricao.values();
    }

    public TipoVeiculo[] getTipos() {
        return TipoVeiculo.values();
    }

    public Situacao[] getSituacoes() {
        return Situacao.values();
    }

    public List<Situacao> getSituacoesAlteracao() {
        return SITUACOES_ALTERACAO;
    }

    public SituacaoFinanceira[] getSituacoesFinanceiras() {
        return SituacaoFinanceira.values();
    }

    public TipoVrg[] getTipoVrg() {
        return TipoVrg.values();
    }


    public IndiceFinanceiro[] getIndiceFinanceiro() {
        return IndiceFinanceiro.values();
    }

    public OrigemCobranca[] getOrigensCobranca() {
        return OrigemCobranca.values();
    }

    public SituacaoCobranca[] getSituacoesCobranca() {
        return SituacaoCobranca.values();
    }

    public List<SituacaoCobranca> getSituacoesCobrancaContaAzul() {
        return Arrays.asList(SituacaoCobranca.GERADA, SituacaoCobranca.ENVIADA);
    }

    public Iterable<Marca> getMarcas() {
        return marcaRepository.findAll();
    }

    public List<Modelo> getModelos(Marca marca, Integer ano) {
        if (marca != null && ano != null) {
            return modeloRepository.findByMarcaAndAnoAndAtivoOrderByDescricao(marca, ano, SimNao.S);
        }
        return new ArrayList<>();
    }


    public TipoBaixaContrato[] getTiposBaixaContrato() {
        return TipoBaixaContrato.values();
    }

    public List<Municipio> getMunicipios(Uf uf) {
        if (uf != null) {
            return municipioRepository.findByUfOrderByDescricao(uf);
        }
        return new ArrayList<>();
    }

    public Uf[] getUfs() {
        return Uf.listar();
    }

    public SituacaoProcessoCredenciamento[] getSituacoesProcessoCredenciamento() {
        return SituacaoProcessoCredenciamento.values();
    }

    public Uf[] getUfsCobranca() {
        return Uf.values();
    }

    public List<Uf> getUfsConsultaGravame() {
        return UFS_CONSULTA_GRAVAME;
    }

    public List<Uf> getUfsDocumentoArrecadacao() {
        return UFS_DOCUMENTO_ARRECADACAO;
    }

    public TipoPesquisaGravame[] getTiposPesquisaGravame(Uf uf) {
        if (uf == Uf.RJ || uf == Uf.MG || uf == Uf.CE)
            return new TipoPesquisaGravame[]{TipoPesquisaGravame.CHASSI};

        return TipoPesquisaGravame.values();
    }

    public SituacaoSolicitacaoDocumento[] getSituacoesSolicitacaoDocumento() {
        return SituacaoSolicitacaoDocumento.values();
    }

    public TipoFinanceira[] getTiposFinanceira() {
        return TipoFinanceira.values();
    }

    public TipoPreco[] getTiposPreco() {
        return TipoPreco.values();
    }

    public TipoOperacao[] getTiposOperacao() {
        return TipoOperacao.values();
    }

    public String getSessionTime() {
        HttpServletRequest request = (HttpServletRequest) FacesContext.getCurrentInstance().getExternalContext().getRequest();
        return String.valueOf(request.getSession().getMaxInactiveInterval() / 60);
    }
}
