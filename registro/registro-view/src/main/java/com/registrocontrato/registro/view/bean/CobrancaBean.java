package com.registrocontrato.registro.view.bean;

import com.registrocontrato.infra.bean.BaseCrud;
import com.registrocontrato.infra.entity.*;
import com.registrocontrato.infra.exception.ServiceException;
import com.registrocontrato.infra.jsf.ViewScope;
import com.registrocontrato.infra.service.BaseService;
import com.registrocontrato.infra.service.audit.AuditContrato;
import com.registrocontrato.infra.util.PlaceconUtil;
import com.registrocontrato.registro.entity.Cobranca;
import com.registrocontrato.registro.entity.Credenciamento;
import com.registrocontrato.registro.entity.BilhetagemGravame;
import com.registrocontrato.registro.enums.SituacaoCobranca;
import com.registrocontrato.registro.repository.CredenciamentoRepository;
import com.registrocontrato.registro.service.cobranca.CobrancaService;
import com.registrocontrato.registro.service.cobranca.CobrancaValidator;
import com.registrocontrato.registro.service.cobranca.gravame.GravameService;
import com.registrocontrato.registro.service.dto.CobrancaDTO;
import com.registrocontrato.registro.service.util.NomeadorBoletosUtil;
import com.registrocontrato.seguranca.entity.Perfil;
import com.registrocontrato.seguranca.entity.Usuario;
import com.registrocontrato.seguranca.service.FinanceiraService;
import com.registrocontrato.seguranca.service.UsuarioService;
import org.apache.commons.lang.time.DateFormatUtils;
import org.primefaces.event.FileUploadEvent;
import org.primefaces.model.DefaultStreamedContent;
import org.primefaces.model.StreamedContent;
import org.primefaces.model.file.UploadedFile;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;

import javax.servlet.http.HttpServletResponse;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.util.*;

@Controller
@ViewScope
public class CobrancaBean extends BaseCrud<Cobranca, CobrancaDTO> {

    private static final String COBRANCAS_SELECIONADOS = "COBRANCAS_SELECIONADOS";

    private static final long serialVersionUID = 1L;

    private Date dataInicio;

    private Date dataFim;

    private Uf uf;


    private Financeira financeira;

    private List<Financeira> financeiraAgrupadas = new ArrayList<>();

    private Usuario usuarioLogado;

    private List<Cobranca> cobrancas = new ArrayList<>();

    private List<Cobranca> cobrancasSelecionadas = new ArrayList<>();

    @Value("${file-boleto.dir-read:null}")
    private String FILE_DIR_READ;

    @Value("${file-nf.dir-read:null}")
    private String fileNfDirRead;

    private final List<Uf> estadosLinhaDigitavel = Arrays.asList(Uf.SC, Uf.PE, Uf.PI, Uf.MS, Uf.PR, Uf.SE);

    @Autowired
    private CobrancaService service;

    @Autowired
    private CredenciamentoRepository credenciamentoRepository;

    @Autowired
    private UsuarioService usuarioService;

    private Boolean administrador = Boolean.FALSE;

    private final GravameService gravameService;

    @Autowired
    private FinanceiraService financeiraService;

    @Autowired
    private CobrancaValidator cobrancaValidator;

    public CobrancaBean(GravameService gravameService) {
        this.gravameService = gravameService;
    }

    @Override
    @SuppressWarnings("unchecked")
    public void postInitialization() {
        cobrancas = new ArrayList<>();
        super.postInitialization();
        CobrancaDTO filter = getFilter();
        filter.setUsuario(getUsername());
        usuarioLogado = usuarioService.findByCpfFinanceiras(getUsername());

        Usuario usuario = usuarioService.findByCpfFinanceiras(getUsername());
        administrador = usuario.isPerfilAdministrador();

        if (getSessionValue(COBRANCAS_SELECIONADOS) == null) {
            cobrancasSelecionadas = new ArrayList<>();
        } else {
            cobrancasSelecionadas = (List<Cobranca>) getSessionValue(COBRANCAS_SELECIONADOS);
        }
    }

    @Override
    public void loadDetails() {
        super.loadDetails();
        if (usuarioLogado.getPerfil() == Perfil.FINANCEIRA || !usuarioLogado.getFinanceiras().isEmpty()) {
            if (!usuarioLogado.getFinanceiras().contains(getEntity().getFinanceira())) {
                unauthorized();
            }
        }
        if (usuarioLogado.getUf() != null && !usuarioLogado.getUf().equals(getEntity().getEstado())) {
            unauthorized();
        }
    }

    @Override
    public String save() {
        return super.save();
    }

    public String enviarEmGrupo(String tipoDestinatario) throws ServiceException {
        putSession(COBRANCAS_SELECIONADOS, null);
        if (cobrancasSelecionadas.isEmpty()) {
            addMessageError("Selecione as Cobranças para reenviar em grupo");
            return null;
        }
        for (Cobranca co : cobrancasSelecionadas) {
            try {
                service.enviaEmailCobrancaEmitido(co, TipoDestinatarioCobranca.valueOf(tipoDestinatario));
            } catch (IOException e) {
                throw new ServiceException(e);
            }
        }
        addMessageInfo("Cobranças reenviadas");
        return "/cobranca/list-envio-email.xhtml?faces-redirect=true";
    }

    public void handleFileUploadXMLNF(FileUploadEvent event) throws IOException {
        try {
            UploadedFile file = event.getFile();
            validacaoArquivosDefault(file, Collections.singletonList("xml"));
            service.saveXmlNotaFiscal(entity, file.getInputStream());
        } catch (ServiceException e) {
            addMessageError(e.getMessage());
        }
    }

    public void handleFileUploadSngNF(FileUploadEvent event) throws IOException {
        try {
            UploadedFile file = event.getFile();
            validacaoArquivosDefault(file, Collections.singletonList("pdf"));
            service.saveNFSng(entity, file.getInputStream());
        } catch (ServiceException e) {
            addMessageError(e.getMessage());
        }
    }

    public void handleFileUploadBoletoSng(FileUploadEvent event) throws IOException {
        try {
            UploadedFile file = event.getFile();
            validacaoArquivosDefault(file, Collections.singletonList("pdf"));
            service.saveBoletoSng(entity, file.getInputStream());
        } catch (ServiceException e) {
            addMessageError(e.getMessage());
        }
    }

    public void handleFileUploadNF(FileUploadEvent event) throws IOException {
        try {
            UploadedFile file = event.getFile();
            validacaoArquivosDefault(file, Collections.singletonList("pdf"));
            service.saveNotaFiscal(entity, file.getInputStream());
        } catch (ServiceException e) {
            addMessageError(e.getMessage());
        }
    }

    public void handleFileUploadNotaReembolso(FileUploadEvent event) throws IOException {
        try {
            UploadedFile file = event.getFile();
            validacaoArquivosDefault(file, Collections.singletonList("pdf"));
            service.saveNotaReembolso(entity, file.getInputStream());
        } catch (ServiceException e) {
            addMessageError(e.getMessage());
        }
    }

    public void handleFileUploadBoletoCredenciada(FileUploadEvent event) throws IOException {
        try {
            UploadedFile file = event.getFile();
            validacaoArquivosDefault(file, Collections.singletonList("pdf"));
            service.saveBoletoCredenciada(entity, file.getInputStream());
        } catch (ServiceException e) {
            addMessageError(e.getMessage());
        }
    }

    public void handleFileUploadBoletoDetran(FileUploadEvent event) throws IOException {
        String segundo = (String) event.getComponent().getAttributes().get("segundoBoleto");
        try {
            UploadedFile file = event.getFile();
            validacaoArquivosDefault(file, Collections.singletonList("pdf"));
            service.saveBoletoDetran(entity, file.getInputStream(), "S".equals(segundo));
        } catch (ServiceException e) {
            addMessageError(e.getMessage());
        }
    }

    public void handleFileUploadBoletoDetranCobrancaUnificada(FileUploadEvent event) throws IOException {
        String boletoDetranFinanceiroPlace = (String) event.getComponent().getAttributes().get("boletoDetranFinanceiroPlace");
        try {
            UploadedFile file = event.getFile();
            validacaoArquivosDefault(file, Collections.singletonList("pdf"));
            service.saveBoletoDetranCobrancaUnificada(entity, file.getInputStream(), "S".equals(boletoDetranFinanceiroPlace));
        } catch (ServiceException e) {
            addMessageError(e.getMessage());
        }
    }

    public void previsao() {
        if (isCobrancaComEstadosUnificados()) {
            realizarPrevisaoCobrancaUnificada();
        } else {
            if (Objects.nonNull(uf)) {
                realizarPrevisaoCobrancaIndividual(uf);
            } else {
                addMessageError("Escolha uma UF, ou configure a financeira para unificar as cobranças.");
            }
        }
    }

    private void realizarPrevisaoCobrancaUnificada() {
        Cobranca cobranca = new Cobranca(financeira, PlaceconUtil.minDateTime(dataInicio), PlaceconUtil.maxDateTime(dataFim), SimNao.S, uf);
        Set<Uf> ufs = service.getUfsCredenciadasNaFinanceira(cobranca);

        if (!PlaceconUtil.isListaVaziaOuNula(ufs))
            ufs.forEach(uf -> validacaoInicial(new Cobranca(uf, PlaceconUtil.minDateTime(dataInicio), PlaceconUtil.maxDateTime(dataFim))));

        cobrancas.clear();
        List<Cobranca> cobrancasCalculadas = service.previsao(cobranca, ufs);
        cobrancas.addAll(cobrancasCalculadas);
    }

    private void realizarPrevisaoCobrancaIndividual(Uf uf) {
        validacaoInicial(new Cobranca(uf, PlaceconUtil.minDateTime(dataInicio), PlaceconUtil.maxDateTime(dataFim)));
        previsaoAposValidacao();
    }

    private Boolean isCobrancaComEstadosUnificados() {
        return isClienteComCobrancaUnificada() && Objects.isNull(uf) && Objects.nonNull(dataInicio) && Objects.nonNull(dataFim);
    }

    private Boolean isClienteComCobrancaUnificada() {
        if (Objects.nonNull(financeira)) {
            Financeira financeira = financeiraService.findByDocumento(this.financeira.getDocumento());
            return Objects.nonNull(financeira) && financeira.getUnificaCobrancas() == SimNao.S;
        }
        return false;
    }

    private void previsaoAposValidacao() {
        logger.info("Realizando previsão, após validações");
        cobrancas = new ArrayList<>();
        if (Objects.isNull(financeira)) {
            realizarPrevisaoDeTodasAsFinanceiras();
        } else {
            realizarPrevisaoDaFinanceira();
        }
    }

    private void realizarPrevisaoDeTodasAsFinanceiras() {
        service.findByFinanceirasCobrancaAberta(uf, dataInicio, dataFim).ifPresent(listaDeFinanceiras -> {
            listaDeFinanceiras.stream().filter(f -> f.getUnificaCobrancas() == SimNao.N || Objects.isNull(f.getUnificaCobrancas())).forEach(financeira -> {
                Cobranca cobranca = inicializarCobranca(financeira);
                validarSituacaoFinanceiraEstado(cobranca);
                cobranca = calcularCobranca(cobranca);
                cobrancas.add(cobranca);
                logger.info("Quantidade de cobranças: " + cobrancas.size());
            });
        });
    }

    private void validarSituacaoFinanceiraEstado(Cobranca cobranca) {
        try {
            cobrancaValidator.validarSituacaoFinanceiraEstado(cobranca);
        } catch (ServiceException e) {
            throw new RuntimeException(e);
        }
    }

    private void realizarPrevisaoDaFinanceira() {
        Financeira f = financeiraService.findOne(financeira.getId());
        if (f.getUnificaCobrancas() == SimNao.S && getUf() != null) {
            addMessageError("Financeira com Cobrança Unificada, remova a UF para gerar uma única cobrança");
        } else {
            Cobranca cobranca = inicializarCobranca(financeira);
            cobranca = calcularCobranca(cobranca);
            cobrancas.add(cobranca);
            logger.info("QTD Cob" + cobranca.getQuantidadeRegistros());
            logger.info("Quantidade de cobranças: " + cobrancas.size());
            logger.info("Realizou previsao da financeira com sucesso");
        }
    }

    private Cobranca calcularCobranca(Cobranca cobranca) {
        try {
            return service.calcularCobranca(cobranca);
        } catch (ServiceException e) {
            addMessageError(e.getMessage());
        }
        return null;
    }

    private Cobranca inicializarCobranca(Financeira financeira) {
        if (usuarioLogado.getUf() != null) {
            uf = usuarioLogado.getUf();
        }
        BilhetagemGravame gravame = gravameService.findByAtivo(uf, dataFim, financeira);
        Credenciamento credenciamento = credenciamentoRepository.findByAtivo(uf, dataFim);
        return new Cobranca(uf, financeira, PlaceconUtil.minDateTime(dataInicio), PlaceconUtil.maxDateTime(dataFim), credenciamento, gravame);
    }

    private void validacaoInicial(Cobranca cobranca) {
        try {
            cobrancaValidator.validacaoInicial(cobranca);
        } catch (ServiceException e) {
            addMessageError(e.getMessage());
        }
    }

    @AuditContrato(action = "Cobranca de Contrato")
    public String gerarCobranca() {
        try {
            cobrancaValidator.verificaNumeroPedidoCobrancas(cobrancas, service.getCnpjsNumeroPedido());
            if (cobrancas.stream().anyMatch(c -> c.getCobrancaUnificada() == SimNao.S)) {
                service.gerarCobrancaUnificada(cobrancas);
            } else {
//                service.enviaEmailFinanceirasSemFaturamento(cobrancas, uf, dataInicio, dataFim, financeira);
                service.save(cobrancas);
            }
            addMessageSuccess();
            return "/cobranca/list.xhtml?faces-redirect=true";
        } catch (Exception e) {
            addMessageError(e.getMessage());
        }
        return null;
    }

    public void download(Cobranca cobranca) {
        entity = cobranca;
    }

    public boolean numeroPedidoNecessario() {
        if (!PlaceconUtil.isListaVaziaOuNula(cobrancas)) for (Cobranca cobranca : cobrancas) {
            return service.getCnpjsNumeroPedido().contains(cobranca.getFinanceira().getDocumento());
        }
        return false;
    }

    public boolean cnpjRequerNumeroPedido(String documento) {
        return service.cnpjsNumeroPedidoContains(documento);
    }

    @Override
    public String delete(Long id) {
        try {
            service.cancelar(id);
            addMessageSuccess();
            return "/cobranca/list.xhtml?faces-redirect=true";
        } catch (ServiceException e) {
            addMessageError(e.getMessage());
        }
        return null;
    }

    public String getEstadosComReembolso() {

        return service.buscarUfsComBoletoDeReembolso(entity).toString();

    }

    public String getEstadosSemReembolso() {
        return service.buscarUfsSemBoletoDeReembolso(entity).toString();
    }

    public Long getTotalRegistros() {
        return cobrancas.stream().mapToLong(Cobranca::getQuantidadeRegistros).sum();
    }

    public BigDecimal getTotalDetran() {
        return cobrancas.stream().map(Cobranca::getValorDetran).reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    public BigDecimal getTotalCredenciada() {
        return cobrancas.stream().map(Cobranca::getValorCredenciada).reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    public BigDecimal getTotalDesconto() {
        return cobrancas.stream().map(Cobranca::getValorDesconto).reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    public BigDecimal getTotalValorCobrado() {
        return cobrancas.stream().map(Cobranca::getValorCobranca).reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    public BigDecimal getTotalValorCobrancaInformadoDetran() {
        return cobrancas.stream().map(Cobranca::getValorCobrancaInformadoDetran).reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    private Boolean isGrupoSafra(Cobranca cobranca) {
        GrupoFinanceira grupoFinanceira = cobranca.getFinanceira().getGrupoFinanceira();
        if (grupoFinanceira != null) return grupoFinanceira.getNome().toLowerCase().contains("safra");
        return false;

    }

    public void excel(Cobranca cobranca) throws Exception {
        HttpServletResponse response = (HttpServletResponse) getExternalContext().getResponse();
        response.setHeader("Content-Disposition", "attachment; filename=\"" + DateFormatUtils.format(new Date(), "dd/MM/yyyy HH:mm") + ".xlsx\"");

        if (isGrupoSafra(cobranca)) {
            service.relatorioGrupoSafra(cobranca, response);
        } else if (service.isBancoDoBrasil(cobranca.getFinanceira().getDocumento())) {
            service.relatorioAgrupadoBancoDoBrasil(cobranca);
        } else {
            service.relatorioDefault(cobranca, response);
        }
    }

    public StreamedContent getFile() {
        try {
            InputStream stream = new FileInputStream(FILE_DIR_READ + PlaceconUtil.leftPad(String.valueOf(entity.getId()), 11, "0") + ".pdf");
            return DefaultStreamedContent.builder().contentType("application/pdf").name("boleto-" + entity.getFinanceira().getDocumento() + "-" + PlaceconUtil.formataData(entity.getDataGeracao()) + ".pdf").stream(() -> stream).build();
        } catch (FileNotFoundException e) {
            addMessageError("Boleto não disponível para esse estado.");
        }
        return null;
    }

    public StreamedContent getFileSngBoleto() {
        try {
            Calendar prefixo = Calendar.getInstance();
            prefixo.setTime(entity.getDataInicio());
            String numDocumento = NomeadorBoletosUtil.gerarNumeroBoletoSng(entity.getId());
            InputStream stream = new FileInputStream(FILE_DIR_READ + numDocumento + ".pdf");
            return DefaultStreamedContent.builder().contentType("application/pdf").name("boleto-detran-" + entity.getFinanceira().getDocumento() + "-" + PlaceconUtil.formataData(entity.getDataGeracao()) + ".pdf").stream(() -> stream).build();
        } catch (FileNotFoundException e) {
            addMessageError("Boleto não disponível para esse estado.");
        }
        return null;
    }

    public StreamedContent getFileBoletoReembolso() {
        try {
            Calendar prefixo = Calendar.getInstance();
            prefixo.setTime(entity.getDataInicio());
            String numDocumento = NomeadorBoletosUtil.gerarNumeroBoletoReembolso(entity.getDataInicio(), entity.getId());
            InputStream stream = new FileInputStream(FILE_DIR_READ + numDocumento + ".pdf");
            return DefaultStreamedContent.builder().contentType("application/pdf").name("boleto-detran-" + entity.getFinanceira().getDocumento() + "-" + PlaceconUtil.formataData(entity.getDataGeracao()) + ".pdf").stream(() -> stream).build();
        } catch (FileNotFoundException e) {
            addMessageError("Boleto não disponível para esse estado.");
        }
        return null;
    }

    public Boolean mostrarLinhaDigitavel(Cobranca c) {
        if (!estadosLinhaDigitavel.contains(c.getEstado())) return false;

        if (c.getSituacaoCobranca() == SituacaoCobranca.GERADA) return false;

        if (administrador == Boolean.TRUE) return Boolean.TRUE;

        SituacaoFinanceiraEstado s = financeiraService.findSituacaoFinanceiraEstado(c.getEstado(), c.getFinanceira());
        if (s == null) return Boolean.FALSE;

        return c.getReembolsoLinhaDigitavel() == SimNao.N;
    }


    public StreamedContent getFileLinhaDigitavel() {
        try {
            Calendar prefixo = Calendar.getInstance();
            prefixo.setTime(entity.getDataInicio());
            String numeroDocumento = NomeadorBoletosUtil.getNumeroDocumentoBoletoDetran(entity.getDataInicio(), entity.getId());
            InputStream stream = new FileInputStream(FILE_DIR_READ + numeroDocumento + ".pdf");
            return DefaultStreamedContent.builder().contentType("application/pdf").name("boleto-" + numeroDocumento + ".pdf").stream(() -> stream).build();
        } catch (FileNotFoundException e) {
            addMessageError("Boleto não disponível para esse estado.");
        }
        return null;
    }

    public StreamedContent getFileBoletoUnificadaDetranCliente() {
        try {
            Calendar prefixo = Calendar.getInstance();
            prefixo.setTime(entity.getDataInicio());
            String numDocumento = NomeadorBoletosUtil.gerarNumeroBoletoDetranCarregadoManualmenteParaCliente(entity.getDataInicio(), entity.getId());
            InputStream stream = new FileInputStream(FILE_DIR_READ + numDocumento + ".pdf");
            return DefaultStreamedContent.builder().contentType("application/pdf").name("boleto-unificada-cliente-" + entity.getFinanceira().getDocumento() + "-" + PlaceconUtil.formataData(entity.getDataGeracao()) + ".pdf").stream(() -> stream).build();
        } catch (FileNotFoundException e) {
            addMessageError("Boleto não disponível para esse estado.");
        }
        return null;
    }

    public StreamedContent getFileBoletoUnificadaDetranFinanceiroPlace() {
        try {
            Calendar prefixo = Calendar.getInstance();
            prefixo.setTime(entity.getDataInicio());
            String numDocumento = NomeadorBoletosUtil.gerarNumeroBoletoDetranCarregadoManualmenteParaFinanceiro(entity.getDataInicio(), entity.getId());
            InputStream stream = new FileInputStream(FILE_DIR_READ + numDocumento + ".pdf");
            return DefaultStreamedContent.builder().contentType("application/pdf").name("boleto-unificada-financeiro-place-" + entity.getFinanceira().getDocumento() + "-" + PlaceconUtil.formataData(entity.getDataGeracao()) + ".pdf").stream(() -> stream).build();
        } catch (FileNotFoundException e) {
            addMessageError("Boleto não disponível para esse estado.");
        }
        return null;
    }

    public StreamedContent getFileBoletoDetranManual() {
        try {
            Calendar prefixo = Calendar.getInstance();
            prefixo.setTime(entity.getDataInicio());
            String numDocumento = NomeadorBoletosUtil.gerarNumeroBoletoDetranCarregadoManualmente(entity.getDataInicio(), entity.getId());
            InputStream stream = new FileInputStream(FILE_DIR_READ + numDocumento + ".pdf");
            return DefaultStreamedContent.builder().contentType("application/pdf").name("boleto-2-detran-" + entity.getFinanceira().getDocumento() + "-" + PlaceconUtil.formataData(entity.getDataGeracao()) + ".pdf").stream(() -> stream).build();
        } catch (FileNotFoundException e) {
            addMessageError("Boleto não disponível para esse estado.");
        }
        return null;
    }

    public StreamedContent getFileXmlNf() {
        try {
            InputStream stream = new FileInputStream(fileNfDirRead + entity.getXmlNotaFiscal());
            return DefaultStreamedContent.builder().contentType("xml").name("nota-fiscal-xml" + entity.getFinanceira().getDocumento() + "-" + PlaceconUtil.formataData(entity.getDataGeracao()) + ".xml").stream(() -> stream).build();
        } catch (FileNotFoundException e) {
            addMessageError("Nota Fiscal em XML não disponível.");
        }
        return null;
    }

    public StreamedContent getFileNf() {
        try {
            InputStream stream = new FileInputStream(fileNfDirRead + entity.getNotaFiscal());
            return DefaultStreamedContent.builder().contentType("application/pdf").name("nota-fiscal-" + entity.getFinanceira().getDocumento() + "-" + PlaceconUtil.formataData(entity.getDataGeracao()) + ".pdf").stream(() -> stream).build();
        } catch (FileNotFoundException e) {
            addMessageError("Nota Fiscal não disponível.");
        }
        return null;
    }

    public StreamedContent getFileSngNf() {
        try {
            InputStream stream = new FileInputStream(fileNfDirRead + entity.getNotaFiascalSng());
            return DefaultStreamedContent.builder().contentType("application/pdf").name("nota-fiscal-" + entity.getFinanceira().getDocumento() + "-" + PlaceconUtil.formataData(entity.getDataGeracao()) + ".pdf").stream(() -> stream).build();
        } catch (FileNotFoundException e) {
            addMessageError("Nota Fiscal não disponível.");
        }
        return null;
    }

    public StreamedContent getFileNotaReembolso() {
        try {
            InputStream stream = new FileInputStream(fileNfDirRead + entity.getNotaReembolso());
            return DefaultStreamedContent.builder().contentType("application/pdf").name("nota-reembolso-" + entity.getFinanceira().getDocumento() + "-" + PlaceconUtil.formataData(entity.getDataGeracao()) + ".pdf").stream(() -> stream).build();
        } catch (FileNotFoundException e) {
            addMessageError("Nota de Reembolso não disponível.");
        }
        return null;
    }

    public void enviarEmLote() {
        int enviadosComSucesso = 0;
        if (PlaceconUtil.isListaVaziaOuNula(cobrancasSelecionadas)) {
            addMessageError("Selecione pelo menos uma cobrança para enviar.");
            return;
        } else {
            for (Cobranca c : cobrancasSelecionadas) {
                try {
                    service.enviarCobranca(c);
                    enviadosComSucesso++;
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }
        if (enviadosComSucesso == cobrancasSelecionadas.size()) {
            addMessageInfo(String.format("%d cobranças enviadas de %d selecionadas", enviadosComSucesso, cobrancasSelecionadas.size()));
        } else {
            addMessageWarn(String.format("%d cobranças enviadas com sucesso de %d selecionadas.", enviadosComSucesso, cobrancasSelecionadas.size()));
        }
        cobrancasSelecionadas.clear();
    }


    @Override
    public BaseService<Cobranca, CobrancaDTO> getService() {
        return service;
    }

    public List<Cobranca> getCobrancas() {
        return cobrancas;
    }

    public void setCobrancas(List<Cobranca> cobrancas) {
        this.cobrancas = cobrancas;
    }

    public Date getDataInicio() {
        return dataInicio;
    }

    public void setDataInicio(Date dataInicio) {
        this.dataInicio = dataInicio;
    }

    public Date getDataFim() {
        return dataFim;
    }

    public void setDataFim(Date dataFim) {
        this.dataFim = dataFim;
    }

    public Uf getUf() {
        return uf;
    }

    public void setUf(Uf uf) {
        this.uf = uf;
    }

    public Financeira getFinanceira() {
        return financeira;
    }

    public void setFinanceira(Financeira financeira) {
        this.financeira = financeira;
    }

    public List<Cobranca> getCobrancasSelecionadas() {
        return cobrancasSelecionadas;
    }

    public void setCobrancasSelecionadas(List<Cobranca> cobrancasSelecionadas) {
        this.cobrancasSelecionadas = cobrancasSelecionadas;
    }
}
