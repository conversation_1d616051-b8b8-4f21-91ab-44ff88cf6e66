package com.registrocontrato.registro.view.bean;

import com.registrocontrato.infra.bean.BaseCrud;
import com.registrocontrato.infra.jsf.ViewScope;
import com.registrocontrato.infra.service.BaseService;
import com.registrocontrato.registro.dto.BilhetagemGravameDTO;
import com.registrocontrato.registro.entity.BilhetagemGravame;
import com.registrocontrato.registro.service.cobranca.gravame.GravameService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;

@Controller
@ViewScope
public class GravameBean extends BaseCrud<BilhetagemGravame, BilhetagemGravameDTO> {


    @Autowired
    private GravameService gravameService;


    @Override
    public BaseService<BilhetagemGravame, BilhetagemGravameDTO> getService() {
        return gravameService;
    }
}
