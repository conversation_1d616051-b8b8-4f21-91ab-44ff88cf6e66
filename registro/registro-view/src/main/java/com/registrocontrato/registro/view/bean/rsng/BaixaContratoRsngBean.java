package com.registrocontrato.registro.view.bean.rsng;

import com.google.common.base.Strings;
import com.registrocontrato.commons.ws.dto.rsng.BaixarApontamentoRequest;
import com.registrocontrato.commons.ws.dto.rsng.CancelamentoBaixaApontamentoResponse;
import com.registrocontrato.commons.ws.dto.rsng.CancelarApontamentoRequest;
import com.registrocontrato.commons.ws.rsng.ContratoCombinadoService;
import com.registrocontrato.commons.ws.rsng.ContratoRsngService;
import com.registrocontrato.infra.bean.BaseBean;
import com.registrocontrato.infra.entity.Financeira;
import com.registrocontrato.infra.entity.Municipio;
import com.registrocontrato.infra.entity.Uf;
import com.registrocontrato.infra.exception.ServiceException;
import com.registrocontrato.infra.jsf.ViewScope;
import com.registrocontrato.infra.service.audit.AuditContrato;
import com.registrocontrato.registro.entity.Contrato;
import com.registrocontrato.registro.entity.ContratoRsng;
import com.registrocontrato.registro.entity.Veiculo;
import com.registrocontrato.registro.entity.VeiculoRsng;
import com.registrocontrato.registro.service.ContratoService;
import com.registrocontrato.registro.service.MunicipioService;
import com.registrocontrato.registro.service.dto.ContratoCombinadoDTO;
import com.registrocontrato.registro.service.dto.ContratoCombinadoFilter;
import com.registrocontrato.seguranca.service.FinanceiraService;
import org.primefaces.model.FilterMeta;
import org.primefaces.model.LazyDataModel;
import org.primefaces.model.SortMeta;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Controller;

import javax.annotation.PostConstruct;
import javax.inject.Inject;
import java.util.List;
import java.util.Map;

@Controller
@ViewScope
public class BaixaContratoRsngBean extends BaseBean {

    private final ContratoRsngService contratoRsngService;
    private final ContratoCombinadoService contratoCombinadoService;
    private final FinanceiraService financeiraService;
    private final ContratoService contratoService;

    private Integer size = 10;
    private Long idToEdit;
    private String tipo;
    private String localidadeDevedor;
    private ContratoCombinadoFilter filter = new ContratoCombinadoFilter();
    private ContratoCombinadoDTO entity = new ContratoCombinadoDTO();
    private LazyDataModel<ContratoCombinadoDTO> list;

    @Inject
    public BaixaContratoRsngBean(ContratoRsngService contratoRsngService,
                                 ContratoCombinadoService contratoCombinadoService,
                                 FinanceiraService financeiraService,
                                 ContratoService contratoService) {
        this.contratoRsngService = contratoRsngService;
        this.contratoCombinadoService = contratoCombinadoService;
        this.financeiraService = financeiraService;
        this.contratoService = contratoService;
    }

    @PostConstruct
    public void postInitialization() {
        filter = new ContratoCombinadoFilter();
        search();
    }

    public void loadDetails() {
        entity = getService().findOne(idToEdit, tipo);

        if (entity != null && entity.getContratoRsng() == null) {
            if ("RSNG".equals(tipo)) {
                ContratoRsng contratoRsng = contratoRsngService.findOne(entity.getIdContratoRsng());
                entity.setContratoRsng(contratoRsng);
            } else {
                Contrato contrato = contratoService.findOne(entity.getIdContrato());
                entity.setContrato(contrato);
            }
        }
    }

    public void search() {
        ContratoCombinadoFilter filter = getFilter();
        filter.setUsuario(getUsername());

        setList(new LazyDataModel<ContratoCombinadoDTO>() {

            private static final long serialVersionUID = 1L;

            @Override
            public int count(Map<String, FilterMeta> filterBy) {
                return 0;
            }

            @Override
            public List<ContratoCombinadoDTO> load(int first, int pageSize, Map<String, SortMeta> sortBy, Map<String, FilterMeta> filterBy) {
                Page<ContratoCombinadoDTO> page = contratoCombinadoService.findAll(first, getSize(), filter);
                setRowCount((int) page.getTotalElements());
                setPageSize(getSize());
                return page.getContent();
            }
        });
    }

    @AuditContrato(action = "Baixa de Contrato")
    public String baixar() {
        if (entity.getContratoRsng() == null || entity.getContratoRsng().getVeiculos() == null || entity.getContratoRsng().getVeiculos().isEmpty()) {
            addMessageError("Não há veículos para baixar");
            return null;
        }

        VeiculoRsng veiculoRsng = entity.getContratoRsng().getVeiculos().get(0);
        BaixarApontamentoRequest request = new BaixarApontamentoRequest();
        request.setChassi(veiculoRsng.getNumeroChassi());
        request.setGravame(veiculoRsng.getApontamento());

        try {
            List<CancelamentoBaixaApontamentoResponse> cancelamento = contratoRsngService.cancelarBaixarApontamento(request);
            for (CancelamentoBaixaApontamentoResponse c : cancelamento) {
                if (!Strings.isNullOrEmpty(c.getMensagemSng())) {
                    addMessageError(c.getMensagemSng());
                    break;
                } else {
                    addMessageInfo("Contrato baixado com sucesso");
                }
            }
        } catch (Exception e) {
            addMessageError("Erro ao realizar baixa do contrato: " + e.getMessage());
        }
        return "/baixarsng/form-detail.xhtml?faces-redirect=true&id=" + entity.getContratoRsng().getId() + "&tipo=" + getTipo();
    }

    public String cancelarBaixa(ContratoCombinadoDTO entity) {
        if (entity.getTipo().equals("RSNG")) {
            return CancelarBaixaContratoSng(entity);
        } else {
            return CancelarBaixaContrato(entity);
        }
    }

    public String CancelarBaixaContrato(ContratoCombinadoDTO entity) {
        if (entity.getContrato() == null || entity.getContrato().getVeiculos() == null || entity.getContrato().getVeiculos().isEmpty()) {
            addMessageError("Não há veículos para baixar");
            return null;
        }

        Veiculo veiculoRsng = entity.getContrato().getVeiculos().get(0);
        CancelarApontamentoRequest request = new CancelarApontamentoRequest();
        request.setChassi(veiculoRsng.getNumeroChassi());
        request.setGravame(veiculoRsng.getNumeroGravame());

        try {
            List<CancelamentoBaixaApontamentoResponse> cancelamento = contratoRsngService.cancelarBaixarApontamentoContrato(request);
            for (CancelamentoBaixaApontamentoResponse c : cancelamento) {
                if (!Strings.isNullOrEmpty(c.getMensagemSng())) {
                    addMessageError(c.getMensagemSng());
                    break;
                } else {
                    addMessageInfo("Baixa de contrato cancelado com sucesso");
                }
            }
        } catch (Exception e) {
            addMessageError("Erro ao cancelar baixa do contrato: " + e.getMessage());
        }
        return "/contrato/form-detail.xhtml?faces-redirect=true&id=" + entity.getContrato().getId();
    }

    public String CancelarBaixaContratoSng(ContratoCombinadoDTO entity) {
        if (entity.getContratoRsng() == null || entity.getContratoRsng().getVeiculos() == null || entity.getContratoRsng().getVeiculos().isEmpty()) {
            addMessageError("Não há veículos para baixar");
            return null;
        }

        VeiculoRsng veiculoRsng = entity.getContratoRsng().getVeiculos().get(0);
        CancelarApontamentoRequest request = new CancelarApontamentoRequest();
        request.setChassi(veiculoRsng.getNumeroChassi());
        request.setGravame(veiculoRsng.getApontamento());

        try {
            List<CancelamentoBaixaApontamentoResponse> cancelamento = contratoRsngService.cancelarBaixarApontamento(request);
            for (CancelamentoBaixaApontamentoResponse c : cancelamento) {
                if (!Strings.isNullOrEmpty(c.getMensagemSng())) {
                    addMessageError(c.getMensagemSng());
                    break;
                } else {
                    addMessageInfo("Baixa de contrato cancelado com sucesso");
                }
            }
        } catch (Exception e) {
            addMessageError("Erro ao cancelar baixa do contrato: " + e.getMessage());
        }
        return "/contrato/form-detail.xhtml?faces-redirect=true&id=" + getEntity().getId();
    }

    public String baixar(ContratoCombinadoDTO entity) {
        if (entity.getTipo().equals("RSNG")) {
            return baixarContratoSng(entity);
        } else {
            return baixarContrato(entity);
        }
    }

    public String baixarContrato(ContratoCombinadoDTO entity) {
        if (entity.getContrato() == null || entity.getContrato().getVeiculos() == null || entity.getContrato().getVeiculos().isEmpty()) {
            addMessageError("Não há veículos para baixar");
            return null;
        }

        Veiculo veiculoRsng = entity.getContrato().getVeiculos().get(0);
        BaixarApontamentoRequest request = new BaixarApontamentoRequest();
        request.setChassi(veiculoRsng.getNumeroChassi());
        request.setGravame(veiculoRsng.getNumeroGravame());

        try {
            List<CancelamentoBaixaApontamentoResponse> cancelamento = contratoRsngService.cancelarBaixarApontamentoContrato(request);
            for (CancelamentoBaixaApontamentoResponse c : cancelamento) {
                if (!Strings.isNullOrEmpty(c.getMensagemSng())) {
                    addMessageError(c.getMensagemSng());
                    break;
                } else {
                    addMessageInfo("Contrato baixado com sucesso");
                }
            }
        } catch (Exception e) {
            addMessageError("Erro ao realizar baixa do contrato: " + e.getMessage());
        }
        return "/contrato/form-detail.xhtml?faces-redirect=true&id=" + getEntity().getId();
    }

    public String baixarContratoSng(ContratoCombinadoDTO entity) {
        if (entity.getContratoRsng() == null || entity.getContratoRsng().getVeiculos() == null || entity.getContratoRsng().getVeiculos().isEmpty()) {
            addMessageError("Não há veículos para baixar");
            return null;
        }

        VeiculoRsng veiculoRsng = entity.getContratoRsng().getVeiculos().get(0);
        BaixarApontamentoRequest request = new BaixarApontamentoRequest();
        request.setChassi(veiculoRsng.getNumeroChassi());
        request.setGravame(veiculoRsng.getApontamento());

        try {
            List<CancelamentoBaixaApontamentoResponse> cancelamento = contratoRsngService.cancelarBaixarApontamento(request);
            for (CancelamentoBaixaApontamentoResponse c : cancelamento) {
                if (!Strings.isNullOrEmpty(c.getMensagemSng())) {
                    addMessageError(c.getMensagemSng());
                    break;
                } else {
                    addMessageInfo("Contrato baixado com sucesso");
                }
            }
        } catch (Exception e) {
            addMessageError("Erro ao realizar baixa do contrato: " + e.getMessage());
        }
        return "/contrato/form-detail.xhtml?faces-redirect=true&id=" + getEntity().getId();
    }

    public void definirLiberacaoCredito() {
        if (entity.getContratoRsng() != null && entity.getContratoRsng().getFinanceira() != null) {
            Financeira financeira = financeiraService.findOne(entity.getContratoRsng().getFinanceira().getId());
            entity.getContratoRsng().setFinanceira(financeira);
            entity.getContratoRsng().setMunicipioLiberacao(financeira.getMunicipio());
            entity.getContratoRsng().setUfLiberacaoCredito(financeira.getUfEndereco());
        }
    }

    public void reset0Km(int index) {
        if (entity.getContratoRsng() != null && entity.getContratoRsng().getVeiculos() != null &&
            index >= 0 && index < entity.getContratoRsng().getVeiculos().size()) {

            VeiculoRsng veiculo = entity.getContratoRsng().getVeiculos().get(index);
            if (veiculo.getVeiculo0Km()) {
                veiculo.setPlaca(null);
                veiculo.setUf(null);
                veiculo.setNumeroRenavam(null);
            }
        }
    }

    public void addVeiculo() {
        if (entity.getContratoRsng() != null) {
            VeiculoRsng veiculo = new VeiculoRsng();
            veiculo.setVeiculo0Km(Boolean.TRUE);
            veiculo.setContratoRsng(entity.getContratoRsng());
            entity.getContratoRsng().getVeiculos().add(veiculo);
        }
    }

    public void removeVeiculo(int index) throws ServiceException {
        if (entity.getContratoRsng() != null && entity.getContratoRsng().getVeiculos() != null) {
            List<VeiculoRsng> veiculos = entity.getContratoRsng().getVeiculos();

            if (veiculos.size() > 1 && index >= 0 && index < veiculos.size()) {
                VeiculoRsng v = veiculos.get(index);
                // Veículo registrado não pode ser excluído
                // if (veiculoFoiRegistrado(v, entity.getContratoRsng())) {
                //     String mensagem = String.format("O Veículo de chassi %s foi registrado" +
                //                 " com o código de confirmação %s",
                //         v.getNumeroChassi(), v.getNumeroRegistroDetran());
                //     addMessageError(mensagem);
                //     return;
                // }

                veiculos.remove(index);
                addMessageInfo(String.format("Removido o veículo de chassi %s", v.getNumeroChassi()));
            }
        }
    }

    public void substituirVeiculo(int index) {
        if (entity.getContratoRsng() != null && entity.getContratoRsng().getVeiculos() != null &&
            index >= 0 && index < entity.getContratoRsng().getVeiculos().size()) {

            VeiculoRsng veiculo = entity.getContratoRsng().getVeiculos().get(index);
            if (entity.getContratoRsng().getChassiSubstituicao() == null) {
                entity.getContratoRsng().setChassiSubstituicao(veiculo.getNumeroChassi());
            } else if (veiculo.getNumeroChassi() != null) {
                entity.getContratoRsng().setChassiSubstituicao(entity.getContratoRsng().getChassiSubstituicao() + ";" + veiculo.getNumeroChassi());
            }

            Long id = veiculo.getId();
            veiculo = new VeiculoRsng();
            veiculo.setId(id);
            veiculo.setVeiculo0Km(Boolean.TRUE);
            veiculo.setContratoRsng(entity.getContratoRsng());
            entity.getContratoRsng().getVeiculos().set(index, veiculo);
        }
    }

    public void findMunicipioDevedor() {
        // Implementação para buscar municípios do devedor
    }

    public void buscarDadosCpfCnpjDevedor() {
        // Implementação para buscar dados do CPF/CNPJ do devedor
    }

    public void resetTipoVrg() {
        if (entity.getContratoRsng() != null &&
            entity.getContratoRsng().getTipoRestricao() != null &&
            !entity.getContratoRsng().getTipoRestricao().toString().equals("ARRENDAMENTO")) {

            entity.getContratoRsng().setTipoVrg(null);
            entity.getContratoRsng().setValorVrg(null);
            entity.getContratoRsng().setClausulaPenalVrg(null);
        }
    }

    public void consultarGravame(VeiculoRsng veiculo) {
        // Implementação para consultar gravame
    }

    public ContratoCombinadoService getService() {
        return contratoCombinadoService;
    }

    public void setList(LazyDataModel<ContratoCombinadoDTO> list) {
        this.list = list;
    }

    public LazyDataModel<ContratoCombinadoDTO> getList() {
        return list;
    }

    public ContratoCombinadoFilter getFilter() {
        return filter;
    }

    public void setFilter(ContratoCombinadoFilter filter) {
        this.filter = filter;
    }

    public ContratoCombinadoDTO getEntity() {
        return entity;
    }

    public void setEntity(ContratoCombinadoDTO entity) {
        this.entity = entity;
    }

    public Long getIdToEdit() {
        return idToEdit;
    }

    public void setIdToEdit(Long idToEdit) {
        this.idToEdit = idToEdit;
    }

    public Integer getSize() {
        return size;
    }

    public void setSize(Integer size) {
        this.size = size;
    }

    public String getTipo() {
        return tipo;
    }

    public void setTipo(String tipo) {
        this.tipo = tipo;
    }

    public String getLocalidadeDevedor() {
        return localidadeDevedor;
    }

    public void setLocalidadeDevedor(String localidadeDevedor) {
        this.localidadeDevedor = localidadeDevedor;
    }
}
