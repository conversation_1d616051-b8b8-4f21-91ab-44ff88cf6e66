package com.registrocontrato.registro.view.bean;

import com.registrocontrato.infra.bean.BaseBean;
import com.registrocontrato.infra.entity.Financeira;
import com.registrocontrato.infra.entity.SimNao;
import com.registrocontrato.infra.entity.SituacaoFinanceiraEstado;
import com.registrocontrato.infra.entity.Uf;
import com.registrocontrato.registro.service.CredenciamentoService;
import com.registrocontrato.seguranca.entity.Perfil;
import com.registrocontrato.seguranca.entity.Permissao;
import com.registrocontrato.seguranca.entity.Usuario;
import com.registrocontrato.seguranca.service.FinanceiraService;
import com.registrocontrato.seguranca.service.UsuarioService;
import org.apache.commons.io.IOUtils;
import org.primefaces.model.DefaultStreamedContent;
import org.primefaces.model.StreamedContent;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.context.annotation.SessionScope;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.*;

@Controller
@SessionScope
public class HelperSessionBean extends BaseBean {

    private static final String CNPJ_UNIPRIME = "01286361000109";

    private static final long serialVersionUID = 1L;

    private Usuario usuario;

    private List<Financeira> financeiras;

    private List<Uf> ufsCredenciamento;

    private Map<Financeira, List<Uf>> ufs = new HashMap<Financeira, List<Uf>>();

    private StreamedContent logo;

    private StreamedContent foto;

    private String valorPesquisaRapida;

    private List<String> ultimosAnos;

    public HelperSessionBean(
        UsuarioService usuarioService,
        FinanceiraService financeiraService,
        CredenciamentoService credenciamentoService) {
        this.usuarioService = usuarioService;
        this.financeiraService = financeiraService;
        this.credenciamentoService = credenciamentoService;
    }

    private final UsuarioService usuarioService;

    private final FinanceiraService financeiraService;

    private final CredenciamentoService credenciamentoService;

    public Usuario getUsuario() {
        if (usuario == null && getUsername() != null) {
            usuario = usuarioService.findByCpfFinanceiras(getUsername());
        }
        return usuario;
    }

    public String pesquisaRapida() {
        String url = String.format("/contrato/list.xhtml?consultaRapida=true&valor=%s&faces-redirect=true", valorPesquisaRapida);
        valorPesquisaRapida = null;
        return url;
    }

    public List<Financeira> getFinanceiras() {
        if (financeiras == null && getUsername() != null) {
            Usuario usuario = getUsuario();
            if (usuario.getPerfil() == Perfil.FINANCEIRA || !usuario.getFinanceiras().isEmpty()) {
                financeiras = usuario.getFinanceiras();
            } else if (usuario.getPerfil() == Perfil.DETRAN) {
                financeiras = Collections.emptyList();
            } else {
                financeiras = financeiraService.findAtivos();
            }

            for (Financeira f : financeiras) {
                for (SituacaoFinanceiraEstado e : f.getSituacoesFinanceiraEstado()) {
                    if (e.getSituacaoFinanceira() == SimNao.S) {
                        List<Uf> list = ufs.get(f);
                        if (list == null) {
                            list = new ArrayList<>();
                        }
                        list.add(e.getUf());
                        ufs.put(f, list);
                    }
                }
            }
        }
        return financeiras;
    }

    public List<Uf> getUfs(Financeira financeira) {
        List<Financeira> financeiras = getFinanceiras();
        if (financeiras != null && financeiras.size() == 1) {
            return ufs.get(financeiras.get(0));
        }
        return ufs.get(financeira);
    }

    public List<Uf> getUfsCredenciamento() {
        if (ufsCredenciamento == null) {
            Uf uf = getUsuario().getUf();
            if (uf == null) {
                ufsCredenciamento = credenciamentoService.findUf();
            } else {
                ufsCredenciamento = Arrays.asList(uf);
            }
        }
        return ufsCredenciamento;
    }

    public StreamedContent getLogo() {
        if (logo == null) {
            if (getUsuario() != null && getUsuario().getPerfil() == Perfil.DETRAN) {
                try {
                    InputStream stream = getExternalContext().getResourceAsStream("/templates/assets/images/" + getUsuario().getUf() + "_sm.png");
                    if (stream != null) {
                        logo = DefaultStreamedContent.builder()
                                .stream(() -> {
                                    try {
                                        return new ByteArrayInputStream(IOUtils.toByteArray(stream));
                                    } catch (IOException e) {
                                        logger.error("DETRAN sem logo configurada", e);
                                    }
                                    return null;
                                }).build();
                    }
                }catch (Exception e) {
                    logger.error("DETRAN sem logo configurada " + e);
                }
            } else if (getUsuario() != null && getUsuario().getPerfil() == Perfil.FINANCEIRA) {
                byte[] logoFinanceira = null;

                if (getFinanceiras() != null) {
                    Financeira financeira = financeiraService.findOne(getFinanceiras().get(0).getId());
                    if (financeira.getGrupoFinanceira() != null) {
                        logoFinanceira = financeira.getGrupoFinanceira().getLogoFinanceira();
                    } else {
                        logoFinanceira = financeira.getLogoFinanceira();
                    }
                }

                if (logoFinanceira != null) {
                    byte[] finalLogoFinanceira = logoFinanceira;
                    logo = DefaultStreamedContent.builder()
                            .stream(() -> new ByteArrayInputStream(finalLogoFinanceira)).build();
                }
            }
        }
        return logo;
    }

    public StreamedContent getFoto() {
        if (foto == null) {
            if (getUsuario() != null && getUsuario().getFoto() != null) {
                foto = DefaultStreamedContent.builder()
                        .stream(() -> new ByteArrayInputStream(getUsuario().getFoto())).build();

            }
        }
        return foto;
    }

    public void setFoto(StreamedContent foto) {
        this.foto = foto;
    }

    public String getValorPesquisaRapida() {
        return valorPesquisaRapida;
    }

    public void setValorPesquisaRapida(String valorPesquisaRapida) {
        this.valorPesquisaRapida = valorPesquisaRapida;
    }

    public List<String> getUltimosAnos() {
        if (ultimosAnos == null) {
            Calendar calendar = Calendar.getInstance();
            int anoAtual = calendar.get(Calendar.YEAR);
            ultimosAnos = Arrays.asList(String.valueOf(anoAtual), String.valueOf(anoAtual - 1), String.valueOf(anoAtual - 2));
        }
        return ultimosAnos;
    }

    public void setUltimosAnos(List<String> ultimosAnos) {
        this.ultimosAnos = ultimosAnos;
    }

    public String getFormularioPadrao() {
        if (getUsuario().isPerfilFinanceira()) {
            for (Financeira f : getUsuario().getFinanceiras()) {
                if (f.getDocumento().equals(CNPJ_UNIPRIME)) {
                    return "form-inputs-uniprime.xhtml";
                }
            }
        }

        return "form-inputs-default.xhtml";
    }

    public String getPaginaInicial() {
        if (getUsuario().isPerfilFinanceira()) {
            return "dashboard/principal-financeira.xhtml";
        } else if (getUsuario().isPerfilAdministrador()) {
            return "dashboard/principal-admin.xhtml";
        }
        return "dashboard/geral.xhtml";
    }

    public String getDashboardRelatorio() {
        if (getUsuario().isPerfilAdministrador()) {
            return montaURLComContextPath("/dashboard/relatorio-admin.xhtml");
        }
        if (getUsuario().isPerfilFinanceira()) {
            return montaURLComContextPath("/dashboard/relatorio-financeira.xhtml");
        }
        return montaURLComContextPath("");
    }

    public String getDashboardObservatorio() {
        if (getUsuario().isPerfilAdministrador()) {
            return montaURLComContextPath("/dashboard/observatorio-admin.xhtml");
        }
        if (getUsuario().isPerfilFinanceira()) {
            return  montaURLComContextPath("/dashboard/observatorio-financeira.xhtml");
        }
        return montaURLComContextPath("");
    }

    public String getAcompanhamento() {
        if (getUsuario().isPerfilAdministrador()) {
            return montaURLComContextPath("/acompanhamento/acompanhamento-place.xhtml");
        }
        if (getUsuario().isPerfilFinanceira()) {
            return  montaURLComContextPath("/acompanhamento/acompanhamento-financeira.xhtml");
        }
        return montaURLComContextPath("");
    }

    public boolean isRoleCanUpdatePagamento() {
        List<Permissao> permissoes = usuarioService.findPermissoesByUsuario(usuario.getId());
        long permissao = permissoes.stream().filter(p -> p.getNome().equals("ALTERAR_PAGAMENTO_COBRANCA")).count();
        if (permissao >= 1)
            return true;
        return false;
    }

}
