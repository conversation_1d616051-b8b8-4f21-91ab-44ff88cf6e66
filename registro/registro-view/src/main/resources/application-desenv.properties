#datasource
spring.datasource.jndi-name=java:jboss/datasources/RegistroDS
#jpa
spring.jpa.database-platform=org.hibernate.dialect.PostgreSQLDialect
spring.jpa.hibernate.ddl-auto=update
spring.jpa.properties.hibernate.id.new_generator_mappings=true
spring.jpa.properties.org.hibernate.envers.store_data_at_delete=true
spring.jpa.show-sql=true
spring.jpa.format-sql=true
spring.jpa.properties.hibernate.jdbc.batch_size=20
spring.jpa.properties.hibernate.order_inserts=true
spring.jpa.properties.hibernate.order_updates=true
#cas
#cas.server=http://localhost:8080/auth
cas.server=https://homol.placecon.com.br/auth
cas.local.login=http://localhost:8080/registro/login/cas
cas.url.ambiente.boasvindas=https://homol.placecon.com.br/

enderecos.migracao=http://localhost:8080/registro-app-ws/api/v1/migracao

#jsf
jsf.PROJECT_STAGE=Development
jsf.FACELETS_REFRESH_PERIOD=0
server.servlet.session.timeout=30
#log
logging.level=DEBUG
#logging.config=classpath:/log4j2.xml
file.dir=c:/data/contratos/
file.dir-read=c:/data/contratos/
#diretorio para anexos de financeira
file-financeira.dir=c:/data/financeira/
file-financeira.dir-read=c:/data/financeira;
#diretorio para boletos
file-boleto.dir=c:/storage/boleto/
file-boleto.dir-read=c:/storage/boleto/
#diretorio para nf de cobranca
file-nf.dir=c:/data/nf/
file-nf.dir-read=c:/data/nf/
# remessa
file.remessa.dir=c:/data/remessa/
file.remessa.dir-read=c:/data/remessa/
# remessa chassi
file.remessa.chassi.dir=c:/data/chassi/
file.remessa.chassi.dir-read=c:/data/chassi/

file.remessa.b3.dir=c:/data/remessab3/

b3.url.base=https://api-revolucaosng-cert.b3.com.br

#boleto
boleto.url=https://sandbox.boletocloud.com/api
boleto.token=api-key_TiW3A-rbaoQo2PJgn-CxGCR3aVxe4gN5htnQmrtbNQM=
boleto.token.conta.itau=api-key_NVyd_7-DAeLGXuhkVJ0vCnMCwCa2lnR3SL90f2Hpa4c=
boleto.versao=v1
boleto.conta=24255-7
boleto.agencia=6630
boleto.banco=341
boleto.carteira=109
boleto.beneficiario.nome=PLACE TECNOLOGIA E INOVACAO S/A
boleto.beneficiario.cprf=06.032.507/0001-03
boleto.beneficiario.endereco.cep=04548-040
boleto.beneficiario.endereco.uf=SP
boleto.beneficiario.endereco.localidade=SAO PAULO
boleto.beneficiario.endereco.bairro=Vila Olimpia
boleto.beneficiario.endereco.logradouro=Rua Tenerife
boleto.beneficiario.endereco.numero=31
boleto.beneficiario.endereco.complemento=4o Andar Sala S18
boleto.titulo=CC
boleto.instrucao1=Nao receber, ambiente de desenvolvimento
boleto.instrucao2=
boleto.instrucao3=
boleto.b3=<EMAIL>
boleto.authorization=Basic YXBpLWtleV9IRmZNcXdOQUlWRTZob1RESFQyWHJEeHJMUEttRXNpc0JWaVFlZjNDamEwPTo=

#service DETRAN CE

detran.ce.default.uri=https://gravamews.detran.ce.gov.br/veiculows_dev
detran.ce.cliente.autenticado=/gravametw/api/autenticar/clienteAutenticado
detran.ce.registro.buscar.apontamento=/gravametw/contrato/api/apontamento
detran.ce.registro.buscar.apontamentopendente=/gravametw/contrato/api/apontamentoPendente
detran.ce.registro.buscar.apontamentospendentes=/gravametw/contrato/api/apontamentosPendentes
detran.ce.registro.buscar.contrato=/gravametw/contrato/api/contrato
detran.ce.registro.buscar.contratos=/gravametw/contrato/api/contratos
detran.ce.registro.buscar.contratosveiculo=/gravametw/contrato/api/contratosVeiculo
detran.ce.registro.buscar.municipiosce=/gravametw/contrato/api/municipios/ce
detran.ce.registro.cancelar=/gravametw/contrato/api/cancelar
detran.ce.registro.download.contratodigitalizado=/gravametw/contrato/api/downloadContratoDigitalizado
detran.ce.registro.enviar.contratodigitalizado=/gravametw/contrato/api/enviarContratoDigitalizado
detran.ce.registro.registrar=/gravametw/contrato/api/registrar
detran.ce.daes.buscar.dae=/daewstw/api/dae
detran.ce.daes.buscar.daes=/daewstw/api/daes
detran.ce.daes.gerar.dae=/daewstw/api/dae/gerar
detran.ce.daes.extrato.dae=/daewstw/api/dae/pdf
detran.ce.daes.buscar.servicos=/daewstw/api/servicos
detran.ce.daes.estoquedaes=/daewstw/api/dae/estoque

#service DETRAN AL

detran.al.default.uri=https://registros.detran.al.gov.br
detran.al.context.uri=/rdc-app-rest/rest/v2
detran.al.gerar.token=/auth
detran.al.consulta=/contrato/chassi/{chassi}
detran.al.registro=/contrato
detran.al.envio.imagem=/arquivo/chassi/{chassi}

#service DETRAN SE
detran.se.default.uri=https://api.registrocontrato.detran.se.gov.br
detran.se.cobranca=/api/v1/creditor/financial/generate
detran.se.gera.token=/api/v1/login
detran.se.refresh.token=/api/v1/refresh
detran.se.profile=/api/v1/profile
detran.se.criar.aditivo=/api/v1/creditor/additive
detran.se.consultar.contrato=/api/v1/creditor/contract/{uuid}
detran.se.criar.registrar.contrato=/api/v1/creditor/contract/create/and/register
detran.se.criar.contrato=/api/v1/creditor/contract
detran.se.registrar.contrato=/api/v1/creditor/contract/{contract}/register
detran.se.atualizar.contrato=/api/v1/creditor/contract/{uuid}
detran.se.salvar.imagem=/api/v1/creditor/contract/save_image
detran.se.cancelar.contrato=/api/v1/creditor/contract/{contract}/canceled
detran.se.baixar.contrato=/api/v1/manager/contract/write-off
detran.se.cancelar.contrato.detran=/api/v1/manager/contract/cancel
detran.se.cnpjresponsible=06032507000103
detran.se.tokenvalidation=8a8836f6ffc560fd950f86f8e8d9052b33bc3337148c297ca8a3a265c7900e9c

# service DETRAN SP
detran.sp.default.uri=http://192.168.50.16:80/homol/eaigever/SircofService
detran.sp.context.path=com.registrocontrato.registro.service.detran.sp.client
detran.sp.usuario=06032507000103
detran.sp.senha=PLACE@
# service DETRAN PR
detran.pr.default.uri.auth=https://auth-cs-hml.identidadedigital.pr.gov.br/centralautenticacao/api/v1/token
detran.pr.default.uri=https://homolog.registrodecontrato.detran.pr.gov.br/detran-regcon/api
detran.pr.usuario=22ac3c5a5bf0b520d281c122d1490650
detran.pr.senha=pl4c3t1
# service DETRAN AP
detran.ap.default.uri.auth=
detran.ap.default.uri=
detran.ap.url.cadastrar=/api/areaRestrita/registroContrato/registrar
detran.ap.url.consultar=/api/areaRestrita/registroContrato/consultar
detran.ap.usuario=
detran.ap.senha=
# service DETRAN RJ
detran.rj.default.uri=http://*************:8080/wsstack/services/REG-CONT
detran.rj.context.path=com.registrocontrato.registro.service.detran.rj.client
detran.rj.usuario=COCTPTIN
detran.rj.senha=SENHA01
# service DETRAN PI
detran.pi.default.uri-boleto=https://www.pi.getran.com.br/financeiro/api/registro/gerarBoleto
detran.pi.default.uri=http://localhost:8080/ws-detran/registroContratoWS
detran.pi.context.path=com.registrocontrato.registro.service.detran.pi.client
detran.pi.usuario=06032507000103
detran.pi.senha=p@c3TecIn0V
detran.pi.rest.token=eyJhbGciOiJIUzI1NiJ9.eyJzdWIiOiIwNjAzMjUwNzAwMDEwMyIsImF1ZCI6WyJSRUdJU1RSQURPUkEiXSwiaXNzIjoiZmluYW5jZWlyby1hcGkiLCJpYXQiOjE3NDg1NDc0ODcsImp0aSI6Ijc5MzkzY2ExLTdlMmQtNDgyOC1iNDMxLWM2YTM0NDUyNDNmYSIsIm5hbWUiOiJQbGFjZSBUZWNub2xvZ2lhIGUgSW5vdmHDp8OjbyBTLiBBLiJ9.XUhO7xSpM9zqcAIgovQ0DxJh4eVVcuTkBdDBei9Iv1E
# service DETRAN RR
detran.rr.default.uri=https://loiwh3.searchtecnologia.com.br
detran.rr.url.cadastrar=/getranServicos/rest/registroContrato/registrarContrato
detran.rr.url.consultarcontrato=/getranServicos/rest/registroContrato/consultarContrato
detran.rr.usuario=PLACETI
detran.rr.senha=ksokv_58azisplgjjazzmb.
# service DETRAN MG
detran.mg.default.uri=http://webservicehomologa.detran.mg.gov.br/sircof/soap/contratos_financeiros/service
detran.mg.context.path=com.registrocontrato.registro.service.detran.mg.client
detran.mg.usuario=06032507000103
detran.mg.senha=9f87fc5fea1f0c56cfa4c54844221c8923aec3ef
# service DETRAN SC
detran.sc.default.uri=
detran.sc.context.path=com.registrocontrato.registro.service.detran.sc.client
detran.sc.usuario=
detran.sc.senha=
detran.sc.registrarcontrato=RegistrarContrato
detran.sc.consultarsequencial=ConsultarSequencialContrato
# service DETRAN BA
detran.ba.default.uri=http://200.187.13.80/wsdetrancontrato/wsdetrancontrato.asmx
detran.ba.context.path=com.registrocontrato.registro.service.detran.ba.client
detran.ba.usuario=100403
detran.ba.senha=PlaceT
# service DETRAN PB
detran.pb.default.uri=https://wsdetran.pb.gov.br/CallWS-Desenvolvimento/CallWSDT
detran.pb.context.path=com.registrocontrato.registro.service.detran.pb.client
detran.pb.usuario=0603250
detran.pb.senha=DET123
detran.pb.codigo=000955
detran.pb.sigla.transacao=PL
detran.pb.sigla.transmissao=PL
# service DETRAN AC
detran.ac.default.uri=https://loiwh3.searchtecnologia.com.br/ac/getranServicos/rest/registroContrato/
detran.ac.context.path=com.registrocontrato.registro.service.detran.ac.client
detran.ac.url.cadastrar=registrarContrato
detran.ac.url.consultar=consultarContrato
detran.ac.usuario=PLACETI
detran.ac.senha=ksokv_58azisplgjjazzmb.
# service DETRAN MT
detran.mt.default.uri=http://wbs2.homologa.detrannet.mt.gov.br/wsRegistroContrato/wsRegistroContrato.asmx
detran.mt.default.endpoint=http://wbs2.homologa.detrannet.mt.gov.br
detran.mt.context.path=com.registrocontrato.registro.service.detran.mt.client
detran.mt.url.cadastrar=RegistraContrato
detran.mt.url.consultar=ConsultaContrato

# service DETRAN PE
detran.pe.default.uri=http://200.238.67.2:41075/WebApiRegistroContratoGravame/RegistraContrato
detran.pe.default.cobranca.uri=http://200.238.67.2:42675/Estabelecimento/Obter/MovimentacaoFinanceira

# service DETRAN RS
detran.rs.default.uri=https://www.vei.detran.rs.gov.br/sng/ContratoIncSoap
detran.rs.context.path=com.registrocontrato.registro.service.detran.rs.client

# service DETRAN MS
detran.ms.default.uri=https://web2.detran.ms.gov.br/s85/rc-api
detran.ms.url.cadastrar=/solicitacao/solicitar-registro
detran.ms.url.autenticacao=/usuario/authenticate
detran.ms.url.desbloquear.contrato=/solicitacao/desbloquear-contrato
detran.ms.url.envio.imagem=/solicitacao/enviar-imagem
detran.ms.url.corrigir.imagem=/solicitacao/corrigir-imagem
detran.ms.url.busca.andamento=/buscar/andamento
detran.ms.url.consulta.boleto=/buscar/guia-pagamento
detran.ms.url.consulta.boleto.byte=/buscar/guia-pagamento/byte

# service DETRAN RN
detran.rn.default.uri=https://sudapih.detran.rn.gov.br
detran.rn.usuario=06032507000103
detran.rn.senha=Pl@c&T1D&tr@n_Pl@c&c0N
detran.rn.url.autenticacao=/auth/login
detran.rn.url.comunicarcontrato=/convenio/externo/registrocontrato/comunicarcontratofinanciamentoveiculo
detran.rn.url.consulta.debito=/convenio/externo/registrocontrato/listardebitoagentefinanceiro
detran.rn.url.consulta.debito.detalhado=/convenio/externo/registrocontrato/listardebitodetalhado
detran.rn.url.regerar.debito=/convenio/externo/registrocontrato/regerardebito

# service ARQDIGITAL

arqdigital.baseurl=https://gecov.com.br/ws
arqdigital.autenticacao=/login
arqdigital.consulta.tipo.documento=/tiposDocumento
arqdigital.registro=/servicos/protocolarRegistro
arqdigital.envio.arquivo=/binario/enviarArquivo/{idDocumento}
arqdigital.pesquisa.documento=/servicos/pesquisaDocumento
arqdigital.quitacao.contrato=/servicos/solicitarQuitacao
arqdigital.definicao.perfil=/login/definirPerfil

#CONTA AZUL
contaazul.id=9kejoR09R6T49lHJo4cOE7f3emdcuoMv
contaazul.secret=3fZmCoOvw4Rq5B03yjMYqBT2JIy3s9I9
contaazul.url=https://api.contaazul.com/auth/authorize
contaazul.redirect=http://localhost:8080/registro/contaazul/confirma-venda.xhtml
contaazul.url-token=https://api.contaazul.com/oauth2/token
contaazul.url-servicos=https://api.contaazul.com/v1/services/
contaazul.url-clientes=https://api.contaazul.com/v1/customers/
contaazul.url-vendas=https://api.contaazul.com/v1/sales
qrcode.validate.url=http://localhost:8080/registro/public/validate.xhtml
sign.url.return=http://localhost:8080
#Banco Pan Service
bancopan.default.uri=https://api.bancopan.com.br/transacional/apipanveiculos
bancopan.obter-token=/token
bancopan.autenticar-token=/oauth/token
bancopan.contrato-digitalizado=/GrvEnvioDocContrato/ContratoDigitalizado
bancopan.130_usuario_senha=MTMwOlVTUl9SRUdfUExBQ0U6UGxAY2UjMjAyMSo=

# email
spring.mail.mailgun.domain=placecon.com.br
spring.mail.mailgun.ws-auth=Basic ************************************************************************
spring.mail.contato=<EMAIL>
spring.mail.ambiente=desenv
spring.mail.destinarios.baixa=<EMAIL>
spring.mail.destinarios.suporte=<EMAIL>

# timeout detran
detran.readTimeout=20000
detran.connectionTimeout=20000
detran.readTimeout.pb=40000
decoder.url=https://api.procob.com/veiculos/v2/V0001?decodificadorChassi=SIM&chassi=
#decoder.usuario=<EMAIL>
#decoder.senha=TesteApi
decoder.usuario=<EMAIL>
decoder.senha=MGtKMBmUXmBAMn5
#ocr
tesseract.datapath=C://Program Files/Tesseract-OCR/tessdata
#NOTAS REEMBOLSO
ufesp.valor=4,074
ufesp.artigo=artigo 6,da portaria 166/2022
ufesp.portariapagamento=465/2016
duda.rj.codigoreceita=031 - 0
duda.rj.portaria=5639 de 31 de maio de 2019
sistema=REGISTRO
#dados place
place.telefone1=+55 (11) 4210-1220
place.telefone2=0800 591 0915
place.site=https://placeti.com.br/
logging.level.org.springframework.web=DEBUG
logging.level.org.springframework.ws.client.MessageTracing.sent=DEBUG
logging.level.org.springframework.ws.server.MessageTracing.sent=DEBUG
logging.level.org.springframework.ws.client.MessageTracing.received=DEBUG
logging.level.org.springframework.ws.server.MessageTracing.received=DEBUG

detran.certificados=C:/storage/certificados/

#API DO BANCO DO BRASIL
bancodobrasil.oauth2.uri=https://oauth.hm.bb.com.br/oauth/token
bancodobrasil.default.uri=https://homologa-api-ip.bb.com.br:7144/pagamentos-lote/v1
bancodobrasil.url.cadastrar.boleto=/batch-slips

bancodobrasil.client.secret=eyJpZCI6IjhlYjNlODUtNjZiYy00MjJmLWIyOTktMmNhMDMyMWE5N2Y3MDRmZGYiLCJjb2RpZ29QdWJsaWNhZG9yIjowLCJjb2RpZ29Tb2Z0d2FyZSI6MjQ2NDksInNlcXVlbmNpYWxJbnN0YWxhY2FvIjoyLCJzZXF1ZW5jaWFsQ3JlZGVuY2lhbCI6MSwiYW1iaWVudGUiOiJob21vbG9nYWNhbyIsImlhdCI6MTYzNzUyMDUyMTgzMH0
bancodobrasil.client.id=eyJpZCI6IjkwMGQyZDUtNzJmYy00YjAiLCJjb2RpZ29QdWJsaWNhZG9yIjowLCJjb2RpZ29Tb2Z0d2FyZSI6MjQ2NDksInNlcXVlbmNpYWxJbnN0YWxhY2FvIjoyfQ
bancodobrasil.app.key=b82315c0c31e0135776b005056891bef

# metabase
METABASE_SITE_URL=https://analytics.placecon.com.br:443
METABASE_SECRET_KEY=f52715a77662a0ad41ece9ea57fa5de51436cf8d26e0f289cdb9c2b65be87ffa

# MENSAGERIA - KAFKA
spring.kafka.producer.bootstrap-servers=localhost:29092
spring.kafka.producer.key-serializer=org.apache.kafka.common.serialization.StringSerializer
spring.kafka.producer.value-serializer=org.apache.kafka.common.serialization.StringSerializer
spring.kafka.producer.client-id=group-id

spring.kafka.consumer.bootstrap-servers=localhost:29092
spring.kafka.consumer.group-id=group-id
spring.kafka.consumer.auto-offset-reset=earliest
spring.kafka.consumer.key-deserializer=org.apache.kafka.common.serialization.StringDeserializer
spring.kafka.consumer.value-deserializer=org.apache.kafka.common.serialization.StringDeserializer

auto.create.topics.enable=true

viper.url.base=http://localhost:8081/api/v1/viper
viper.user=viper
viper.password=f52715a77662a0ad41ece9ea57fa5de51436cf8d26e0f289cdb9c2b65be87ffa

milvus.url.chamado = https://apiintegracao.milvus.com.br/api/chamado
milvus.url.cliente = https://apiintegracao.milvus.com.br/api/cliente
milvus.token = k7WPKrhaKaF3wGBRzOspDyNjWAasD2QvJQHZ4y2tn84q8trW6oyJxLoaj4og2njKf70AAD6x4FIhF6fMTiWVPoltzohxXCeUTFlbD
