<ui:composition xmlns="http://www.w3.org/1999/xhtml"
				xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
				xmlns:h="http://java.sun.com/jsf/html"
				xmlns:f="http://java.sun.com/jsf/core"
				xmlns:p="http://primefaces.org/ui"
				xmlns:jsf="http://xmlns.jcp.org/jsf">

	<div class="panel panel-default" jsf:rendered="#{contratoRsngBean.entity.protocoloPlaceconRsng != null}">
		<div class="panel-heading">
			<div class="panel-title">Registro Eletrônico</div>
		</div>
		<div class="panel-body fix-checkbox">
			<div class="row">
				<div class="col-lg-6">
					<div class="form-group form-group form-group-default required">
						<label>Número</label>
                        <input jsf:id="protocoloPlaceconRsng" type="text" maxlength="255"
                               jsf:value="#{contratoRsngBean.entity.protocoloPlaceconRsng}"
                               class="form-control" disabled="disabled" required="required">
                        </input>
                    </div>
                </div>
                <div class="col-lg-6">
                    <div class="form-group form-group form-group-default">
                        <label>Situação</label>
                        <input jsf:id="situacao" type="text" maxlength="255"
                               jsf:value="#{contratoRsngBean.entity.situacao.descricao}"
                               class="form-control" disabled="disabled"/>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <span jsf:id="tipoContratoComplementoCredor">
		<div class="panel panel-default" jsf:rendered="#{contratoRsngBean.entity.tipoContrato == 'CESSAO_DIREITO_CREDOR'}">
			<div class="panel-heading">
				<div class="panel-title">Cessão de Direitos Credor</div>
			</div>
			<div class="panel-body">
				<div class="row">
					<div class="col-lg-4">
						<div class="form-group form-group form-group-default required">
							<label>CPF/CNPJ do Credor Anterior</label>
							<input jsf:id="cpfCnpjCessaoDireitoCredor" type="text" required="true" jsf:required="true"
                                   jsf:value="#{contratoRsngBean.entity.cpfCnpjCessaoDireito}"
                                   jsf:label="CPF/CNPJ do Credor Anterior"
                                   jsf:validator="cpfCnpjValidator"
                                   class="form-control doc" disabled="#{disabled}"
                            />
						</div>
					</div>
					<div class="col-lg-8">
						<div class="form-group form-group form-group-default required">
							<label>Nome do Credor Anterior</label>
							<input jsf:id="nomeCessaoDireitoCredor" type="text" maxlength="40" required="true"
                                   jsf:required="true"
                                   jsf:value="#{contratoRsngBean.entity.nomeCessaoDireito}"
                                   jsf:label="Nome do Credor Anterior" class="form-control" disabled="#{disabled}">
								<f:converter converterId="trimConverter"/>
							</input>
						</div>
					</div>
				</div>
			</div>
		</div>
	</span>
    <div class="panel panel-default">
        <div class="panel-heading">
            <div class="panel-title">Agente Financeiro</div>
        </div>
        <div class="panel-body" jsf:id="divAgenteUfRegistro">
            <div class="row">
                <div class="col-lg-8" jsf:rendered="#{alteracao or disabled eq 'disabled'}">
                    <div class="form-group form-group form-group-default">
                        <label>CNPJ</label>
                        <input type="text"
                               jsf:value="#{contratoRsngBean.entity.financeira.documento} - #{contratoRsngBean.entity.financeira.nome}"
                               class="form-control" disabled="disabled">
                        </input>
                    </div>
                </div>
                <div class="col-lg-4" jsf:rendered="#{alteracao or disabled eq 'disabled'}">
                    <div class="form-group form-group form-group-default">
                        <label>UF do Registro</label>
                        <input type="text" jsf:value="#{contratoRsngBean.entity.ufRegistro}"
                               class="form-control" disabled="disabled">
                        </input>
                    </div>
                </div>
                <div class="col-lg-8" jsf:rendered="#{!alteracao and disabled ne 'disabled'}">
                    <div class="form-group form-group form-group-default required">
                        <label>CNPJ</label>
                        <select jsf:id="financeira" jsf:value="#{contratoRsngBean.entity.financeira}"
                                class="form-control full-width select2" jsf:label="CNPJ"
                                jsf:required="true" required="required" jsf:disabled="#{disabled eq 'disabled'}">
							<f:selectItem itemLabel="Selecione uma Financeira"/>
							<f:selectItems value="#{helperSessionBean.financeiras}" var="i" itemValue="#{i}"
                                           itemLabel="#{i.documento} - #{i.nome}"/>
                            <f:ajax event="change" execute="@this"
                                    render="ufRegistroDiv municipioLiberacaoDiv ufLiberacaoDiv spanAddOutroVeiculo"
                                    listener="#{contratoRsngBean.definirLiberacaoCredito()}"
                                    onevent="function(data) { $.masks(); PrimeFaces.focus('ufRegistro'); $.ufRegistro();}"/>
                            <f:converter converterId="financeiraConverter"/>
                        </select>
                    </div>
                </div>
                <div class="col-lg-4" jsf:rendered="#{!alteracao and disabled ne 'disabled'}" jsf:id="ufRegistroDiv">
                    <div class="form-group form-group form-group-default required">
                        <label>UF do Registro</label>
                        <select jsf:id="ufRegistro" jsf:value="#{contratoRsngBean.entity.ufRegistro}" required="required"
                                class="select2 form-control full-width" jsf:label="UF do Registro"
                                jsf:required="true" jsf:disabled="#{disabled eq 'disabled'}">
                            <f:selectItems value="#{helperSessionBean.getUfs(contratoRsngBean.entity.financeira)}" var="i"/>
                            <f:ajax event="change" execute="@this"
									render="nomeDevedorFinanciado numeroEndereco spanAddOutroVeiculo TaxaContratoRegDiv ValorIOFDiv TaxaJurosMesDiv TaxaJurosAnoDiv"
							/>
                        </select>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <span jsf:id="veiculo">
		<ui:repeat id="veiculos" var="object" value="#{contratoRsngBean.entity.veiculos}" varStatus="status">
			<div class="panel panel-default">
				<div class="panel-heading">
					<div class="panel-title">Veículo #{status.index + 1}</div>
				</div>
				<div class="panel-body">
					<div class="row">
						<div class="col-lg-12">
							<div class="alert alert-danger" role="alert"
                                 jsf:rendered="#{object.id != null and object.mensagemErro != null}">
								<button class="close" data-dismiss="alert"></button>
                                #{object.mensagemErro}
							</div>
						</div>
					</div>
					<div class="row">
						<div class="col-lg-#{object.apontamento == null ? '6' : '3'}">
							<div class="form-group form-group form-group-default required">
								<label>Chassi</label>
								<input jsf:id="numeroChassi" type="text" maxlength="21"
                                       jsf:value="#{object.numeroChassi}"
									   jsf:disabled="#{disabled eq 'disabled'}"
                                       jsf:required="true" jsf:label="Chassi" class="form-control chassi"
                                       jsf:validator="chassiValidator">
									<p:ajax
                                            process="@this divAgenteUfRegistro"
                                            update="veiculo messages devedor numeroContrato"
                                            oncomplete="$.masks(); $.ufRegistro();"/>
								</input>
							  </div>
						</div>

						<div class="col-lg-3" jsf:rendered="#{object.apontamento != null}">
							<div class="form-group form-group form-group-default">
								<label>Apontamento</label>
								<input jsf:id="numeroApontamento" type="text" maxlength="8"
									   jsf:value="#{object.apontamento}" required="true"
									   jsf:required="true" jsf:label="Apontamento" class="form-control naoCola"
									   jsf:disabled="#{disabled eq 'disabled'}"
									   onkeypress="return /^-?[0-9]*$/.test(this.value+event.key)">
									<p:ajax process="@this divAgenteUfRegistro"
											update="veiculo messages devedor numeroContrato"
											oncomplete="$.masks(); $.ufRegistro();"/>
								</input>
							</div>
						</div>

						<div class="col-lg-3">
							<div class="form-group form-group form-group-default required">
								<label>Remarcado</label>
								<select jsf:id="chassiRemarcado" jsf:value="#{object.chassiRemarcado}"
										class="select2 form-control full-width"
										jsf:required="true" required="required"
										jsf:disabled="#{disabled eq 'disabled'}"
										jsf:label="Remarcado" size="1">
									<f:selectItem itemLabel="Não" itemValue="#{false}"/>
									<f:selectItem itemLabel="Sim" itemValue="#{true}"/>
								</select>
							</div>
						</div>
						<div class="col-lg-3">
							<div class="form-group form-group form-group-default required">
								<label>0 Km</label>
								<select jsf:id="veiculo0Km" jsf:value="#{object.veiculo0Km}"
                                        class="select2 form-control full-width"
										jsf:disabled="#{disabled eq 'disabled'}"
										jsf:required="true" required="required"
                                        size="1" jsf:label="0 Km">
									<f:selectItem itemLabel="Sim" itemValue="#{true}"/>
									<f:selectItem itemLabel="Não" itemValue="#{false}"/>
									<p:ajax process="@this" update="veiculoDados"
                                            listener="#{contratoRsngBean.reset0Km(status.index)}"
                                            oncomplete="$.masks(); PrimeFaces.focus('veiculos:#{status.index}:placa'); "/>
								</select>
							</div>
						</div>
					</div>
					<div class="row" jsf:id="veiculoDados">
						<div class="col-lg-3">
							<div class="form-group form-group form-group-default #{!object.veiculo0Km ? 'required' : ''}">
								<label>Placa</label>
								<input jsf:id="placa" type="text" maxlength="7"
                                       jsf:value="#{object.placa}" jsf:disabled="#{disabled eq 'disabled'}"
									   jsf:label="Placa" class="form-control placa "
                                       jsf:required="#{!object.veiculo0Km}"/>
							</div>
						</div>
						<div class="col-lg-3">
							<div class="form-group form-group form-group-default #{!object.veiculo0Km ? 'required' : ''}">
								<label>UF</label>
								<select jsf:id="uf" jsf:value="#{object.uf}" class="select2 form-control full-width"
										jsf:disabled="#{disabled eq 'disabled'}"
                                        jsf:required="#{!object.veiculo0Km}"
                                        jsf:label="UF do Veículo">
									<f:selectItem itemLabel="Selecione"/>
									<f:selectItems value="#{helperBean.ufs}"/>
								</select>
							</div>
						</div>
						<div class="col-lg-3">
							<div class="form-group form-group form-group-default #{!object.veiculo0Km ? 'required' : ''}">
								<label>Renavam</label>
								<input jsf:id="numeroRenavam" type="text" maxlength="11"
									   jsf:disabled="#{disabled eq 'disabled'}"
                                       jsf:value="#{object.numeroRenavam}" jsf:label="Renavam"
                                       class="form-control integerZero"
                                       jsf:required="#{!object.veiculo0Km}"/>
							</div>
						</div>
						<div class="col-lg-3">
							<div class="form-group form-group form-group-default">
								<label>Tipo</label>
								<select jsf:id="tipo" jsf:value="#{object.tipo}"
                                        class="select2 form-control full-width tipoVeiculo"
                                        onchange="PrimeFaces.focus('veiculos:#{status.index}:anoFabricacao');" size="1"
                                        jsf:disabled="#{disabled eq 'disabled' or (contratoRsngBean.entity.protocoloPlaceconRsng != null)}"
                                        jsf:label="Tipo do Veículo">
									<f:selectItem itemLabel="Selecione"/>
									<f:selectItems value="#{helperBean.tipos}" var="e" itemLabel="#{e.descricao}"/>
								</select>
							</div>
						</div>
					</div>
					<div class="row">
						<div class="col-lg-3">
							<div class="form-group form-group form-group-default required">
								<label>Ano de Fabricação</label>
								<input jsf:id="anoFabricacao" type="text" maxlength="4"
									   jsf:disabled="#{disabled eq 'disabled'}"
                                       jsf:value="#{object.anoFabricacao}" jsf:label="Ano de Fabricação"
                                       class="form-control ano"
                                       required="true" jsf:required="true"/>
							</div>
						</div>
						<div class="col-lg-3">
							<div class="form-group form-group form-group-default required">
								<label>Ano do Modelo</label>
								<input jsf:id="anoModelo" type="text" maxlength="4" onblur=""
									   jsf:disabled="#{disabled eq 'disabled'}"
                                       jsf:value="#{object.anoModelo}" jsf:label="Ano do Modelo"
                                       class="form-control ano"
                                       required="true" jsf:required="true">
									<f:ajax execute="@this marca" render="modeloDiv"
                                            onevent="function(data) { $.masks(); PrimeFaces.focus('veiculos:#{status.index}:marca');}"/>
								</input>
							</div>
						</div>

						<div class="col-lg-3">
							<div class="form-group form-group form-group-default">
								<label>Marca</label>
								<select jsf:id="marca" jsf:label="Marca do Veículo" jsf:value="#{object.marca}"
                                        class="form-control full-width select2"
                                        jsf:disabled="#{disabled eq 'disabled' or (contratoRsngBean.entity.protocoloPlaceconRsng != null)}">
									<f:selectItem itemLabel="Selecione"/>
									<f:selectItems value="#{helperBean.marcas}" var="e" itemLabel="#{e.descricao}"/>
									<f:converter converterId="entityConverter"/>
									<p:ajax update="modeloDiv" process="@this anoModelo"
                                            oncomplete="$.masks(); PrimeFaces.focus('veiculos:#{status.index}:modelo');"></p:ajax>
								</select>
							</div>
						</div>
						<div class="col-lg-3" jsf:id="modeloDiv">
							<div class="form-group form-group form-group-default">
								<label>Modelo</label>
								<select onchange="PrimeFaces.focus('cpfCnpjDevedorFinanciado')" jsf:id="modelo"
                                        jsf:label="Modelo do Veículo" jsf:value="#{object.modelo}"
                                        class="select2 form-control full-width"
                                        jsf:disabled="#{disabled eq 'disabled' or (contratoRsngBean.entity.protocoloPlaceconRsng != null)}">
									<f:selectItem itemLabel="Selecione"/>
									<f:selectItems value="#{helperBean.getModelos(object.marca, object.anoModelo)}"
                                                   var="e" itemLabel="#{e.descricao}"/>
									<f:converter converterId="entityConverter"/>
								</select>

							</div>
						</div>
					</div>
					<span jsf:rendered="#{contratoRsngBean.entity.veiculosSize gt 1}">
						<h:commandLink styleClass="btn btn-sm  btn-rounded btn-primary" title="Remover"
                                       rendered="#{contratoRsngBean.entity.id != null and contratoRsngBean.entity.situacao == 'ERRO' and disabled ne 'disabled'}"
                                       action="#{contratoRsngBean.removeVeiculo(status.index)}"
                                       onclick="return confirm('Confirmar a exclusão desse veículo?')">
							Remover

							<f:ajax execute="@this" render="veiculo messages"
                                    onevent="function(data) { $.masks(); $.ufRegistro();}"/>
						</h:commandLink>
						<h:commandLink styleClass="btn btn-sm  btn-rounded btn-primary" title="Remover"
                                       rendered="#{contratoRsngBean.entity.id != null and contratoRsngBean.entity.situacao == 'PENDENTE_PROCESSAMENTO' or contratoRsngBean.entity.situacao == 'PENDENTE_ENVIO'}"
                                       action="#{contratoRsngBean.removeVeiculo(status.index)}"
                                       onclick="return confirm('Confirmar a exclusão desse veículo?')">
							Remover
							<f:ajax execute="@this" render="veiculo"
                                    onevent="function(data) { $.masks(); $.ufRegistro();}"/>
						</h:commandLink>
						<h:commandLink styleClass="btn btn-sm  btn-rounded btn-primary" title="Remover"
                                       rendered="#{contratoRsngBean.entity.id == null and contratoRsngBean.entity.protocoloPlaceconRsng == null}"
                                       action="#{contratoRsngBean.removeVeiculo(status.index)}"
                                       onclick="return confirm('Confirmar a exclusão desse veículo?')">
							Remover
							<f:ajax execute="@this" render="veiculo"
                                    onevent="function(data) { $.masks(); $.ufRegistro();}"/>
						</h:commandLink>
						<h:commandLink styleClass="btn btn-sm  btn-rounded btn-primary" title="Remover"
                                       rendered="#{contratoRsngBean.entity.protocoloPlaceconRsng != null and contratoRsngBean.entity.tipoContrato == 'SUBSTITUICAO_GARANTIA'}"
                                       action="#{contratoRsngBean.removeVeiculo(status.index)}"
                                       onclick="return confirm('Confirmar a exclusão desse veículo?')">
							Remover
							<f:ajax execute="@this" render="veiculo"
                                    onevent="function(data) { $.masks(); $.ufRegistro();}"/>
						</h:commandLink>
					</span>
					&#160;
					<h:commandLink styleClass="btn btn-sm  btn-rounded btn-primary"
                                   action="#{contratoRsngBean.substituirVeiculo(status.index)}"
                                   onclick="return confirm('Confirmar a substituição desse veículo?')"
                                   rendered="#{contratoRsngBean.entity.protocoloPlaceconRsng != null and contratoRsngBean.entity.tipoContrato == 'SUBSTITUICAO_GARANTIA' and disabled ne 'disabled'}">
						Substituir
						<f:ajax execute="@this" render="veiculo substituicaoComplemento"
                                onevent="function(data) { $.masks(); $.ufRegistro();}"/>
					</h:commandLink>
				</div>
			</div>
			<input type="hidden" id="validationFailed" value="#{facesContext.validationFailed}"/>
		</ui:repeat>
		<span jsf:id="spanAddOutroVeiculo" style="float: right;">
			<h:commandLink rendered="#{(disabled ne 'disabled' and !alteracao) and contratoRsngBean.entity.ufRegistro ne 'PI' and contratoRsngBean.entity.ufRegistro ne 'RS'}"
                           styleClass="btn btn-sm  btn-rounded btn-primary" title="+ 1" id="btOutroVeiculo"
                           action="#{contratoRsngBean.addVeiculo()}">
				Adicionar outro Veículo
				<f:ajax execute="veiculo" render="veiculo"
                        onevent="function(data) { $.masks(); $.ufRegistro(); if(data.status == 'success') { if($('#validationFailed').val() == 'true') alert('Informe os dados corretos do veículo já existente.'); } }"/>
			</h:commandLink>
		</span>
	</span>
	<br/>
	<br/>

    <span jsf:id="devedor">
		<div class="panel panel-default">
			<div class="panel-heading">
				<div class="panel-title">Devedor</div>
			</div>
			<div class="panel-body">
				<h:inputHidden id="localidadeDevedor" value="#{contratoRsngBean.localidadeDevedor}"/>
				<div class="row">
					<div class="col-lg-4">
						<div class="form-group form-group form-group-default required">
							<label>CPF/CNPJ</label>
							<input jsf:id="cpfCnpjDevedorFinanciado" type="text" required="true" jsf:required="true"
                                   jsf:value="#{contratoRsngBean.entity.cpfCnpjDevedorFinanciado}"
                                   jsf:validator="cpfCnpjValidator"
                                   jsf:label="CPF/CNPJ do Devedor" class="form-control doc" disabled="#{disabled}"
                                   readonly="#{alteracao}">
								<f:converter converterId="cpfCnpjConverter"/>
								<f:ajax event="change" listener="#{contratoRsngBean.buscarDadosCpfCnpjDevedor()}" render="devedor"
                                        execute="@this"
                                        onevent="function(data) {
										 		$.masks(); $.ufRegistro();
										 		if(data.status == 'success'){
										 			if($('#cpfCnpjDevedorFinanciado').val() == $('#cpfCnpjCessaoDireito').val()) { alert('Esse CPF/CNPJ já foi indicado como Devedor Anterior');}
										 			else {  PrimeFaces.focus('nomeDevedorFinanciado'); }
												}
											}"/>
							</input>
						</div>
					</div>
					<div class="col-lg-8">
						<div class="form-group form-group form-group-default required">
							<label>Nome</label>
							<input jsf:id="nomeDevedorFinanciado" type="text"
								   maxlength="40" required="true"
								   jsf:required="true"
								   jsf:value="#{contratoRsngBean.entity.nomeDevedorFinanciado}"
								   jsf:label="Nome do Devedor" class="form-control" disabled="#{disabled}">
								<f:converter converterId="trimConverter"/>
							</input>
						</div>
					</div>
				</div>
				<div class="row">
					<div class="col-lg-4">
						<div class="form-group form-group form-group-default required">
							<label>CEP</label>
							<input jsf:id="cepDevedor" type="text" required="true" jsf:required="true"
                                   jsf:value="#{contratoRsngBean.entity.cepDevedor}"
                                   jsf:label="CEP do Devedor" class="form-control cep" disabled="#{disabled}">
								<f:converter converterId="trimConverter"/>
							</input>
						</div>
					</div>
					<div class="col-lg-4">
						<div class="form-group form-group form-group-default required">
							<label>Logradouro</label>
							<input jsf:id="enderecoDevedor" type="text" maxlength="30" required="true"
                                   jsf:required="true"
                                   jsf:value="#{contratoRsngBean.entity.enderecoDevedor}"
                                   jsf:label="Logradouro do Devedor" class="form-control" disabled="#{disabled}">
								<f:converter converterId="trimConverter"/>
							</input>
						</div>
					</div>
					<div class="col-lg-4">
						<div class="form-group form-group form-group-default required">
							<label>Bairro</label>
							<input jsf:id="bairroDevedor" type="text" maxlength="20"
                                   jsf:value="#{contratoRsngBean.entity.bairroDevedor}" required="true" jsf:required="true"
                                   jsf:label="Bairro do Devedor" class="form-control" disabled="#{disabled}">
								<f:converter converterId="trimConverter"/>
							</input>
						</div>
					</div>
				</div>
				<div class="row">
					<div class="col-lg-4">
						<div class="form-group form-group form-group-default required">
							<label>UF</label>
							<select jsf:id="ufEnderecoDevedor" jsf:value="#{contratoRsngBean.entity.ufEnderecoDevedor}"
                                    class="select2 form-control full-width" jsf:required="true" required="required"
                                    size="1"
                                    jsf:disabled="#{disabled eq 'disabled'}" jsf:label="UF do Devedor">
								<f:selectItem itemLabel="Selecione"/>
								<f:selectItems value="#{helperBean.ufs}"/>
								<p:ajax process="@this localidadeDevedor" update="municipioDevedorDiv"
                                        listener="#{contratoRsngBean.findMunicipioDevedor}"
                                        oncomplete="if($('#municipioDevedor').val() != ''){PrimeFaces.focus('numeroEnderecoDevedor');}else{PrimeFaces.focus('municipioDevedor');}"/>

							</select>
						</div>
					</div>
					<div class="col-lg-4" jsf:id="municipioDevedorDiv">
						<div class="form-group form-group form-group-default required">
							<label>Município</label>
							<select jsf:id="municipioDevedor" onchange="PrimeFaces.focus('numeroEnderecoDevedor');"
                                    jsf:value="#{contratoRsngBean.entity.municipioDevedor}"
                                    class="select2 form-control full-width" jsf:required="true" required="required"
                                    jsf:disabled="#{disabled eq 'disabled'}" label="Município do Devedor">
								<f:converter converterId="municipioConverter"/>
								<f:selectItem itemLabel="Selecione"/>
								<f:selectItems value="#{helperBean.getMunicipios(contratoRsngBean.entity.ufEnderecoDevedor)}"
                                               var="m" itemLabel="#{m.descricao}"/>
							</select>
						</div>
					</div>
					<div class="col-lg-4">
						<div jsf:id="numeroEndereco" class="form-group form-group form-group-default required">
							<label>Número</label>
							<input jsf:id="numeroEnderecoDevedor" type="text" maxlength="5"
                                   jsf:value="#{contratoRsngBean.entity.numeroEnderecoDevedor}"
								   jsf:required="true" required="required"
                                   jsf:label="Número do Devedor" class="form-control" disabled="#{disabled}">
								<f:converter converterId="trimConverter"/>
							</input>
						</div>
					</div>
				</div>
				<div class="row">
					<div class="col-lg-4">
						<div class="form-group form-group form-group-default">
							<label>Complemento</label>
							<input jsf:id="complementoEnderecoDevedor" type="text" maxlength="20"
                                   jsf:value="#{contratoRsngBean.entity.complementoEnderecoDevedor}"
                                   jsf:label="Complemento do Devedor" class="form-control" disabled="#{disabled}">
								<f:converter converterId="trimConverter"/>
							</input>
						</div>
					</div>
					<div class="col-lg-2">
						<div class="form-group form-group form-group-default required">
							<label>DDD</label>
							<input jsf:id="dddDevedor" type="text" maxlength="2"
                                   jsf:value="#{contratoRsngBean.entity.dddDevedor}" jsf:required="true" required="required"
                                   jsf:label="DDD do Devedor" class="form-control integer" disabled="#{disabled}"/>
						</div>
					</div>
					<div class="col-lg-2">
						<div class="form-group form-group form-group-default required">
							<label>Telefone</label>
							<input jsf:id="telefoneDevedor" type="text" required="required"
                                   jsf:value="#{contratoRsngBean.entity.telefoneDevedor}" jsf:required="true"
                                   jsf:label="Telefone do Devedor" class="form-control phonenumber"
                                   disabled="#{disabled}"/>
						</div>
					</div>
					<div class="col-lg-4">
						<div class="form-group form-group form-group-default">
							<label>E-mail</label>
							<input jsf:id="emailDevedor" type="email" maxlength="255"
                                   jsf:value="#{contratoRsngBean.entity.emailDevedor}"
                                   jsf:label="E-mail do Representante" class="form-control"
                                   disabled="#{disabled}">
								<f:converter converterId="trimConverter"/>
							</input>
						</div>
					</div>
				</div>
			</div>
		</div>
	</span>

    <div class="panel panel-default">
        <div class="panel-heading">
            <div class="panel-title">Dados do Contrato</div>
        </div>
        <div class="panel-body">
            <div class="row">
                <div class="col-lg-3">
                    <div class="form-group form-group form-group-default required">
                        <label>Tipo da Restrição</label>
                        <select jsf:id="tipoRestricao" jsf:value="#{contratoRsngBean.entity.tipoRestricao}"
                                class="select2 form-control full-width tipoRestricao"
                                jsf:required="true" required="required" jsf:disabled="#{disabled eq 'disabled'}"
                                jsf:label="Tipo da Restrição" size="1">
                            <f:selectItem itemLabel="Selecione"/>
                            <f:selectItems value="#{helperBean.tiposRestricao}" var="i" itemLabel="#{i.descricao}"/>
                            <p:ajax process="@this"
                                    update="tipoVrgDiv complementoGrupoConsorcio complementoCotaConsorcio"
                                    listener="#{contratoRsngBean.resetTipoVrg()}"
                                    oncomplete="$.masks(); PrimeFaces.focus('possuiGarantidor');"/>
                        </select>
                    </div>
                </div>
				<div class="col-lg-3">
					<div class="form-group form-group form-group-default required">
						<label>Índ. de Correção Monet.</label>
						<select jsf:label="Índice Financeiro" jsf:required="true" required="required"
								jsf:value="#{contratoRsngBean.entity.siglaIndiceFinaceiro}"
								jsf:id="indiceFinanceiro" class="select2 form-control full-width"
								onchange="PrimeFaces.focus('numeroContrato')"
								jsf:disabled="#{disabled eq 'disabled'}" size="1">
							<f:selectItem itemLabel="Selecione"/>
							<f:selectItems value="#{helperBean.indiceFinanceiro}" var="i" itemLabel="#{i.descricao}"/>
						</select>
					</div>
				</div>
                <div class="col-lg-6">
                    <div class="form-group form-group form-group-default required">
                        <label>Número do Contrato</label>
                        <input jsf:id="numeroContrato" type="text" maxlength="20"
                               jsf:disabled="#{disabled eq 'disabled'}"
                               jsf:value="#{contratoRsngBean.entity.numeroContrato}" required="true" jsf:required="true"
                               jsf:label="Número do Contrato" class="form-control">
                            <f:converter converterId="trimConverter"/>
                        </input>
                    </div>
                </div>
            </div>
            <div class="row">
				<div class="col-lg-3">
					<div class="form-group form-group form-group-default required">
						<label>Data do Contrato</label>
						<p:calendar id="dataContrato" styleClass="form-control" locale="pt_BR"
									onchange="validarDataContrato();" navigator="true" yearRange="c-10:c+10"
									value="#{contratoRsngBean.entity.dataContrato}" pattern="dd/MM/yyyy" mask="true" required="true"
									label="Data do Contrato" disabled="#{disabled eq 'disabled'}">
							<p:ajax event="dateSelect" onstart="validarDataContrato(); return false;"/>
						</p:calendar>
					</div>
				</div>
				<div class="col-lg-3">
					<div class="form-group form-group form-group-default required">
						<label>Data da Lib. do Crédito</label>
						<p:calendar id="dataLiberacaoCredito" styleClass="form-control" locale="pt_BR"
									onchange="validarDataLiberacaoCredito();" navigator="true" yearRange="c-10:c+10"
									value="#{contratoRsngBean.entity.dataLiberacaoCredito}" pattern="dd/MM/yyyy" mask="true"
									required="true" label="Data da Lib. do Crédito"
									disabled="#{disabled eq 'disabled'}">
							<p:ajax event="dateSelect" onstart="validarDataLiberacaoCredito(); return false;"/>
						</p:calendar>
					</div>
				</div>
                <div class="col-lg-3">
                    <div class="form-group form-group form-group-default required">
                        <label>Venc. da 1a Parcela</label>
                        <p:calendar navigator="true" yearRange="c-10:c+10" id="dataVencimentoPrimeiraParcela"
                                    styleClass="form-control" locale="pt_BR"
                                    onchange="validarDataVencimentoUltimaParcela();"
                                    value="#{contratoRsngBean.entity.dataVencimentoPrimeiraParcela}" pattern="dd/MM/yyyy"
                                    mask="true" required="true" label="Data da Lib. do Crédito"
                                    disabled="#{disabled eq 'disabled'}">
                            <p:ajax event="dateSelect" onstart="validarDataVencimentoUltimaParcela(); return false;"/>
                        </p:calendar>
                    </div>
                </div>
                <div class="col-lg-3">
                    <div class="form-group form-group form-group-default required">
                        <label>Venc. da Última Parcela</label>
                        <p:calendar id="dataVencimentoUltimaParcela" styleClass="form-control" locale="pt_BR"
                                    navigator="true" yearRange="c-10:c+10"
                                    onchange="validarDataVencimentoUltimaParcela();"
                                    value="#{contratoRsngBean.entity.dataVencimentoUltimaParcela}" pattern="dd/MM/yyyy" mask="true"
                                    required="true" label="Venc. da Última Parcela"
                                    disabled="#{disabled eq 'disabled'}">
                            <p:ajax event="dateSelect" onstart="validarDataVencimentoUltimaParcela(); return false;"/>
                        </p:calendar>
                    </div>
                </div>
            </div>
            <div class="row">
				<div class="col-lg-3">
					<div class="form-group form-group form-group-default required">
						<label>Quantidade de Meses</label>
						<input jsf:id="quantidadeMeses" type="text" maxlength="3"
							   jsf:value="#{contratoRsngBean.entity.quantidadeMeses}" required="true" jsf:required="true"
							   jsf:label="Quantidade de Meses" class="form-control integer" disabled="#{disabled}"/>
					</div>
				</div>
                <div class="col-lg-3">
                    <div class="form-group form-group form-group-default required">
                        <label>Valor da Parcela</label>
                        <p:inputNumber id="valorParcela" value="#{contratoRsngBean.entity.valorParcela}"
                                       inputStyle="width: 100%; text-align: right"
                                       maxValue="9999999.99" required="true" decimalPlaces="2" decimalSeparator=","
                                       thousandSeparator="."
                                       label="Total da Dívida" class="form-control"
                                       disabled="#{disabled eq 'disabled'}"/>
                    </div>
                </div>
                <div class="col-lg-3">
                    <div class="form-group form-group form-group-default required">
                        <label>Total da Dívida</label>
                        <p:inputNumber id="valorTotalDivida" value="#{contratoRsngBean.entity.valorTotalDivida}"
                                       inputStyle="width: 100%; text-align: right"
                                       maxValue="*********.99" required="true" decimalPlaces="2"
                                       label="Total da Dívida" class="form-control"
                                       disabled="#{disabled eq 'disabled'}"/>
                    </div>
                </div>
                <div class="col-lg-3">
                    <div class="form-group form-group form-group-default required">
                        <label>Valor do Crédito</label>
                        <p:inputNumber id="valorCredito" value="#{contratoRsngBean.entity.valorCredito}"
                                       inputStyle="width: 100%; text-align: right"
                                       maxValue="*********.99" required="true" decimalPlaces="2"
                                       label="Valor do Crédito" class="form-control"
                                       disabled="#{disabled eq 'disabled'}"/>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-lg-3">
                    <div class="form-group form-group form-group-default required" jsf:id="TaxaContratoRegDiv">
                        <label>Taxa de Contrato</label>
                        <p:inputNumber id="valorTaxaContrato" value="#{contratoRsngBean.entity.valorTaxaContrato}"
                                       inputStyle="width: 100%; text-align: right"
									   decimalPlaces="2" required="true"
                                       label="Taxa de Contrato" class="form-control"
                                       disabled="#{disabled eq 'disabled'}">
                        </p:inputNumber>
                    </div>
                </div>
                <div class="col-lg-3">
                    <div class="form-group form-group form-group-default required" jsf:id="ValorIOFDiv">
                        <label>Valor do IOF</label>
                        <p:inputNumber id="valorIOF" value="#{contratoRsngBean.entity.valorIOF}"
                                       inputStyle="width: 100%;  text-align: right"
                                       maxValue="9999999.99" required="true" decimalPlaces="2"
                                       label="Valor do IOF" class="form-control" disabled="#{disabled eq 'disabled'}">
                        </p:inputNumber>
                    </div>
                </div>
                <div class="col-lg-3">
                    <div class="form-group form-group form-group-default required" jsf:id="TaxaJurosMesDiv">
                        <label>Taxa de Juros ao Mês</label>
                        <p:inputNumber id="valorTaxaJurosMes" value="#{contratoRsngBean.entity.valorTaxaJurosMes}"
                                       inputStyle="width: 100%; text-align: right"
                                       maxValue="999.999" required="required" decimalPlaces="2"
                                       label="Taxa de Juros ao Mês" class="form-control"
                                       disabled="#{disabled eq 'disabled'}">
                        </p:inputNumber>
                    </div>
                </div>
                <div class="col-lg-3">
                    <div class="form-group form-group form-group-default required" jsf:id="TaxaJurosAnoDiv">
                        <label>Taxa de Juros ao Ano</label>
                        <p:inputNumber id="valorTaxaJurosAno" value="#{contratoRsngBean.entity.valorTaxaJurosAno}"
                                       inputStyle="width: 100%; text-align: right"
                                       maxValue="999.999" required="true" decimalPlaces="2"
                                       label="Taxa de Juros ao Ano" class="form-control"
                                       disabled="#{disabled eq 'disabled'}">
                        </p:inputNumber>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-lg-3">
                    <div class="form-group form-group form-group-default required">
                        <label>Indicativo de Multa</label>
                        <select jsf:id="indicadorTaxaMulta" jsf:value="#{contratoRsngBean.entity.indicadorTaxaMulta}"
                                class="select2 form-control full-width" jsf:required="true" required="required"
                                jsf:disabled="#{disabled eq 'disabled'}" jsf:label="Indicativo de Multa" size="1">
                            <f:selectItem itemLabel="Sim" itemValue="#{true}"/>
                            <f:selectItem itemLabel="Não" itemValue="#{false}"/>
                            <p:ajax process="@this" update="valorTaxaMultaDiv"
                                    oncomplete="$.masks(); PrimeFaces.focus('valorTaxaMulta');"/>
                        </select>
                    </div>
                </div>
                <div class="col-lg-3" jsf:id="valorTaxaMultaDiv">
                    <div class="form-group form-group form-group-default #{contratoRsngBean.entity.indicadorTaxaMulta ? 'required' : ''}">
                        <label>Taxa de Multa</label>
                        <p:inputNumber id="valorTaxaMulta" value="#{contratoRsngBean.entity.valorTaxaMulta}"
                                       inputStyle="width: 100%; text-align: right"
                                       maxValue="999.999" required="#{contratoRsngBean.entity.indicadorTaxaMulta}" decimalPlaces="2"
                                       label="Taxa de Multa" class="form-control" disabled="#{disabled eq 'disabled'}">
                        </p:inputNumber>
                    </div>
                </div>
                <div class="col-lg-3">
                    <div class="form-group form-group form-group-default required">
                        <label>Indicativo de Mora/Dia</label>
                        <select jsf:id="indicadorTaxaMoraDia" jsf:value="#{contratoRsngBean.entity.indicadorTaxaMoraDia}"
                                class="select2 form-control full-width" jsf:required="true" required="required" size="1"
                                jsf:disabled="#{disabled eq 'disabled'}" jsf:label="Indicativo de Mora ao Dia">
                            <f:selectItem itemLabel="Sim" itemValue="#{true}"/>
                            <f:selectItem itemLabel="Não" itemValue="#{false}"/>
                            <p:ajax process="@this" update="valorTaxaMoraDiaDiv"
                                    oncomplete="$.masks(); PrimeFaces.focus('valorTaxaMoraDia');"/>
                        </select>
                    </div>
                </div>
                <div class="col-lg-3" jsf:id="valorTaxaMoraDiaDiv">
                    <div class="form-group form-group form-group-default #{contratoRsngBean.entity.indicadorTaxaMoraDia ? 'required' : ''}">
                        <label>Taxa de Mora/Dia</label>
                        <p:inputNumber id="valorTaxaMoraDia" value="#{contratoRsngBean.entity.valorTaxaMoraDia}"
                                       inputStyle="text-align: right; width: 100%"
                                       maxValue="999.999" required="#{contratoRsngBean.entity.indicadorTaxaMoraDia}"
                                       decimalPlaces="2"
                                       label="Taxa de Mora ao Dia" class="form-control"
                                       disabled="#{disabled eq 'disabled'}">
                        </p:inputNumber>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-lg-3">
                    <div class="form-group form-group form-group-default required">
                        <label>Indicativo de Penalidade</label>
                        <select jsf:id="indicadorPenalidade" jsf:value="#{contratoRsngBean.entity.indicadorPenalidade}"
                                class="select2 form-control full-width" jsf:required="true" required="required" size="1"
                                jsf:disabled="#{disabled eq 'disabled'}" jsf:label="Indicativo de Penalidade">
                            <f:selectItem itemLabel="Sim" itemValue="#{true}"/>
                            <f:selectItem itemLabel="Não" itemValue="#{false}"/>
                            <p:ajax process="@this" update="descricaoPenalidadeDiv"
                                    oncomplete="$.masks(); 	PrimeFaces.focus('descricaoPenalidade');"/>
                        </select>
                    </div>
                </div>
                <div class="col-lg-3" jsf:id="descricaoPenalidadeDiv">
                    <div class="form-group form-group form-group-default #{contratoRsngBean.entity.indicadorPenalidade ? 'required' : ''}">
                        <label>Descrição da Penalidade</label>
                        <input jsf:id="descricaoPenalidade" type="text" maxlength="50"
                               jsf:value="#{contratoRsngBean.entity.descricaoPenalidade}"
                               jsf:required="#{contratoRsngBean.entity.indicadorPenalidade}"
                               required="#{contratoRsngBean.entity.indicadorPenalidade ? 'required' : null}"
                               jsf:label="Descrição da Penalidade" class="form-control" disabled="#{disabled}"/>
                    </div>
                </div>
                <div class="col-lg-3">
                    <div class="form-group form-group form-group-default required">
                        <label>Indicativo de Comissão</label>
                        <select jsf:id="indicadorComissao" jsf:value="#{contratoRsngBean.entity.indicadorComissao}" size="1"
                                class="form-control full-width select2" jsf:required="true" required="required"
                                jsf:disabled="#{disabled eq 'disabled'}"
                                jsf:label="Indicativo de Comissão">
                            <f:selectItem itemLabel="Sim" itemValue="#{true}"/>
                            <f:selectItem itemLabel="Não" itemValue="#{false}"/>
                            <p:ajax process="@this" update="percentualComissaoDiv"
                                    oncomplete=" $.masks();	PrimeFaces.focus('percentualComissao');"/>
                        </select>
                    </div>
                </div>
                <div class="col-lg-3" jsf:id="percentualComissaoDiv">
                    <div class="form-group form-group form-group-default #{contratoRsngBean.entity.indicadorComissao ? 'required' : ''}">
                        <label>Comissão</label>
                        <p:inputNumber id="percentualComissao" value="#{contratoRsngBean.entity.percentualComissao}"
                                       inputStyle="text-align: right"
                                       maxValue="9999999.99" required="#{contratoRsngBean.entity.indicadorComissao}"
                                       decimalPlaces="2"
                                       label="Comissão" class="form-control" disabled="#{disabled eq 'disabled'}">
                        </p:inputNumber>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-lg-3" jsf:id="ufLiberacaoDiv">
                    <div class="form-group form-group form-group-default required">
                        <label>UF da Lib. de Crédito</label>
                        <select jsf:id="ufLiberacaoCredito" jsf:value="#{contratoRsngBean.entity.ufLiberacaoCredito}" size="1"
                                class="form-control full-width select2" required="required"
                                jsf:required="true" jsf:disabled="#{disabled eq 'disabled'}"
                                jsf:label="UF da Lib. de Crédito">
                            <f:selectItem itemLabel="Selecione"/>
                            <f:selectItems value="#{helperBean.ufs}"/>
                            <p:ajax process="@this" update="municipioLiberacaoDiv"
                                    oncomplete="$.masks(); 	PrimeFaces.focus('municipioLiberacao');"/>
                        </select>
                    </div>
                </div>
                <div class="col-lg-3" jsf:id="municipioLiberacaoDiv">
                    <div class="form-group form-group form-group-default required">
                        <label>Munic. Lib. de Crédito</label>
                        <select jsf:id="municipioLiberacao" jsf:value="#{contratoRsngBean.entity.municipioLiberacao}"
                                class="select2 form-control full-width" jsf:required="true" required="required"
                                jsf:disabled="#{disabled eq 'disabled'}" jsf:label="Município Lib. de Crédito">
                            <f:converter converterId="municipioConverter"/>
                            <f:selectItem itemLabel="Selecione"/>
                            <f:selectItems value="#{helperBean.getMunicipios(contratoRsngBean.entity.ufLiberacaoCredito)}" var="m"
                                           itemLabel="#{m.descricao}"/>
                            <p:ajax process="@this" update="municipioLiberacaoDiv"
                                    oncomplete="$.masks(); PrimeFaces.focus('indiceFinanceiro'); PrimeFaces.focus('tipoVrg'); PrimeFaces.focus('numeroGrupoConsorcio');"/>
                        </select>
                    </div>
                </div>
                <div class="col-lg-3" jsf:id="complementoGrupoConsorcio">
                    <div class="form-group form-group form-group-default #{contratoRsngBean.entity.tipoRestricao == 'ALIENACAO_FIDUCIARIA_CONS' ? 'required' : ''}">
                        <label>Nr do Grupo do Consórcio</label>
                        <input jsf:id="numeroGrupoConsorcio" type="text" maxlength="6"
                               jsf:value="#{contratoRsngBean.entity.numeroGrupoConsorcio}"
                               required="#{contratoRsngBean.entity.tipoRestricao == 'ALIENACAO_FIDUCIARIA_CONS' ? 'required' : null}"
                               jsf:required="#{contratoRsngBean.entity.tipoRestricao == 'ALIENACAO_FIDUCIARIA_CONS'}"
                               disabled="#{disabled eq 'disabled' or contratoRsngBean.entity.tipoRestricao != 'ALIENACAO_FIDUCIARIA_CONS' ? 'disabled' : null}"
                               jsf:label="Nr do Grupo do Consórcio" class="form-control">
                            <f:converter converterId="trimConverter"/>
                        </input>
                    </div>
                </div>
                <div class="col-lg-3" jsf:id="complementoCotaConsorcio">
                    <div class="form-group form-group form-group-default #{contratoRsngBean.entity.tipoRestricao == 'ALIENACAO_FIDUCIARIA_CONS' ? 'required' : ''}">
                        <label>Nr da Cota do Consórcio</label>
                        <input jsf:id="numeroCotaConsorcio" type="text" maxlength="6"
                               jsf:value="#{contratoRsngBean.entity.numeroCotaConsorcio}"
                               required="#{contratoRsngBean.entity.tipoRestricao == 'ALIENACAO_FIDUCIARIA_CONS' ? 'required' : null}"
                               jsf:required="#{contratoRsngBean.entity.tipoRestricao == 'ALIENACAO_FIDUCIARIA_CONS'}"
                               jsf:label="Nr da Cota do Consórcio" class="form-control integer"
                               disabled="#{disabled eq 'disabled' or contratoRsngBean.entity.tipoRestricao != 'ALIENACAO_FIDUCIARIA_CONS' ? 'disabled' : null}">
                            <f:converter converterId="trimConverter"/>
                        </input>
                    </div>
                </div>
            </div>
            <span jsf:id="tipoVrgDiv">
				<div class="row" jsf:rendered="#{contratoRsngBean.entity.tipoRestricao == 'ARRENDAMENTO'}">
					<div class="col-lg-6">
						<div class="form-group form-group form-group-default #{contratoRsngBean.entity.tipoRestricao == 'ARRENDAMENTO' ? 'required' : ''}">
							<label>Tipo de VGR</label>
							<select jsf:id="tipoVrg" jsf:value="#{contratoRsngBean.entity.tipoVrg}"
                                    class="select2 form-control full-width" jsf:required="true" required="required"
                                    jsf:label="Tipo de VGR"
                                    jsf:disabled="#{disabled eq 'disabled' or contratoRsngBean.entity.tipoRestricao != 'ARRENDAMENTO' ? 'disabled' : null}">
								<f:selectItem itemLabel="Selecione"/>
								<f:selectItems value="#{helperBean.tipoVrg}" var="i" itemLabel="#{i.descricao}"/>
								<p:ajax process="@this" update="valorVrgDiv"
                                        oncomplete="$.masks(); 	PrimeFaces.focus('valorVrg');"/>
							</select>
						</div>
					</div>
					<div class="col-lg-6" jsf:id="valorVrgDiv">
						<div class="form-group form-group form-group-default #{contratoRsngBean.entity.tipoRestricao == 'ARRENDAMENTO' ? 'required' : ''}">
							<label>Valor VRG</label>
							<input jsf:id="valorVrg" type="text" jsf:rendered="#{contratoRsngBean.entity.tipoVrg != 'C'}"
                                   jsf:value="#{contratoRsngBean.entity.valorVrg}" required="true" jsf:required="true"
                                   jsf:label="Valor VRG" class="form-control percentual valorVrg"
                                   disabled="#{disabled eq 'disabled' or contratoRsngBean.entity.tipoRestricao != 'ARRENDAMENTO' ? 'disabled' : null}">
								<f:convertNumber pattern="#,##0.00" locale="pt_BR"/>
							</input>
							<input jsf:id="clausulaPenalVrg" type="text" jsf:rendered="#{contratoRsngBean.entity.tipoVrg == 'C'}"
                                   jsf:value="#{contratoRsngBean.entity.clausulaPenalVrg}" required="true" jsf:required="true"
                                   jsf:label="Valor VRG" class="form-control contrato valorVrg"
                                   disabled="#{disabled eq 'disabled' or contratoRsngBean.entity.tipoRestricao != 'ARRENDAMENTO' ? 'disabled' : null}"/>
						</div>
					</div>
				</div>
			</span>
            <div class="row">
                <div class="col-lg-9">
                    <div class="form-group form-group form-group-default">
                        <label>Observação</label>
                        <input jsf:id="comentario" type="text" maxlength="255"
                               jsf:value="#{contratoRsngBean.entity.comentario}" jsf:label="Observação" class="form-control"
                               disabled="#{disabled}">
                            <f:converter converterId="trimConverter"/>
                        </input>
                    </div>
                </div>
				<div class="col-lg-3">
					<div class="form-group form-group form-group-default required">
						<label>Enviar Automático no Detran</label>
						<select jsf:id="registroAutomatico" jsf:value="#{contratoRsngBean.entity.registroAutomaticoDetran}"
								class="select2 form-control full-width" jsf:required="true" required="required" size="1"
								jsf:disabled="#{disabled eq 'disabled'}" jsf:label="Enviar Automático no Detran">
							<f:selectItem itemLabel="Selecione" />
							<f:selectItem itemLabel="Sim" itemValue="#{true}"/>
							<f:selectItem itemLabel="Não" itemValue="#{false}"/>
							<p:ajax process="@this"
									oncomplete="$.masks(); 	PrimeFaces.focus('descricaoPenalidade');"/>
						</select>
					</div>
				</div>
            </div>
        </div>
    </div>

    <div class="panel panel-default">
        <div class="panel-heading">
            <div class="panel-title">Dados Complementares</div>
        </div>
        <div class="panel-body">

            <ul class="nav nav-tabs tabs-bordered">
                <li class="nav-item" jsf:id="label-tab-transacoes" jsf:rendered="#{not empty contratoRsngBean.entity.envios}">
                    <a href="#tab-transacoes" data-toggle="tab" aria-expanded="false" class="nav-link">
                        Transações
                    </a>
                </li>
            </ul>

			<div class="tab-content">
				<div class="tab-pane" jsf:id="tab-transacoes" jsf:rendered="#{not empty contratoRsngBean.entity.envios}">
					<!-- transacoes -->
					<ui:include src="/contratorsng/form-inputs-transacoes.xhtml"/>
				</div>
			</div>

        </div>
    </div>

</ui:composition>
