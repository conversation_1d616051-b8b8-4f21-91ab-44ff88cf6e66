<ui:composition xmlns="http://www.w3.org/1999/xhtml"
	xmlns:h="http://xmlns.jcp.org/jsf/html"
	xmlns:f="http://xmlns.jcp.org/jsf/core"
	xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
	xmlns:p="http://primefaces.org/ui"
	xmlns:jsf="http://xmlns.jcp.org/jsf"
	xmlns:sec="http://www.springframework.org/security/tags"
	template="/templates/blank.xhtml">

	<ui:define name="content">

		<!-- Start content -->
        <div class="content">
            <div class="container-fluid">

                <div class="row">
                    <div class="col-sm-12">
                        <h4 class="header-title">Consulta Boleto</h4>

                        <div class="row" jsf:id="divConsultaCobrancas">
                            <div class="col-lg-12">
								<div class="m-b-20">
	                                 <h6 class="font-14 mt-4">Consulta por Mês de Referência</h6>
									<form jsf:id="form" jsf:prependId="false">
										<h:messages id="messages" warnClass="alert alert-warning alert-dismissable"
											infoClass="alert alert-success alert-dismissable"
											errorClass="alert alert-danger alert-dismissable" />
										<div class="row">
											<div class="col-lg-4">
												<div class="form-group form-group-default form-group-default-select2 required">
													<label>UF</label>
													<select jsf:required="#{true}" jsf:id="uf" jsf:value="#{consultaBoletoBean.uf}" jsf:label="UF"
														class="form-control full-width select2" size="1">
														<f:selectItem itemLabel="Selecione" />
														<f:selectItems value="#{consultaBoletoBean.ufs}" var="u" itemLabel="#{u}"/>
													</select>
												</div>
											</div>
											<div class="col-lg-6">
												<div class="form-group form-group-default form-group-default-select2 required">
													<label>Financeira</label>
													<select jsf:required="#{true}" jsf:id="financeira" jsf:value="#{consultaBoletoBean.financeira}" jsf:label="Financeira"
														class="form-control full-width select2" size="1">
														<f:selectItem itemLabel="Selecione" />
														<f:selectItems value="#{helperSessionBean.financeiras}" var="i" itemValue="#{i}" itemLabel="#{i.documento} - #{i.nome}" />
														<f:converter converterId="financeiraConverter"/>
													</select>
												</div>
											</div>
											<div class="col-lg-2">
												<div class="form-group form-group-default">
													<label>Mês Referência</label>
													<p:inputMask style="border: 1px solid gainsboro !important;" styleClass="form-control" mask="99/9999" value="#{consultaBoletoBean.periodo}" label="Mês de Referência"/>
												</div>
											</div>
										</div>
										<div class="row text-center">
											<div class="col-lg-12">
												<hr class="buttons" />
												<a href="#{request.contextPath}/consultaapoio/consulta-boleto.xhtml" class="btn btn-default">Limpar</a>
												<button type="submit" class="btn btn-primary btn-cons" jsf:action="#{consultaBoletoBean.consultar}">Consultar</button>
											</div>
										</div>
										<input type="hidden" name="${_csrf.parameterName}" value="${_csrf.token}"/>
									</form>
								</div>
							</div>
						</div>

						<div jsf:id="divRetornoSequencial">
							<h:panelGroup rendered="#{consultaBoletoBean.response ne null}"> <!-- DETRAN PI -->
								<form jsf:id="formBoletoPi" jsf:prependId="false">

									<div class="row">
										<div class="col-lg-3">
												<div class="form-group form-group-default">
													<label>Data de Vencimento</label>
													<input type="text" jsf:value="#{consultaBoletoBean.response.dataVencimento}" class="form-control" disabled="disabled">
													</input>
												</div>
											</div>
										<div class="col-lg-3">
											<div class="form-group form-group-default">
												<label>Mês/Referência</label>
												<input type="text" jsf:value="#{consultaBoletoBean.response.mes}/#{consultaBoletoBean.response.ano}" class="form-control" disabled="disabled">
												</input>
											</div>
										</div>
										<div class="col-lg-3">
											<div class="form-group form-group-default">
												<label>Qtd. Registros</label>
												<input type="text" jsf:value="#{consultaBoletoBean.response.quantidadeRegistros}" class="form-control" disabled="disabled">
												</input>
											</div>
										</div>
										<div class="col-lg-2">
											<div class="form-group form-group-default">
												<label>Valor</label>
												<input type="text" jsf:value="#{consultaBoletoBean.response.valor}" class="form-control" disabled="disabled">
												</input>
											</div>
										</div>
									</div>
									<div class="row">
										<div class="col-lg-12">
											<div class="form-group form-group-default">
												<label>Linha Digitável</label>
												<input type="text" jsf:value="#{consultaBoletoBean.response.linhaDigitavel}" class="form-control" disabled="disabled">
												</input>
											</div>
										</div>
									</div>

<!--									<div class="row">-->
<!--										<div class="col-lg-12">-->
											<p:commandLink onclick="PrimeFaces.monitorDownload(loading, closeLoading);"
														   styleClass="btn" title="Download do Boleto" ajax="false">

												<p:fileDownload value="#{consultaBoletoBean.getBoletoDetranPi()}"/>
												<button class="btn btn-primary btn-cons">Download do Boleto</button>
											</p:commandLink>
<!--										</div>-->
<!--									</div>-->
									<input type="hidden" name="${_csrf.parameterName}" value="${_csrf.token}" />
								</form>
							</h:panelGroup>

							<h:panelGroup rendered="#{consultaBoletoBean.responseMs ne null}">
								<div class="row">
									<div class="col-lg-3">
										<div class="form-group form-group-default">
											<label>Data de Vencimento</label>
											<input type="text" jsf:value="#{consultaBoletoBean.responseMs.vencimento}" class="form-control" disabled="disabled">
											</input>
										</div>
									</div>
									<div class="col-lg-3">
										<div class="form-group form-group-default">
											<label>Data de Emisão</label>
											<input type="text" jsf:value="#{consultaBoletoBean.responseMs.dataEmissao}" class="form-control" disabled="disabled">
											</input>
										</div>
									</div>
									<div class="col-lg-3">
										<div class="form-group form-group-default">
											<label>Data de Processamento</label>
											<input type="text" jsf:value="#{consultaBoletoBean.responseMs.dataDeProcessamento}" class="form-control" disabled="disabled">
											</input>
										</div>
									</div>
									<div class="col-lg-3">
										<div class="form-group form-group-default">
											<label>Pago</label>
											<input type="text" jsf:value="#{consultaBoletoBean.responseMs.pago}" class="form-control" disabled="disabled">
											</input>
										</div>
									</div>
								</div>
								<div class="row">
									<div class="col-lg-3">
										<div class="form-group form-group-default">
											<label>Número do Documento</label>
											<input type="text" jsf:value="#{consultaBoletoBean.responseMs.numeroDocumento}" class="form-control" disabled="disabled">
											</input>
										</div>
									</div>
									<div class="col-lg-3">
										<div class="form-group form-group-default">
											<label>Nosso Número</label>
											<input type="text" jsf:value="#{consultaBoletoBean.responseMs.nossoNumero}" class="form-control" disabled="disabled">
											</input>
										</div>
									</div>
									<div class="col-lg-3">
										<div class="form-group form-group-default">
											<label>Total Geral</label>
											<input type="text" jsf:value="#{consultaBoletoBean.responseMs.totalGeral}" class="form-control" disabled="disabled">
											</input>
										</div>
									</div>
									<div class="col-lg-3">
										<div class="form-group form-group-default">
											<label></label>
										</div>
									</div>
								</div>
								<div class="row">
									<div class="col-lg-6">
										<div class="form-group form-group-default">
											<label>Linha Digitável</label>
											<input type="text" jsf:value="#{consultaBoletoBean.responseMs.linhaDigitalvel}" class="form-control" disabled="disabled">
											</input>
										</div>
									</div>
									<div class="col-lg-6">
										<div class="form-group form-group-default">
											<label>Código de Barras</label>
											<input type="text" jsf:value="#{consultaBoletoBean.responseMs.codigoDeBarras}" class="form-control" disabled="disabled">
											</input>
										</div>
									</div>
								</div>
								<div class="table-responsive">
									<form jsf:id="formDataTable" jsf:prependId="false">
										<p:dataTable id="dataTable" var="object" value="#{consultaBoletoBean.responseMsList}"
													 paginator="true" rows="10" paginatorPosition="bottom"
													 emptyMessage="Nenhum registro encontrado"
													 currentPageReportTemplate="({currentPage} de {totalPages})"
													 paginatorTemplate="{FirstPageLink} {PreviousPageLink} {CurrentPageReport} {NextPageLink} {LastPageLink}"
													 lazy="true" draggableColumns="true" resizableColumns="true"
													 tableStyleClass="table table-hover mails m-0 table table-actions-bar">
											<p:column headerText="Chassi" styleClass="text-center">
												#{object.chassi}
											</p:column>
											<p:column headerText="Número do Contrato" styleClass="text-center">
												#{object.numeroContrato}
											</p:column>
											<p:column headerText="Registro" styleClass="text-center">
												#{object.registro}
											</p:column>
											<p:column headerText="Data de Emissão" styleClass="text-center">
												#{object.dataEmissao}
											</p:column>
											<p:column headerText="Data de Vencimento" styleClass="text-center">
												#{object.vencimento}
											</p:column>
										</p:dataTable>
										<p:commandLink onclick="PrimeFaces.monitorDownload(loading, closeLoading);"  styleClass="btn" title="Download de Relatório" ajax="false" action="#{consultaBoletoBean.excel()}" >
											<button class="btn btn-primary btn-cons">Fazer Download do Relatório</button>
										</p:commandLink>
										<p:commandLink onclick="PrimeFaces.monitorDownload(loading, closeLoading);"  styleClass="btn" title="Download de Boleto" ajax="false">
											<p:fileDownload value="#{consultaBoletoBean.getBoletoDetran()}"/>
											<button class="btn btn-primary btn-cons">Fazer Download do Boleto</button>
										</p:commandLink>
										<input type="hidden" name="${_csrf.parameterName}" value="${_csrf.token}" />
									</form>
								</div>
							</h:panelGroup>

							<h:panelGroup rendered="#{consultaBoletoBean.responsePe ne null}">
								<p:dataTable id="dataTablePe" var="object" value="#{consultaBoletoBean.responsePe}" rowIndexVar="index" tableStyleClass="table table-hover m-0">
									<p:column headerText="Envio">
										#{object.MesEnvio}/#{object.AnoEnvio}
									</p:column>
									<p:column headerText="Qtd Envio">
										#{object.QuantidadeEnvio}
									</p:column>
									<p:column headerText="Vlt Unitário">
										#{object.ValorUnitarioEnvio}
									</p:column>
									<p:column headerText="Vencimento">
										#{object.DataVencimento}
									</p:column>
									<p:column headerText="Agente">
										#{object.CpfCnpjAgenteFinanceiro} - #{object.NomeAgenteFinanceiro}
									</p:column>
									<p:column headerText="Qtd Gravame">
										#{object.QuantidadeGravame}
									</p:column>
									<p:column headerText="Pagamento">
										#{object.DataPagamento}
									</p:column>
									<p:column headerText="Pago">
										#{object.ValorPago}
									</p:column>
									<p:column headerText="Aberto">
										#{object.SaldoAberto}
									</p:column>
								</p:dataTable>
							</h:panelGroup>
						</div>

					</div>
				</div>
			</div>
		</div>
	</ui:define>
</ui:composition>
