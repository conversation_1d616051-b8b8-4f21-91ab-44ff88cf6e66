<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:h="http://xmlns.jcp.org/jsf/html"
                xmlns:f="http://xmlns.jcp.org/jsf/core"
                xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
                xmlns:p="http://primefaces.org/ui"
                xmlns:jsf="http://xmlns.jcp.org/jsf"
                template="/templates/blank.xhtml">

    <ui:define name="content">
        <div class="row">
            <div class="col-sm-12">
                <h4 class="header-title m-t-0 m-b-20">Seja Bem-vindo!</h4>
            </div>
            <div class="col-sm-12">
                <h:messages id="messages" warnClass="alert alert-warning alert-dismissable"
                            infoClass="alert alert-success alert-dismissable"
                            errorClass="alert alert-danger alert-dismissable"/>
            </div>
        </div>
        <div id="tab-estatisticas">
            <div class="row">
                <div class="col-sm-12">
                    <h6 class="m-t-0">Pendências</h6>
                    <div class="card-box widget-inline">
                        <div class="row">
                            <div class="col-lg-4 col-sm-6">
                                <div class="widget-inline-box text-center">
                                    <h3 class="m-t-10">
                                        <i class="text-danger mdi mdi-alert-outline"></i>
                                        <b>
                                            <a class="text-muted" href="#{request.contextPath}/envio/list.xhtml">
                                                #{dashboardBean.quantidadeEnviar}
                                            </a>
                                        </b>
                                    </h3>
                                    <p class="text-muted">
                                        <a class="text-muted" href="#{request.contextPath}/envio/list.xhtml">
                                            Contratos Pendentes
                                        </a>
                                    </p>
                                    <div class="progress progress-sm m-0">
                                        <div class="progress-bar progress-bar-danger" role="progressbar"
                                             aria-valuenow="100"
                                             aria-valuemin="0" aria-valuemax="100"
                                             style="width: #{dashboardBean.quantidadeEnviar > 0 ? 100: 0}%;">
                                            <span class="sr-only">#{dashboardBean.quantidadeEnviar > 0 ? 100: 0}% Complete</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-lg-4 col-sm-6">
                                <div class="widget-inline-box text-center">
                                    <h3 class="m-t-10">
                                        <i class="text-warning mdi mdi-cash-100"></i>
                                        <b>#{dashboardBean.quantidadeContratosPagamento}</b>
                                    </h3>
                                    <p class="text-muted">Aguardando Confirmação DETRAN</p>
                                    <div class="progress progress-sm m-0">
                                        <div class="progress-bar progress-bar-danger" role="progressbar"
                                             aria-valuenow="100"
                                             aria-valuemin="0" aria-valuemax="100"
                                             style="width: #{dashboardBean.quantidadeContratosPagamento > 0 ? 100 : 0}%;">
                                            <span class="sr-only">#{dashboardBean.quantidadeContratosPagamento > 0 ? 100 : 0}% Complete</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-lg-4 col-sm-6">
                                <div class="widget-inline-box text-center">
                                    <h3 class="m-t-10">
                                        <i class="text-danger mdi mdi-lan-disconnect"></i>
                                        <b>#{dashboardBean.quantidadeErroComunicacao}</b>
                                    </h3>
                                    <p class="text-muted">Erro de Comunicação</p>
                                    <div class="progress progress-sm m-0">
                                        <div class="progress-bar progress-bar-danger" role="progressbar"
                                             aria-valuenow="100"
                                             aria-valuemin="0" aria-valuemax="100"
                                             style="width: #{dashboardBean.quantidadeErroComunicacao > 0 ? 100: 0}%;">
                                            <span class="sr-only">#{dashboardBean.quantidadeErroComunicacao > 0 ? 100: 0}% Complete</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-sm-12">
                    <h6 class="m-t-0">Quantitativos</h6>
                    <div class="card-box widget-inline">
                        <div class="row">
                            <div class="col-lg-3 col-sm-6">
                                <div class="widget-inline-box text-center">
                                    <h3 class="m-t-10">
                                        <i class="text-warning mdi mdi-attachment"></i>
                                        <b>
                                            <a class="text-muted"
                                               href="#{request.contextPath}/documento/list.xhtml?anexo=N">
                                                #{dashboardBean.quantidadeSemAnexo}
                                            </a>
                                        </b>
                                    </h3>
                                    <p class="text-muted">
                                        <a class="text-muted"
                                           href="#{request.contextPath}/documento/list.xhtml?anexo=N">
                                            Contratos sem Anexo
                                        </a>
                                    </p>
                                    <div class="progress progress-sm m-0">
                                        <div class="progress-bar progress-bar-warning" role="progressbar"
                                             aria-valuenow="#{dashboardBean.percentualSemAnexo}" aria-valuemin="0"
                                             aria-valuemax="100"
                                             style="width: #{dashboardBean.percentualSemAnexo}%;">
                                            <span class="sr-only">#{dashboardBean.percentualSemAnexo}% Complete</span>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="col-lg-3 col-sm-6">
                                <div class="widget-inline-box text-center b-0">
                                    <h3 class="m-t-10">
                                        <i class="text-success mdi mdi-led-on"></i>
                                        <b>
                                            <a class="text-muted"
                                               href="#{request.contextPath}/contrato/list.xhtml?situacao=ATIVO">
                                                #{dashboardBean.quantidadeContratosAtivos}
                                            </a>
                                        </b>
                                    </h3>
                                    <p class="text-muted">
                                        <a class="text-muted"
                                           href="#{request.contextPath}/contrato/list.xhtml?situacao=ATIVO">
                                            Contratos Ativos
                                        </a>
                                    </p>
                                    <div class="progress progress-sm m-0">
                                        <div class="progress-bar progress-bar-success" role="progressbar"
                                             aria-valuenow="#{dashboardBean.percentualAtivos}" aria-valuemin="0"
                                             aria-valuemax="100" style="width: #{dashboardBean.percentualAtivos}%;">
                                            <span class="sr-only">#{dashboardBean.percentualAtivos}% Complete</span>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="col-lg-3 col-sm-6">
                                <div class="widget-inline-box text-center">
                                    <h3 class="m-t-10">
                                        <i class="text-info mdi mdi-led-variant-off"></i>
                                        <b>
                                            <a class="text-muted"
                                               href="#{request.contextPath}/contrato/list.xhtml?situacao=BAIXADO">
                                                #{dashboardBean.quantidadeContratosBaixados}
                                            </a>
                                        </b>
                                    </h3>
                                    <p class="text-muted">
                                        <a class="text-muted"
                                           href="#{request.contextPath}/contrato/list.xhtml?situacao=BAIXADO">
                                            Contratos Baixados
                                        </a>
                                    </p>
                                    <div class="progress progress-sm m-0">
                                        <div class="progress-bar progress-bar-info" role="progressbar"
                                             aria-valuenow="#{dashboardBean.percentualBaixados}" aria-valuemin="0"
                                             aria-valuemax="100"
                                             style="width: #{dashboardBean.percentualBaixados}%;">
                                            <span class="sr-only">#{dashboardBean.percentualBaixados}% Complete</span>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="col-lg-3 col-sm-6">
                                <div class="widget-inline-box text-center">
                                    <h3 class="m-t-10">
                                        <i class="text-success mdi mdi-link-variant"></i>
                                        <b>
                                            <a class="text-muted"
                                               href="#{request.contextPath}/contrato/list.xhtml?situacao=ADITIVADO">
                                                #{dashboardBean.quantidadeContratosAditivados}
                                            </a>
                                        </b>
                                    </h3>
                                    <p class="text-muted">
                                        <a class="text-muted"
                                           href="#{request.contextPath}/contrato/list.xhtml?situacao=ADITIVADO">
                                            Contratos Aditivados
                                        </a>
                                    </p>
                                    <div class="progress progress-sm m-0">
                                        <div class="progress-bar progress-bar-success" role="progressbar"
                                             aria-valuenow="#{dashboardBean.percentualAditivados}" aria-valuemin="0"
                                             aria-valuemax="100"
                                             style="width: #{dashboardBean.percentualAditivados}%;">
                                            <span class="sr-only">#{dashboardBean.percentualAditivados}% Complete</span>
                                        </div>
                                    </div>
                                </div>
                            </div>

                        </div>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-lg-12">
                    <div class="card-box">
                        <h6 class="m-t-0">Contratos Registrados</h6>
                        <h:form>
                            <div>
                                <div class="row">
                                    <div class="col-lg-3">
                                        <div class="form-group form-group-default form-group-default-select2">
                                            <select jsf:value="#{dashboardBean.cnpjGeral}"
                                                    class="form-control full-width select2" size="1">
                                                <f:selectItem itemLabel="Agrupar por CNPJ" itemValue="C"/>
                                                <f:selectItem itemLabel="Agrupar Tudo" itemValue="G"/>
                                                <p:ajax process="@this" update="lineChartTransacao"
                                                        listener="#{dashboardBean.initLinearModel()}"/>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-lg-3">
                                        <div class="form-group form-group-default form-group-default-select2">
                                            <select jsf:value="#{dashboardBean.diarioCumulativo}"
                                                    class="form-control full-width select2" size="1">
                                                <f:selectItem itemLabel="Diário" itemValue="D"/>
                                                <f:selectItem itemLabel="Acumulativo" itemValue="C"/>
                                                <p:ajax process="@this" update="lineChartTransacao"
                                                        listener="#{dashboardBean.initLinearModel()}"/>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-lg-2">
                                        <div class="form-group form-group-default form-group-default-select2">
                                            <select jsf:value="#{dashboardBean.ufTransacoes}"
                                                    class="form-control full-width select2" size="1">
                                                <f:selectItem itemLabel="Estados" itemValue=""/>
                                                <f:selectItems value="#{helperSessionBean.ufsCredenciamento}"/>
                                                <p:ajax process="@this" update="lineChartTransacao"
                                                        listener="#{dashboardBean.initLinearModel()}"/>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-lg-2">
                                        <div class="form-group form-group-default form-group-default-select2">
                                            <select jsf:value="#{dashboardBean.tipoVisualizacao}"
                                                    class="form-control full-width select2" size="1">
                                                <f:selectItem itemLabel="Registros" itemValue="0"/>
                                                <f:selectItem itemLabel="Contratos" itemValue="1"/>
                                                <p:ajax process="@this" update="lineChartTransacao"
                                                        listener="#{dashboardBean.initLinearModel()}"/>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-lg-2">
                                        <div class="form-group form-group-default form-group-default-select2">
                                            <select jsf:value="#{dashboardBean.periodoTransacao}"
                                                    class="form-control full-width select2" size="1">
                                                <f:selectItem itemLabel="Mês Atual" itemValue="0"/>
                                                <f:selectItem itemLabel="Mês Anterior" itemValue="-1"/>
                                                <f:selectItem itemLabel="Dois Meses Atrás" itemValue="-2"/>
                                                <f:selectItem itemLabel="Três Meses Atrás" itemValue="-3"/>
                                                <f:selectItem itemLabel="Quatro Meses Atrás" itemValue="-4"/>
                                                <f:selectItem itemLabel="Cinco Meses Atrás" itemValue="-5"/>
                                                <f:selectItem itemLabel="Seis Meses Atrás" itemValue="-6"/>
                                                <p:ajax process="@this" update="lineChartTransacao"
                                                        listener="#{dashboardBean.initLinearModel()}"/>
                                            </select>
                                            <input type="hidden" name="${_csrf.parameterName}"
                                                   value="${_csrf.token}"/>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </h:form>
                        <div jsf:id="lineChartTransacao">
                            <p:lineChart style="width: 100%; height: 100vh;"
                                         rendered="#{dashboardBean.lineChartModelTransacoes ne null}"
                                         model="#{dashboardBean.lineChartModelTransacoes}"/>
                        </div>
                    </div>
                </div>

                <!-- end col -->
            </div>

            <div class="row" jsf:rendered="#{not empty dashboardBean.financeiras and dashboardBean.usuario.perfil ne 'ADMINISTRADOR'}">
                <div class="col-sm-12">
                    <div class="card-box">
                        <h6 class="m-t-0 text-info">Representações</h6>
                        <div class="table-responsive">
                            <table class="table table-hover mails m-0 table table-actions-bar">
                                <thead>
                                <tr>
                                    <th>CNPJ</th>
                                    <th>Financeira</th>
                                </tr>
                                </thead>
                                <tbody>
                                <ui:repeat var="f" value="#{dashboardBean.financeiras}">
                                    <tr>
                                        <td>#{f.documento}</td>
                                        <td>#{f.nome}</td>
                                    </tr>
                                </ui:repeat>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row" jsf:rendered="#{not empty dashboardBean.financeiras and dashboardBean.usuario.perfil ne 'ADMINISTRADOR'}">
                <div class="col-sm-12">
                    <div class="card-box">
                        <h6 class="m-t-0 text-warning">Situação de Cadastro</h6>
                        <div class="table-responsive">
                            <table class="table table-hover mails m-0 table table-actions-bar">
                                <ui:repeat var="object" value="#{dashboardBean.financeiras}">
                                    <thead>
                                    <tr>
                                        <th>#{object.nome}</th>
                                        <th>Situação</th>
                                        <th>Credenciamento</th>
                                    </tr>
                                    </thead>
                                    <ui:repeat var="o" value="#{object.situacoesFinanceiraEstado}">
                                        <tbody>
                                        <tr>
                                            <td>#{o.uf}</td>
                                            <td>#{o.situacaoFinanceira == 'S' ? 'Ativo' : 'Inativo'}</td>
                                            <td>
													<span jsf:rendered="#{o.dataInicioCredenciamentoDetran != null}">
															<h:outputText value="#{o.dataInicioCredenciamentoDetran}">
																<f:convertDateTime pattern="dd/MM/yyyy"/>
															</h:outputText>
															a
															<h:outputText value="#{o.dataFimCredenciamentoDetran}">
																<f:convertDateTime pattern="dd/MM/yyyy"/>
															</h:outputText>
													</span>
                                                <span jsf:rendered="#{o.dataInicioCredenciamentoDetran == null}">
														Sem credenciamento
													</span>
                                            </td>
                                        </tr>
                                        </tbody>
                                    </ui:repeat>
                                </ui:repeat>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

    </ui:define>

</ui:composition>
