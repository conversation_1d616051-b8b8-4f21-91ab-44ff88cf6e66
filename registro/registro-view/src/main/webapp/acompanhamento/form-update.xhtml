<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:h="http://xmlns.jcp.org/jsf/html"
                xmlns:f="http://xmlns.jcp.org/jsf/core"
                xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
                xmlns:p="http://primefaces.org/ui"
                xmlns:jsf="http://xmlns.jcp.org/jsf"
                xmlns:sec="http://www.springframework.org/security/tags"
                template="/templates/blank.xhtml">

    <ui:define name="content">

        <f:metadata>
            <f:viewParam id="id" name="id" value="#{acompanhamentoObservatorioBean.idToEdit}"/>
            <f:viewAction action="#{acompanhamentoObservatorioBean.loadDetails()}"/>
        </f:metadata>

        <div class="content">
            <div class="container-fluid">
                <div class="col-lg-12">
                    <div class="m-b-20">
                        <h6 class="font-14 mt-4">Visualizar</h6>
                        <h:messages id="messages" warnClass="alert alert-warning alert-dismissable"
                                    infoClass="alert alert-success alert-dismissable"
                                    errorClass="alert alert-danger alert-dismissable"/>
                        <form jsf:id="form" jsf:prependId="false" class="form">
                            <ui:include src="form-inputs.xhtml">
                            </ui:include>
                            <div class="row text-center">
                                <div class="col-lg-12">
                                    <hr class="buttons"/>
                                    <a href="#{request.contextPath}/acompanhamento/acompanhamento-place.xhtml"
                                       class="btn btn-default">Voltar</a>
                                    <a href="#{request.contextPath}/acompanhamento/form-detail.xhtml?id=#{acompanhamentoObservatorioBean.entity.id}" class="btn btn-default">Cancelar</a>
                                    <button type="submit" jsf:action="#{acompanhamentoObservatorioBean.save}" jsf:immediate="true" class="btn btn-primary btn-cons">Salvar</button>
                                    <input type="hidden" name="${_csrf.parameterName}" value="${_csrf.token}"/>

                                    <input type="hidden" name="${_csrf.parameterName}" value="${_csrf.token}"/>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </ui:define>
</ui:composition>
