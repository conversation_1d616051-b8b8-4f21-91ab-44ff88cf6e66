<ui:composition xmlns="http://www.w3.org/1999/xhtml"
	xmlns:h="http://xmlns.jcp.org/jsf/html"
	xmlns:f="http://xmlns.jcp.org/jsf/core"
	xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
	xmlns:p="http://primefaces.org/ui"
	xmlns:jsf="http://xmlns.jcp.org/jsf"
	xmlns:sec="http://www.springframework.org/security/tags"
	template="/templates/blank.xhtml">

	<ui:define name="content">

		<f:metadata>
			<f:viewAction action="#{contaAzulBean.initPesquisa}"/>
		</f:metadata>

		 <!-- Start content -->
        <div class="content">
            <div class="container-fluid">

                <div class="row">
                    <div class="col-sm-12">
                        <h4 class="header-title">Cobranças Disponíveis para enviar para Conta Azul</h4>

                        <div class="row">
                            <div class="col-lg-12">
								<div class="m-b-20">
	                                 <h6 class="font-14 mt-4">Pesquisar</h6>
									<form jsf:id="form" jsf:prependId="false">
										<p:poll interval="240" listener="#{contaAzulBean.refreshToken}" update="messages" />
										<h:messages id="messages" warnClass="alert alert-warning alert-dismissable"
											infoClass="alert alert-success alert-dismissable"
											errorClass="alert alert-danger alert-dismissable" />
										<div class="row">
											<div class="col-lg-4">
												<div class="form-group form-group-default form-group-default-select2">
													<label>UF</label>
													<select jsf:id="uf" jsf:value="#{contaAzulBean.filter.estado}"
														class="form-control full-width select2" size="1" disabled="#{disabled}">
														<f:selectItem itemLabel="Selecione" />
														<f:selectItems value="#{helperBean.ufsCobranca}" var="i" itemLabel="#{i}" />
													</select>
												</div>
											</div>
											<div class="col-lg-4">
												<div class="form-group form-group-default form-group-default-select2">
													<label>Situação</label>
													<select jsf:id="situacaoFinanceira" jsf:value="#{contaAzulBean.filter.situacaoCobranca}"
														class="form-control full-width select2" size="1" disabled="#{disabled}">
														<f:selectItems value="#{helperBean.situacoesCobrancaContaAzul}" var="i" itemLabel="#{i}" />
													</select>
												</div>
											</div>
											<div class="col-lg-4">
												<div class="form-group form-group-default form-group-default-select2">
													<label>Financeira</label>
													<select jsf:id="financeira" jsf:value="#{contaAzulBean.filter.financeira}"
														class="form-control full-width select2" size="1" disabled="#{disabled}">
														<f:selectItem itemLabel="Selecione" />
														<f:selectItems value="#{helperSessionBean.financeiras}" var="i" itemValue="#{i}" itemLabel="#{i.documento} - #{i.nome}" />
														<f:converter converterId="financeiraConverter"/>
													</select>
												</div>
											</div>
										</div>
										<div class="row text-center">
											<div class="col-lg-12">
												<hr class="buttons" />
												<a href="#{request.contextPath}/contaazul/list-disponiveis.xhtml" class="btn btn-default">Limpar</a>
												<button type="submit" class="btn btn-primary btn-cons" jsf:action="#{contaAzulBean.search}">Pesquisar</button>
											</div>
										</div>
										<input type="hidden" name="${_csrf.parameterName}" value="${_csrf.token}"/>
									</form>
								</div>
							</div>
						</div>
						<div class="row">
							<div class="table-responsive">
								<form jsf:id="formDataTable" jsf:prependId="false">
									<div style="min-height: 30px;">
										<div style="float: right; padding: 0;">
											Resultados por página
											<select jsf:id="registros" jsf:value="#{contaAzulBean.size}" size="1" >
												<f:selectItem itemLabel="10" itemValue="10" />
												<f:selectItem itemLabel="25" itemValue="25" />
												<f:selectItem itemLabel="50" itemValue="50" />
												<f:selectItem itemLabel="100" itemValue="100" />
												<f:ajax execute="@this" render="formDataTable" listener="#{contaAzulBean.search}" onevent="function(data){$.masks();}" />
											</select>
										</div>
									</div>
									<p:dataTable id="dataTable" var="object" value="#{contaAzulBean.list}" paginator="true" rows="#{contaAzulBean.size}" paginatorPosition="bottom" emptyMessage="Nenhum registro encontrado"
										currentPageReportTemplate="({currentPage} de {totalPages})" paginatorTemplate="{FirstPageLink} {PreviousPageLink} {CurrentPageReport} {NextPageLink} {LastPageLink}" lazy="true" draggableColumns="true" resizableColumns="true"
										tableStyleClass="table table-hover m-0">
										<p:column headerText="Data da Cobrança">
											<i class="mdi mdi-plus-circle" style="color: #23b195;" title="Integra+" jsf:rendered="#{object.integraMais == 'S'}"></i>
											<h:outputText value="#{object.dataGeracao}">
												<f:convertDateTime type="date"/>
											</h:outputText>
										</p:column>
										<p:column headerText="Financeira">
											<span title="#{object.financeira.nome}">
												#{object.financeira.documento}
											</span>
										</p:column>
										<p:column headerText="UF">
											#{object.estado}
										</p:column>
										<p:column headerText="Situação">
											#{object.situacaoCobranca.descricao}
										</p:column>
										<p:column headerText="Registros">
											#{object.quantidadeRegistros}
										</p:column>
										<p:column headerText="Total">
											<h:outputText value="#{object.valorCobranca}">
												<f:convertNumber pattern="#,##0.00" locale="pt_BR" />
											</h:outputText>
										</p:column>
										<p:column headerText="Credenciada">
											<h:outputText value="#{object.valorCredenciadaIntegra}">
												<f:convertNumber pattern="#,##0.00" locale="pt_BR" />
											</h:outputText>
										</p:column>
										<p:column headerText="Desconto">
											<h:outputText value="#{object.valorDesconto}">
												<f:convertNumber pattern="#,##0.00" locale="pt_BR" />
											</h:outputText>
										</p:column>
										<p:column headerText="Ações" styleClass="text-center">
											<p:commandLink rendered="#{object.notaAzul == null or object.notaAzul == false}" action="#{contaAzulBean.irParaConfirmarVenda(object)}" styleClass="btn btn-link" title="Emitir NF" ajax="false" onclick="PrimeFaces.monitorDownload(loading, closeLoading);">
													<span class="mdi mdi-receipt text-success" />
											</p:commandLink>
											<p:commandLink rendered="#{(object.notaAzulSng == null or object.notaAzulSng == false) and object.quantidadeRegistrosSng > 0}" action="#{contaAzulBean.irParaConfirmarVendaSng(object)}" styleClass="btn btn-link" title="Emitir NF SNG" ajax="false" onclick="PrimeFaces.monitorDownload(loading, closeLoading);">
													<span class="mdi mdi-receipt text-info" />
											</p:commandLink>
										</p:column>
									</p:dataTable>
									<input type="hidden" name="${_csrf.parameterName}" value="${_csrf.token}" />
								</form>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</ui:define>
</ui:composition>