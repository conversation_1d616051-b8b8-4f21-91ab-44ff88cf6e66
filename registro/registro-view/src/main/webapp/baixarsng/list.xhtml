<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:h="http://xmlns.jcp.org/jsf/html"
                xmlns:f="http://xmlns.jcp.org/jsf/core"
                xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
                xmlns:p="http://primefaces.org/ui"
                xmlns:jsf="http://xmlns.jcp.org/jsf"
                xmlns:sec="http://www.springframework.org/security/tags"
                template="/templates/blank.xhtml">
    <h:head>
        <style>
            .limited-text {
                display: -webkit-box;
                -webkit-line-clamp: 3;
                -webkit-box-orient: vertical;
                overflow: hidden;
                text-overflow: ellipsis;
                line-height: 1.2em;
                max-height: 3.6em;
                position: relative;
                cursor: help;
                white-space: normal;
            }
        </style>
    </h:head>
    <ui:define name="content">
        <div class="content">
            <div class="container-fluid">
                <div class="row">
                    <div class="col-sm-12">
                        <h4 class="header-title">Baixar Apontamento RSNG</h4>

                        <div class="row">
                            <div class="col-lg-12">
                                <div class="m-b-20">
                                    <h6 class="font-14 mt-4">Pesquisar</h6>
                                    <form jsf:id="form" jsf:prependId="false">
                                        <h:messages id="messages" warnClass="alert alert-warning alert-dismissable"
                                                    infoClass="alert alert-success alert-dismissable"
                                                    errorClass="alert alert-danger alert-dismissable"/>
                                        <div class="row">
                                            <div class="col-lg-4">
                                                <div class="form-group form-group-default">
                                                    <label>Número do Registro Eletrônico</label>
                                                    <input jsf:id="protocoloPlaceconRsng" type="text"
                                                           jsf:value="#{baixaContratoRsngBean.filter.protocoloPlaceconRsng}"
                                                           class="form-control first integer"/>
                                                </div>
                                            </div>
                                            <div class="col-lg-4">
                                                <div class="form-group form-group-default">
                                                    <label>Número do Contrato</label>
                                                    <input jsf:id="numeroContrato" type="text"
                                                           jsf:value="#{baixaContratoRsngBean.filter.numeroContrato}"
                                                           class="form-control">
                                                        <f:converter converterId="trimConverter"/>
                                                    </input>
                                                </div>
                                            </div>
                                            <div class="col-lg-4">
                                                <div class="form-group form-group-default">
                                                    <label>CPF/CNPJ do Devedor</label>
                                                    <input jsf:id="cpfCnpjDevedorFinanciado" type="text"
                                                           jsf:value="#{baixaContratoRsngBean.filter.cpfCnpjDevedorFinanciado}"
                                                           jsf:validator="cpfCnpjValidator" class="form-control doc">
                                                        <f:converter converterId="trimConverter"/>
                                                    </input>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-lg-4">
                                                <div class="form-group form-group-default">
                                                    <label>Apontamento</label>
                                                    <input jsf:id="gravame" type="text" maxlength="8"
                                                           jsf:value="#{baixaContratoRsngBean.filter.numeroApontamento}"
                                                           class="form-control naoCola"
                                                           onkeypress="return /^-?[0-9]*$/.test(this.value+event.key)">
                                                        <f:converter converterId="trimConverter"/>
                                                    </input>
                                                </div>
                                            </div>
                                            <div class="col-lg-4">
                                                <div class="form-group form-group-default">
                                                    <label>Chassi</label>
                                                    <input jsf:id="chassi" type="text"
                                                           jsf:value="#{baixaContratoRsngBean.filter.chassi}"
                                                           class="form-control chassi">
                                                        <f:converter converterId="trimConverter"/>
                                                    </input>
                                                </div>
                                            </div>
                                            <div class="col-lg-4">
                                                <div class="form-group form-group-default">
                                                    <label>Placa</label>
                                                    <input jsf:id="placa" type="text"
                                                           jsf:value="#{baixaContratoRsngBean.filter.placa}"
                                                           class="form-control">
                                                        <f:converter converterId="trimConverter"/>
                                                    </input>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="row text-center">
                                            <div class="col-lg-12">
                                                <hr class="buttons"/>
                                                <a href="#{request.contextPath}/baixarsng/list.xhtml"
                                                   class="btn btn-default">Limpar</a>
                                                <button type="submit" class="btn btn-primary btn-cons"
                                                        jsf:action="#{baixaContratoRsngBean.search}">Pesquisar
                                                </button>
                                            </div>
                                        </div>
                                        <input type="hidden" name="${_csrf.parameterName}" value="${_csrf.token}"/>
                                    </form>
                                </div>
                            </div>
                        </div>
                        <div class="row" jsf:rendered="#{baixaContratoRsngBean.list != null}">
                            <div class="table-responsive">
                                <form jsf:id="formDataTable" jsf:prependId="false">
                                    <div style="min-height: 30px;">
                                        <div style="float: right; padding: 0;">
                                            Resultados por página
                                            <select jsf:id="registros" jsf:value="#{baixaContratoRsngBean.size}"
                                                    size="1">
                                                <f:selectItem itemLabel="10" itemValue="10"/>
                                                <f:selectItem itemLabel="25" itemValue="25"/>
                                                <f:selectItem itemLabel="50" itemValue="50"/>
                                                <f:selectItem itemLabel="100" itemValue="100"/>
                                                <f:ajax execute="@this" render="formDataTable"
                                                        listener="#{baixaContratoRsngBean.search}"
                                                        onevent="function(data){$.masks();}"/>
                                            </select>
                                        </div>
                                    </div>
                                    <p:dataTable id="dataTable" var="object" value="#{baixaContratoRsngBean.list}"
                                                 paginator="true" rows="#{baixaContratoRsngBean.size}"
                                                 paginatorPosition="bottom" emptyMessage="Nenhum registro encontrado"
                                                 currentPageReportTemplate="({currentPage} de {totalPages})"
                                                 paginatorTemplate="{FirstPageLink} {PreviousPageLink} {CurrentPageReport} {NextPageLink} {LastPageLink}"
                                                 lazy="true" draggableColumns="true" resizableColumns="true"
                                                 tableStyleClass="table table-hover m-0"
                                    >
                                        <p:column headerText="Registro">
                                            <a class="" href="#{request.contextPath}/contrato/form-detail.xhtml?id=#{object.id}" jsf:rendered="#{object.tipo eq 'CONTRATO'}">
                                                <i class="mdi mdi-file" jsf:style="color: #00aced" title="Contrato" jsf:rendered="#{object.tipo eq 'CONTRATO'}"></i>
                                                #{object.numeroRegistroEletronico}
                                            </a>
                                            <a class="" href="#{request.contextPath}/contratorsng/form-detail.xhtml?id=#{object.id}" jsf:rendered="#{object.tipo eq 'RSNG'}">
                                                <i class="mdi mdi-file" jsf:style="color: #1d937c" title="Contrato RSNG" jsf:rendered="#{object.tipo eq 'RSNG'}"></i>
                                                #{object.protocoloPlaceconRsng}
                                            </a>
                                        </p:column>
                                        <p:column headerText="Contrato">
                                            #{object.numeroContrato}
                                        </p:column>
                                        <p:column headerText="Devedor">
                                            <h:outputText value="#{object.cpfCnpjDevedorFinanciado}"
                                                          title="#{object.nomeDevedorFinanciado}">
                                                <f:converter converterId="cpfCnpjConverter"/>
                                            </h:outputText>
                                        </p:column>
                                        <p:column headerText="Gravame">
                                            <h:outputText styleClass="limited-text" value="#{object.apontamentoOuGravame}" escape="false"/>
                                        </p:column>
                                        <p:column headerText="Chassi">
                                            <h:outputText styleClass="limited-text" value="#{object.chassi}" escape="false"/>
                                        </p:column>
                                        <p:column headerText="Ações" styleClass="text-center">
                                            <h:commandLink styleClass="btn btn-link" title="Baixar"
                                                           onclick="return confirm('Confirma a Baixa do Contrato?')"
                                                           action="#{baixaContratoRsngBean.baixar(object)}"  rendered="#{object.situacaoBaixaB3 != 'BAIXADO'}">
                                                <i class="mdi mdi-check" title="Baixa RSNG de Contrato"></i>
                                            </h:commandLink>
                                            <h:commandLink styleClass="btn btn-link" title="Cancelar Baixa"
                                                           onclick="return confirm('Confirma o Cancelamento de Baixa do Contrato?')"
                                                           action="#{baixaContratoRsngBean.cancelarBaixa(object)}" rendered="#{object.situacaoBaixaB3 != 'CANCELADO'}">
                                                <i class="mdi mdi-close" title="Cancelamento de Baixa RSNG de Contrato"></i>
                                            </h:commandLink>
                                        </p:column>
                                    </p:dataTable>
                                    <input type="hidden" name="${_csrf.parameterName}" value="${_csrf.token}"/>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </ui:define>
</ui:composition>
