<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:h="http://xmlns.jcp.org/jsf/html"
                xmlns:f="http://xmlns.jcp.org/jsf/core"
                xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
                xmlns:p="http://primefaces.org/ui"
                xmlns:jsf="http://xmlns.jcp.org/jsf"
                xmlns:sec="http://www.springframework.org/security/tags"
                template="/templates/blank.xhtml">

    <ui:define name="scripts">
        <script src="#{request.contextPath}/templates/assets/js/scripts_registro.js" type="text/javascript"></script>
    </ui:define>

    <ui:define name="content">
        <f:metadata>
            <f:viewParam id="id" name="id" value="#{baixaContratoRsngBean.idToEdit}"/>
            <f:viewParam id="tipo" name="tipo" value="#{baixaContratoRsngBean.tipo}"/>
            <f:viewAction action="#{baixaContratoRsngBean.loadDetails()}"/>
        </f:metadata>

        <!-- Start content -->
        <div class="content">
            <div class="container-fluid">

                <div class="row">
                    <div class="col-sm-12">
                        <h4 class="header-title">Baixa de Contrato B3</h4>
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="m-b-20">
                                    <h6 class="font-14 mt-4">Baixar</h6>
                                    <h:messages id="messages" warnClass="alert alert-warning alert-dismissable"
                                                infoClass="alert alert-success alert-dismissable"
                                                errorClass="alert alert-danger alert-dismissable"/>

                                    <form jsf:id="form" jsf:prependId="false" class="form">
                                        <ui:include src="/baixarsng/form-inputs-#{baixaContratoRsngBean.tipo}.xhtml">
                                            <ui:param name="disabled" value="disabled"/>
                                            <ui:param name="baixa" value="#{true}"/>
                                        </ui:include>
                                        <div class="row text-center">
                                            <div class="col-lg-12">
                                                <hr class="buttons"/>
                                                <sec:authorize ifAnyGranted="BAIXAR_CONTRATO">
                                                    <h:commandLink styleClass="btn btn-primary btn-cons" title="Baixar"
                                                                   onclick="return confirm('Confirma a Baixa do Contrato?')"
                                                                   action="#{baixaContratoRsngBean.baixar()}">
                                                        Baixar Contrato
                                                    </h:commandLink>
                                                </sec:authorize>
                                                <a href="#{request.contextPath}/baixarsng/list.xhtml"
                                                   class="btn btn-default">Voltar</a>
                                                <input type="hidden" name="${_csrf.parameterName}"
                                                       value="${_csrf.token}"/>
                                            </div>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </ui:define>

</ui:composition>
