<ui:composition xmlns="http://www.w3.org/1999/xhtml"
	xmlns:h="http://xmlns.jcp.org/jsf/html"
	xmlns:f="http://xmlns.jcp.org/jsf/core"
	xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
	xmlns:p="http://primefaces.org/ui"
	xmlns:jsf="http://xmlns.jcp.org/jsf"
	xmlns:sec="http://www.springframework.org/security/tags"
	template="/templates/blank.xhtml">

	<ui:define name="content">
		
		<f:metadata>
			<f:viewParam name="anexo" value="#{contratoBean.filter.possuiAnexo}" />
			<f:viewParam name="situacao" value="#{contratoBean.filter.situacao}" />
			<f:viewParam name="assinado" value="#{contratoBean.filter.assinado}" />
			<f:viewParam name="consultaRapida" value="#{contratoBean.filter.consultaRapida}" />
			<f:viewParam name="valor" value="#{contratoBean.filter.valor}" />
		</f:metadata>
	
        <!-- Start content -->
        <div class="content">
            <div class="container-fluid">

                <div class="row">
                    <div class="col-sm-12">
                        <h4 class="header-title">Contrato</h4>
                        <div class="row">
                            <div class="col-lg-12">
								<div class="m-b-20">
	                                 <h6 class="font-14 mt-4">Pesquisar</h6>
									<form jsf:id="form" jsf:prependId="false">
										<h:messages id="messages" warnClass="alert alert-warning alert-dismissable"
											infoClass="alert alert-success alert-dismissable"
											errorClass="alert alert-danger alert-dismissable" />
										<div class="row">
											<div class="col-lg-3">
												<div class="form-group form-group-default">
													<label>Registro Eletrônico</label>
													<input jsf:id="numeroRegistroEletronico" type="text" maxlength="14"
														   jsf:value="#{contratoBean.filter.numeroRegistroEletronico}"
														   class="form-control first integer">
													</input>
												</div>
											</div>
											<div class="col-lg-3">
												<div class="form-group form-group-default">
													<label>Número do Contrato</label>
													<input jsf:id="numeroContrato" type="text" 
														jsf:value="#{contratoBean.filter.numeroContrato}" class="form-control">
														<f:converter converterId="trimConverter"/>
													</input>
												</div>
											</div>
											<div class="col-lg-3">
												<div class="form-group form-group-default">
													<label>CPF/CNPJ Nome do Devedor</label>
													<input jsf:id="cpfCnpjDevedorFinanciado" type="text"
														   jsf:value="#{contratoBean.filter.cpfCnpjDevedorFinanciado}"
														   jsf:validator="cpfCnpjValidator" class="form-control doc">
														<f:converter converterId="trimConverter"/>
													</input>
												</div>
											</div>
											<div class="col-lg-3">
												<div class="form-group form-group-default">
													<label>Gravame</label>
													<input jsf:id="numeroGravame" type="text" maxlength="8"
														   jsf:value="#{contratoBean.filter.numeroGravame}"
														   class="form-control naoCola"
														   onkeypress="return /^-?[0-9]*$/.test(this.value+event.key)">
														<f:converter converterId="trimConverter"/>
													</input>
												</div>
											</div>
										</div>
										<div class="row">	
											<div class="col-lg-3">
												<div class="form-group form-group-default">
													<label>Chassi</label>
													<input jsf:id="chassi" type="text" class="form-control chassi"
														   jsf:value="#{contratoBean.filter.chassi}" >
														<f:converter converterId="trimConverter"/>
													</input>
												</div>
											</div>
											<div class="col-lg-3">
												<div class="form-group form-group-default form-group-default-select2">
													<label>Situação do Contrato</label>
													<select jsf:id="situacao" jsf:value="#{contratoBean.filter.situacao}"
														class="form-control full-width select2" size="1">
														<f:selectItem itemLabel="Selecione" />
														<f:selectItems value="#{helperBean.situacoes}" var="i" itemLabel="#{i.descricao}" />
													</select>
												</div>
											</div>
											<div class="col-lg-3">
												<div class="form-group form-group-default form-group-default-select2">
													<label>Situação Financeira</label>
													<select jsf:id="situacaoFinanceira" jsf:value="#{contratoBean.filter.situacaoFinanceira}"
														class="form-control full-width select2" size="1">
														<f:selectItem itemLabel="Selecione" />
														<f:selectItems value="#{helperBean.situacoesFinanceiras}" var="i" itemLabel="#{i.descricao}" />
													</select>
												</div>
											</div>
											<div class="col-lg-3">
												<div class="form-group form-group-default form-group-default-select2">
													<label>Possui Anexo</label>
													<select jsf:id="possuiAnexo" jsf:value="#{contratoBean.filter.possuiAnexo}"
														class="form-control full-width select2" size="1">
														<f:selectItem itemLabel="Selecione" />
														<f:selectItems value="#{helperBean.simNao}" var="i" itemLabel="#{i.descricao}" />
													</select>
												</div>	
											</div>
										</div>
										<div class="row">	
											<div class="col-lg-3">
												<div class="form-group form-group-default form-group-default-select2">
													<label>UF</label>
													<select jsf:id="ufRegistros" jsf:value="#{contratoBean.filter.ufRegistro}"
														class="form-control full-width uf select2" size="1" jsf:label="UF">
														<f:selectItem itemLabel="Selecione" />
														<f:selectItems value="#{helperBean.ufs}" var="i" />
													</select>
												</div>
											</div>
											<div class="col-lg-3">
												<div class="form-group form-group-default form-group-default-select2">
													<label>Tipo do Contrato</label>
													<select jsf:id="tipoContrato" jsf:value="#{contratoBean.filter.tipoContrato}" 
														class="form-control full-width select2" size="1" jsf:label="Tipo do Contrato">
														<f:selectItem itemLabel="Selecione" />
														<f:selectItems value="#{helperBean.tiposContrato}" var="i" itemLabel="#{i.descricao}" />
													</select>
												</div>
											</div>
											<div class="col-lg-3">
												<div class="form-group form-group-default form-group-default-select2">
													<label>Auditoria Aprovada</label>
													<select jsf:id="situacaoAuditoria" jsf:value="#{contratoBean.filter.aprovadoAuditoria}"
														class="form-control full-width select2" size="1">
														<f:selectItem itemLabel="Selecione" />
														<f:selectItems value="#{helperBean.simNao}" var="i" itemLabel="#{i.descricao}" />
													</select>
												</div>
											</div>
											<div class="col-lg-3">
												<div class="form-group form-group-default form-group-default-select2">
													<label>Processo B3</label>
													<select jsf:id="integra" jsf:value="#{contratoBean.filter.integra}" class="form-control full-width select2" size="1">
														<f:selectItem itemLabel="Selecione" />
														<f:selectItems value="#{helperBean.simNao}" var="i" itemLabel="#{i.descricao}" />
													</select>
												</div>
											</div>
										</div>
										<div class="row">
											<div class="#{helperSessionBean.usuario.perfil eq 'ADMINISTRADOR' ? 'col-lg-6': 'col-lg-9'}">
												<div class="form-group form-group-default form-group-default-select2">
													<label>Financeira</label>
													<select jsf:id="financeira" jsf:value="#{contratoBean.filter.financeira}" class="form-control full-width select2" size="1">
														<f:selectItem itemLabel="Selecione" />
														<f:selectItems value="#{contratoBean.financeiras}" var="f" itemValue="#{f}" itemLabel="#{f.documento} - #{f.nome}" />
														<f:ajax render="agenteDiv" listener="#{contratoBean.recuperaAgentesFinanceira(contratoBean.filter.financeira)}" event="change" />
														<f:converter converterId="financeiraConverter"/>
													</select>
												</div>
											</div>
											<div class="col-lg-3" jsf:rendered="#{helperSessionBean.usuario.perfil eq 'ADMINISTRADOR'}">
												<div jsf:id="agenteDiv"  class="form-group form-group-default form-group-default-select2">
													<label>Agente</label>
													<select jsf:id="agente" jsf:value="#{contratoBean.filter.agente}" class="form-control full-width select2" size="1">
														<f:selectItem itemLabel="Selecione" />
														<f:selectItems value="#{contratoBean.agentes}" var="f" itemValue="#{f}" itemLabel="#{f.nome}" />
														<f:converter converterId="agenteConverter"/>
													</select>
												</div>
											</div>
											<div class="col-lg-3">
												<div class="form-group form-group-default">
													<label>Data do Cadastro</label>
													<p:calendar id="dataCadastro" styleClass="form-control" locale="pt_BR" navigator="true" yearRange="c-10:c+10"
														value="#{contratoBean.filter.dataCadastro}" pattern="dd/MM/yyyy" mask="true" />
												</div>
											</div>
										</div>
										<div class="row text-center">
											<div class="col-lg-12">
												<hr class="buttons" />
												<a href="#{request.contextPath}/contrato/list.xhtml" class="btn btn-default">Limpar</a>
												<sec:authorize ifAnyGranted="REGISTRAR_CONTRATO">
													<a href="#{request.contextPath}/contrato/form-add.xhtml" class="btn btn-primary btn-cons">Novo</a>
												</sec:authorize>
												<button type="submit" class="btn btn-primary btn-cons" jsf:action="#{contratoBean.search}">Pesquisar</button>
											</div>
										</div>
										<input type="hidden" name="${_csrf.parameterName}" value="${_csrf.token}"/>
									</form>
								</div>
							</div>
						</div>
						<div class="row">
							<div class="table-responsive">
								<form jsf:id="formDataTable" jsf:prependId="false">
									<div style="min-height: 30px;">
										<div style="float: right; padding: 0;">
											Resultados por página
											<select jsf:id="registros" jsf:value="#{contratoBean.size}" size="1">
												<f:selectItem itemLabel="10" itemValue="10" />
												<f:selectItem itemLabel="25" itemValue="25" />
												<f:selectItem itemLabel="50" itemValue="50" />
												<f:selectItem itemLabel="100" itemValue="100" />
												<f:ajax execute="@this" render="formDataTable" listener="#{contratoBean.search}" onevent="function(data){$.masks();}" />
											</select>
										</div>
									</div>
									<p:dataTable id="dataTable" var="object" value="#{contratoBean.list}" paginator="true" rows="#{contratoBean.size}" paginatorPosition="bottom" emptyMessage="Nenhum registro encontrado"
										currentPageReportTemplate="({currentPage} de {totalPages})" paginatorTemplate="{FirstPageLink} {PreviousPageLink} {CurrentPageReport} {NextPageLink} {LastPageLink}" lazy="true" draggableColumns="true" resizableColumns="true"
										tableStyleClass="table table-hover m-0">
										<p:column headerText="Registro">
											<span class="fa fa-certificate" jsf:rendered="#{object.assinado == 'S'}" title="Assinado Digitalmente"/>
											<span class="fa fa-eye" jsf:rendered="#{object.aprovadoAuditoria == 'S'}" title="Aprovado na Auditoria"/>
											<span class="fa fa-eye-slash" jsf:rendered="#{object.aprovadoAuditoria == 'N'}" title="Reprovado na Auditoria"/>
											<i class="mdi mdi-car" style="color: #23b195;" title="Gravame enviado pelo Placecon" jsf:rendered="#{object.contratoRsng != null}"></i>
											<i class="mdi mdi-plus-circle" style="color: #23b195;" title="Contrato Integra+" jsf:rendered="#{object.idProcessoB3 != null}"></i>
											<i class="mdi mdi-plus-circle" style="color: #458BC4;" title="Contrato Send" jsf:rendered="#{object.idProcessoSENDB3 != null}"></i>
											<i class="mdi mdi-plus-circle" style="color: #{object.integradora.cor}" title="#{object.integradora.descricao}" jsf:rendered="#{object.integradora != null}"></i>
											<i class="mdi mdi-alert" style="color: #d57171;" title="Contrato Pendente de Envio" jsf:rendered="#{object.situacao == 'ERRO'}"></i>
											<i class="mdi mdi-alert" style="color: #e2ab3b;" title=" Aguardando Confirmação do DETRAN" jsf:rendered="#{object.situacao == 'PENDENTE'}"></i>
											<a class="btn btn-link"
											   href="#{request.contextPath}/contrato/form-detail.xhtml?id=#{object.id}">#{object.numeroRegistroEletronico}</a>
										</p:column>
										<p:column headerText="Contrato">
											#{object.numeroContrato}
										</p:column>
										<p:column headerText="UF">
											#{object.ufRegistro}
										</p:column>
										<p:column headerText="Cadastro">
											<h:outputText value="#{object.dataCadastro}">
												<f:convertDateTime type="date" />
											</h:outputText>
										</p:column>
										<p:column headerText="Devedor">
											<h:outputText value="#{object.cpfCnpjDevedorFinanciado}" title="#{object.nomeDevedorFinanciado}">
												<f:converter converterId="cpfCnpjConverter" />
											</h:outputText>
										</p:column>
										<p:column headerText="Gravame">
											<ui:repeat var="v" value="#{object.veiculosLimitados}">
												#{v.numeroGravame} <br />
											</ui:repeat>
										</p:column>
										<p:column headerText="Chassi">
											<ui:repeat var="v" value="#{object.veiculosLimitados}">
												<i class="mdi mdi-information" style="color: #3db9dc;" title="#{v.mensagemRetornoDetalhada}" jsf:rendered="#{object.dataConclusaoDETRAN == null}"></i>
												<span>#{v.numeroChassi} </span> <br />
											</ui:repeat>
										</p:column>
										<p:column headerText="Ações" styleClass="text-center">
											<sec:authorize ifAnyGranted="CONSULTAR_CONTRATO">
												<a class="btn btn-link" title="Visualizar" href="#{request.contextPath}/contrato/form-detail.xhtml?id=#{object.id}">
													<span class="fa fa-search" />
												</a>
												<a class="btn btn-link" title="Histórico" href="#{request.contextPath}/contrato/timeline.xhtml?id=#{object.id}">
													<span class="fa  fa-clock-o" />
												</a>
											</sec:authorize>
											<sec:authorize ifAnyGranted="REGISTRAR_CONTRATO">
												<span jsf:rendered="#{object.situacao == 'ERRO' and !object.alteracao}">
													<a class="btn btn-link" title="Registrar"
														href="#{request.contextPath}/contrato/form-update.xhtml?id=#{object.id}">
														<span class="fa fa-edit" />
													</a>
													<h:commandLink styleClass="btn btn-link" title="Excluir" 
														onclick="return confirm('Confirmar a exclusão do contrato?')"
														action="#{contratoBean.delete(object.id)}"
														renderered="#{object.getVeiculosComErro() == object.getVeiculos().size()}">
														<span class="fa fa-remove" />
													</h:commandLink>
												</span>
												<span jsf:rendered="#{object.situacao == 'ERRO' and object.alteracao}">
													<a class="btn btn-link" title="Alterar"
														href="#{request.contextPath}/alteracao/form-update.xhtml?id=#{object.id}">
														<span class="fa fa-edit" />
													</a>
												</span>
											</sec:authorize>
											<sec:authorize ifAnyGranted="EXCLUIR_CONTRATO_ASSINATURA">
												<span jsf:rendered="#{object.situacao == 'ERRO' and !object.alteracao and certificadoSessionBean.loaded}">
													<h:commandLink styleClass="btn btn-link" title="Excluir o Contrato com Assinatura" rendered="#{object.situacao == 'ERRO' and !object.alteracao and certificadoSessionBean.loaded}"
														onclick="return confirm('Confirma a exclusão do Contrato com assinatura?')" action="#{contratoBean.signAndDelete(object.id)}">
														<span class="mdi mdi-key-remove" />
													</h:commandLink>
												</span>
											</sec:authorize>													
											<span jsf:rendered="#{object.situacao != 'ERRO' and object.situacao != 'PENDENTE'}">
												<a href="#{request.contextPath}/contrato/print.xhtml?id=#{object.id}" 
													class="btn btn-link" target="_blank" title="Imprimir">
													<span class="fa fa-print" />
												</a>
											</span>
										</p:column>
									</p:dataTable>
									<input type="hidden" name="${_csrf.parameterName}" value="${_csrf.token}" />
								</form>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</ui:define>
</ui:composition>
