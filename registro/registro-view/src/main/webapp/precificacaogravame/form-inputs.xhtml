<ui:composition xmlns="http://www.w3.org/1999/xhtml"
	xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
	xmlns:c="http://java.sun.com/jsp/jstl/core"
	xmlns:h="http://java.sun.com/jsf/html"
	xmlns:f="http://java.sun.com/jsf/core"
	xmlns:p="http://primefaces.org/ui"
	xmlns:sec="http://www.springframework.org/security/tags"
	xmlns:jsf="http://xmlns.jcp.org/jsf">
	<div class="row">
		<div class="col-lg-4">
			<div class="form-group form-group form-group-default form-group-default-select2 required">
				<label>UF</label>
				<select jsf:id="ufs" jsf:value="#{gravameBean.entity.uf}" jsf:label="UF" jsf:required="true" required="required"
					class="form-control full-width select2" size="1" disabled="#{disabled}">
					<f:selectItem itemLabel="Selecione" />
					<f:selectItems value="#{helperBean.ufs}" var="e" itemValue="#{e}" itemLabel="#{e}" />
                 </select>
			</div>
		</div>
		<div class="col-lg-4">
			<div class="form-group form-group form-group-default required">
				<label>Início</label>
				<p:calendar id="dataInicio" styleClass="form-control" locale="pt_BR" navigator="true" yearRange="c-10:c+10"
					value="#{gravameBean.entity.dataInicio}" pattern="dd/MM/yyyy" mask="true" required="true" label="Início" disabled="#{disabled eq 'disabled'}"/>
			</div>
		</div>
		<div class="col-lg-4">
			<div class="form-group form-group form-group-default required">
				<label>Fim</label>
				<p:calendar id="dataFim" styleClass="form-control" locale="pt_BR" navigator="true" yearRange="c-10:c+10"
					value="#{gravameBean.entity.dataFim}" pattern="dd/MM/yyyy" mask="true" required="true" label="Fim" disabled="#{disabled eq 'disabled'}"/>
			</div>
		</div>
		<div class="col-lg-6">
			<div class="form-group form-group-default form-group-default-select2">
				<label>Financeira</label>
				<select jsf:id="financeira" jsf:value="#{gravameBean.entity.financeira}"
						class="form-control full-width select2" size="1" disabled="#{disabled}">
					<f:selectItem itemLabel="Selecione" />
					<f:selectItems value="#{helperSessionBean.financeiras}" var="i" itemValue="#{i}" itemLabel="#{i.documento} - #{i.nome}" />
					<f:converter converterId="financeiraConverter"/>
				</select>
			</div>
		</div>
		<div class="col-lg-6">
			<div class="form-group form-group form-group-default required">
				<label>Valor RSNG</label>
				<input jsf:id="valorRegistro" type="text" jsf:value="#{gravameBean.entity.valorPlace}"
					   required="true" jsf:required="true" jsf:label="Valor Total do Registro" class="form-control money" disabled="#{disabled}">
					<f:ajax event="change" render="valorTotal" />
					<f:convertNumber pattern="#,##0.00" locale="pt_BR" />
				</input>
			</div>
		</div>
		<div class="col-lg-4">
			<div class="form-group form-group form-group-default required">
				<label>Valor Cetip</label>
				<input jsf:id="valorCEtip" type="text" jsf:value="#{gravameBean.entity.valorCetip}"
					   jsf:update="valorTotal"
					   required="true" jsf:required="true" jsf:label="Valor Total do Registro" class="form-control money" disabled="#{disabled}">
					<f:ajax event="change" render="valorTotal" />
					<f:convertNumber pattern="#,##0.00" locale="pt_BR" />
				</input>
			</div>
		</div>
		<div class="col-lg-4">
			<div class="form-group form-group form-group-default required">
				<label>Valor Fenaseg</label>
				<input jsf:id="valorFEnaseg" type="text" jsf:value="#{gravameBean.entity.valorFenaseg}"
					   jsf:update="valorTotal"
					   required="true" jsf:required="true" jsf:label="Valor Total do Registro" class="form-control money" disabled="#{disabled}">
					<f:ajax event="change" render="valorTotal" />
					<f:convertNumber pattern="#,##0.00" locale="pt_BR" />
				</input>
			</div>
		</div>
		<div class="col-lg-4">
			<div class="form-group form-group form-group-default required">
				<label>Valor Total</label>
				<input jsf:id="valorTotal" type="text" jsf:value="#{gravameBean.entity.valorTotal}"
					   required="true" jsf:required="false" jsf:label="Valor Total do Registro" class="form-control money" disabled="disabled">
					<f:convertNumber pattern="#,##0.00" locale="pt_BR" />
				</input>
			</div>
		</div>
	</div>
</ui:composition>
