<ui:composition xmlns="http://www.w3.org/1999/xhtml"
	xmlns:h="http://xmlns.jcp.org/jsf/html"
	xmlns:f="http://xmlns.jcp.org/jsf/core"
	xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
	xmlns:p="http://primefaces.org/ui"
	xmlns:jsf="http://xmlns.jcp.org/jsf"
	xmlns:sec="http://www.springframework.org/security/tags"
	template="/templates/blank.xhtml">

	<ui:define name="content">
        <!-- Start content -->
        <div class="content">
            <div class="container-fluid">

                <div class="row">
                    <div class="col-sm-12">
                        <h4 class="header-title">Gravame</h4>

                        <div class="row">
                            <div class="col-lg-12">
								<div class="m-b-20">
	                                 <h6 class="font-14 mt-4">Pesquisar</h6>
									<form jsf:id="form" jsf:prependId="false">
										<h:messages id="messages" warnClass="alert alert-warning alert-dismissable"
											infoClass="alert alert-success alert-dismissable"
											errorClass="alert alert-danger alert-dismissable" />
										<div class="row">
											<div class="col-lg-4">
												<div class="form-group form-group-default form-group-default-select2">
													<label>UF</label>
													<select jsf:id="uf" jsf:value="#{gravameBean.filter.uf}"
														class="form-control full-width select2" size="1" disabled="#{disabled}">
														<f:selectItem itemLabel="Selecione" />
														<f:selectItems value="#{helperBean.ufs}" var="i" itemLabel="#{i}" />
													</select>
												</div>
											</div>
											<div class="col-lg-4">
												<div class="form-group form-group-default form-group-default-select2">
													<label>Financeira</label>
													<select jsf:id="financeira" jsf:value="#{gravameBean.filter.financeira}"
															class="form-control full-width select2" size="1" disabled="#{disabled}">
														<f:selectItem itemLabel="Selecione" />
														<f:selectItems value="#{helperSessionBean.financeiras}" var="i" itemValue="#{i}" itemLabel="#{i.documento} - #{i.nome}" />
														<f:converter converterId="financeiraConverter"/>
													</select>
												</div>
											</div>
											<div class="col-lg-4">
												<div class="form-group form-group-default">
													<label>Início</label>
													<p:calendar id="dataInicio" styleClass="form-control" locale="pt_BR" navigator="true" yearRange="c-10:c+10"
														value="#{gravameBean.filter.dataInicio}" pattern="dd/MM/yyyy" mask="true" label="Início" />
												</div>
											</div>
											<div class="col-lg-4">
												<div class="form-group form-group-default">
													<label>Fim</label>
													<p:calendar id="dataFim" styleClass="form-control" locale="pt_BR" navigator="true" yearRange="c-10:c+10"
														value="#{gravameBean.filter.dataFim}" pattern="dd/MM/yyyy" mask="true" label="Fim" />
												</div>
											</div>
										</div>
										<div class="row text-center">
											<div class="col-lg-12">
												<hr class="buttons" />
												<a href="#{request.contextPath}/precificacaogravame/list.xhtml" class="btn btn-default">Limpar</a>
												<sec:authorize ifAnyGranted="CADASTRAR_CREDENCIAMENTO">
													<a href="#{request.contextPath}/precificacaogravame/form-add.xhtml" class="btn btn-primary btn-cons">Novo</a>
												</sec:authorize>
												<button type="submit" class="btn btn-primary btn-cons" jsf:action="#{gravameBean.search}">Pesquisar</button>
											</div>
										</div>
										<input type="hidden" name="${_csrf.parameterName}" value="${_csrf.token}"/>
									</form>
								</div>
							</div>
						</div>
						<div class="row">
							<div class="table-responsive">
								<form jsf:id="formDataTable" jsf:prependId="false">
									<div style="min-height: 30px;">
										<div style="float: right; padding: 0;">
											Resultados por página
											<select jsf:id="registros" jsf:value="#{gravameBean.size}" size="1">
												<f:selectItem itemLabel="10" itemValue="10" />
												<f:selectItem itemLabel="25" itemValue="25" />
												<f:selectItem itemLabel="50" itemValue="50" />
												<f:selectItem itemLabel="100" itemValue="100" />
												<f:ajax execute="@this" render="formDataTable" listener="#{gravameBean.search}" onevent="function(data){$.masks();}" />
											</select>
										</div>
									</div>
									<p:dataTable id="dataTable" var="object" value="#{gravameBean.list}" paginator="true" rows="#{gravameBean.size}" paginatorPosition="bottom" emptyMessage="Nenhum registro encontrado"
										currentPageReportTemplate="({currentPage} de {totalPages})" paginatorTemplate="{FirstPageLink} {PreviousPageLink} {CurrentPageReport} {NextPageLink} {LastPageLink}" lazy="true" draggableColumns="true" resizableColumns="true"
										tableStyleClass="table table-hover m-0">
										<p:column headerText="UF">
											#{object.uf}
										</p:column>
										<p:column headerText="Início">
											<a class="btn btn-link"
											   href="#{request.contextPath}/precificacaogravame/form-detail.xhtml?id=#{object.id}"><h:outputText value="#{object.dataInicio}">
												<f:convertDateTime type="date"/>
											</h:outputText></a>
										</p:column>
										<p:column headerText="Fim">
											<h:outputText value="#{object.dataFim}">
												<f:convertDateTime type="date"/>
											</h:outputText>
										</p:column>
										<p:column headerText="Total">
											<h:outputText value="#{object.valorTotal}">
												<f:convertNumber pattern="#,##0.00" locale="pt_BR" />
											</h:outputText>
										</p:column>
										<p:column headerText="Ações" styleClass="text-center">
											<a class="btn btn-link" title="Visualizar" href="#{request.contextPath}/precificacaogravame/form-detail.xhtml?id=#{object.id}">
												<span class="fa fa-search" />
											</a>
											<sec:authorize ifAnyGranted="CADASTRAR_CREDENCIAMENTO">
												<a class="btn btn-link" title="Editar" href="#{request.contextPath}/precificacaogravame/form-update.xhtml?id=#{object.id}">
													<span class="fa fa-edit" />
												</a>
											</sec:authorize>
											<sec:authorize ifAnyGranted="EXCLUIR_CREDENCIAMENTO">
												<h:commandLink styleClass="btn btn-link" title="Excluir" onclick="return confirm('Confirma a exclusão?')" action="#{gravameBean.delete(object.id)}">
													<span class="fa fa-remove" />
												</h:commandLink>
											</sec:authorize>
										</p:column>
									</p:dataTable>
									<input type="hidden" name="${_csrf.parameterName}" value="${_csrf.token}" />
								</form>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</ui:define>
</ui:composition>