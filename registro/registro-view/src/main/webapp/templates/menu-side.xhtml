<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
                xmlns:h="http://xmlns.jcp.org/jsf/html"
                xmlns:p="http://primefaces.org/ui"
                xmlns:jsf="http://xmlns.jcp.org/jsf"
                xmlns:sec="http://www.springframework.org/security/tags">

    <!-- ========== Left Sidebar Start ========== -->
    <div class="left side-menu">
        <div class="user-details">
            <div class="pull-left">
                <img src="#{request.contextPath}/templates/assets/images/users/avatar-1.png"
                     alt="Foto do usuário default" class="rounded-circle"
                     jsf:rendered="#{helperSessionBean.logo == null}"></img>
                <p:graphicImage value="#{helperSessionBean.logo}" stream="true" cache="false"
                                alt="Foto do usuário customizada" styleClass="rounded-circle"
                                rendered="#{helperSessionBean.logo != null}"/>
            </div>
            <div class="user-info">
                <a href="#">#{helperSessionBean.usuario.nome.split(' ')[0]}</a>
                <p class="text-muted m-0">#{helperSessionBean.usuario.perfil.descricao}</p>
                <p class="text-muted m-0">#{helperSessionBean.usuario.agente.nome}</p>
            </div>
        </div>

        <!--- Sidemenu -->
        <div id="sidebar-menu">
            <!-- Left Menu Start -->
            <ul class="metismenu" id="side-menu">
                <li class="menu-title">Registros</li>
                <li>
                    <a href="javascript:void(0)">
                        <i class="ti-home"></i>
                        <span> Início </span>
                        <span class="menu-arrow"></span>
                    </a>
                    <ul class="nav-second-level">
                        <li>
                            <a href="#{request.contextPath}/">Principal</a>
                        </li>
                        <sec:authorize ifAnyGranted="CONSULTAR_RELATORIO_ANUAL, CONSULTAR_DASHBOARDS_ADMIN">
                            <li>
                                <a href="#{helperSessionBean.getDashboardRelatorio()}">Relatório de Contratos</a>
                            </li>
                        </sec:authorize>
                    </ul>
                </li>


                <sec:authorize ifAnyGranted="CONSULTAR_CONTRATO, REGISTRAR_CONTRATO, ASSINAR_CONTRATO, ADITIVAR_CONTRATO, BAIXAR_CONTRATO,
						ALTERAR_CONTRATO, ANULAR_CONTRATO,IMPRIMIR_CERTIDAO">
                    <li>
                        <a href="javascript: void(0);">
                            <i class="fa fa-file-text"></i>
                            <span>Contrato</span>
                            <span class="menu-arrow"></span>
                        </a>
                        <ul class="nav-second-level">
                            <sec:authorize ifAllGranted="CONSULTAR_CONTRATO">

                                <li>
                                    <a href="#{request.contextPath}/contrato/list.xhtml">Pesquisar</a>
                                </li>
                            </sec:authorize>

                            <sec:authorize ifAllGranted="REGISTRAR_CONTRATO">
                                <li>
                                    <a href="#{request.contextPath}/contrato/form-add.xhtml">Registrar</a>
                                </li>
                            </sec:authorize>
                            <sec:authorize ifAllGranted="REGISTRAR_CONTRATO_SIMPLIFICADO">
                                <li>
                                    <a href="#{request.contextPath}/contrato/form-add-smart.xhtml">
                                        Registro Rápido
                                    </a>
                                </li>
                            </sec:authorize>
                            <sec:authorize ifAllGranted="REGISTRAR_CONTRATO">
                                <li>
                                    <a href="#{request.contextPath}/envio/list.xhtml">
                                        Pendente
                                        <span class="badge badge-custom pull-right">#{dashboardBean.quantidadeEnviar}</span>
                                    </a>
                                </li>
                            </sec:authorize>
                            <sec:authorize ifAllGranted="ADITIVAR_CONTRATO">
                                <li>
                                    <a href="#{request.contextPath}/aditivo/list.xhtml">Aditivar</a>
                                </li>
                            </sec:authorize>
                            <sec:authorize ifAnyGranted="ANULAR_CONTRATO">
                                <li>
                                    <a href="#{request.contextPath}/anulacao/list.xhtml">Anular</a>
                                </li>
                            </sec:authorize>
                            <sec:authorize ifAnyGranted="BAIXAR_CONTRATO">
                                <li>
                                    <a href="#{request.contextPath}/baixa/list.xhtml">Baixar</a>
                                </li>
                            </sec:authorize>
                            <sec:authorize ifAllGranted="ALTERAR_CONTRATO">
                                <li>
                                    <a href="#{request.contextPath}/alteracao/list.xhtml">Alterar</a>
                                </li>
                            </sec:authorize>
                            <sec:authorize ifAllGranted="ASSINAR_CONTRATO">
                                <li>
                                    <a href="#{request.contextPath}/assinatura/list.xhtml">
                                        Assinar
                                    </a>
                                </li>
                            </sec:authorize>
                            <sec:authorize ifAllGranted="REGISTRAR_CONTRATO">
                                <li>
                                    <a href="#{request.contextPath}/contrato/form-add-upload.xhtml">Registrar com OCR</a>
                                </li>
                            </sec:authorize>
                            <sec:authorize ifAnyGranted="IMPRIMIR_CERTIDAO">
                                <li><a href="#{request.contextPath}/certidao/list.xhtml">Imprimir Certidão</a></li>
                            </sec:authorize>
                        </ul>
                    </li>
                </sec:authorize>

                <sec:authorize ifAnyGranted="CONSULTAR_OBSERVATORIO_FINANCEIRA, CONSULTAR_DASHBOARDS_ADMIN">
                    <li>
                        <a href="javascript:void(0)">
                            <i class="mdi mdi-web"></i>
                            <span> Observatório </span>
                            <span class="menu-arrow"></span>
                        </a>
                        <ul class="nav-second-level">
                            <li>
                                <a href="#{helperSessionBean.getDashboardObservatorio()}">Observatório de Credenciamento</a>
                            </li>

                            <sec:authorize ifAnyGranted="CONSULTAR_OBSERVATORIO_FINANCEIRA, CONSULTAR_DASHBOARDS_ADMIN">
                                <li>
                                    <a href="#{helperSessionBean.getAcompanhamento()}">Acompanhamento de Credenciamento</a>
                                </li>
                            </sec:authorize>
                        </ul>
                    </li>
                </sec:authorize>

                <sec:authorize ifAnyGranted="CONSULTAR_CONTRATO_RSNG, CONSULTAR_REMESSA_RSNG">
                    <li>
                        <a href="javascript: void(0);">
                            <!--                            <i class="fa fa-share"/>-->
                            <img src="#{request.contextPath}/templates/assets/images/new.png" alt="Ícone RSNG"
                                 style="width:20px; height:20px; margin-right: 12px"/>
                            <span>Revolução SNG</span>
                            <span class="menu-arrow"></span>
                        </a>
                        <ul class="nav-second-level">
                            <sec:authorize ifAnyGranted="CONSULTAR_REMESSA_RSNG">
                                <li>
                                    <a href="#{request.contextPath}/remessarsng/list.xhtml">Registrar Contrato</a>
                                </li>
                            </sec:authorize>

                            <sec:authorize ifAllGranted="CONSULTAR_CONTRATO_RSNG">
                                <li>
                                    <a href="#{request.contextPath}/contratorsng/form-add.xhtml">Gerar Apontamento</a>
                                </li>
                            </sec:authorize>

                            <sec:authorize ifAllGranted="CONSULTAR_CONTRATO_RSNG">
                                <li>
                                    <a href="#{request.contextPath}/contratorsng/list.xhtml">Contratos RSNG</a>
                                </li>
                            </sec:authorize>

                            <sec:authorize ifAllGranted="CONSULTAR_CONTRATO_RSNG">
                                <li>
                                    <a href="#{request.contextPath}/baixarsng/list.xhtml">Baixar Apontamento</a>
                                </li>
                            </sec:authorize>
                            <sec:authorize ifAllGranted="CONSULTAR_REMESSA_RSNG">
                                <li>
                                    <a href="#{request.contextPath}/remessasng/list.xhtml">Remessa</a>
                                </li>
                            </sec:authorize>
                            <sec:authorize ifAllGranted="CONSULTAR_REMESSA_RSNG">
                                <li>
                                    <a href="#{request.contextPath}/templateremessarsng/list.xhtml">Template de Remessa</a>
                                </li>
                            </sec:authorize>
                        </ul>
                    </li>

                </sec:authorize>

                <sec:authorize
                        ifAnyGranted="CONSULTAR_REMESSA, REGISTRAR_REMESSA, CADASTRAR_TEMPLATEREMESSA,CONSULTAR_TEMPLATEREMESSA,CONSULTAR_REMESSA_CHASSI,REGISTRAR_REMESSA_CHASSI,REGISTRAR_REMESSA_CHASSI_USUARIO">
                    <li>
                        <a href="javascript: void(0);">
                            <i class="fa fa-stack-overflow"></i>
                            <span>Remessa</span>
                            <span class="menu-arrow"></span>
                        </a>
                        <ul class="nav-second-level">
                            <sec:authorize ifAllGranted="CONSULTAR_REMESSA">
                                <li>
                                    <a href="#{request.contextPath}/remessa/list.xhtml">Pesquisar</a>
                                </li>
                            </sec:authorize>
                            <sec:authorize ifAllGranted="REGISTRAR_REMESSA">
                                <li>
                                    <a href="#{request.contextPath}/remessa/form-add.xhtml">Nova</a>
                                </li>
                            </sec:authorize>
                            <sec:authorize ifAllGranted="CONSULTAR_REMESSA_CHASSI">
                                <li>
                                    <a href="#{request.contextPath}/remessachassi/list.xhtml">Remessa de Chassi</a>
                                </li>
                            </sec:authorize>
                            <sec:authorize ifAllGranted="CONSULTAR_REMESSA_DOCUMENTO">
                                <li>
                                    <a href="#{request.contextPath}/remessadocumento/list.xhtml">Remessa de Documento</a>
                                </li>
                            </sec:authorize>
                            <sec:authorize ifAllGranted="CONSULTAR_TEMPLATEREMESSA">
                                <li>
                                    <a href="#{request.contextPath}/configuracaoremessa/list.xhtml">Templates</a>
                                </li>
                            </sec:authorize>
                            <sec:authorize ifAllGranted="CADASTRAR_TEMPLATEREMESSA">
                                <li>
                                    <a href="#{request.contextPath}/configuracaoremessa/form-add.xhtml">Novo Template</a>
                                </li>
                            </sec:authorize>
                        </ul>
                    </li>
                </sec:authorize>
                <sec:authorize
                        ifAnyGranted="CADASTRAR_DOCUMENTO,CONSULTAR_DOCUMENTO,EXCLUIR_DOCUMENTO,AUDITAR_DOCUMENTO,ATENDER_SOLICITACAO_DOCUMENTO,SOLICITAR_DOCUMENTO">
                    <li>
                        <a href="javascript: void(0);">
                            <i class="fa fa-file-pdf-o"></i>
                            <span>Documentos</span>
                            <span class="menu-arrow"></span>
                        </a>
                        <ul class="nav-second-level">
                            <sec:authorize ifAnyGranted="CADASTRAR_DOCUMENTO,CONSULTAR_DOCUMENTO,EXCLUIR_DOCUMENTO">
                                <li>
                                    <a href="#{request.contextPath}/documento/list.xhtml">Anexar</a>
                                </li>
                                <li>
                                    <a href="#{request.contextPath}/documento/lote/list.xhtml">Anexar em Lote</a>
                                </li>
                            </sec:authorize>
                            <sec:authorize ifAnyGranted="AUDITAR_DOCUMENTO">
                                <li>
                                    <a href="#{request.contextPath}/auditoria/list.xhtml">
                                        Auditar
                                        <span class="badge badge-custom pull-right">#{dashboardBean.quantidadeAuditar}</span>
                                    </a>
                                </li>
                            </sec:authorize>
                            <sec:authorize ifAnyGranted="ATENDER_SOLICITACAO_DOCUMENTO,SOLICITAR_DOCUMENTO">
                                <li>
                                    <a href="#{request.contextPath}/solicitacaodocumento/list.xhtml">Solicitações de Documento</a>
                                </li>
                            </sec:authorize>

                        </ul>
                    </li>
                </sec:authorize>
                <sec:authorize
                        ifAnyGranted="CONSULTAR_FINANCEIRA, CADASTRAR_FINANCEIRA, CONSULTAR_GRUPO_FINANCEIRA, CADASTRAR_GRUPO_FINANCEIRA">
                    <li>
                        <a href="javascript: void(0);">
                            <i class="fa fa-bank"></i>
                            <span>Financeira</span>
                            <span class="menu-arrow"></span>
                        </a>
                        <ul class="nav-second-level">
                            <sec:authorize ifAllGranted="CONSULTAR_FINANCEIRA">
                                <li>
                                    <a href="#{request.contextPath}/financeira/list.xhtml">Pesquisar</a>
                                </li>
                            </sec:authorize>
                            <sec:authorize ifAllGranted="CADASTRAR_FINANCEIRA">
                                <li>
                                    <a href="#{request.contextPath}/financeira/form-add.xhtml">Novo</a>
                                </li>
                            </sec:authorize>
                            <sec:authorize ifAllGranted="CONSULTAR_GRUPO_FINANCEIRA">
                                <li>
                                    <a href="#{request.contextPath}/grupofinanceira/list.xhtml">Grupo</a>
                                </li>
                            </sec:authorize>
                            <sec:authorize ifAllGranted="EDITAR_CREDENCIAMENTO_FINANCEIRA">
                                <li>
                                    <a href="#{request.contextPath}/financeira/editar-credenciamento.xhtml">Editar Credenciamento</a>
                                </li>
                            </sec:authorize>
                        </ul>
                    </li>
                </sec:authorize>

                <sec:authorize ifAnyGranted="CONSULTAR_INTEGRADORA, CADASTRAR_INTEGRADORA">
                    <li>
                        <a href="javascript: void(0);">
                            <i class="mdi mdi-arrow-all"></i>
                            <span>Integradoras</span>
                            <span class="menu-arrow"></span>
                        </a>
                        <ul class="nav-second-level">
                            <sec:authorize ifAllGranted="CONSULTAR_INTEGRADORA">
                                <li>
                                    <a href="#{request.contextPath}/integradora/list.xhtml">Pesquisar</a>
                                </li>
                            </sec:authorize>
                            <sec:authorize ifAllGranted="CADASTRAR_INTEGRADORA">
                                <li>
                                    <a href="#{request.contextPath}/integradora/form-add.xhtml">Novo</a>
                                </li>
                            </sec:authorize>
                        </ul>
                    </li>
                </sec:authorize>

                <sec:authorize ifAnyGranted="CONSULTAR_CREDENCIAMENTO, CADASTRAR_CREDENCIAMENTO">
                    <li>
                        <a href="javascript: void(0);">
                            <i class="fa fa-flag"></i>
                            <span>Precificação</span>
                            <span class="menu-arrow"></span>
                        </a>
                        <ul class="nav-second-level">
                            <sec:authorize ifAllGranted="CONSULTAR_CREDENCIAMENTO">
                                <li>
                                    <a href="#{request.contextPath}/credenciamento/list.xhtml">Credenciamento</a>
                                </li>
                            </sec:authorize>
                            <sec:authorize ifAllGranted="CADASTRAR_CREDENCIAMENTO">
                                <li>
                                    <a href="#{request.contextPath}/precificacaogravame/list.xhtml">Bilhetagem</a>
                                </li>
                            </sec:authorize>
                        </ul>
                    </li>
                </sec:authorize>

                <sec:authorize ifAnyGranted="CONSULTAR_COBRANCA,CADASTRAR_COBRANCA,CONSULTAR_CUPOM_DESCONTO,CONSULTAR_DOCUMENTO_ARRECADACAO,
				                                        UPLOAD_CATEGORIA_VEICULO_CONTRATO">
                    <li>
                        <a href="javascript: void(0);">
                            <i class="fa fa-dollar"></i>
                            <span>Cobrança</span>
                            <span class="menu-arrow"></span>
                        </a>
                        <ul class="nav-second-level">
                            <sec:authorize ifAnyGranted="CONSULTAR_COBRANCA,CADASTRAR_COBRANCA">
                                <li>
                                    <a href="#{request.contextPath}/cobranca/list.xhtml">Pesquisar</a>
                                </li>
                            </sec:authorize>
                            <sec:authorize ifAllGranted="CADASTRAR_COBRANCA">
                                <li>
                                    <a href="#{request.contextPath}/cobranca/form-add.xhtml">Novo</a>
                                </li>
                                <li>
                                    <a href="#{request.contextPath}/cobranca/form-upload-nf.xhtml">Anexar NF</a>
                                </li>
                                <li>
                                    <a href="#{request.contextPath}/cobranca/form-remove.xhtml">Ajuste</a>
                                </li>
                                <li>
                                    <a href="#{request.contextPath}/cobranca/list-envio-email.xhtml">Reenvio E-mail</a>
                                </li>
                            </sec:authorize>
                            <sec:authorize ifAllGranted="CONSULTAR_CUPOM_DESCONTO_DESCONTINUADO">
                                <li>
                                    <a href="#{request.contextPath}/desconto/list.xhtml">Desconto</a>
                                </li>
                            </sec:authorize>
                            <sec:authorize ifAllGranted="CONSULTAR_CUPOM_DESCONTO">
                                <li>
                                    <a href="#{request.contextPath}/descontogeral/list.xhtml">Desconto Geral</a>
                                </li>
                            </sec:authorize>
                            <sec:authorize ifAllGranted="CONSULTAR_DOCUMENTO_ARRECADACAO">
                                <li>
                                    <a href="#{request.contextPath}/documentoarrecadacao/list.xhtml">Doc. Arrecadação</a>
                                </li>
                            </sec:authorize>
                            <sec:authorize ifAllGranted="CONTA_AZUL">
                                <li>
                                    <a href="#{facesContext.externalContext.requestContextPath}/contaazul/list-disponiveis.xhtml">Emitir NF Conta Azul</a>
                                </li>
                                <li>
                                    <a href="#{facesContext.externalContext.requestContextPath}/contaazul/list-servicos.xhtml">Serviços Conta Azul</a>
                                </li>
                            </sec:authorize>
                        </ul>
                    </li>
                </sec:authorize>
                <sec:authorize ifAnyGranted="CADASTRAR_NOTIFICACAO">
                    <li>
                        <a href="javascript: void(0);">
                            <i class="fa fa-comments-o" aria-hidden="true"></i>
                            <span>Notificação</span>
                            <span class="menu-arrow"></span>
                        </a>
                        <ul class="nav-second-level">
                            <li><a href="#{request.contextPath}/notificacao/list.xhtml">Pesquisar</a></li>
                            <li><a href="#{request.contextPath}/notificacao/form-add.xhtml">Novo</a></li>

                        </ul>
                    </li>
                </sec:authorize>
                <sec:authorize ifAnyGranted="CONSULTAR_GRAVAME,CONSULTAR_COBRANCA_PR,CONSULTAR_CONTRATO_DETRAN,CONSULTAR_NUMERO_DETRAN,
							ALTERAR_SENHA_DETRAN,CONSULTAR_BOLETO_DETRAN,CONSULTAR_ARQUIVO_COBRANCA_SC, FINANCEIRA_ACESSO_DETRAN, CONSULTAR_TAXA_UTILIZADA_DETRAN,
							CONSULTAR_STATUS_TAXA_DETRAN, CONSULTAR_ARQUIVO_COBRANCA_FTP">
                    <li>
                        <a href="javascript: void(0);">
                            <i class="fa fa-building" aria-hidden="true"></i>
                            <span>DETRAN</span>
                            <span class="menu-arrow"></span>
                        </a>
                        <ul class="nav-second-level">
                            <sec:authorize ifAllGranted="CONSULTAR_GRAVAME">
                                <li><a href="#{request.contextPath}/gravame/list.xhtml">Consultar Gravame</a></li>
                            </sec:authorize>
                            <sec:authorize ifAllGranted="CONSULTAR_CONTRATO_DETRAN">
                                <li>
                                    <a href="#{request.contextPath}/consultaapoio/list-contrato.xhtml">Consultar Contrato</a>
                                </li>
                            </sec:authorize>
                            <sec:authorize ifAllGranted="CONSULTAR_COBRANCA_PR">
                                <li>
                                    <a href="#{request.contextPath}/consultaapoio/list-cobranca-pr.xhtml">Consultar Cobrança PR</a>
                                </li>
                            </sec:authorize>
                            <sec:authorize ifAllGranted="CONSULTAR_NUMERO_DETRAN">
                                <li>
                                    <a href="#{request.contextPath}/consultaapoio/list-sequencial-sc.xhtml">Consultar Sequencial SC</a>
                                </li>
                            </sec:authorize>
                            <sec:authorize ifAllGranted="ALTERAR_SENHA_DETRAN">
                                <li>
                                    <a href="#{request.contextPath}/consultaapoio/alterar-senha-sc.xhtml">Alterar Senha SC</a>
                                </li>
                            </sec:authorize>
                            <sec:authorize ifAllGranted="ALTERAR_SENHA_DETRAN">
                                <li>
                                    <a href="#{request.contextPath}/consultaapoio/alterar-senha-ba.xhtml">Alterar Senha BA</a>
                                </li>
                            </sec:authorize>
                            <sec:authorize ifAllGranted="CONSULTAR_BOLETO_DETRAN">
                                <li>
                                    <a href="#{request.contextPath}/consultaapoio/consulta-boleto.xhtml">Consulta Boleto</a>
                                </li>
                            </sec:authorize>
                            <sec:authorize ifAllGranted="CONSULTAR_STATUS_TAXA_DETRAN">
                                <li>
                                    <a href="#{request.contextPath}/consultaapoio/list-status-taxa-rs.xhtml">Consulta Taxa Veículo RS</a>
                                </li>
                            </sec:authorize>
                            <sec:authorize ifAllGranted="CONSULTAR_TAXA_UTILIZADA_DETRAN">
                                <li>
                                    <a href="#{request.contextPath}/consultaapoio/list-taxa-lote-rs.xhtml">Consulta Tarifa Utilizada RS</a>
                                </li>
                            </sec:authorize>
                            <sec:authorize ifAllGranted="CONSULTAR_STATUS_TAXA_DETRAN">
                                <li>
                                    <a href="#{request.contextPath}/consultaapoio/statusdae-ce.xhtml">Consulta Estoque DAE - CE</a>
                                </li>
                            </sec:authorize>
                            <sec:authorize ifAnyGranted="CONSULTAR_ARQUIVO_COBRANCA_SC, CONSULTAR_ARQUIVO_COBRANCA_FTP">
                                <li>
                                    <a href="#{request.contextPath}/consultaapoio/list-arquivo-cobranca-ftp.xhtml">Arquivo Cobrança (FTP)</a>
                                </li>
                            </sec:authorize>
                        </ul>
                    </li>
                </sec:authorize>

                <sec:authorize ifAnyGranted="CADASTRAR_IMPOSTO">
                    <li>
                        <a href="javascript: void(0);">
                            <i class="fa fa-money" aria-hidden="true"></i>
                            <span>Imposto</span>
                            <span class="menu-arrow"></span>
                        </a>
                        <ul class="nav-second-level">
                            <li><a href="#{request.contextPath}/imposto/list.xhtml">Consultar</a></li>
                            <li><a href="#{request.contextPath}/imposto/form-add.xhtml">Novo</a></li>
                        </ul>
                    </li>
                </sec:authorize>

                <sec:authorize ifAnyGranted="CONSULTAR_SITUACAO,ATIVAR_CONTRATO,EXCLUIR_CONTRATO,ALTERAR_TIPO_CONTRATO,CONSULTA_DETALHADA,
							CONSULTA_DETALHADA,DECODIFICADOR,INCREMENTAR_TRANSACAO_CONTRATO">
                    <li>
                        <a href="javascript: void(0);">
                            <i class="fa fa-support" aria-hidden="true"></i>
                            <span>Apoio</span>
                            <span class="menu-arrow"></span>
                        </a>
                        <ul class="nav-second-level">
                            <sec:authorize ifAnyGranted="CONSULTAR_SITUACAO">
                                <li>
                                    <a href="#{request.contextPath}/consulta/consulta-situacao.xhtml">Consultar Situação</a>
                                </li>
                            </sec:authorize>
                            <li>
                                <a href="#{request.contextPath}/contrato/list-ativar.xhtml">Alterar Situação Contrato</a>
                            </li>
                            <sec:authorize ifAllGranted="INCREMENTAR_TRANSACAO_CONTRATO">
                                <li>
                                    <a href="#{request.contextPath}/contrato/list-incrementar-transacao-contrato.xhtml">Incrementar Transação Contrato</a>
                                </li>
                            </sec:authorize>
                            <sec:authorize ifAllGranted="EXCLUIR_CONTRATO">
                                <li><a href="#{request.contextPath}/contrato/list-excluir.xhtml">Excluir Contrato</a>
                                </li>
                            </sec:authorize>
                            <sec:authorize ifAllGranted="ALTERAR_TIPO_CONTRATO">
                                <li>
                                    <a href="#{request.contextPath}/contrato/list-principal.xhtml">Alterar Tipo Contrato</a>
                                </li>
                            </sec:authorize>
                            <sec:authorize ifAllGranted="CONSULTA_DETALHADA">
                                <li><a href="#{request.contextPath}/contrato/list-full.xhtml">Consulta Detalhada</a>
                                </li>
                            </sec:authorize>
                            <sec:authorize ifAllGranted="DECODIFICADOR">
                                <li><a href="#{request.contextPath}/decodificador/list.xhtml">Decodificador</a></li>
                            </sec:authorize>
                        </ul>
                    </li>
                </sec:authorize>
                <sec:authorize ifAnyGranted="CONSULTAR_MENSAGEM_RETORNO, CADASTRAR_MENSAGEM_RETORNO">
                    <li>
                        <a href="javascript: void(0);">
                            <i class="fa fa-comment" aria-hidden="true"></i>
                            <span>Mensagem de Retorno</span>
                            <span class="menu-arrow"></span>
                        </a>
                        <ul class="nav-second-level">
                            <sec:authorize ifAllGranted="CONSULTAR_MENSAGEM_RETORNO">
                                <li><a href="#{request.contextPath}/mensagemretorno/list.xhtml">Pesquisar</a></li>
                            </sec:authorize>
                            <sec:authorize ifAllGranted="CADASTRAR_MENSAGEM_RETORNO">
                                <li><a href="#{request.contextPath}/mensagemretorno/form-add.xhtml">Novo</a></li>
                            </sec:authorize>
                        </ul>
                    </li>
                </sec:authorize>

                <sec:authorize ifAnyGranted="CONSULTAR_FAQ, CADASTRAR_FAQ">
                    <li>
                        <a href="javascript: void(0);">
                            <i class="fa fa-question" aria-hidden="true"></i>
                            <span>FAQ</span>
                            <span class="menu-arrow"></span>
                        </a>
                        <ul class="nav-second-level">
                            <sec:authorize ifAllGranted="CONSULTAR_FAQ">
                                <li><a href="#{request.contextPath}/faq/list.xhtml">Pesquisar</a></li>
                            </sec:authorize>
                            <sec:authorize ifAllGranted="CADASTRAR_FAQ">
                                <li><a href="#{request.contextPath}/faq/form-add.xhtml">Novo</a></li>
                            </sec:authorize>
                        </ul>
                    </li>
                </sec:authorize>

                <sec:authorize ifAnyGranted="CONSULTAR_LIVRO, CADASTRAR_LIVRO">
                    <li>
                        <a href="javascript: void(0);">
                            <i class="fa fa-book" aria-hidden="true"></i>
                            <span>Livro</span>
                            <span class="menu-arrow"></span>
                        </a>
                        <ul class="nav-second-level">
                            <sec:authorize ifAllGranted="CONSULTAR_LIVRO">
                                <li><a href="#{request.contextPath}/livro/list.xhtml">Pesquisar</a></li>
                            </sec:authorize>
                            <sec:authorize ifAllGranted="CADASTRAR_LIVRO">
                                <li><a href="#{request.contextPath}/livro/form-add.xhtml">Novo</a></li>
                            </sec:authorize>
                        </ul>
                    </li>
                </sec:authorize>
                <sec:authorize ifAnyGranted="CONSULTAR_MUNICIPIO, CADASTRAR_MUNICIPIO">
                    <li>
                        <a href="javascript: void(0);">
                            <i class="fa fa-map-marker" aria-hidden="true"></i>
                            <span>Município</span>
                            <span class="menu-arrow"></span>
                        </a>
                        <ul class="nav-second-level">
                            <sec:authorize ifAllGranted="CONSULTAR_MUNICIPIO">
                                <li><a href="#{request.contextPath}/municipio/list.xhtml">Pesquisar</a></li>
                            </sec:authorize>
                            <sec:authorize ifAllGranted="CADASTRAR_MUNICIPIO">
                                <li><a href="#{request.contextPath}/municipio/form-add.xhtml">Novo</a></li>
                            </sec:authorize>
                        </ul>
                    </li>
                </sec:authorize>
                <sec:authorize ifAnyGranted="CONSULTAR_MARCA, CADASTRAR_MARCA">
                    <li>
                        <a href="javascript: void(0);">
                            <i class="fa fa-copyright" aria-hidden="true"></i>
                            <span>Marca</span>
                            <span class="menu-arrow"></span>
                        </a>
                        <ul class="nav-second-level">
                            <sec:authorize ifAllGranted="CONSULTAR_MARCA">
                                <li><a href="#{request.contextPath}/marca/list.xhtml">Pesquisar</a></li>
                            </sec:authorize>
                            <sec:authorize ifAllGranted="CADASTRAR_MARCA">
                                <li><a href="#{request.contextPath}/marca/form-add.xhtml">Novo</a></li>
                            </sec:authorize>
                        </ul>
                    </li>
                </sec:authorize>
                <sec:authorize ifAnyGranted="CONSULTAR_MODELO, CADASTRAR_MODELO">
                    <li>
                        <a href="javascript: void(0);">
                            <i class="fa fa-automobile" aria-hidden="true"></i>
                            <span>Modelo</span>
                            <span class="menu-arrow"></span>
                        </a>
                        <ul class="nav-second-level">
                            <sec:authorize ifAllGranted="CONSULTAR_MODELO">
                                <li><a href="#{request.contextPath}/modelo/list.xhtml">Pesquisar</a></li>
                            </sec:authorize>
                            <sec:authorize ifAllGranted="CADASTRAR_MODELO">
                                <li><a href="#{request.contextPath}/modelo/form-add.xhtml">Novo</a></li>
                            </sec:authorize>
                        </ul>
                    </li>
                </sec:authorize>
                <li>
                    <a href="#{request.contextPath}/logout/cas">
                        <i class="ti-power-off"></i><span> Sair </span>
                    </a>
                </li>

            </ul>

        </div>
        <!-- Sidebar -->
        <div class="clearfix"></div>

    </div>
    <!-- Left Sidebar End -->

</ui:composition>
