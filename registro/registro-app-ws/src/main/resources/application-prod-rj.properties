#datasource
spring.datasource.jndi-name=java:jboss/datasources/RegistroAppDS



#jpa
spring.jpa.hibernate.dialect=PostgreSQLDialect
spring.jpa.hibernate.ddl-auto=update
spring.jpa.properties.hibernate.id.new_generator_mappings=true
spring.jpa.properties.org.hibernate.envers.store_data_at_delete=true
spring.jpa.show-sql=false
spring.jpa.format-sql=false

# email
spring.mail.mailgun.domain=placecon.com.br
spring.mail.mailgun.ws-auth=Basic ************************************************************************
spring.mail.mailgun.webhook-auth=0e317c159eb46036606cadecc36dad72
spring.mail.contato=<EMAIL>
spring.mail.ambiente=prod
spring.mail.destinarios.baixa=<EMAIL>
spring.mail.destinarios.suporte=<EMAIL>

# service ARQDIGITAL

arqdigital.baseurl=https://gecov.com.br/ws
arqdigital.autenticacao=/login
arqdigital.consulta.tipo.documento=/tiposDocumento
arqdigital.registro=/servicos/protocolarRegistro
arqdigital.envio.arquivo=/binario/enviarArquivo/{idDocumento}
arqdigital.pesquisa.documento=/servicos/pesquisaDocumento
arqdigital.quitacao.contrato=/servicos/solicitarQuitacao
arqdigital.definicao.perfil=/login/definirPerfil

#service DETRAN CE

detran.ce.default.uri=https://gravamews.detran.ce.gov.br/veiculows
detran.ce.cliente.autenticado=/gravametw/api/autenticar/clienteAutenticado
detran.ce.registro.buscar.apontamento=/gravametw/contrato/api/apontamento
detran.ce.registro.buscar.apontamentopendente=/gravametw/contrato/api/apontamentoPendente
detran.ce.registro.buscar.apontamentospendentes=/gravametw/contrato/api/apontamentosPendentes
detran.ce.registro.buscar.contrato=/gravametw/contrato/api/contrato
detran.ce.registro.buscar.contratos=/gravametw/contrato/api/contratos
detran.ce.registro.buscar.contratosveiculo=/gravametw/contrato/api/contratosVeiculo
detran.ce.registro.buscar.municipiosce=/gravametw/contrato/api/municipios/ce
detran.ce.registro.cancelar=/gravametw/contrato/api/cancelar
detran.ce.registro.download.contratodigitalizado=/gravametw/contrato/api/downloadContratoDigitalizado
detran.ce.registro.enviar.contratodigitalizado=/gravametw/contrato/api/enviarContratoDigitalizado
detran.ce.registro.registrar=/gravametw/contrato/api/registrar
detran.ce.daes.buscar.dae=/daewstw/api/dae
detran.ce.daes.buscar.daes=/daewstw/api/daes
detran.ce.daes.gerar.dae=/daewstw/api/dae/gerar
detran.ce.daes.extrato.dae=/daewstw/api/dae/pdf
detran.ce.daes.buscar.servicos=/daewstw/api/servicos
detran.ce.daes.estoquedaes=/daewstw/api/dae/estoque

#service DETRAN SE
detran.se.default.uri=https://api.registrocontrato.detran.se.gov.br
detran.se.gera.token=/api/v1/login
detran.se.refresh.token=/api/v1/refresh
detran.se.profile=/api/v1/profile
detran.se.criar.aditivo=/api/v1/creditor/additive
detran.se.consultar.contrato=/api/v1/creditor/contract/{uuid}
detran.se.criar.registrar.contrato=/api/v1/creditor/contract/create/and/register
detran.se.criar.contrato=/api/v1/creditor/contract
detran.se.registrar.contrato=/api/v1/creditor/contract/{contract}/register
detran.se.atualizar.contrato=/api/v1/creditor/contract/{uuid}
detran.se.salvar.imagem=/api/v1/creditor/contract/save_image
detran.se.cancelar.contrato=/api/v1/creditor/contract/{contract}/canceled
detran.se.baixar.contrato=/api/v1/manager/contract/write-off
detran.se.cancelar.contrato.detran=/api/v1/manager/contract/cancel
detran.se.cnpjresponsible=06032507000103
detran.se.tokenvalidation=8a8836f6ffc560fd950f86f8e8d9052b33bc3337148c297ca8a3a265c7900e9c

# service DETRAN RN
detran.rn.default.uri=https://gateway.detran.rn.gov.br
detran.rn.usuario=06032507000103
detran.rn.senha=Pl@c&T1D&tr@n_Pl@c&c0N
detran.rn.url.autenticacao=/auth/login
detran.rn.url.comunicarcontrato=/v2/convenio/externo/registrocontrato/comunicarcontratofinanciamentoveiculo
detran.rn.url.consulta.debito=/v2/convenio/externo/registrocontrato/listardebitoagentefinanceiro
detran.rn.url.consulta.debito.detalhado=/v2/convenio/externo/registrocontrato/listardebitodetalhado
detran.rn.url.regerar.debito=/v2/convenio/externo/registrocontrato/regerardebito

# service DETRAN SP
detran.sp.default.uri=http://10.200.47.78:80/eaigever/SircofService
detran.sp.context.path=com.registrocontrato.registro.service.detran.sp.client
detran.sp.usuario=06032507000103
detran.sp.senha=PL@CE1

# service DETRAN PR
detran.pr.default.uri.auth=https://auth-cs.identidadedigital.pr.gov.br/centralautenticacao/api/v1/token
detran.pr.default.uri=https://www.registrodecontrato.detran.pr.gov.br/detran-regcon/api
detran.pr.usuario=e8c0653fea13f91bf3c48159f7c24f78
detran.pr.senha=G9F8jX5apHgkAmWF

# service DETRAN AP
detran.ap.default.uri.auth=https://www.ap.getran.com.br/integracao-api-rest/api/autenticacao
detran.ap.default.uri=https://www.ap.getran.com.br/integracao-api-rest
detran.ap.url.cadastrar=/api/areaRestrita/registroContrato/registrar
detran.ap.url.consultar=/api/areaRestrita/registroContrato/consultar
detran.ap.usuario=PLACETI
detran.ap.senha=723Klenr989

# service DETRAN AC
detran.ac.default.uri=https://www.ac.getran.com.br/getranServicos/rest/registroContrato/
detran.ac.context.path=com.registrocontrato.registro.service.detran.ac.client
detran.ac.url.cadastrar=registrarContrato
detran.ac.url.consultar=consultarContrato

# service DETRAN RJ
detran.rj.default.uri=http://201.57.220.227:8080/wsstack/services/REG-CONT
detran.rj.context.path=com.registrocontrato.registro.service.detran.rj.client
detran.rj.usuario=COCTPTIN
detran.rj.senha=010919Ag

# service DETRAN PI
detran.pi.default.uri-boleto=https://www.pi.getran.com.br/financeiro/api/registro/gerarBoleto
detran.pi.default.uri=https://www.pi.getran.com.br/registro-contrato
detran.pi.context.path=com.registrocontrato.registro.service.detran.pi.client
detran.pi.usuario=06032507000103
detran.pi.senha=d(89@^1Y,]j1
detran.pi.rest.token=eyJhbGciOiJIUzI1NiJ9.eyJzdWIiOiIwNjAzMjUwNzAwMDEwMyIsImF1ZCI6WyJSRUdJU1RSQURPUkEiXSwiaXNzIjoiZmluYW5jZWlyby1hcGkiLCJpYXQiOjE3NDg1NDc0ODcsImp0aSI6Ijc5MzkzY2ExLTdlMmQtNDgyOC1iNDMxLWM2YTM0NDUyNDNmYSIsIm5hbWUiOiJQbGFjZSBUZWNub2xvZ2lhIGUgSW5vdmHDp8OjbyBTLiBBLiJ9.XUhO7xSpM9zqcAIgovQ0DxJh4eVVcuTkBdDBei9Iv1E

# service DETRAN PB
detran.pb.default.uri=https://wsdetran.pb.gov.br/CallWS/CallWSDT
detran.pb.context.path=com.registrocontrato.registro.service.detran.pb.client
detran.pb.usuario=0603250
detran.pb.sigla.transacao=PL
detran.pb.sigla.transmissao=PL

# service DETRAN RR
detran.rr.default.uri=https://www.rr.getran.com.br
detran.rr.url.cadastrar=/getranServicos/rest/registroContrato/registrarContrato
detran.rr.url.consultarcontrato=/getranServicos/rest/registroContrato/consultarContrato
detran.rr.usuario=PLACETI
detran.rr.senha=X5IGJAGOPFJLK087987

# service DETRAN MG
detran.mg.default.uri=http://webservice.detran.mg.gov.br/sircof/soap/contratos_financeiros/service
detran.mg.context.path=com.registrocontrato.registro.service.detran.mg.client
detran.mg.usuario=06032507000103
detran.mg.senha=2262df343111ee477711fce38808f3412e584804

# service DETRAN SC
detran.sc.default.uri=http://webservicesp.detrannet.sc.gov.br/RegistroContrato/RegistroContrato.asmx
detran.sc.context.path=com.registrocontrato.registro.service.detran.sc.client
detran.sc.usuario=placeti
detran.sc.senha=#0603250ProD7000103@
detran.sc.registrarcontrato=RegistrarContrato
detran.sc.consultarsequencial=ConsultarSequencialContrato

# service DETRAN MT
detran.mt.default.uri=https://ws.detrannet.mt.gov.br/wsRegistroContrato/wsRegistroContrato.asmx
detran.mt.default.endpoint=http://ws.detrannet.mt.gov.br
detran.mt.context.path=com.registrocontrato.registro.service.detran.mt.client
detran.mt.url.cadastrar=RegistraContrato
detran.mt.url.consultar=ConsultaContrato

# service DETRAN PE
detran.pe.default.uri=http://200.238.67.2:51075/WebApiRegistroContratoGravame/RegistraContrato

# service DETRAN MS
detran.ms.default.uri=https://web2.detran.ms.gov.br/s56/rc-api
detran.ms.url.cadastrar=/solicitacao/solicitar-registro
detran.ms.url.autenticacao=/usuario/authenticate
detran.ms.url.desbloquear.contrato=/solicitacao/desbloquear-contrato
detran.ms.url.envio.imagem=/solicitacao/enviar-imagem
detran.ms.url.corrigir.imagem=/solicitacao/corrigir-imagem
detran.ms.url.busca.andamento=/buscar/andamento
detran.ms.url.consulta.boleto.byte=/buscar/guia-pagamento/byte



#Banco Pan Service
bancopan.default.uri=https://api.bancopan.com.br/transacional/apipanveiculos
bancopan.obter-token=/token
bancopan.autenticar-token=/oauth/token
bancopan.contrato-digitalizado=/GrvEnvioDocContrato/ContratoDigitalizado
bancopan.130_usuario_senha=MTMwOlVTUl9SRUdfUExBQ0U6UGxAY2UjMjAyMSo=



# service DETRAN RS
detran.rs.default.uri=https://www.vei.detran.rs.gov.br/sng/ContratoIncSoap
detran.rs.context.path=com.registrocontrato.registro.service.detran.rs.client

# service DETRAN BA
detran.ba.default.uri=http://*************/wsdetrancontrato/wsdetrancontrato.asmx
detran.ba.context.path=com.registrocontrato.registro.service.detran.ba.client

#jsf
jsf.PROJECT_STAGE=Production

# remessa
file.remessa.dir=/storage/remessas/
file.remessa.dir-read=/storage/remessas/

# remessa chassi
file.remessa.chassi.dir=/storage/remessaschassi/

#certificado
detran.certificados=/storage/certificados/

#chave
placecon.cripto.key=/opt/chaves/prod.key
#log
logging.level=INFO
logging.config=classpath:/log4j2.xml

file.dir=/storage/contratos/
file.dir-read=/data/contratos/

# MENSAGERIA - KAFKA
spring.kafka.producer.bootstrap-servers=***********:9092
spring.kafka.producer.key-serializer=org.apache.kafka.common.serialization.StringSerializer
spring.kafka.producer.value-serializer=org.apache.kafka.common.serialization.StringSerializer
spring.kafka.producer.client-id=group-id

spring.kafka.consumer.bootstrap-servers=***********:9092
spring.kafka.consumer.group-id=group-id
spring.kafka.consumer.auto-offset-reset=earliest
spring.kafka.consumer.key-deserializer=org.apache.kafka.common.serialization.StringDeserializer
spring.kafka.consumer.value-deserializer=org.apache.kafka.common.serialization.StringDeserializer

auto.create.topics.enable=true
