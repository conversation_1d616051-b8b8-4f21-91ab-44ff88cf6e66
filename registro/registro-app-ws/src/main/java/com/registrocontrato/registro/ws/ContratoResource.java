package com.registrocontrato.registro.ws;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.registrocontrato.commons.ws.dto.ContratoResumoDTO;
import com.registrocontrato.commons.ws.dto.RetornoUploadChassiDTO;
import com.registrocontrato.commons.ws.mapper.ContratoMapper;
import com.registrocontrato.commons.ws.rsng.RemessaService;
import com.registrocontrato.infra.entity.Financeira;
import com.registrocontrato.infra.entity.SimNao;
import com.registrocontrato.infra.entity.StatusProcessamento;
import com.registrocontrato.infra.exception.ServiceException;
import com.registrocontrato.registro.dto.RemessaChassiMensagemDTO;
import com.registrocontrato.registro.dto.RemessaDocumentoMensagemDTO;
import com.registrocontrato.registro.entity.*;
import com.registrocontrato.registro.repository.ComprovanteRepository;
import com.registrocontrato.registro.repository.ContratoRepository;
import com.registrocontrato.registro.repository.NotificacaoEcommRepository;
import com.registrocontrato.registro.service.*;
import com.registrocontrato.registro.service.dto.RemessaChassiDTO;
import com.registrocontrato.registro.service.dto.RemessaDocumentoDTO;
import com.registrocontrato.registro.service.dto.ValidacaoEnum;
import com.registrocontrato.seguranca.service.FinanceiraService;
import org.apache.commons.lang3.RandomStringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.ws.rs.core.Response;
import java.io.*;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

@RestController
@RequestMapping(value = "/api/v1/contrato/")
public class ContratoResource extends BaseResource {

    private static final long serialVersionUID = 4099530407178118208L;

    private static final String CONSULTAR_REGISTRO_CHASSI = "Consultar um Registro Eletrônico de Contrato por CHASSI";
    private static final String CONSULTAR_REGISTRO_CONTRATO_CHASSI = "Consultar um Registro Eletrônico de Contrato por número do Contrato e CHASSI";
    private static final String CONSULTAR_REGISTRO_REGISTRO_COMPROVANTE = "Consultar um Comprovante por Número do Registro Eletrônico e pelo Comprovante";
    private static final String UPLOAD = "Enviar arquivo de Registro Eletrônico de Contrato";
    private static final String UPLOAD_ECOMM = "Enviar arquivo ecomm de um Registro Eletrônico de Contrato";
    private static final String UPLOAD_REMESSA_CHASSI = "Enviar arquivo remessa chassi";
    private static final String RETORNO_ECOMM = "Receber arquivo de retorno ecomm";
    private static final String DOWNLOAD_ARQUIVO_REMESSA_CHASSI = "Realizar download do arquivo remessa de chassi";
    private static final String DOWNLOAD_ARQUIVO_DOCUMENTO_CHASSI = "Realizar download do arquivo remessa de leitura de documento";
    private static final String LISTAR_ARQUIVO_REMESSA_CHASSI = "Listar arquivo remessa de chassi não processados";
    private static final String LISTAR_DOCUMENTO_REMESSA_CHASSI = "Listar arquivo remessa de documentos não processados";
    private static final String RECEBER_RELATORIO_REMESSA_CHASSI = "Recebe relatório com informações sobre a execução da remessa Chassi";
    private static final String RECEBER_RELATORIO_REMESSA_DOCUMENTO = "Recebe relatório com informações sobre a execução da remessa documento";
    private static final String RECEBER_RELATORIO_REMESSA_CHASSI_NOVO = "Recebe relatório com informações sobre a execução da remessa Chassi";
    private static final String ALTERAR_STATUS_PROCESSAMENTO_REMESSA_CHASSI = "Finaliza processamento do arquivo remessa de chassi.";
    private static final String ALTERAR_STATUS_PROCESSAMENTO_REMESSA_DOCUMENTO = "Finaliza processamento do arquivo remessa de documento.";
    private static final String ERRO_LOGIN = "Erro de Login";


    private final Logger log = LoggerFactory.getLogger(ContratoResource.class);

    @Value("${file.remessa.chassi.dir:null}")
    private String fileRemessaDir;

    @Autowired
    private ContratoMapper mapper;

    @Autowired
    private FinanceiraService financeiraService;

    @Autowired
    private ContratoRepository contratoRepository;

    @Autowired
    private RemessaService remessaService;

    @Autowired
    private TemplateRemessaService templateRemessaService;

    @Autowired
    private ComprovanteRepository comprovanteRepository;

    @Autowired
    private RemessaChassiService remessaChassiService;

    @Autowired
    private RemessaDocumentoService remessaDocumentoService;

    @Autowired
    private ArquivoRemessaService arquivoRemessaService;

    @Autowired
    private TemplateRemessaService templateService;

    @Autowired
    private NotificacaoEcommRepository notificacaoEcommRepository;

    public ContratoResource() {
    }

    @GetMapping(value = "chassi/{numeroChassi}", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public ContratoResumoDTO consultarRegistroChassi(@PathVariable(required = true, name = "numeroChassi") String numeroChassi) throws ServiceException {

        Contrato contrato = contratoRepository.findTop1ByChassi(numeroChassi);
        if (contrato != null) {
            return mapper.convertToDTO(contrato);
        }
        return null;
    }

    @GetMapping(value = "contratoChassi/{numeroContrato}/{numeroChassi}", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public ContratoResumoDTO consultarRegistroContratoChassi(@PathVariable(required = true, name = "numeroContrato") String numeroContrato,
                                                             @PathVariable(name = "numeroChassi", required = true) String numeroChassi) throws ServiceException {

        Contrato contrato = contratoRepository.findTop1ByChassiAndNumeroContrato(numeroChassi, numeroContrato);
        if (contrato != null) {
            return mapper.convertToDTO(contrato);
        }

        return null;
    }

    @GetMapping(value = "registroComprovante/{numeroRegistro}/{codigoComprovante}", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public ContratoResumoDTO consultarComprovante(@PathVariable(name = "numeroRegistro", required = true) Long numeroRegistro,
                                                  @PathVariable(name = "codigoComprovante", required = true) String codigoComprovante) {

        Comprovante comprovante = comprovanteRepository.findByCodigoAndNumeroRegistroEletronico(codigoComprovante, numeroRegistro);
        if (comprovante != null) {
            Contrato contrato = contratoRepository.findOne(comprovante.getIdContrato());
            return mapper.convertToDTO(contrato);
        }

        return null;
    }

    @PostMapping(value = "upload/remessaecomm/retorno", produces = MediaType.TEXT_PLAIN_VALUE)
    public ResponseEntity<String> uploadRetornoFileEcomm(@RequestParam(required = true, name = "arquivo") String arquivo) {

        try {
            ArquivoRemessa ar = arquivoRemessaService.findArquivoEcommPan(arquivo);
            if (ar != null) {
                if (ar.getStatus() != null && ar.getStatus() == 1) {
                    return new ResponseEntity<>(getRetornoStringArquivo(ar).toString(), HttpStatus.OK);
                }
                return new ResponseEntity<>(null, HttpStatus.NOT_ACCEPTABLE);
            } else {
                return new ResponseEntity<>(null, HttpStatus.NOT_FOUND);
            }
        } catch (Exception e) {
            return new ResponseEntity<>(e.getMessage(), HttpStatus.BAD_REQUEST);
        }
    }

    private StringBuilder getRetornoStringArquivo(ArquivoRemessa ar) throws ServiceException, IOException {
        InputStream retorno = remessaService.gerarArquivoRetorno(ar);

        StringBuilder textBuilder = new StringBuilder();
        try (Reader reader = new BufferedReader(new InputStreamReader
                (retorno, Charset.forName(StandardCharsets.UTF_8.name())))) {
            int c = 0;
            while ((c = reader.read()) != -1) {
                textBuilder.append((char) c);
            }
        }
        return textBuilder;
    }

    @PostMapping(value = "upload/remessaecomm", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<RetornoUploadChassiDTO> uploadRemessaFileEcomm(@RequestParam(name = "file", required = true) MultipartFile uploadfile) {

        String currentPrincipal = SecurityContextHolder.getContext().getAuthentication().getName();
        RetornoUploadChassiDTO retornoRest = new RetornoUploadChassiDTO();
        retornoRest.setCodResposta(ValidacaoEnum.SUCESSO.getCodigo());
        retornoRest.setHashOperacao(RandomStringUtils.randomAlphabetic(20));
        retornoRest.setCnpjAgente(currentPrincipal);
        retornoRest.setOperacao(UPLOAD);

        Long idTemplate = templateRemessaService.findEcomm();
        if (idTemplate == null) {
            retornoRest.setCodResposta(ValidacaoEnum.ERRO.getCodigo());
            retornoRest.setMsgResposta("Não há um template ECOMM registrado para processamento de remessas");
            return new ResponseEntity<>(retornoRest, HttpStatus.OK);
        }
        File f = null;
        try {
            f = File.createTempFile(uploadfile.getOriginalFilename(), ".txt");

            Files.copy(uploadfile.getInputStream(), f.toPath(), StandardCopyOption.REPLACE_EXISTING);

//            InputStream is = new  FileInputStream(f);
//            remessaService.saveArquivo(currentPrincipal, f, uploadfile.getOriginalFilename(), idTemplate);

            ArquivoRemessa arquivoRemessa = remessaService.save(uploadfile.getOriginalFilename(), new FileInputStream(f), idTemplate);

            remessaService.saveItensRemessaAssync(uploadfile.getOriginalFilename(), new FileInputStream(f), idTemplate, currentPrincipal, arquivoRemessa);

//remessaService.saveArquivo(currentPrincipal, f, uploadfile.getOriginalFilename(), idTemplate);
            retornoRest.setCodResposta(ValidacaoEnum.SUCESSO.getCodigo());
            retornoRest.setMsgResposta("Arquivo de Remessa enviado com sucesso.");
        } catch (IOException e) {
            retornoRest.setCodResposta(ValidacaoEnum.ERRO.getCodigo());
            retornoRest.setMsgResposta(e.getMessage());
            e.printStackTrace();
        } catch (Exception e) {
            e.printStackTrace();
            retornoRest.setCodResposta(ValidacaoEnum.ERRO.getCodigo());
            retornoRest.setMsgResposta(e.getMessage());
        } finally {
            if (f != null) {
                f.delete();
            }
        }
        try {
            NotificacaoEcomm notifica = notificacaoEcommRepository.findByDateNotificado(new Date());
            if (notifica == null) {
                notifica = new NotificacaoEcomm();
                notifica.setDateNotificado(new Date());
            }
            notifica.setEnviado(SimNao.S);
            notificacaoEcommRepository.save(notifica);

        } catch (RuntimeException e) {
            log.error("Ecomm Desktop Funcionando, porém Não foi salvo a notificacao");
        }

        return new ResponseEntity<RetornoUploadChassiDTO>(retornoRest, HttpStatus.OK);
    }

    @GetMapping(value = "download/remessa/chassi/{idArquivo}/{nomeArquivo}")
    public ResponseEntity<Resource> downloadRemessaChassi(@PathVariable(name = "idArquivo", required = true) Long idArquivo,
                                                          @PathVariable(name = "nomeArquivo", required = true) String nomeArquivo) throws IOException, ServiceException {
        File file = new File(fileRemessaDir + nomeArquivo);
        HttpHeaders header = new HttpHeaders();
        header.add(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=" + nomeArquivo + ".csv");
        header.add("Cache-Control", "no-cache, no-store, must-revalidate");
        header.add("Pragma", "no-cache");
        header.add("Expires", "0");//20200224_6ZV4a

        Path path = Paths.get(file.getAbsolutePath());
        ByteArrayResource resource = new ByteArrayResource(Files.readAllBytes(path));

        return ResponseEntity.ok().headers(header).contentLength(file.length())
                .contentType(MediaType.parseMediaType("application/octet-stream"))
                .body(resource);
    }

    @GetMapping(value = "listar/remessa/naoProcessada")
    public List<RemessaChassiDTO> listaArquivosNaoProcessados() {
        List<RemessaChassi> remessasChassi = null;
        List<RemessaChassiDTO> retorno = new ArrayList<>();
        RemessaChassiDTO remessaChassiRest = null;

        remessasChassi = remessaChassiService.getArquivosRemessaNaoProcessados();
        if (remessasChassi != null && !remessasChassi.isEmpty()) {
            for (RemessaChassi v : remessasChassi) {
                remessaChassiRest = new RemessaChassiDTO();
                remessaChassiRest.setIdArquivo(v.getId());
                remessaChassiRest.setHash(v.getHash());
                remessaChassiRest.setUsuario(v.getUsuario());
                remessaChassiRest.setNomeArquivo(v.getNome());
                remessaChassiRest.setDataInicio(v.getDataTransacao());
                remessaChassiRest.setCnpjFinanceira(v.getFinanceira().getDocumento());
                retorno.add(remessaChassiRest);
            }
        }
        return retorno;
    }

    @GetMapping(value = "listar/remessa/processada")
    public List<RemessaChassiDTO> listaArquivosProcessados() {
        List<RemessaChassi> remessasChassi = null;
        List<RemessaChassiDTO> retorno = new ArrayList<>();
        RemessaChassiDTO remessaChassiRest = null;

        remessasChassi = remessaChassiService.getArquivosRemessaEmProcessamento();
        if (remessasChassi != null && !remessasChassi.isEmpty()) {
            for (RemessaChassi v : remessasChassi) {
                if (v.getArquivoRemessa() == null || v.getArquivoRemessa().getStatus().equals(1)) {
                    remessaChassiRest = new RemessaChassiDTO();
                    remessaChassiRest.setIdArquivo(v.getId());
                    remessaChassiRest.setHash(v.getHash());
                    remessaChassiRest.setUsuario(v.getUsuario());
                    remessaChassiRest.setNomeArquivo(v.getNome());
                    remessaChassiRest.setDataInicio(v.getDataTransacao());
                    remessaChassiRest.setCnpjFinanceira(v.getFinanceira().getDocumento());
                    retorno.add(remessaChassiRest);
                }
            }
        }
        return retorno;
    }

    @PostMapping(value = "upload/remessa", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<RetornoUploadChassiDTO> uploadRemessaFile(@RequestParam(name = "file", required = true) MultipartFile uploadfile,
                                                                    @RequestParam(name = "idRemessaChassi", required = false) Long idRemessaChassi) {
        RemessaChassi remessaChassi = remessaChassiService.findOne(idRemessaChassi);
        String currentPrincipal = remessaChassi.getUsuario();

        RetornoUploadChassiDTO retornoRest = new RetornoUploadChassiDTO();
        retornoRest.setCodResposta(ValidacaoEnum.SUCESSO.getCodigo());
        retornoRest.setHashOperacao(RandomStringUtils.randomAlphabetic(20));
        retornoRest.setCnpjAgente(currentPrincipal);
        retornoRest.setOperacao(UPLOAD);

        Long templateId = templateService.findDefault(SecurityContextHolder.getContext().getAuthentication().getName());
        if (templateId == null) {
            retornoRest.setCodResposta(ValidacaoEnum.ERRO.getCodigo());
            retornoRest.setMsgResposta("Não há um template registrado para processamento de remessas");
            return new ResponseEntity<>(retornoRest, HttpStatus.OK);
        }

        File f = null;
        try {
            f = File.createTempFile(uploadfile.getOriginalFilename(), ".txt");

            Files.copy(uploadfile.getInputStream(), f.toPath(), StandardCopyOption.REPLACE_EXISTING);
            if (idRemessaChassi != null) {
                remessaService.saveArquivoRemessaChassi(currentPrincipal, f, uploadfile.getOriginalFilename(), templateId, idRemessaChassi);
            } else {
                remessaService.saveArquivo(currentPrincipal, f, uploadfile.getOriginalFilename(), templateId);
            }
            retornoRest.setCodResposta(ValidacaoEnum.SUCESSO.getCodigo());
            retornoRest.setMsgResposta("Arquivo de Remessa enviado com sucesso.");
        } catch (IOException e) {
            retornoRest.setCodResposta(ValidacaoEnum.ERRO.getCodigo());
            retornoRest.setMsgResposta(e.getMessage());
        } catch (Exception e) {
            retornoRest.setCodResposta(ValidacaoEnum.ERRO.getCodigo());
            retornoRest.setMsgResposta(e.getMessage());
        }
        return new ResponseEntity<>(retornoRest, HttpStatus.OK);
    }

    @GetMapping(value = "remessa/chassi/{idOperacao}/{idArquivo}")
    public Response finalizaProcessamentoRemessaChassi(@PathVariable(name = "idOperacao", required = true) int operacao,
                                                       @PathVariable(name = "idArquivo", required = true) Long idArquivo) throws ServiceException {
        RemessaChassi findOne = null;

        findOne = remessaChassiService.findOne(idArquivo);
        if (findOne == null) {
            return Response.status(Response.Status.NOT_ACCEPTABLE)
                    .entity("Arquivo de id: " + idArquivo + "não encontrado para Atualizacao")
                    .build();
        } else if (findOne.getStatus() == StatusProcessamento.values()[operacao]) {
            return Response.status(Response.Status.OK)
                    .entity("Arquivo já se encontra com o status " + StatusProcessamento.values()[operacao])
                    .build();
        } else if (StatusProcessamento.values().length > operacao && operacao > -1) {
            remessaChassiService.save(findOne, StatusProcessamento.values()[operacao]);
            String msg = "Arquivo colocado com status " + StatusProcessamento.values()[operacao].getDescricao() + " com sucesso!";
            return Response.status(Response.Status.OK)
                    .entity(msg)
                    .build();
        } else {
            return Response.status(Response.Status.NOT_ACCEPTABLE)
                    .entity("Operação não permitida ou cadastrada.")
                    .build();
        }
    }

    @PostMapping(value = "remessa/chassi/relatorio/erro")
    public Response receberRelatorio(@RequestParam(name = "idArquivo", required = true) String idArquivo,
                                     @RequestParam(name = "listaMensagens", required = true) String mensagens) {
        RemessaChassi remessa = remessaChassiService.findOne(Long.valueOf(idArquivo));
        try {
            ObjectMapper objectMapper = new ObjectMapper();
            List<RemessaChassiMensagemDTO> dtos = objectMapper.readValue(mensagens, new TypeReference<List<RemessaChassiMensagemDTO>>() {
            });
            List<RemessaChassiMensagens> msgs = new ArrayList<>();
            for (RemessaChassiMensagemDTO dto : dtos) {
                if (SimNao.N.equals(dto.getSucesso())) {
                    RemessaChassiMensagens msg = new RemessaChassiMensagens();
                    msg.setChassi(dto.getChassi());
                    msg.setMensagen(dto.getMensagem());
                    msg.setSucesso(dto.getSucesso());
                    msg.setRemessaChassi(remessa);
                    msgs.add(msg);
                }
            }
            if (remessa.getMensagens() != null && !remessa.getMensagens().isEmpty()) {
                remessa.getMensagens().clear();
            }
            remessa.getMensagens().addAll(msgs);

            remessaChassiService.save(remessa);
            return Response.status(Response.Status.OK)
                    .entity("Mensagens registradas com Sucesso.")
                    .build();
        } catch (ServiceException e) {
            return Response.status(Response.Status.NOT_IMPLEMENTED)
                    .entity("Em desenvolvimento.")
                    .build();
        } catch (IOException e) {
            return Response.status(Response.Status.NOT_IMPLEMENTED)
                    .entity("Em desenvolvimento.")
                    .build();
        }
    }

    @PostMapping(value = "remessa/erro", produces = MediaType.TEXT_PLAIN_VALUE)
    public ResponseEntity<String> notificaErroLogin(@RequestParam(required = true, name = "erro") String erro,
                                                    @RequestParam(required = true, name = "idArquivoRemessa") Long id) {
        try {
            remessaChassiService.finalizarRemessaComErro(id, StatusProcessamento.PROCESSADO_ERRO, erro);
            return new ResponseEntity<>(HttpStatus.OK);
        } catch (Exception e) {
            return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
        }
    }

    @GetMapping(value = "listar/documento/naoProcessada")
    public List<RemessaDocumentoDTO> listaArquivosDeLeituraNaoProcessados() {
        List<RemessaDocumento> remessaDocumento = null;
        List<RemessaDocumentoDTO> retorno = new ArrayList<>();
        RemessaDocumentoDTO remessaDocumentoRest = null;

        remessaDocumento = remessaDocumentoService.getArquivosRemessaNaoProcessados();
        if (remessaDocumento != null && !remessaDocumento.isEmpty()) {
            for (RemessaDocumento v : remessaDocumento) {
                remessaDocumentoRest = new RemessaDocumentoDTO();
                remessaDocumentoRest.setIdArquivo(v.getId());
                remessaDocumentoRest.setHash(v.getHash());
                remessaDocumentoRest.setUsuario(v.getUsuario());
                remessaDocumentoRest.setNomeArquivo(v.getNome());
                remessaDocumentoRest.setDataInicio(v.getDataTransacao());
                remessaDocumentoRest.setCnpjFinanceira(v.getFinanceira().getDocumento());
                retorno.add(remessaDocumentoRest);
            }
        }
        return retorno;
    }

    @GetMapping(value = "download/remessa/documento/{idArquivo}/{nomeArquivo}")
    public ResponseEntity<Resource> donwloadRemessaDocumento(@PathVariable(name = "idArquivo", required = true) Long idArquivo,
                                                             @PathVariable(name = "nomeArquivo", required = true) String nomeArquivo) throws IOException {
        File file = new File(fileRemessaDir + nomeArquivo);
        HttpHeaders header = new HttpHeaders();
        header.add(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=" + nomeArquivo + ".csv");
        header.add("Cache-Control", "no-cache, no-store, must-revalidate");
        header.add("Pragma", "no-cache");
        header.add("Expires", "0");//20200224_6ZV4a

        Path path = Paths.get(file.getAbsolutePath());
        ByteArrayResource resource = new ByteArrayResource(Files.readAllBytes(path));

        return ResponseEntity.ok().headers(header).contentLength(file.length())
                .contentType(MediaType.parseMediaType("application/octet-stream"))
                .body(resource);
    }

    @PutMapping(value = "remessa/documento/{idOperacao}/{idArquivo}")
    public Response finalizaProcessamentoRemessaDocumento(@PathVariable(name = "idOperacao", required = true) int operacao,
                                                          @PathVariable(name = "idArquivo", required = true) Long idArquivo) throws ServiceException {

        RemessaDocumento findOne = remessaDocumentoService.findOne(idArquivo);
        if (findOne == null) {
            return Response.status(Response.Status.NOT_ACCEPTABLE)
                    .entity("Arquivo de id: " + idArquivo + "não encontrado para Atualizacao")
                    .build();
        } else if (StatusProcessamento.values().length > operacao && operacao > -1) {
            remessaDocumentoService.save(findOne, StatusProcessamento.values()[operacao]);
            String msg = "Arquivo colocado com status " + StatusProcessamento.values()[operacao].getDescricao() + " com sucesso!";
            return Response.status(Response.Status.OK)
                    .entity(msg)
                    .build();
        } else {
            return Response.status(Response.Status.NOT_ACCEPTABLE)
                    .entity("Operação não permitida ou cadastrada.")
                    .build();
        }
    }

    @PostMapping(value = "/remessa/documento/relatorio")
    public Response receberRelatorioDocumento(@RequestParam(name = "idArquivo") String idArquivo,
                                              @RequestParam(name = "listaMensagens") String mensagens) {
        RemessaDocumento remessa = remessaDocumentoService.findOne(Long.valueOf(idArquivo));
        try {
            ObjectMapper objectMapper = new ObjectMapper();
            List<RemessaDocumentoMensagemDTO> dtos = objectMapper.readValue(mensagens, new TypeReference<List<RemessaDocumentoMensagemDTO>>() {
            });
            List<RemessaDocumentoMensagens> msgs = new ArrayList<>();
            for (RemessaDocumentoMensagemDTO dto : dtos) {
                RemessaDocumentoMensagens msg = new RemessaDocumentoMensagens();
                msg.setChassi(dto.getChassi());
                msg.setMensagen(dto.getMensagem());
                msg.setSucesso(dto.getSucesso());
                msg.setRemessaDocumento(remessa);
                msgs.add(msg);
            }
            if (remessa.getMensagens() != null && !remessa.getMensagens().isEmpty()) {
                remessa.getMensagens().clear();
            }
            remessa.getMensagens().addAll(msgs);

            remessaDocumentoService.save(remessa);
            return Response.status(Response.Status.OK)
                    .entity("Mensagens registradas com Sucesso.")
                    .build();
        } catch (ServiceException | IOException e) {
            return Response.status(Response.Status.NOT_IMPLEMENTED)
                    .entity("Em desenvolvimento.")
                    .build();
        }
    }

    @GetMapping(value = "listar/remessa/documento/processada")
    public List<RemessaDocumentoDTO> listaArquivosRemessaDocumentoProcessados() {
        List<RemessaDocumentoDTO> retorno = new ArrayList<>();
        RemessaDocumentoDTO remessaDocumentoRest = null;


        List<RemessaDocumento> remessaDocumento = remessaDocumentoService.getArquivosRemessaEmProcessamento();
        if (remessaDocumento != null && !remessaDocumento.isEmpty()) {
            for (RemessaDocumento v : remessaDocumento) {
                if (v.getArquivoRemessa() == null || v.getArquivoRemessa().getStatus().equals(1)) {
                    remessaDocumentoRest = new RemessaDocumentoDTO();
                    remessaDocumentoRest.setIdArquivo(v.getId());
                    remessaDocumentoRest.setHash(v.getHash());
                    remessaDocumentoRest.setUsuario(v.getUsuario());
                    remessaDocumentoRest.setNomeArquivo(v.getNome());
                    remessaDocumentoRest.setDataInicio(v.getDataTransacao());
                    remessaDocumentoRest.setCnpjFinanceira(v.getFinanceira().getDocumento());
                    retorno.add(remessaDocumentoRest);
                }
            }
        }
        return retorno;
    }

    @PostMapping(value = "upload/remessachassi", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<Long> uploadRemessaChassi(@RequestParam(name = "file") MultipartFile uploadfile,
                                                    @RequestParam(name = "documento") String documento,
                                                    @RequestParam(name = "usuario") String usuario) {
        File f = null;
        Long idRemessaChassi = 0l;
        Financeira financeira = financeiraService.findByDocumento(documento);
        try {
            f = File.createTempFile(uploadfile.getOriginalFilename(), ".txt");
            Files.copy(uploadfile.getInputStream(), f.toPath(), StandardCopyOption.REPLACE_EXISTING);
            Long templateId = templateService.findDefault(SecurityContextHolder.getContext().getAuthentication().getName());
            if (templateId == null) {
                return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
            }
            RemessaChassi resultado = remessaChassiService.salvarRemessaChassi(uploadfile.getOriginalFilename(), f, usuario, financeira);
            idRemessaChassi = resultado.getId();
        } catch (IOException | ServiceException e) {
            e.printStackTrace();
        } catch (Exception e) {
            throw new RuntimeException(e);
        } finally {
            if (f != null) {
                f.delete();
            }
        }
        return new ResponseEntity<>(idRemessaChassi, HttpStatus.OK);
    }

    @PostMapping(value = "remessa/chassi/relatorio/mensagens")
    public ResponseEntity<?> receberRelatorioNovo(@RequestParam(name = "idArquivo") String idArquivo,
                                                  @RequestBody List<RemessaChassiMensagemDTO> mensagens) {
        RemessaChassi remessa = remessaChassiService.findOne(Long.valueOf(idArquivo));
        if (Objects.nonNull(remessa)) {
            remessa.setMensagens(new RemessaChassiMensagemDTO().dtoToEntity(mensagens, remessa));
        }
        try {
            remessaChassiService.save(remessa);
            return ResponseEntity.ok("Mensagens registradas com sucesso");
        } catch (ServiceException e) {
            return ResponseEntity.unprocessableEntity().body("Não foi possível realizar o registro");
        }
    }
}
