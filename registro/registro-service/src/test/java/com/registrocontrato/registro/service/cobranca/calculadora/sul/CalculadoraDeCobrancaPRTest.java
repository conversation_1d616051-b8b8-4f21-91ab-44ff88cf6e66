package com.registrocontrato.registro.service.cobranca.calculadora.sul;

import com.registrocontrato.infra.entity.Financeira;
import com.registrocontrato.infra.exception.ServiceException;
import com.registrocontrato.registro.entity.Cobranca;
import com.registrocontrato.registro.service.CredenciamentoService;
import com.registrocontrato.registro.service.CupomDescontoService;
import com.registrocontrato.registro.service.MigracaoService;
import com.registrocontrato.registro.service.cobranca.CobrancaService;
import com.registrocontrato.registro.service.cobranca.gravame.GravameService;
import com.registrocontrato.registro.service.detran.pr.client.response.AgenteSituacaoBoletoMensalPrResponse;
import com.registrocontrato.registro.service.detran.pr.client.response.PeriodoRelatorioSituacaoBoletoMensalPrResponse;
import com.registrocontrato.registro.service.detran.pr.client.response.RegistradoraRelatorioSituacaoBoletoMensalPrResponse;
import com.registrocontrato.registro.service.detran.pr.rest.WsDetranPR;
import org.assertj.core.util.Arrays;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;

import java.math.BigDecimal;
import java.util.Date;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
class CalculadoraDeCobrancaPRTest {

    public CalculadoraDeCobrancaPRTest() {}

    static class CalculadoraCobrancaPR extends CalculadoraDeCobrancaPR {

        public CalculadoraCobrancaPR(
                CobrancaService cobrancaService,
                CredenciamentoService credenciamentoService,
                CupomDescontoService cupomDescontoService,
                WsDetranPR wsDetranPrRestClient,
                GravameService gravameService,
                MigracaoService migracaoService) {
            super(cobrancaService, credenciamentoService, cupomDescontoService, wsDetranPrRestClient, migracaoService, gravameService);
        }
    }

    @InjectMocks
    private CalculadoraCobrancaPR calculadoraCobrancaPR;

    @Mock
    CobrancaService cobrancaService;
    @Mock
    CredenciamentoService credenciamentoService;
    @Mock
    CupomDescontoService cupomDescontoService;
    @Mock
    GravameService gravameService;
    @Mock
    WsDetranPR wsDetranPrRestClient;

    private AutoCloseable closeable;

    @BeforeEach
    void setup() {
        closeable = MockitoAnnotations.openMocks(this);
    }

    @AfterEach
    void shutdown() throws Exception {
        closeable.close();
    }

//    @Test
//    void test_getValorBoletoDetran_erroWebService_deveRetornarZero() throws ServiceException {
//        Cobranca cobranca = mock(Cobranca.class);
//        Date dataInicio = mock(Date.class);
//        Date dataFim = mock(Date.class);
//        Financeira financeira = mock(Financeira.class);
//
//        doReturn(dataInicio).when(cobranca).getDataInicio();
//        doReturn(dataFim).when(cobranca).getDataFim();
//        doReturn(financeira).when(cobranca).getFinanceira();
//
//        doThrow(ServiceException.class).when(wsDetranPrRestClient).consultarCobrancas(eq(dataInicio), eq(dataFim), eq(financeira));
//
//        BigDecimal resultado = calculadoraCobrancaPR.getValorBoletoDetran(cobranca);
//        assertEquals(BigDecimal.ZERO, resultado);
//    }
//
//    @Test
//    void test_getValorBoletoDetran_saidaNull_deveRetornarZero() throws ServiceException {
//        Cobranca cobranca = mock(Cobranca.class);
//        Date dataInicio = mock(Date.class);
//        Date dataFim = mock(Date.class);
//        Financeira financeira = mock(Financeira.class);
//
//        doReturn(dataInicio).when(cobranca).getDataInicio();
//        doReturn(dataFim).when(cobranca).getDataFim();
//        doReturn(financeira).when(cobranca).getFinanceira();
//
//        doReturn(null).when(wsDetranPrRestClient).consultarCobrancas(eq(dataInicio), eq(dataFim), eq(financeira));
//
//        BigDecimal resultado = calculadoraCobrancaPR.getValorBoletoDetran(cobranca);
//        assertEquals(BigDecimal.ZERO, resultado);
//    }
//
//    @Test
//    void test_getValorBoletoDetran_registrosNull_deveRetornarZero() throws ServiceException {
//        Cobranca cobranca = mock(Cobranca.class);
//        Date dataInicio = mock(Date.class);
//        Date dataFim = mock(Date.class);
//        Financeira financeira = mock(Financeira.class);
//
//        doReturn(dataInicio).when(cobranca).getDataInicio();
//        doReturn(dataFim).when(cobranca).getDataFim();
//        doReturn(financeira).when(cobranca).getFinanceira();
//
//        PeriodoRelatorioSituacaoBoletoMensalPrResponse consultarBoleto = mock(PeriodoRelatorioSituacaoBoletoMensalPrResponse.class);
//        doReturn(null).when(consultarBoleto).getRegistradoras();
//
//        doReturn(Arrays.array(consultarBoleto)).when(wsDetranPrRestClient).consultarCobrancas(eq(dataInicio), eq(dataFim), eq(financeira));
//
//        BigDecimal resultado = calculadoraCobrancaPR.getValorBoletoDetran(cobranca);
//        assertEquals(BigDecimal.ZERO, resultado);
//    }
//
//    @Test
//    void test_getValorBoletoDetran_valorExistente_deveRetornarValor() throws ServiceException {
//        Cobranca cobranca = mock(Cobranca.class);
//        Date dataInicio = mock(Date.class);
//        Date dataFim = mock(Date.class);
//        Financeira financeira = mock(Financeira.class);
//
//        doReturn(dataInicio).when(cobranca).getDataInicio();
//        doReturn(dataFim).when(cobranca).getDataFim();
//        doReturn(financeira).when(cobranca).getFinanceira();
//
//        PeriodoRelatorioSituacaoBoletoMensalPrResponse consultarBoleto = mock(PeriodoRelatorioSituacaoBoletoMensalPrResponse.class);
//        RegistradoraRelatorioSituacaoBoletoMensalPrResponse registradora = mock(RegistradoraRelatorioSituacaoBoletoMensalPrResponse.class);
//        AgenteSituacaoBoletoMensalPrResponse agente = mock(AgenteSituacaoBoletoMensalPrResponse.class);
//        Double valorBoleto = 100.00;
//        when(agente.getValorBoleto()).thenReturn(valorBoleto);
//        when(registradora.getAgentes()).thenReturn(Arrays.array(agente));
//        when(consultarBoleto.getRegistradoras()).thenReturn(Arrays.array(registradora));
//
//        doReturn(Arrays.array(consultarBoleto)).when(wsDetranPrRestClient).consultarCobrancas(eq(dataInicio), eq(dataFim), eq(financeira));
//
//        BigDecimal resultado = calculadoraCobrancaPR.getValorBoletoDetran(cobranca);
//        assertEquals(BigDecimal.valueOf(valorBoleto), resultado);
//
//    }
}
