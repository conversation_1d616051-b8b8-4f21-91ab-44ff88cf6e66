package com.registrocontrato.registro.service.cobranca.calculadora.nordeste;

import com.registrocontrato.infra.entity.Financeira;
import com.registrocontrato.infra.entity.Uf;
import com.registrocontrato.registro.entity.*;
import com.registrocontrato.registro.service.CredenciamentoService;
import com.registrocontrato.registro.service.CupomDescontoService;
import com.registrocontrato.registro.service.cobranca.CobrancaService;
import com.registrocontrato.registro.service.cobranca.gravame.GravameService;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
class CalculadoraDeCobrancaCETest {

    public CalculadoraDeCobrancaCETest() {}

    private static class CalculadoraCobrancaCE extends CalculadoraDeCobrancaCE {
        public CalculadoraCobrancaCE(CobrancaService cobrancaService, CredenciamentoService credenciamentoService, CupomDescontoService cupomDescontoService, GravameService gravameService) {
            super(cobrancaService, credenciamentoService, cupomDescontoService, gravameService);
        }
    }

    @InjectMocks
    private CalculadoraCobrancaCE calculadoraCobrancaCE;
    @Mock
    private CobrancaService cobrancaService;
    @Mock
    private CredenciamentoService credenciamentoService;
    @Mock
    private CupomDescontoService cupomDescontoService;
    @Mock
    private GravameService gravameService;

    private AutoCloseable closeable;

    @BeforeEach
    void setup() {
        closeable = MockitoAnnotations.openMocks(this);
    }

    @AfterEach
    void shutdown() throws Exception {
        closeable.close();
    }

    @Test
    void test_definirValoresDaCobranca() {
        Cobranca cobranca = mock(Cobranca.class);
        Financeira financeira = mock(Financeira.class);
        Uf uf = Uf.CE;
        Date dataInicio = mock(Date.class);
        Date dataFim = mock(Date.class);

        doReturn(financeira).when(cobranca).getFinanceira();
        doReturn(uf).when(cobranca).getEstado();
        doReturn(dataInicio).when(cobranca).getDataInicio();
        doReturn(dataFim).when(cobranca).getDataFim();

        doReturn(0L)
                .doCallRealMethod()
                .when(cobranca).getQuantidadeRegistros();
        doReturn(BigDecimal.ZERO)
                .doCallRealMethod()
                .when(cobranca).getValorCobranca();
        doReturn(BigDecimal.ZERO)
                .doCallRealMethod()
                .when(cobranca).getValorCredenciada();
        doReturn(BigDecimal.ZERO)
                .doCallRealMethod()
                .when(cobranca).getValorDetran();
        doCallRealMethod().when(cobranca).getValorDetranPrincipal();
        doCallRealMethod().when(cobranca).getQuantidadePrincipal();
        doCallRealMethod().when(cobranca).setQuantidadeRegistros(anyLong());
        doCallRealMethod().when(cobranca).setValorCobranca(any(BigDecimal.class));
        doCallRealMethod().when(cobranca).setValorCredenciada(any(BigDecimal.class));
        doCallRealMethod().when(cobranca).setValorDetran(any(BigDecimal.class));
        doCallRealMethod().when(cobranca).setValorDetranPrincipal(any(BigDecimal.class));
        doCallRealMethod().when(cobranca).setQuantidadePrincipal(anyLong());

        Credenciamento credenciamento = mock(Credenciamento.class);
        doReturn(credenciamento).when(cobranca).getCredenciamento();

        PrecoComposto precoComposto = mock(PrecoComposto.class);
        Long quantidadeRegistrosPreco = 10L;
        BigDecimal valorTotal = new BigDecimal(10);
        BigDecimal valorCredenciada = new BigDecimal(9);
        BigDecimal valorDETRAN = new BigDecimal(1);
        doReturn("4").when(precoComposto).getValorParametro();
        doReturn(valorTotal).when(precoComposto).getValorTotal();
        doReturn(valorCredenciada).when(precoComposto).getValorCredenciada();
        doReturn(valorDETRAN).when(precoComposto).getValorDETRAN();

        PrecoComposto precoComposto1 = mock(PrecoComposto.class);
        Long quantidadeRegistrosPreco1 = 8L;
        BigDecimal valorTotal1 = new BigDecimal(100);
        BigDecimal valorCredenciada1 = new BigDecimal(90);
        BigDecimal valorDETRAN1 = new BigDecimal(10);
        doReturn("2").when(precoComposto1).getValorParametro();
        doReturn(valorTotal1).when(precoComposto1).getValorTotal();
        doReturn(valorCredenciada1).when(precoComposto1).getValorCredenciada();
        doReturn(valorDETRAN1).when(precoComposto1).getValorDETRAN();

        List<PrecoComposto> precosCompostos = new ArrayList<>();
        precosCompostos.add(precoComposto);
        precosCompostos.add(precoComposto1);

        doReturn(precosCompostos).when(credenciamento).getPrecosCompostos();

        doReturn(quantidadeRegistrosPreco).when(cobrancaService).countByFinanceirasVeiculos4Rodas(eq(financeira), eq(uf), eq(dataInicio), eq(dataFim));
        doReturn(quantidadeRegistrosPreco1).when(cobrancaService).countByFinanceirasVeiculos2Rodas(eq(financeira), eq(uf), eq(dataInicio), eq(dataFim));

        calculadoraCobrancaCE.definirValoresDaCobranca(cobranca, credenciamento);
        Long esperadoQuantidadeRegistros = quantidadeRegistrosPreco + quantidadeRegistrosPreco1;
        BigDecimal esperadoValorCobranca = new BigDecimal(900);
        BigDecimal esperadoValorCredenciada = new BigDecimal(810);
        BigDecimal esperadoValorDetran = new BigDecimal(90);

//        assertEquals(esperadoQuantidadeRegistros, cobranca.getQuantidadeRegistros());
//        assertEquals(esperadoQuantidadeRegistros, cobranca.getQuantidadePrincipal());
//        assertEquals(esperadoValorCobranca, cobranca.getValorCobranca());
//        assertEquals(esperadoValorCredenciada, cobranca.getValorCredenciada());
//        assertEquals(esperadoValorDetran, cobranca.getValorDetran());
//        assertEquals(esperadoValorDetran, cobranca.getValorDetranPrincipal());

    }

//    --------------------------------------
    @Test
    void test_aplicarDesconto() {
        Cobranca cobranca = mock(Cobranca.class);
        Financeira financeira = mock(Financeira.class);
        Uf uf = Uf.CE;
        Date dataInicio = mock(Date.class);
        Date dataFim = mock(Date.class);

        doReturn(financeira).when(cobranca).getFinanceira();
        doReturn(uf).when(cobranca).getEstado();
        doReturn(dataInicio).when(cobranca).getDataInicio();
        doReturn(dataFim).when(cobranca).getDataFim();

        doCallRealMethod().when(cobranca).getCupomDesconto();
        doCallRealMethod().when(cobranca).getValorDesconto();
        doReturn(new BigDecimal(900))
                .doCallRealMethod()
                .when(cobranca).getValorCobranca();
        doCallRealMethod().when(cobranca).setCupomDesconto(any(CupomDesconto.class));
        doCallRealMethod().when(cobranca).setValorDesconto(any(BigDecimal.class));
        doCallRealMethod().when(cobranca).setValorCobranca(any(BigDecimal.class));

        CupomDesconto cupom = mock(CupomDesconto.class);

        FaixaDesconto faixaDesconto = mock(FaixaDesconto.class);
        PrecoComposto precoComposto = mock(PrecoComposto.class);
        Long quantidadeRegistrosFaixa = 10L;
        BigDecimal percentual = new BigDecimal(10);

        doReturn(precoComposto).when(faixaDesconto).getPrecoComposto();
        doReturn("2").when(precoComposto).getValorParametro();
        doReturn(quantidadeRegistrosFaixa).when(cobrancaService).countByFinanceirasVeiculos2RodasOfCe(eq(financeira), eq(uf), eq(dataInicio), eq(dataFim));
        doReturn(percentual).when(faixaDesconto).getPercentual();

        FaixaDesconto faixaDesconto1 = mock(FaixaDesconto.class);
        PrecoComposto precoComposto1 = mock(PrecoComposto.class);
        Long quantidadeRegistrosFaixa1 = 8L;
        BigDecimal percentual1 = new BigDecimal(30);

        doReturn(precoComposto1).when(faixaDesconto1).getPrecoComposto();
        doReturn("4").when(precoComposto1).getValorParametro();
        doReturn(quantidadeRegistrosFaixa1).when(cobrancaService).countByFinanceirasVeiculos4RodasOfCe(eq(financeira), eq(uf), eq(dataInicio), eq(dataFim));
        doReturn(percentual1).when(faixaDesconto1).getPercentual();

        List<FaixaDesconto> faixasDesconto = new ArrayList<>();
        faixasDesconto.add(faixaDesconto);
        faixasDesconto.add(faixaDesconto1);

        doReturn(faixasDesconto).when(cupom).getFaixasDesconto();

        List<CupomDesconto> cuponsDisponiveis = new ArrayList<>();
        cuponsDisponiveis.add(cupom);


        calculadoraCobrancaCE.aplicarDesconto(cobranca, cuponsDisponiveis);
        BigDecimal valorDescontoEsperado = new BigDecimal(340);
        BigDecimal valorCobrancaEsperado = new BigDecimal(900-340);
//        assertEquals(cupom, cobranca.getCupomDesconto());
//        assertEquals(valorDescontoEsperado, cobranca.getValorDesconto());
//        assertEquals(valorCobrancaEsperado, cobranca.getValorCobranca());
    }
}
