package com.registrocontrato.registro.service.cobranca.boleto;

import com.registrocontrato.infra.entity.Financeira;
import com.registrocontrato.infra.entity.Uf;
import com.registrocontrato.registro.entity.Cobranca;
import com.registrocontrato.registro.service.cobranca.BoletoService;
import com.registrocontrato.seguranca.service.FinanceiraService;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.anyBoolean;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;
import static org.mockito.Mockito.doReturn;

@RunWith(MockitoJUnitRunner.class)
class CobrancaComBoletoDeReembolsoTest {

    private CobrancaComBoletoDeReembolso cobrancaComBoletoDeReembolso;

    @Mock
    private FinanceiraService financeiraService;
    @Mock
    private BoletoService boletoService;

    private final Uf ufImpl = Uf.DF;

    private AutoCloseable closeable;

    @BeforeEach
    void setup() {
        closeable = MockitoAnnotations.openMocks(this);
        cobrancaComBoletoDeReembolso = new CobrancaComBoletoDeReembolso(financeiraService, boletoService) {
            @Override
            public Uf getUf() {
                return ufImpl;
            }

            @Override
            public List<String> getCnpjFinanceiras() {
                return Collections.emptyList();
            }
        };
    }

    @AfterEach
    void shutdown() throws Exception {
        closeable.close();
    }

    @Test
    void test_emitirBoleto_deveGerarBoletoPlaceEBoletoReembolsoEAtualizarInformacoes() throws Exception {
        Cobranca cobrancaMock = mock(Cobranca.class);
        Financeira financeiraMock = mock(Financeira.class);
        doCallRealMethod().when(cobrancaMock).setTokenBoleto(anyString());
        doCallRealMethod().when(cobrancaMock).setNumeroDocumentoCredenciada(anyString());
        doCallRealMethod().when(cobrancaMock).setBoletoEmitido(anyBoolean());
        doCallRealMethod().when(cobrancaMock).setTokenReembolsoBoleto(anyString());
        doCallRealMethod().when(cobrancaMock).setNumeroDocumentoDetran(anyString());
        doCallRealMethod().when(cobrancaMock).setBoletoReembolsoEmitido(anyBoolean());

        doCallRealMethod().when(cobrancaMock).getTokenBoleto();
        doCallRealMethod().when(cobrancaMock).getNumeroDocumentoCredenciada();
        doCallRealMethod().when(cobrancaMock).getBoletoEmitido();
        doCallRealMethod().when(cobrancaMock).getTokenReembolsoBoleto();
        doCallRealMethod().when(cobrancaMock).getNumeroDocumentoDetran();
        doCallRealMethod().when(cobrancaMock).getBoletoReembolsoEmitido();

        BoletoResponse boletoResponse = mock(BoletoResponse.class);
        String token = "token1231";
        String numeroDocumento = "111092";
        doReturn(token).when(boletoResponse).getToken();
        doReturn(numeroDocumento).when(boletoResponse).getNumeroDocumento();

        BoletoResponse boletoResponseDetran = mock(BoletoResponse.class);
        String tokenReembolso = "tokenreembolso123";
        String numeroDocumentoReembolso = "99393";
        doReturn(tokenReembolso).when(boletoResponseDetran).getToken();
        doReturn(numeroDocumentoReembolso).when(boletoResponseDetran).getNumeroDocumento();

        BoletoResponse boletoResponseSng = mock(BoletoResponse.class);
        String tokenReembolsoSs = "tokenreembolso123";
        String numeroDocumentoReembolsoSs = "99393";
        doReturn(tokenReembolsoSs).when(boletoResponseSng).getToken();
        doReturn(numeroDocumentoReembolsoSs).when(boletoResponseSng).getNumeroDocumento();

        doReturn(boletoResponse).when(boletoService).criarBoletoPlace(eq(cobrancaMock));
        doReturn(boletoResponseDetran).when(boletoService).criarBoletoReembolso(eq(cobrancaMock));
        doReturn(boletoResponseSng).when(boletoService).criarBoletoSng(eq(cobrancaMock));
        doReturn(financeiraMock).when(cobrancaMock).getFinanceira();

        cobrancaComBoletoDeReembolso.emitirBoleto(cobrancaMock);

        assertEquals(token, cobrancaMock.getTokenBoleto());
        assertEquals(numeroDocumento, cobrancaMock.getNumeroDocumentoCredenciada());
        assertEquals(tokenReembolso, cobrancaMock.getTokenReembolsoBoleto());
        assertEquals(numeroDocumentoReembolso, cobrancaMock.getNumeroDocumentoDetran());
        assertTrue(cobrancaMock.getBoletoEmitido());
        assertTrue(cobrancaMock.getBoletoReembolsoEmitido());
    }
}
