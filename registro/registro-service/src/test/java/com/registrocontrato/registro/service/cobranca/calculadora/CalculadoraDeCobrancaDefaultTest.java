package com.registrocontrato.registro.service.cobranca.calculadora;

import com.registrocontrato.infra.entity.Financeira;
import com.registrocontrato.infra.entity.Uf;
import com.registrocontrato.infra.exception.ServiceException;
import com.registrocontrato.registro.entity.Cobranca;
import com.registrocontrato.registro.entity.Credenciamento;
import com.registrocontrato.registro.enums.TipoCobranca;
import com.registrocontrato.registro.service.CredenciamentoService;
import com.registrocontrato.registro.service.CupomDescontoService;
import com.registrocontrato.registro.service.cobranca.CobrancaService;
import com.registrocontrato.registro.service.cobranca.gravame.GravameService;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Collections;
import java.util.Date;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
class CalculadoraDeCobrancaDefaultTest {

    static class CalculadoraDeCobrancaDefaultImpl extends CalculadoraDeCobrancaDefault {

        public CalculadoraDeCobrancaDefaultImpl(CobrancaService cobrancaService, CredenciamentoService credenciamentoService, CupomDescontoService cupomDescontoService, GravameService gravameService) {
            super(cobrancaService, credenciamentoService, cupomDescontoService, gravameService);
        }

        @Override
        public Uf getUf() {
            return Uf.DF;
        }
    }

    private CalculadoraDeCobrancaDefaultImpl calculadoraDeCobrancaDefault;

    @Mock
    private CobrancaService cobrancaService;

    @Mock
    private CredenciamentoService credenciamentoService;

    @Mock
    private CupomDescontoService cupomDescontoService;

    @Mock
    private GravameService gravameService;

    private AutoCloseable closeable;

    CalculadoraDeCobrancaDefaultTest() {}

    @BeforeEach
    void setup() {
        closeable = MockitoAnnotations.openMocks(this);
        calculadoraDeCobrancaDefault = new CalculadoraDeCobrancaDefaultImpl(cobrancaService, credenciamentoService, cupomDescontoService, gravameService);
    }

    @AfterEach
    void shutdown() throws Exception {
        closeable.close();
    }

    @Test
    void test_calcularCobranca_deveBuscarCredenciamentoEDefinirValoresEAplicarDesconto() throws ServiceException {
        CalculadoraDeCobrancaDefaultImpl calculadoraDeCobrancaDefault1 = spy(calculadoraDeCobrancaDefault);

        Cobranca cobrancaMock = mock(Cobranca.class);
        doCallRealMethod().when(cobrancaMock).getCredenciamento();
        doCallRealMethod().when(cobrancaMock).setCredenciamento(any(Credenciamento.class));
        Credenciamento credenciamentoMock = mock(Credenciamento.class);

        doReturn(Collections.emptyList()).when(cupomDescontoService).findCuponsDisponiveis(eq(cobrancaMock));

        doReturn(Optional.of(credenciamentoMock)).when(calculadoraDeCobrancaDefault1).buscarCredenciamento(any(Cobranca.class));
        doNothing().when(calculadoraDeCobrancaDefault1).definirValoresDaCobranca(eq(cobrancaMock), eq(credenciamentoMock));
        doNothing().when(calculadoraDeCobrancaDefault1).aplicarDesconto(eq(cobrancaMock), anyList());

        calculadoraDeCobrancaDefault1.calcularCobranca(cobrancaMock);
        assertEquals(credenciamentoMock, cobrancaMock.getCredenciamento());
        verify(calculadoraDeCobrancaDefault1, times(1)).definirValoresDaCobranca(eq(cobrancaMock), eq(credenciamentoMock));
        verify(calculadoraDeCobrancaDefault1, times(1)).aplicarDesconto(eq(cobrancaMock), anyList());
    }

    @Test
    void test_calcularCobranca_deveLancarExcecao_casoBuscarCredenciamentoLanceExcecao() throws ServiceException {
        CalculadoraDeCobrancaDefaultImpl calculadoraDeCobrancaDefault1 = spy(calculadoraDeCobrancaDefault);
        Cobranca cobrancaMock = mock(Cobranca.class);

        doThrow(ServiceException.class).when(calculadoraDeCobrancaDefault1).buscarCredenciamento(eq(cobrancaMock));


        assertThrows(ServiceException.class, () -> {
            calculadoraDeCobrancaDefault1.calcularCobranca(cobrancaMock);
        });

        verify(calculadoraDeCobrancaDefault1, never()).definirValoresDaCobranca(eq(cobrancaMock), any(Credenciamento.class));
        verify(calculadoraDeCobrancaDefault1, never()).aplicarDesconto(eq(cobrancaMock), anyList());
        verify(cobrancaMock, never()).getCredenciamento();
    }

//    --------------------

    @Test
    void test_buscarQuantidadeContratosPrincipal_tipoCobrancaCONTRATO_deveContarPorContratos() {
        Cobranca cobranca = mock(Cobranca.class);
        Financeira financeira = mock(Financeira.class);
        Date dataInicio = mock(Date.class);
        Date dataFim = mock(Date.class);

        Credenciamento credenciamento = mock(Credenciamento.class);
        doReturn(TipoCobranca.CONTRATO).when(credenciamento).getTipoCobranca();

        doReturn(financeira).when(cobranca).getFinanceira();
        doReturn(dataInicio).when(cobranca).getDataInicio();
        doReturn(dataFim).when(cobranca).getDataFim();
        doReturn(Uf.DF).when(cobranca).getEstado();
        doReturn(credenciamento).when(cobranca).getCredenciamento();

        Long countContratos = 10L;

        doReturn(countContratos).when(cobrancaService).countContratosByFinanceiraAndUfRegistro(eq(financeira), eq(Uf.DF), eq(dataInicio), eq(dataFim));

        Long resultado = calculadoraDeCobrancaDefault.buscarQuantidadeContratosPrincipal(cobranca);

        assertEquals(resultado, countContratos);
        verify(cobrancaService, times(1)).countContratosByFinanceiraAndUfRegistro(eq(financeira), eq(Uf.DF), eq(dataInicio), eq(dataFim));
        verify(cobrancaService, never()).countVeiculosByFinanceiraAndUfRegistro(eq(financeira), eq(Uf.DF), eq(dataInicio), eq(dataFim));
    }

    @Test
    void test_buscarQuantidadeContratosPrincipal_tipoCobrancaCHASSI_deveContarPorVeiculos() {
        Cobranca cobranca = mock(Cobranca.class);
        Financeira financeira = mock(Financeira.class);
        Date dataInicio = mock(Date.class);
        Date dataFim = mock(Date.class);

        Credenciamento credenciamento = mock(Credenciamento.class);
        doReturn(TipoCobranca.CHASSI).when(credenciamento).getTipoCobranca();

        doReturn(financeira).when(cobranca).getFinanceira();
        doReturn(dataInicio).when(cobranca).getDataInicio();
        doReturn(dataFim).when(cobranca).getDataFim();
        doReturn(Uf.DF).when(cobranca).getEstado();
        doReturn(credenciamento).when(cobranca).getCredenciamento();

        Long countChassis = 10L;

        doReturn(countChassis).when(cobrancaService).countVeiculosByFinanceiraAndUfRegistro(eq(financeira), eq(Uf.DF), eq(dataInicio), eq(dataFim));

        Long resultado = calculadoraDeCobrancaDefault.buscarQuantidadeContratosPrincipal(cobranca);

        assertEquals(countChassis, resultado);
        verify(cobrancaService, never()).countContratosByFinanceiraAndUfRegistro(eq(financeira), eq(Uf.DF), eq(dataInicio), eq(dataFim));
        verify(cobrancaService, times(1)).countVeiculosByFinanceiraAndUfRegistro(eq(financeira), eq(Uf.DF), eq(dataInicio), eq(dataFim));
    }

//    000000000000000000000000000

    @Test
    void test_buscarQuantidadeContratosAditivos_tipoCobrancaCONTRATO_deveContarPorContratos() {
        Cobranca cobranca = mock(Cobranca.class);
        Financeira financeira = mock(Financeira.class);
        Uf estado = Uf.DF;
        Date dataInicio = new Date();
        Date dataFim = new Date();
        Credenciamento credenciamento = mock(Credenciamento.class);
        doReturn(credenciamento).when(cobranca).getCredenciamento();

        doReturn(financeira).when(cobranca).getFinanceira();
        doReturn(estado).when(cobranca).getEstado();
        doReturn(dataInicio).when(cobranca).getDataInicio();
        doReturn(dataFim).when(cobranca).getDataFim();
        doReturn(TipoCobranca.CONTRATO).when(credenciamento).getTipoCobranca();

        Long countContratos = 111L;
        doReturn(countContratos).when(cobrancaService)
                .countContratosByFinanceiraAndUfRegistroAditivo(eq(financeira), eq(estado), eq(dataInicio), eq(dataFim));

        Long resultado = calculadoraDeCobrancaDefault.buscarQuantidadeContratosAditivos(cobranca);

        assertEquals(countContratos, resultado);
        verify(cobrancaService, times(1))
                .countContratosByFinanceiraAndUfRegistroAditivo(eq(financeira), eq(estado), eq(dataInicio), eq(dataFim));
        verify(cobrancaService, never())
                .countVeiculosByFinanceiraAndUfRegistroAditivo(eq(financeira), eq(estado), eq(dataInicio), eq(dataFim));
    }

    @Test
    void test_buscarQuantidadeContratosAditivos_tipoCobrancaCHASSI_deveContarPorVeiculos() {
        Cobranca cobranca = mock(Cobranca.class);
        Financeira financeira = mock(Financeira.class);
        Uf estado = Uf.DF;
        Date dataInicio = new Date();
        Date dataFim = new Date();
        Credenciamento credenciamento = mock(Credenciamento.class);
        doReturn(credenciamento).when(cobranca).getCredenciamento();

        doReturn(financeira).when(cobranca).getFinanceira();
        doReturn(estado).when(cobranca).getEstado();
        doReturn(dataInicio).when(cobranca).getDataInicio();
        doReturn(dataFim).when(cobranca).getDataFim();
        doReturn(TipoCobranca.CHASSI).when(credenciamento).getTipoCobranca();

        Long countChassis = 111L;
        doReturn(countChassis).when(cobrancaService)
                .countVeiculosByFinanceiraAndUfRegistroAditivo(eq(financeira), eq(estado), eq(dataInicio), eq(dataFim));

        Long resultado = calculadoraDeCobrancaDefault.buscarQuantidadeContratosAditivos(cobranca);

        assertEquals(countChassis, resultado);
        verify(cobrancaService, never())
                .countContratosByFinanceiraAndUfRegistroAditivo(eq(financeira), eq(estado), eq(dataInicio), eq(dataFim));
        verify(cobrancaService, times(1))
                .countVeiculosByFinanceiraAndUfRegistroAditivo(eq(financeira), eq(estado), eq(dataInicio), eq(dataFim));
    }

//    -----------------------------

    @Test
    void test_buscaCredenciamento_credenciamentoExistente_deveRetonarCredenciamento() {
        Cobranca cobranca = mock(Cobranca.class);
        Credenciamento credenciamento = mock(Credenciamento.class);

        doReturn(Optional.of(credenciamento)).when(credenciamentoService).findByUfAndDataInicioLessThanEqualAndDataFimGreaterThanEqual(eq(cobranca));

        Credenciamento resultado = assertDoesNotThrow(() -> calculadoraDeCobrancaDefault.buscarCredenciamento(cobranca)).get();
        assertEquals(credenciamento, resultado);
    }
}
