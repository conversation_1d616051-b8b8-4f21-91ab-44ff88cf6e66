package com.registrocontrato.registro.service.cobranca.boleto;

import com.registrocontrato.infra.entity.Financeira;
import com.registrocontrato.infra.entity.SimNao;
import com.registrocontrato.infra.entity.SituacaoFinanceiraEstado;
import com.registrocontrato.infra.entity.Uf;
import com.registrocontrato.infra.exception.ServiceException;
import com.registrocontrato.registro.entity.Cobranca;
import com.registrocontrato.registro.service.cobranca.BoletoService;
import com.registrocontrato.seguranca.service.FinanceiraService;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
class CobrancaComBoletoDefaultTest {

    private CobrancaComBoletoDefault cobrancaComBoletoDefaultImpl;
    @Mock
    private BoletoService boletoService;
    @Mock
    private FinanceiraService financeiraService;

    private final Uf ufImpl = Uf.DF;

    private AutoCloseable closeable;

    @BeforeEach
    void setup() {
        closeable = MockitoAnnotations.openMocks(this);
        cobrancaComBoletoDefaultImpl = new CobrancaComBoletoDefault(boletoService, financeiraService) {
            @Override
            public Uf getUf() {
                return ufImpl;
            }

            @Override
            public List<String> getCnpjFinanceiras() {
                return Collections.emptyList();
            }
        };
    }

    @AfterEach
    void shutdown() throws Exception {
        closeable.close();
    }

    @Test
    void test_emitirBoleto_criarBoletoPlaceApresentaErro_naoDeveAtualizarDadosDaCobranca() throws Exception {
        Cobranca cobrancaMock = mock(Cobranca.class);

        doThrow(ServiceException.class).when(boletoService).criarBoletoPlace(eq(cobrancaMock));

        assertThrows(ServiceException.class, () -> cobrancaComBoletoDefaultImpl.emitirBoleto(cobrancaMock));
    }

    @Test
    void test_emitirBoleto_reembolsoLinhaDigitavelN_deveAtualizarInfosBoletoPlace_MasNaoAtualizarInfosBoletoReembolso() throws Exception {
        Cobranca cobrancaMock = mock(Cobranca.class);
        Financeira financeiraMock = mock(Financeira.class);
        doReturn(financeiraMock).when(cobrancaMock).getFinanceira();
        doCallRealMethod().when(cobrancaMock).setTokenBoleto(anyString());
        doCallRealMethod().when(cobrancaMock).setNumeroDocumentoCredenciada(anyString());
        doCallRealMethod().when(cobrancaMock).setBoletoEmitido(anyBoolean());
        doCallRealMethod().when(cobrancaMock).getTokenBoleto();
        doCallRealMethod().when(cobrancaMock).getNumeroDocumentoCredenciada();
        doCallRealMethod().when(cobrancaMock).getBoletoEmitido();

        BoletoResponse boletoResponse = mock(BoletoResponse.class);
        String token = "token1231";
        String numeroDocumento = "111092";
        doReturn(token).when(boletoResponse).getToken();
        doReturn(numeroDocumento).when(boletoResponse).getNumeroDocumento();

        BoletoResponse boletoResponses = mock(BoletoResponse.class);
        String tokens = "token1231";
        String numeroDocumentos = "111092";
        doReturn(tokens).when(boletoResponses).getToken();
        doReturn(numeroDocumentos).when(boletoResponses).getNumeroDocumento();

        doReturn(boletoResponse).when(boletoService).criarBoletoPlace(eq(cobrancaMock));
        doReturn(boletoResponses).when(boletoService).criarBoletoSng(eq(cobrancaMock));

        SituacaoFinanceiraEstado sfeMock = mock(SituacaoFinanceiraEstado.class);
        doReturn(SimNao.N).when(sfeMock).getReembolsoLinhaDigitavel();
        doReturn(sfeMock).when(financeiraService).findSituacaoFinanceiraEstado(eq(ufImpl), eq(financeiraMock));

        cobrancaComBoletoDefaultImpl.emitirBoleto(cobrancaMock);
        verify(boletoService, never()).criarBoletoReembolso(eq(cobrancaMock));

        assertEquals(token, cobrancaMock.getTokenBoleto());
        assertEquals(numeroDocumento, cobrancaMock.getNumeroDocumentoCredenciada());
        assertTrue(cobrancaMock.getBoletoEmitido());
    }

    @Test
    void test_emitirBoleto_reembolsoLinhaDigitavelS_deveAtualizarInfosBoletoReembolso() throws Exception {
        Cobranca cobrancaMock = mock(Cobranca.class);
        Financeira financeiraMock = mock(Financeira.class);
        doReturn(financeiraMock).when(cobrancaMock).getFinanceira();

        doCallRealMethod().when(cobrancaMock).setTokenBoleto(anyString());
        doCallRealMethod().when(cobrancaMock).setNumeroDocumentoCredenciada(anyString());
        doCallRealMethod().when(cobrancaMock).setBoletoEmitido(anyBoolean());
        doCallRealMethod().when(cobrancaMock).setTokenReembolsoBoleto(anyString());
        doCallRealMethod().when(cobrancaMock).setNumeroDocumentoDetran(anyString());
        doCallRealMethod().when(cobrancaMock).setBoletoReembolsoEmitido(anyBoolean());

        doCallRealMethod().when(cobrancaMock).getTokenBoleto();
        doCallRealMethod().when(cobrancaMock).getNumeroDocumentoCredenciada();
        doCallRealMethod().when(cobrancaMock).getBoletoEmitido();
        doCallRealMethod().when(cobrancaMock).getTokenReembolsoBoleto();
        doCallRealMethod().when(cobrancaMock).getNumeroDocumentoDetran();
        doCallRealMethod().when(cobrancaMock).getBoletoReembolsoEmitido();

        BoletoResponse boletoResponse = mock(BoletoResponse.class);
        String token = "token1231";
        String numeroDocumento = "111092";
        doReturn(token).when(boletoResponse).getToken();
        doReturn(numeroDocumento).when(boletoResponse).getNumeroDocumento();

        BoletoResponse boletoResponseDetran = mock(BoletoResponse.class);
        String tokenReembolso = "tokenreembolso123";
        String numeroDocumentoReembolso = "99393";
        doReturn(tokenReembolso).when(boletoResponseDetran).getToken();
        doReturn(numeroDocumentoReembolso).when(boletoResponseDetran).getNumeroDocumento();

        BoletoResponse boletoResponses = mock(BoletoResponse.class);
        String tokens = "token1231";
        String numeroDocumentos = "111092";
        doReturn(tokens).when(boletoResponses).getToken();
        doReturn(numeroDocumentos).when(boletoResponses).getNumeroDocumento();

        doReturn(boletoResponse).when(boletoService).criarBoletoPlace(eq(cobrancaMock));
        doReturn(boletoResponseDetran).when(boletoService).criarBoletoReembolso(eq(cobrancaMock));
        doReturn(boletoResponses).when(boletoService).criarBoletoSng(eq(cobrancaMock));

        SituacaoFinanceiraEstado sfeMock = mock(SituacaoFinanceiraEstado.class);
        doReturn(SimNao.S).when(sfeMock).getReembolsoLinhaDigitavel();
        doReturn(sfeMock).when(financeiraService).findSituacaoFinanceiraEstado(eq(ufImpl), eq(financeiraMock));

        cobrancaComBoletoDefaultImpl.emitirBoleto(cobrancaMock);

        assertEquals(token, cobrancaMock.getTokenBoleto());
        assertEquals(numeroDocumento, cobrancaMock.getNumeroDocumentoCredenciada());
        assertEquals(tokenReembolso, cobrancaMock.getTokenReembolsoBoleto());
        assertEquals(numeroDocumentoReembolso, cobrancaMock.getNumeroDocumentoDetran());
        assertTrue(cobrancaMock.getBoletoEmitido());
        assertTrue(cobrancaMock.getBoletoReembolsoEmitido());
    }
}
