package com.registrocontrato.registro.service.cobranca.boleto.sudeste;

import com.registrocontrato.infra.entity.Financeira;
import com.registrocontrato.infra.util.PlaceconUtil;
import com.registrocontrato.registro.entity.Cobranca;
import com.registrocontrato.registro.service.cobranca.BoletoService;
import com.registrocontrato.registro.service.cobranca.boleto.BoletoResponse;
import com.registrocontrato.seguranca.service.FinanceiraService;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;

import java.time.ZoneId;
import java.util.Date;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
class BoletoDeCobrancaSPTest {

    public BoletoDeCobrancaSPTest() {}

    static class BoletoCobrancaSP extends BoletoDeCobrancaSP {
        public BoletoCobrancaSP(FinanceiraService financeiraService, BoletoService boletoService) {
            super(financeiraService, boletoService);
        }
    }

    @InjectMocks
    private BoletoCobrancaSP boletoCobrancaSP;
    @Mock
    private FinanceiraService financeiraService;
    @Mock
    private BoletoService boletoService;

    private AutoCloseable closeable;

    @BeforeEach
    void setup() {
        closeable = MockitoAnnotations.openMocks(this);
    }

    @AfterEach
    void shutdown() throws Exception {
        closeable.close();
    }

    @Test
    void test_emitirBoleto_financeiraDesenvolveSP_dataVencimentoBoletoDeveSer32DiasNaFrente() throws Exception {
        Cobranca cobranca = mock(Cobranca.class);
        Financeira financeira = mock(Financeira.class);
        String documento = "10663610000129";

        doCallRealMethod().when(cobranca).setDataVencimentoBoleto(any(Date.class));
        doCallRealMethod().when(cobranca).getDataVencimentoBoleto();
        doReturn(financeira).when(cobranca).getFinanceira();
        doReturn(documento).when(financeira).getDocumento();

        BoletoResponse retornoBoletoPlace = mock(BoletoResponse.class);
        BoletoResponse retornoBoletoReembolso = mock(BoletoResponse.class);
        BoletoResponse retornoBoletoSng = mock(BoletoResponse.class);

        doReturn(retornoBoletoPlace).when(boletoService).criarBoletoPlace(eq(cobranca));
        doReturn(retornoBoletoReembolso).when(boletoService).criarBoletoReembolso(eq(cobranca));
        doReturn(retornoBoletoSng).when(boletoService).criarBoletoSng(eq(cobranca));

        BoletoCobrancaSP boletoCobrancaSP1 = spy(boletoCobrancaSP);
        doReturn(cobranca).when(boletoCobrancaSP1).atualizarCobranca(eq(cobranca), eq(retornoBoletoPlace), eq(retornoBoletoReembolso), eq(retornoBoletoSng));

        boletoCobrancaSP1.emitirBoleto(cobranca);
        Date dataEsperada = PlaceconUtil.converteLocalDateEmDate(new Date().toInstant().atZone(ZoneId.systemDefault()).toLocalDate().plusDays(32));
        assertEquals(dataEsperada, cobranca.getDataVencimentoBoleto());
    }

    @Test
    void test_emitirBoleto_financeiraNaoEDesenvolveSP_naoDeveAlterarDataVencimentoBoleto() throws Exception {
        Cobranca cobranca = mock(Cobranca.class);
        Financeira financeira = mock(Financeira.class);
        String documento = "1819200";

        doReturn(financeira).when(cobranca).getFinanceira();
        doReturn(documento).when(financeira).getDocumento();

        BoletoResponse retornoBoletoPlace = mock(BoletoResponse.class);
        BoletoResponse retornoBoletoReembolso = mock(BoletoResponse.class);
        BoletoResponse retornoBoletoSng = mock(BoletoResponse.class);

        doReturn(retornoBoletoPlace).when(boletoService).criarBoletoPlace(eq(cobranca));
        doReturn(retornoBoletoReembolso).when(boletoService).criarBoletoReembolso(eq(cobranca));
        doReturn(retornoBoletoSng).when(boletoService).criarBoletoSng(eq(cobranca));

        BoletoCobrancaSP boletoCobrancaSP1 = spy(boletoCobrancaSP);
        doReturn(cobranca).when(boletoCobrancaSP1).atualizarCobranca(eq(cobranca), eq(retornoBoletoPlace), eq(retornoBoletoReembolso), eq(retornoBoletoSng));

        boletoCobrancaSP1.emitirBoleto(cobranca);
        verify(cobranca, never()).setDataVencimentoBoleto(any(Date.class));
    }
}
