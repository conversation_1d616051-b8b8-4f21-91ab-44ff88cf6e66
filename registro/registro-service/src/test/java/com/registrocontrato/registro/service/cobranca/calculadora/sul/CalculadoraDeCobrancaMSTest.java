package com.registrocontrato.registro.service.cobranca.calculadora.sul;

import com.registrocontrato.infra.entity.Financeira;
import com.registrocontrato.infra.exception.ServiceException;
import com.registrocontrato.infra.util.PlaceconUtil;
import com.registrocontrato.registro.entity.Cobranca;
import com.registrocontrato.registro.service.CredenciamentoService;
import com.registrocontrato.registro.service.CupomDescontoService;
import com.registrocontrato.registro.service.detran.ms.rest.WsDetranMS;
import com.registrocontrato.registro.service.detran.ms.client.BoletoCobrancaDTO;
import com.registrocontrato.registro.service.cobranca.CobrancaService;
import com.registrocontrato.registro.service.cobranca.gravame.GravameService;
import org.assertj.core.util.Arrays;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.math.BigDecimal;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

class CalculadoraDeCobrancaMSTest {

    public CalculadoraDeCobrancaMSTest() {
    }

    static class CalculadoraCobrancaMS extends CalculadoraDeCobrancaMS {
        public CalculadoraCobrancaMS(CobrancaService cobrancaService, CredenciamentoService credenciamentoService, CupomDescontoService cupomDescontoService, GravameService gravameService, WsDetranMS wsDetranMsRestClient) {
            super(cobrancaService, credenciamentoService, wsDetranMsRestClient, cupomDescontoService, gravameService);
        }
    }

    @InjectMocks
    private CalculadoraCobrancaMS calculadoraCobrancaMS;
    @Mock
    private CobrancaService cobrancaService;
    @Mock
    private CredenciamentoService credenciamentoService;
    @Mock
    private CupomDescontoService cupomDescontoService;
    @Mock
    private GravameService gravameService;
    @Mock
    private WsDetranMS wsDetranMsRestClient;

    private AutoCloseable closeable;

    @BeforeEach
    void setup() {
        closeable = MockitoAnnotations.openMocks(this);
    }

    @AfterEach
    void shutdown() throws Exception {
        closeable.close();
    }

    @Test
    void test_getValorBoletoDetran_erroWebService_deveRetornarZero() throws ServiceException {
        Cobranca cobranca = mock(Cobranca.class);
        Financeira financeira = mock(Financeira.class);
        String documento = "22828";

        doReturn(financeira).when(cobranca).getFinanceira();
        doReturn(documento).when(financeira).getDocumento();

        doThrow(ServiceException.class).when(wsDetranMsRestClient).consultarBoleto(eq(documento));

        BigDecimal resultado = calculadoraCobrancaMS.getValorBoletoDetran(cobranca);
        assertEquals(BigDecimal.ZERO, resultado);

    }

    @Test
    void test_getValorBoletoDetran_saidaNull_deveRetornarZero() throws ServiceException {
        Cobranca cobranca = mock(Cobranca.class);
        Financeira financeira = mock(Financeira.class);
        String documento = "22828";

        doReturn(financeira).when(cobranca).getFinanceira();
        doReturn(documento).when(financeira).getDocumento();

        doReturn(null).when(wsDetranMsRestClient).consultarBoleto(eq(documento));

        BigDecimal resultado = calculadoraCobrancaMS.getValorBoletoDetran(cobranca);
        assertEquals(BigDecimal.ZERO, resultado);
    }

    @Test
    void test_getValorBoletoDetran_valorNull_deveRetornarZero() throws ServiceException {
        Cobranca cobranca = mock(Cobranca.class);
        Financeira financeira = mock(Financeira.class);
        String documento = "22828";

        doReturn(financeira).when(cobranca).getFinanceira();
        doReturn(documento).when(financeira).getDocumento();

        BoletoCobrancaDTO consultarBoleto = mock(BoletoCobrancaDTO.class);
        doReturn(null).when(consultarBoleto).getTotalGeral();

        doReturn(Arrays.array(consultarBoleto)).when(wsDetranMsRestClient).consultarBoleto(eq(documento));

        BigDecimal resultado = calculadoraCobrancaMS.getValorBoletoDetran(cobranca);
        assertEquals(BigDecimal.ZERO, resultado);
    }

    @Test
    void test_getValorBoletoDetran_valorExistente_deveRetonarValor() throws ServiceException {
        Cobranca cobranca = mock(Cobranca.class);
        Financeira financeira = mock(Financeira.class);
        String documento = "22828";

        doReturn(financeira).when(cobranca).getFinanceira();
        doReturn(documento).when(financeira).getDocumento();

        BoletoCobrancaDTO consultarBoleto = mock(BoletoCobrancaDTO.class);
        String totalGeral = "10.000,90";
        doReturn(totalGeral).when(consultarBoleto).getTotalGeral();

        doReturn(Arrays.array(consultarBoleto)).when(wsDetranMsRestClient).consultarBoleto(eq(documento));

        BigDecimal resultado = calculadoraCobrancaMS.getValorBoletoDetran(cobranca);
        assertEquals(PlaceconUtil.getValorBigDecimal("10000.90"), resultado);
    }

}
