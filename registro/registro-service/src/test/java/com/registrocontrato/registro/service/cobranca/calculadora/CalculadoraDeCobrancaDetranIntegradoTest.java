package com.registrocontrato.registro.service.cobranca.calculadora;

import com.registrocontrato.infra.entity.Uf;
import com.registrocontrato.infra.exception.ServiceException;
import com.registrocontrato.registro.entity.Cobranca;
import com.registrocontrato.registro.entity.Credenciamento;
import com.registrocontrato.registro.service.CredenciamentoService;
import com.registrocontrato.registro.service.CupomDescontoService;
import com.registrocontrato.registro.service.cobranca.CobrancaService;
import com.registrocontrato.registro.service.cobranca.gravame.GravameService;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.math.BigDecimal;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.*;

class CalculadoraDeCobrancaDetranIntegradoTest {

    public CalculadoraDeCobrancaDetranIntegradoTest() {}

    private final static BigDecimal VALOR_BOLETO_DETRAN = new BigDecimal(100);

    static class CalculadoraCobrancaDetranIntegrado extends CalculadoraDeCobrancaDetranIntegrado {

        public CalculadoraCobrancaDetranIntegrado(CobrancaService cobrancaService, CredenciamentoService credenciamentoService, CupomDescontoService cupomDescontoService, GravameService gravameService) {
            super(cobrancaService, credenciamentoService, cupomDescontoService, gravameService);
        }

        @Override
        protected BigDecimal getValorBoletoDetran(Cobranca cobranca) {
            return VALOR_BOLETO_DETRAN;
        }

        @Override
        public Uf getUf() {
            return Uf.MS;
        }

        @Override
        protected Long buscarQuantidadeContratosAditivos(Cobranca cobranca) {
            return 100L;
        }

        @Override
        protected Long buscarQuantidadeContratosPrincipal(Cobranca cobranca) {
            return 101L;
        }
    }

    @InjectMocks
    private CalculadoraCobrancaDetranIntegrado calculadoraCobrancaBoletoIntegrado;
    @Mock
    private CobrancaService cobrancaService;
    @Mock
    private CredenciamentoService credenciamentoService;
    @Mock
    private CupomDescontoService cupomDescontoService;
    @Mock
    private GravameService gravameService;

    private AutoCloseable closeable;

    @BeforeEach
    void setup() {
        closeable = MockitoAnnotations.openMocks(this);
    }

    @AfterEach
    void shutdown() throws Exception {
        closeable.close();
    }

    @Test
    void test_definirValoresCobranca() throws ServiceException {
        Cobranca cobranca = mock(Cobranca.class);

        doCallRealMethod().when(cobranca).getQuantidadeRegistros();
        doCallRealMethod().when(cobranca).getQuantidadeAditivo();
        doCallRealMethod().when(cobranca).getQuantidadePrincipal();
        doCallRealMethod().when(cobranca).getValorDetranPrincipal();
        doCallRealMethod().when(cobranca).getValorDetranAditivo();
        doCallRealMethod().when(cobranca).getValorCredenciada();
        doCallRealMethod().when(cobranca).getValorDetran();
        doCallRealMethod().when(cobranca).getValorCobranca();
        doCallRealMethod().when(cobranca).setQuantidadeRegistros(anyLong());
        doCallRealMethod().when(cobranca).setQuantidadeAditivo(anyLong());
        doCallRealMethod().when(cobranca).setQuantidadePrincipal(anyLong());
        doCallRealMethod().when(cobranca).setValorDetranPrincipal(any(BigDecimal.class));
        doCallRealMethod().when(cobranca).setValorDetranAditivo(any(BigDecimal.class));
        doCallRealMethod().when(cobranca).setValorCredenciada(any(BigDecimal.class));
        doCallRealMethod().when(cobranca).setValorDetran(any(BigDecimal.class));
        doCallRealMethod().when(cobranca).setValorCobranca(any(BigDecimal.class));

        doCallRealMethod().when(cobranca).setValorCobrancaInformadoDetran(any(BigDecimal.class));
        doCallRealMethod().when(cobranca).getValorCobrancaInformadoDetran();

        Credenciamento credenciamento = mock(Credenciamento.class);
        doReturn(new BigDecimal(10)).when(credenciamento).getValorDETRAN();
        doReturn(new BigDecimal(10)).when(credenciamento).getValorAditivo();
        doReturn(new BigDecimal(10)).when(credenciamento).getValorCredenciada();

        calculadoraCobrancaBoletoIntegrado.definirValoresDaCobranca(cobranca, credenciamento);
        assertEquals(VALOR_BOLETO_DETRAN, cobranca.getValorCobrancaInformadoDetran());
    }

}
