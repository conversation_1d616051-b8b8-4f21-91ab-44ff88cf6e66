package com.registrocontrato.registro.service.cobranca.calculadora.nordeste;

import com.registrocontrato.infra.entity.Financeira;
import com.registrocontrato.infra.exception.ServiceException;
import com.registrocontrato.registro.entity.Cobranca;
import com.registrocontrato.registro.service.CredenciamentoService;
import com.registrocontrato.registro.service.CupomDescontoService;
import com.registrocontrato.registro.service.MigracaoService;
import com.registrocontrato.registro.service.detran.pi.client.ConsultaBoletoPiResponse;
import com.registrocontrato.registro.service.detran.pi.rest.WsDetranPiRest;
import com.registrocontrato.registro.service.cobranca.CobrancaService;
import com.registrocontrato.registro.service.cobranca.gravame.GravameService;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;

import java.math.BigDecimal;
import java.util.Date;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
class CalculadoraDeCobrancaPITest {

    public CalculadoraDeCobrancaPITest() {}

    static class CalculadoraCobrancaPI extends CalculadoraDeCobrancaPI {
        public CalculadoraCobrancaPI(
                CobrancaService cobrancaService,
                CredenciamentoService credenciamentoService,
                GravameService gravameService,
                CupomDescontoService cupomDescontoService,
                WsDetranPiRest wsDetranPiRest,
                MigracaoService migracaoService) {
            super(cobrancaService, credenciamentoService, cupomDescontoService, wsDetranPiRest, gravameService, migracaoService);
        }
    }

    @InjectMocks
    private CalculadoraCobrancaPI calculadoraCobrancaPI;
    @Mock
    private CobrancaService cobrancaService;
    @Mock
    private CredenciamentoService credenciamentoService;
    @Mock
    private CupomDescontoService cupomDescontoService;
    @Mock
    private GravameService gravameService;
    @Mock
    private WsDetranPiRest wsDetranPiRest;

    private AutoCloseable closeable;

    @BeforeEach
    void setup() {
        closeable = MockitoAnnotations.openMocks(this);
    }

    @AfterEach
    void shutdown() throws Exception {
        closeable.close();
    }

    @Test
    void test_getValorBoletoDetran_erroWebService_deveRetornarZero() throws ServiceException {
        Cobranca cobranca = mock(Cobranca.class);
        Financeira financeira = mock(Financeira.class);
        String documento = "22828";
        Date dataInicio = mock(Date.class);

        doReturn(financeira).when(cobranca).getFinanceira();
        doReturn(dataInicio).when(cobranca).getDataInicio();
        doReturn(documento).when(financeira).getDocumento();

        doThrow(ServiceException.class).when(wsDetranPiRest).consultarBoleto(eq(dataInicio), eq(documento));

        BigDecimal resultado = calculadoraCobrancaPI.getValorBoletoDetran(cobranca);
        assertEquals(BigDecimal.ZERO, resultado);

    }

    @Test
    void test_getValorBoletoDetran_saidaNull_deveRetornarZero() throws ServiceException {
        Cobranca cobranca = mock(Cobranca.class);
        Financeira financeira = mock(Financeira.class);
        String documento = "22828";
        Date dataInicio = mock(Date.class);

        doReturn(financeira).when(cobranca).getFinanceira();
        doReturn(dataInicio).when(cobranca).getDataInicio();
        doReturn(documento).when(financeira).getDocumento();

        doReturn(null).when(wsDetranPiRest).consultarBoleto(eq(dataInicio), eq(documento));

        BigDecimal resultado = calculadoraCobrancaPI.getValorBoletoDetran(cobranca);
        assertEquals(BigDecimal.ZERO, resultado);
    }

    @Test
    void test_getValorBoletoDetran_valorNull_deveRetornarZero() throws ServiceException {
        Cobranca cobranca = mock(Cobranca.class);
        Financeira financeira = mock(Financeira.class);
        String documento = "22828";
        Date dataInicio = mock(Date.class);

        doReturn(financeira).when(cobranca).getFinanceira();
        doReturn(dataInicio).when(cobranca).getDataInicio();
        doReturn(documento).when(financeira).getDocumento();

        ConsultaBoletoPiResponse consultarBoleto = mock(ConsultaBoletoPiResponse.class);
        doReturn(null).when(consultarBoleto).getValor();

        doReturn(consultarBoleto).when(wsDetranPiRest).consultarBoleto(eq(dataInicio), eq(documento));

        BigDecimal resultado = calculadoraCobrancaPI.getValorBoletoDetran(cobranca);
        assertEquals(BigDecimal.ZERO, resultado);
    }

//    @Test
//    void test_getValorBoletoDetran_valorExistente_deveRetonarValor() throws ServiceException {
//        Cobranca cobranca = mock(Cobranca.class);
//        Financeira financeira = mock(Financeira.class);
//        String documento = "22828";
//        Date dataInicio = mock(Date.class);
//
//        doReturn(financeira).when(cobranca).getFinanceira();
//        doReturn(dataInicio).when(cobranca).getDataInicio();
//        doReturn(documento).when(financeira).getDocumento();
//
//        ConsultaBoletoPiResponse consultarBoleto = mock(ConsultaBoletoPiResponse.class);
//        Double valor = 100.00;
//        doReturn(valor).when(consultarBoleto).getValor();
//
//        doReturn(consultarBoleto).when(wsDetranPiRest).consultarBoleto(eq(dataInicio), eq(documento));
//
//        BigDecimal resultado = calculadoraCobrancaPI.getValorBoletoDetran(cobranca);
//        assertEquals(BigDecimal.valueOf(valor), resultado);
//    }
}
