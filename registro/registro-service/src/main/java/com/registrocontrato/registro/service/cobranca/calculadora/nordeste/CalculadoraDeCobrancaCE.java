package com.registrocontrato.registro.service.cobranca.calculadora.nordeste;

import com.registrocontrato.infra.entity.SimNao;
import com.registrocontrato.infra.entity.Uf;
import com.registrocontrato.infra.exception.ServiceException;
import com.registrocontrato.infra.util.PlaceconUtil;
import com.registrocontrato.registro.entity.*;
import com.registrocontrato.registro.service.CredenciamentoService;
import com.registrocontrato.registro.service.CupomDescontoService;
import com.registrocontrato.registro.service.cobranca.CobrancaService;
import com.registrocontrato.registro.service.cobranca.calculadora.CalculadoraDeCobrancaDefault;
import com.registrocontrato.registro.service.cobranca.gravame.GravameService;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;

@Component
public class CalculadoraDeCobrancaCE extends CalculadoraDeCobrancaDefault {

    public CalculadoraDeCobrancaCE(@Lazy CobrancaService cobrancaService, CredenciamentoService credenciamentoService, CupomDescontoService cupomDescontoService, GravameService gravameService) {
        super(cobrancaService, credenciamentoService, cupomDescontoService, gravameService);
    }

    @Override
    protected void definirValoresDaCobranca(Cobranca cobranca, Credenciamento credenciamento) {
        getValuesInPrecoComposto(cobranca);
        cobranca.setValorDetranPrincipal(cobranca.getValorDetran());
        cobranca.setQuantidadePrincipal(cobranca.getQuantidadeRegistros());
    }

    @Override
    protected void aplicarDesconto(Cobranca c, List<CupomDesconto> cuponsDisponiveis) {
        if (!PlaceconUtil.isListaVaziaOuNula(cuponsDisponiveis)) {
            BigDecimal desconto = new BigDecimal(0l);
            for (CupomDesconto cupom : cuponsDisponiveis) {
                for (FaixaDesconto faixaDesconto : cupom.getFaixasDesconto()) {
                    if (Objects.nonNull(c.getId()))
                        desconto = desconto.add(calculaDescontoCeara(c, faixaDesconto, SimNao.S));
                    else
                        desconto = desconto.add(calculaDescontoCeara(c, faixaDesconto));
                }
            }
            c.setValorDesconto(desconto);
            c.setValorCobranca(c.getValorCobranca().subtract(c.getValorDesconto()));
        }
    }

    private void getValuesInPrecoComposto(Cobranca cobranca) {
        cobranca.setQuantidadeRegistros(0l);
        cobranca.setQuantidadePrincipal(0l);
        cobranca.getCredenciamento().getPrecosCompostos().forEach(precoComposto -> {
            Long quantidadeRegistros = getQuantidadeRegistros(cobranca, precoComposto);

            logger.info("Quantiade de registros CE: " + quantidadeRegistros);

            BigDecimal valorTotal = precoComposto.getValorTotal();
            BigDecimal valorCredenciada = precoComposto.getValorCredenciada();
            BigDecimal valorDETRAN = precoComposto.getValorDETRAN();

            cobranca.setQuantidadeRegistros(cobranca.getQuantidadeRegistros() + quantidadeRegistros);
            cobranca.setValorCobranca(cobranca.getValorCobranca().add(valorTotal.multiply(new BigDecimal(quantidadeRegistros))));
            cobranca.setValorCredenciada(cobranca.getValorCredenciada().add(valorCredenciada.multiply(new BigDecimal(quantidadeRegistros))));
            cobranca.setValorDetran(cobranca.getValorDetran().add(valorDETRAN.multiply(new BigDecimal(quantidadeRegistros))));
        });
    }

    private Long getQuantidadeRegistros(Cobranca cobranca, PrecoComposto p) {
        if (Objects.nonNull(cobranca.getId())) {
            if (p.getValorParametro().equals("2")) {
                return cobrancaService.count2RodasByCobrancaAndUf(cobranca);
            }
            return cobrancaService.count4RodasByCobrancaAndUf(cobranca);
        } else {
            if (p.getValorParametro().equals("2")) {
                return cobrancaService.countByFinanceirasVeiculos2Rodas(cobranca.getFinanceira(), cobranca.getEstado(), cobranca.getDataInicio(), cobranca.getDataFim());
            }
            return cobrancaService.countByFinanceirasVeiculos4Rodas(cobranca.getFinanceira(), cobranca.getEstado(), cobranca.getDataInicio(), cobranca.getDataFim());
        }
    }


    @Override
    protected void definirValoresDaCobrancaUnificada(Cobranca cobranca, Credenciamento credenciamento) throws ServiceException {
        cobranca.setQuantidadeRegistros(buscarQuantidadeContratosPrincipalUnificada(cobranca));
        cobranca.setQuantidadeAditivo(buscarQuantidadeContratosAditivosUnificada(cobranca));
        cobranca.setQuantidadePrincipal(cobranca.getQuantidadeRegistros() - cobranca.getQuantidadeAditivo());
        cobranca.setValorDetranAditivo(credenciamento.getValorAditivo().multiply(new BigDecimal(cobranca.getQuantidadeAditivo())));
        getValuesInPrecoComposto(cobranca);
    }

    private BigDecimal calculaDescontoCeara(Cobranca c, FaixaDesconto faixaDesconto, SimNao unificada) {
        if (unificada == SimNao.S) {
            BigDecimal desconto = new BigDecimal(0l);
            Long quantidadeDuasRodas = 0l;
            Long quantidadeQuatroRodas = 0l;
            if (faixaDesconto.getPrecoComposto().getValorParametro().equals("2")) {
                quantidadeDuasRodas = cobrancaService.count2RodasByCobrancaAndUf(c);
                desconto = (faixaDesconto.getPercentual().multiply(new BigDecimal(quantidadeDuasRodas)));
            } else if (faixaDesconto.getPrecoComposto().getValorParametro().equals("4")) {
                quantidadeQuatroRodas = cobrancaService.count4RodasByCobrancaAndUf(c);
                desconto = (faixaDesconto.getPercentual().multiply(new BigDecimal(quantidadeQuatroRodas)));
            } else {
                try {
                    throw new ServiceException("Os parâmetros cadastrados no credenciamento deste Estado devem ser 2 ou 4");
                } catch (ServiceException e) {
                    logger.error("Erros ao lançar exceção.");
                }
            }
            return desconto;
        }
        return BigDecimal.ZERO;

    }

    private BigDecimal calculaDescontoCeara(Cobranca c, FaixaDesconto faixaDesconto) {

        BigDecimal desconto = new BigDecimal(0l);
        Long quantidadeDuasRodas = 0l;
        Long quantidadeQuatroRodas = 0l;
        if (faixaDesconto.getPrecoComposto().getValorParametro().equals("2")) {
            quantidadeDuasRodas = cobrancaService.countByFinanceirasVeiculos2Rodas(c.getFinanceira(), c.getEstado(), c.getDataInicio(), c.getDataFim());
            desconto = (faixaDesconto.getPercentual().multiply(new BigDecimal(quantidadeDuasRodas)));
        } else if (faixaDesconto.getPrecoComposto().getValorParametro().equals("4")) {
            quantidadeQuatroRodas = cobrancaService.countByFinanceirasVeiculos4Rodas(c.getFinanceira(), c.getEstado(), c.getDataInicio(), c.getDataFim());
            desconto = (faixaDesconto.getPercentual().multiply(new BigDecimal(quantidadeQuatroRodas)));
        } else {
            try {
                throw new ServiceException("Os parâmetros cadastrados no credenciamento deste Estado devem ser 2 ou 4");
            } catch (ServiceException e) {
                logger.error("Erros ao lançar exceção.");
            }
        }

        return desconto;

    }

    @Override
    public Uf getUf() {
        return Uf.CE;
    }

}
