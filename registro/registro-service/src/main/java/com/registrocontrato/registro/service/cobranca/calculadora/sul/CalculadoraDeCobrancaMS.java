package com.registrocontrato.registro.service.cobranca.calculadora.sul;

import com.registrocontrato.infra.entity.Uf;
import com.registrocontrato.infra.util.PlaceconUtil;
import com.registrocontrato.registro.entity.Cobranca;
import com.registrocontrato.registro.service.CredenciamentoService;
import com.registrocontrato.registro.service.CupomDescontoService;
import com.registrocontrato.registro.service.cobranca.gravame.GravameService;
import com.registrocontrato.registro.service.detran.ms.rest.WsDetranMS;
import com.registrocontrato.registro.service.detran.ms.client.BoletoCobrancaDTO;
import com.registrocontrato.registro.service.cobranca.CobrancaService;
import com.registrocontrato.registro.service.cobranca.calculadora.CalculadoraDeCobrancaDetranIntegrado;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.Objects;

@Component
public class CalculadoraDeCobrancaMS extends CalculadoraDeCobrancaDetranIntegrado {

    private final WsDetranMS wsDetranMsRestClient;

    public CalculadoraDeCobrancaMS(@Lazy CobrancaService cobrancaService, CredenciamentoService credenciamentoService, WsDetranMS wsDetranMsRestClient, CupomDescontoService cupomDescontoService, GravameService gravameService) {
        super(cobrancaService, credenciamentoService, cupomDescontoService, gravameService);
        this.wsDetranMsRestClient = wsDetranMsRestClient;
    }

    @Override
    protected BigDecimal getValorBoletoDetran(Cobranca cobranca) {
        try {
            BoletoCobrancaDTO[] boleto = wsDetranMsRestClient.consultarBoleto(cobranca.getFinanceira().getDocumento());
            if (Objects.nonNull(boleto) && boleto.length > 0) {
                String valor = boleto[0].getTotalGeral().trim().replace(".", "").replace(",", ".");
                return PlaceconUtil.getValorBigDecimal(valor);
            }
        } catch (Exception e) {
            logger.error(e.getMessage());
            return BigDecimal.ZERO;
        }
        return BigDecimal.ZERO;
    }

    @Override
    public Uf getUf() {
        return Uf.MS;
    }
}
