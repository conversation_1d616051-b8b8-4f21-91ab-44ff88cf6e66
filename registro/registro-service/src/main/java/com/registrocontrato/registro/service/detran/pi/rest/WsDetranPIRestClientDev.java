package com.registrocontrato.registro.service.detran.pi.rest;

import com.registrocontrato.infra.exception.ServiceException;
import com.registrocontrato.infra.util.PlaceconUtil;
import com.registrocontrato.registro.service.detran.pi.client.ConsultaBoletoPiResponse;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Component;

import javax.ws.rs.client.ClientBuilder;
import javax.ws.rs.core.HttpHeaders;
import javax.ws.rs.core.Response;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.Date;

import static com.registrocontrato.infra.util.PlaceconUtil.leftPad;
import static javax.ws.rs.core.MediaType.WILDCARD;

@Component
@Profile({"desenv", "homol"})
public class WsDetranPIRestClientDev extends WsDetranPiRest {

    @Override
    public ConsultaBoletoPiResponse consultarBoleto(Date dataInicio, String cnpj) throws ServiceException {

        Date data = new Date(dataInicio.getTime());

        ConsultaBoletoPiResponse consultaMock = new ConsultaBoletoPiResponse();
        consultaMock.setDataVencimento(data.toString());
        consultaMock.setDataPagamento(data.toString());
        consultaMock.setAno("2030");
        consultaMock.setMes("01");
        consultaMock.setValor(new Double(1000));
        consultaMock.setLinhaDigitavel("AAABBBBCCC001122");
        consultaMock.setQuantidadeRegistros("100");

        return consultaMock;
    }
}
