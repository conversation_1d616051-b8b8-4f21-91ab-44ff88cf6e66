package com.registrocontrato.registro.service;

import com.google.zxing.BarcodeFormat;
import com.google.zxing.MultiFormatWriter;
import com.google.zxing.WriterException;
import com.google.zxing.client.j2se.MatrixToImageConfig;
import com.google.zxing.client.j2se.MatrixToImageWriter;
import com.google.zxing.common.BitMatrix;
import com.itextpdf.text.DocumentException;
import com.openhtmltopdf.pdfboxout.PdfRendererBuilder;
import com.registrocontrato.infra.entity.*;
import com.registrocontrato.infra.exception.ServiceException;
import com.registrocontrato.infra.service.BaseService;
import com.registrocontrato.infra.service.audit.AuditContrato;
import com.registrocontrato.infra.util.PlaceconUtil;
import com.registrocontrato.registro.entity.*;
import com.registrocontrato.registro.enums.*;
import com.registrocontrato.registro.repository.*;
import com.registrocontrato.registro.service.detran.HandlerWsDetranClient;
import com.registrocontrato.registro.service.detran.ce.client.request.SituacaoContrato;
import com.registrocontrato.registro.service.dto.ContratoDTO;
import com.registrocontrato.registro.service.dto.MensagemRetornoDTO;
import com.registrocontrato.registro.service.dto.RetornoContratoDetranDTO;
import com.registrocontrato.registro.service.validation.ContratoValidation;
import com.registrocontrato.seguranca.entity.Perfil;
import com.registrocontrato.seguranca.entity.Usuario;
import com.registrocontrato.seguranca.repository.FinanceiraRepository;
import com.registrocontrato.seguranca.service.UsuarioService;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.Hibernate;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.core.io.Resource;
import org.springframework.core.io.ResourceLoader;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.data.domain.Sort.Direction;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.data.repository.PagingAndSortingRepository;
import org.springframework.http.MediaType;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.AsyncResult;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.concurrent.ListenableFuture;
import org.springframework.web.servlet.support.ServletUriComponentsBuilder;
import org.thymeleaf.TemplateEngine;
import org.thymeleaf.context.Context;
import org.xhtmlrenderer.pdf.ITextRenderer;

import javax.persistence.criteria.*;
import javax.servlet.ServletContext;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class ContratoService extends BaseService<Contrato, ContratoDTO> {

    @Autowired
    TemplateEngine templateEngine;

    private static final Logger LOGGER = LoggerFactory.getLogger(ContratoService.class);

    private static final int MAX_REGISTROS = 200000;

    private static final long serialVersionUID = 1L;

    private static Logger logger = LoggerFactory.getLogger(ContratoService.class);

    private List<Uf> envioMultiplo = Arrays.asList(Uf.PR, Uf.MA, Uf.AM, Uf.PA);

    private List<Uf> ufBaixa = Arrays.asList(Uf.BA, Uf.CE);

    private List<Uf> envioRobo = Arrays.asList(Uf.GO, Uf.RO, Uf.ES);

    private List<Uf> contratoAtivoAposEnviar = Arrays.asList(Uf.ES, Uf.RO);

    private List<Uf> ufCobraAlteracao = Arrays.asList(Uf.SP);

    @Value("${file.dir:null}")
    private String FILE_DIR;


    @Autowired
    private ComprovanteRepository comprovanteRepository;

    @Autowired
    private ContratoValidation contratoValidation;

    @Autowired
    private ContratoRepository contratoRepository;

    @Autowired
    private VeiculoRepository veiculoRepository;

    @Autowired
    private FinanceiraRepository financeiraRepository;

    @Autowired
    private AssinaturaRepository assinaturaRepository;

    @Autowired
    private UsuarioService usuarioService;

    @Autowired
    private MensagemRetornoRepository mensagemRetornoDETRANRepository;

    @Autowired
    private AnexoRepository anexoRepository;

    @Autowired
    private MunicipioService municipioService;

    @Autowired
    private DocumentoArrecadacaoService documentoArrecadacaoService;

    @Autowired
    private List<HandlerWsDetranClient> listaWsDetranClient;

    @Autowired
    private MigracaoService migracaoService;

    @Autowired
    private ResourceLoader resourceLoader;

    @Override
    public Contrato findOne(Long id) {
        Contrato contrato = super.findOne(id);
        if (contrato != null) {
            Hibernate.initialize(contrato.getMunicipioDevedor());
            Hibernate.initialize(contrato.getMunicipioLiberacao());
            Hibernate.initialize(contrato.getMunicipioGarantidor());
            Hibernate.initialize(contrato.getVeiculos());
            Hibernate.initialize(contrato.getAnexos());
            Hibernate.initialize(contrato.getAuditorias());
            Hibernate.initialize(contrato.getAssinaturas());
            Hibernate.initialize(contrato.getIntegradora());

            for (Assinatura a : contrato.getAssinaturas()) {
                a.setNomeUsuario(usuarioService.findByCpf(a.getUsuario()).getNome());
            }
            Hibernate.initialize(contrato.getEnvios());
            for (Veiculo v : contrato.getVeiculos()) {
                Hibernate.initialize(v.getMarca());
                Hibernate.initialize(v.getModelo());
                Hibernate.initialize(v.getMensagemRetorno());
            }
            return contrato;
        }
        return null;
    }

    public void ativar(Contrato entity) throws ServiceException {
        Contrato contrato = findOne(entity.getId());
        if (contrato.getSituacao() != Situacao.ERRO
            && contrato.getSituacao() != Situacao.PENDENTE
            && contrato.getSituacao() != Situacao.BAIXADO
            && contrato.getSituacao() != Situacao.PENDENTE_PAGAMENTO) {
            throw new ServiceException("Só é possível ativar contratos na situação de ERRO, PENDENTE ou BAIXADO");
        }
        if (contrato.getAlteracao() == Boolean.TRUE) {
            throw new ServiceException("Só é possível ativar contratos que não possuam alteração");
        }

        MensagemRetorno m = mensagemRetornoDETRANRepository.findTop1BySucessoAndUf(true, contrato.getUfRegistro());
        contrato.setDataConclusaoDETRAN(new Date());
        contrato.setSituacao(Situacao.ATIVO);

        for (Veiculo v : contrato.getVeiculos()) {
            for (Veiculo l : entity.getVeiculos()) {
                if (v.getNumeroChassi().equals(l.getNumeroChassi())) {
                    v.setNumeroRegistroDetran(l.getNumeroRegistroDetran());
                }
            }
            v.setMensagemRetornoDetalhada(null);
            v.setMensagemRetorno(m);
        }

        contratoRepository.save(contrato);
    }

    public void ativarContratoRobot(Contrato contrato) throws Exception {
        if (contrato.getSituacao() == Situacao.ATIVO) throw new Exception("O contrato já está ativado");
        contrato.setSituacao(Situacao.ATIVO);
        contrato.getVeiculos().forEach(veiculo -> {
            veiculo.setMensagemRetorno(null);
            veiculo.setMensagemRetornoDetalhada(null);

            if (contrato.getDataConclusaoDETRAN() == null)
                contrato.setDataConclusaoDETRAN(new Date());
        });
        contratoRepository.save(contrato);
    }

    public void resolverPendencia(Contrato entity) {
        Contrato contrato = findOne(entity.getId());
        contrato.setDataConclusaoDETRAN(new Date());
        contrato.setSituacao(Situacao.ATIVO);
        contratoRepository.save(contrato);
    }

    public void informarPendencia(Contrato entity) {
        Contrato contrato = findOne(entity.getId());
        contrato.setDataConclusaoDETRAN(null);
        contrato.setSituacao(Situacao.ERRO);
        contratoRepository.save(contrato);
    }

    public void excluir(Contrato contrato) throws ServiceException {
        validateExclusao(contrato);

        contratoRepository.delete(contrato);
    }

    public void validateExclusao(Contrato contrato) throws ServiceException {
        if (contratoRepository.possuiCobranca(contrato) == Boolean.TRUE)
            throw new ServiceException("O Contrato não pode ser excluído pois possui cobrança associada ao mesmo");
    }

    @Override
    @Transactional
    public Page<Contrato> findAll(int first, int pageSize, ContratoDTO filter) {
        Page<Contrato> contratos = findAllLazy(first, pageSize, filter);
        for (Contrato c : contratos) {
            Hibernate.initialize(c.getIntegradora());
            c.getVeiculos().forEach(v -> {
                if (v.getMensagemRetorno() != null) v.getMensagemRetorno().getId();
            });

        }
        if (filter.getComCobranca() == Boolean.TRUE) {
            contratos.forEach(c -> {
                if (c.getCobranca() != null) c.getCobranca().getId();
            });
        }
        return contratos;
    }


    public Long getTotalContratosAtivosPeriodo(Financeira financeira, Uf uf, Date dataInicio, Date dataFim) {
        return contratoRepository.getTotalContratosAtivosPeriodo(financeira, uf, dataInicio, dataFim);
    }

    public Page<Contrato> findAllLazy(int first, int pageSize, ContratoDTO filter) {
        Specification<Contrato> contratoSpec = new Specification<Contrato>() {

            @Override
            public Predicate toPredicate(Root<Contrato> root, CriteriaQuery<?> cq, CriteriaBuilder cb) {
                cq.distinct(true);
                List<Predicate> predicates = new ArrayList<>();

                if (filter.getNumeroRegistroEletronico() != null && filter.getNumeroRegistroEletronico() != 0) {
                    predicates.add(cb.equal(root.<Long>get("numeroRegistroEletronico"), filter.getNumeroRegistroEletronico()));
                }
                if (StringUtils.isNotEmpty(filter.getNumeroContrato())) {
                    predicates.add(cb.like(cb.lower(root.<String>get("numeroContrato")), "%" + filter.getNumeroContrato().toLowerCase() + "%"));
                }
                if (filter.getUfRegistro() != null) {
                    predicates.add(cb.equal(root.<Uf>get("ufRegistro"), filter.getUfRegistro()));
                }
                if (StringUtils.isNotEmpty(filter.getCpfCnpjDevedorFinanciado())) {
                    String string = PlaceconUtil.retiraFormatacao(filter.getCpfCnpjDevedorFinanciado()).toLowerCase();
                    predicates.add(cb.like(cb.lower(root.<String>get(StringUtils.isNumeric(string) ? "cpfCnpjDevedorFinanciado" : "nomeDevedorFinanciado")), "%" + string + "%"));
                }
                if (filter.getUfEnderecoDevedor() != null) {
                    predicates.add(cb.equal(root.<Uf>get("ufEnderecoDevedor"), filter.getUfEnderecoDevedor()));
                }
                if (filter.getMunicipioEnderecoDevedor() != null) {
                    predicates.add(cb.equal(root.<Municipio>get("municipioDevedor"), filter.getMunicipioEnderecoDevedor()));
                }
                if (filter.getSituacao() != null) {
                    predicates.add(cb.equal(root.<Situacao>get("situacao"), filter.getSituacao()));
                }
                if (filter.getTipoContrato() != null) {
                    predicates.add(cb.equal(root.<TipoContrato>get("tipoContrato"), filter.getTipoContrato()));
                }
                if (filter.getAprovadoAuditoria() != null) {
                    if (filter.getAprovadoAuditoria() == SimNao.S) {
                        predicates.add(cb.equal(root.<SimNao>get("aprovadoAuditoria"), filter.getAprovadoAuditoria()));
                    } else {
                        predicates.add(cb.or(cb.equal(root.<SimNao>get("aprovadoAuditoria"), filter.getAprovadoAuditoria()), root.<SimNao>get("aprovadoAuditoria").isNull()));
                    }
                }
                if (!filter.getSituacoes().isEmpty()) {
                    predicates.add(root.<Situacao>get("situacao").in(filter.getSituacoes()));
                }
                if (filter.getSituacaoFinanceira() != null) {
                    predicates.add(cb.equal(root.<SituacaoFinanceira>get("situacaoFinanceira"), filter.getSituacaoFinanceira()));
                }
                if (filter.getAssinado() != null) {
                    predicates.add(cb.equal(root.<SimNao>get("assinado"), filter.getAssinado()));
                }
                if (filter.getIntegra() != null) {
                    if (filter.getIntegra() == SimNao.S) {
                        predicates.add(cb.or(cb.isNotNull(root.<Long>get("idProcessoB3")), cb.isNotNull(root.<Long>get("idProcessoSENDB3"))));

                    } else {
                        predicates.add(cb.or(cb.isNull(root.<Long>get("idProcessoB3")), cb.isNull(root.<Long>get("idProcessoSENDB3"))));
                    }
                }
                if (filter.getDataAditivo() != null) {
                    LocalDate localDate = filter.getDataAditivo().toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
                    Date dataInicio = Date.from(localDate.atStartOfDay(ZoneId.systemDefault()).toInstant());
                    localDate = localDate.plusDays(1);
                    Date dataFim = Date.from(localDate.atStartOfDay(ZoneId.systemDefault()).toInstant());
                    predicates.add(cb.between(root.<Date>get("dataAditivoContrato"), dataInicio, dataFim));
                }
                if (filter.getDataCadastro() != null) {
                    LocalDate localDate = filter.getDataCadastro().toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
                    Date dataInicio = Date.from(localDate.atStartOfDay(ZoneId.systemDefault()).toInstant());
                    localDate = localDate.plusDays(1);
                    Date dataFim = Date.from(localDate.atStartOfDay(ZoneId.systemDefault()).toInstant());
                    predicates.add(cb.between(root.<Date>get("dataCadastro"), dataInicio, dataFim));
                }
                if (filter.getDataContrato() != null) {
                    LocalDate localDate = filter.getDataContrato().toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
                    Date dataInicio = Date.from(localDate.atStartOfDay(ZoneId.systemDefault()).toInstant());
                    localDate = localDate.plusDays(1);
                    Date dataFim = Date.from(localDate.atStartOfDay(ZoneId.systemDefault()).toInstant());
                    predicates.add(cb.between(root.<Date>get("dataContrato"), dataInicio, dataFim));
                }
                if (filter.getDataRegistroDETRAN() != null) {
                    LocalDate localDate = filter.getDataRegistroDETRAN().toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
                    Date dataInicio = Date.from(localDate.atStartOfDay(ZoneId.systemDefault()).toInstant());
                    localDate = localDate.plusDays(1);
                    Date dataFim = Date.from(localDate.atStartOfDay(ZoneId.systemDefault()).toInstant());
                    predicates.add(cb.between(root.<Date>get("dataConclusaoDETRAN"), dataInicio, dataFim));
                }
                if (StringUtils.isNotEmpty(filter.getChassi())) {
                    predicates.add(cb.like(cb.lower(root.join("veiculos").<String>get("numeroChassi")), "%" + filter.getChassi().toLowerCase() + "%"));
                }
                if (StringUtils.isNotEmpty(filter.getPlaca())) {
                    predicates.add(cb.like(cb.lower(root.join("veiculos").<String>get("placa")), "%" + filter.getPlaca().toLowerCase() + "%"));
                }
                if (StringUtils.isNotEmpty(filter.getNumeroGravame())) {
                    predicates.add(cb.like(cb.lower(root.join("veiculos").<String>get("numeroGravame")), "%" + filter.getNumeroGravame().toLowerCase() + "%"));
                }
                if (filter.getNumeroRenavam() != null) {
                    predicates.add(cb.equal(root.join("veiculos").<Long>get("numeroRenavam"), filter.getNumeroGravame()));
                }
                if (filter.getMensagemRetorno() != null) {
                    predicates.add(cb.equal(root.join("veiculos").<Long>get("mensagemRetorno"), filter.getMensagemRetorno()));
                }
                if (filter.getAnoModelo() != null) {
                    predicates.add(cb.equal(root.join("veiculos").<Long>get("anoModelo"), filter.getAnoModelo()));
                }
                if (filter.getTipoVeiculo() != null) {
                    predicates.add(cb.equal(root.join("veiculos").<TipoVeiculo>get("tipo"), filter.getTipoVeiculo()));
                }

                if (filter.getPossuiAnexo() != null) {
                    Join<Contrato, Anexo> joinAnexo = root.join("anexos", JoinType.LEFT);
                    if (filter.getPossuiAnexo() == SimNao.N) {
                        predicates.add(cb.isNull(joinAnexo));
                    } else {
                        predicates.add(cb.isNotNull(joinAnexo));
                    }
                }
                if (filter.getFinanceira() != null) {
                    predicates.add(cb.equal(root.<Financeira>get("financeira"), filter.getFinanceira()));
                }

                if (!filter.getFinanceiras().isEmpty()) {
                    predicates.add(root.<Financeira>get("financeira").in(filter.getFinanceiras()));
                }


                if (filter.getAgente() != null) {
                    predicates.add(cb.equal(root.<Agente>get("agente"), filter.getAgente()));
                }

                // se o usuario eh financeira filtrar somente os usuario da financeira
                Usuario usuarioLogado = usuarioService.findByCpfFinanceiras(filter.getUsuario());
                if (usuarioLogado.getPerfil() == Perfil.FINANCEIRA || !usuarioLogado.getFinanceiras().isEmpty()) {
                    List<Financeira> values = usuarioLogado.getFinanceiras();
                    predicates.add(root.<Financeira>get("financeira").in(values));

                    if (usuarioLogado.getAgente() != null) {
                        predicates.add(cb.equal(root.<Agente>get("agente"), usuarioLogado.getAgente()));
                    }
                }
                if (usuarioLogado.getPerfil() == Perfil.DETRAN) {
                    predicates.add(cb.isNotNull(root.<Long>get("numeroRegistroEletronico")));
                }
                if (usuarioLogado.getUf() != null) {
                    predicates.add(cb.equal(root.<Uf>get("ufRegistro"), usuarioLogado.getUf()));
                }

                if (filter.getCobranca() != null) {
                    predicates.add(cb.equal(root.<Cobranca>get("cobranca"), filter.getCobranca()));
                }
                if (filter.getConsultaRapida() == Boolean.TRUE) {
                    if (!StringUtils.isBlank(filter.getValor())) {
                        Long numero = 0l;
                        try {
                            numero = Long.parseLong(filter.getValor());
                        } catch (Exception e) {

                        }
                        predicates.add(cb.or(cb.like(cb.lower(root.join("veiculos").<String>get("placa")), "%" + filter.getValor().toLowerCase() + "%"), cb.like(cb.lower(root.join("veiculos").<String>get("numeroChassi")), "%" + filter.getValor().toLowerCase() + "%"), cb.equal(root.<Long>get("numeroRegistroEletronico"), numero), cb.like(cb.lower(root.<String>get("numeroContrato")), "%" + filter.getValor().toLowerCase() + "%")));
                    }

                    filter.setConsultaRapida(null);
                    filter.setValor(null);
                }

                if (!StringUtils.isBlank(filter.getAgenteCadastro())) {

                    Subquery<RegistroEnvio> sq = cq.subquery(RegistroEnvio.class);
                    Root<RegistroEnvio> r2 = sq.from(RegistroEnvio.class);
                    sq.select(r2).where(cb.like(cb.lower(r2.get("usuario")), "%" + filter.getAgenteCadastro().toLowerCase() + "%"), cb.equal(r2.get("contrato"), root));
                    predicates.add(cb.exists(sq));
                }

                if (filter.getDataInclusaoAnexo() != null) {
                    LocalDate localDate = filter.getDataInclusaoAnexo().toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
                    Date dataInicio = Date.from(localDate.atStartOfDay(ZoneId.systemDefault()).toInstant());
                    localDate = localDate.plusDays(1);
                    Date dataFim = Date.from(localDate.atStartOfDay(ZoneId.systemDefault()).toInstant());
                    Subquery<Anexo> sq = cq.subquery(Anexo.class);
                    Root<Anexo> r2 = sq.from(Anexo.class);
                    sq.select(r2).where(cb.between(root.<Date>get("dataCadastro"), dataInicio, dataFim), cb.equal(r2.get("contrato"), root));
                    predicates.add(cb.exists(sq));
                }


                if (filter.getAlteracao() != null) {
                    predicates.add(cb.or(cb.equal(root.<Boolean>get("alteracao"), filter.getAlteracao()), cb.isNull(root.<Boolean>get("alteracao"))));
                }

                if (!PlaceconUtil.isListaVaziaOuNula(filter.getUfsIgnoradas())) {
                    predicates.add(cb.not(cb.in(root.get("ufRegistro")).value(filter.getUfsIgnoradas())));
                }

                if (filter.getSituacaoBaixaB3() != null) {
                    predicates.add(cb.equal(root.<SituacaoBaixaB3>get("situacaoBaixa"), filter.getSituacaoBaixaB3()));
                }

                if (filter.getFindBaixa() != null && filter.getFindBaixa()) {
                    predicates.add(
                            cb.or(
                                    cb.not(root.<SituacaoBaixaB3>get("situacaoBaixa").in(SituacaoBaixaB3.BAIXADO, SituacaoBaixaB3.CANCELADO)),
                                    cb.isNull(root.<SituacaoBaixaB3>get("situacaoBaixa"))
                            )
                    );
                }

                return andTogether(predicates, cb);
            }

            private Predicate andTogether(List<Predicate> predicates, CriteriaBuilder cb) {
                return cb.and(predicates.toArray(new Predicate[0]));
            }
        };
        if (pageSize == 0) {
            Page<Contrato> list = contratoRepository.findAll(contratoSpec, new PageRequest(0, MAX_REGISTROS, new Sort(Direction.ASC, "id")));
            list.forEach(l -> l.getVeiculos().forEach(v -> Hibernate.initialize(v.getMarca())));
            list.forEach(l -> l.getAnexos().forEach(a -> a.getId()));
            return list;
        }

        Direction direction = filter.getDirection() == null ? Direction.DESC : filter.getDirection();
        return contratoRepository.findAll(contratoSpec, new PageRequest(first / pageSize, pageSize, new Sort(direction, "id")));
    }

    @Override
    protected PagingAndSortingRepository<Contrato, Long> getRepository() {
        return contratoRepository;
    }


    /**
     * Transmitir a alteracao de um contrato
     *
     * @param entity
     * @param username
     * @throws ServiceException
     */
    public Contrato transmitirAlteracao(Contrato entity, String username) throws ServiceException {
        if (entity.getUfRegistro() == Uf.CE) {
            throw new ServiceException("Não é permitido fazer alterações contratuais para uf do RJ e CE");
        }
        entity.setAlteracao(true);

        // entrada do ws
        if (entity.getId() == null) {
            // b3
            if (entity.getIdProcessoB3() != null) {
                Contrato contratoAtual = contratoRepository.findByChassiGravame(entity.getVeiculos().get(0).getNumeroChassi(), entity.getVeiculos().get(0).getNumeroGravame());
                if (contratoAtual == null) {
                    entity.setDataCadastro(new Date());
                    entity.setAssinado(SimNao.N);
                    entity.setSituacaoFinanceira(SituacaoFinanceira.PAGO);
                    entity = contratoRepository.save(entity);
                    gerarNumeroRegistroEletronico(entity);
                    enviarParaDetran(entity);
                    definirSucessoContrato(entity);
                    if (entity.getSituacao() == Situacao.ATIVO) entity.setDataConclusaoDETRAN(new Date());
                    return entity;
                } else {
                    utilizarContratoParaAlteracao(entity, contratoAtual);
                }
            } else {
                // api
                Contrato contratoAtual = contratoRepository.findByNumeroRegistroEletronico(entity.getNumeroRegistroEletronico());
                if (contratoAtual == null) {
                    throw new ServiceException(String.format("Registro Eletrônico %s não encontrado.", entity.getNumeroRegistroEletronico().toString()));
                }
                utilizarContratoParaAlteracao(entity, contratoAtual);
            }
        }
        if (isUfCobraAlteracao(entity.getUfRegistro())) {
            contratoValidation.validarAlteracao(entity, username);
            Contrato newContrato = copyContrato(entity);
            baixar(contratoRepository.findByNumeroRegistroEletronico(entity.getNumeroRegistroEletronico()));
            newContrato = contratoRepository.save(newContrato);
            copiarVeiculosParaNovoContrato(newContrato);
            gerarNumeroRegistroEletronico(newContrato);
            enviarParaDetran(newContrato);
            definirSucessoContrato(newContrato);
            return newContrato;
        }
        contratoValidation.validarAlteracao(entity, username);
        salvarArquivo(entity);
        entity = contratoRepository.save(entity);
        enviarParaDetran(entity);
        definirSucessoContrato(entity);

        return entity;
    }

    private void copiarVeiculosParaNovoContrato(Contrato newContrato) {
        for (Veiculo v : newContrato.getVeiculos()) {
            v.setContrato(newContrato);
        }
    }

    private void utilizarContratoParaAlteracao(Contrato entity, Contrato contratoAtual) {
        entity.setId(contratoAtual.getId());
        entity.setSituacaoFinanceira(contratoAtual.getSituacaoFinanceira());
        entity.setNumeroRegistroEletronicoOrigem(contratoAtual.getNumeroRegistroEletronicoOrigem());

        for (Veiculo v : contratoAtual.getVeiculos()) {
            entity.getVeiculos().forEach(e -> {
                if (v.getNumeroChassi().equals(e.getNumeroChassi())) {
                    e.setId(v.getId());
                }
            });
        }
    }

    public List<Contrato> findDadosDevedorFinanciado(String cpfCnpjDevedorFinanciado, Long id) {
        return contratoRepository.findByCpfCnpjDevedorFinanciado(cpfCnpjDevedorFinanciado, id);
    }

    /**
     * Transmitir uma inclusao ou aditivo de contrato
     *
     * @param entity
     * @param username
     * @return numero do registro eletronico gerado
     * @throws ServiceException
     */
    public Contrato transmitir(Contrato entity, String username) throws ServiceException {
        entity.setAlteracao(false);
        if (entity.getTipoContrato() == TipoContrato.CONTRATO_PRINCIPAL) {
            entity.setNumeroAditivoContrato(null);
            entity.setDataAditivoContrato(null);
            entity.setNomeCessaoDireito(null);
            entity.setCpfCnpjCessaoDireito(null);
            entity.setChassiSubstituicao(null);
            contratoValidation.validarInclusao(entity, username);
        } else {
            if (entity.getTipoContrato() == TipoContrato.SUBSTITUICAO_GARANTIA) {
                entity.setNomeCessaoDireito(null);
                entity.setCpfCnpjCessaoDireito(null);
            } else {
                entity.setChassiSubstituicao(null);
            }
            // validar aditivo
            contratoValidation.validarAditivo(entity, username);
        }

        if (entity.getSituacao() == Situacao.ERRO) {
            if (verificaExistenciaContratoDetran(entity)) {
                return updateDadosContratoJaEnviado(entity);
            }
        }

        if (entity.getDataCadastro() == null) {
            entity.setDataCadastro(new Date());
        }
        entity.setAssinado(SimNao.N);
        entity.setSituacaoFinanceira(SituacaoFinanceira.NAO_PAGO);
        salvarArquivo(entity);

        entity = contratoRepository.save(entity);
        gerarNumeroRegistroEletronico(entity);

        if (envioRobo.contains(entity.getUfRegistro())) {
            entity.setSituacao(Situacao.PENDENTE);
            for (Veiculo v : entity.getVeiculos()) {
                v.setMensagemRetorno(mensagemRetornoDETRANRepository.findTop1ByErroPadraoAndUf(true, entity.getUfRegistro()));
                v.setMensagemRetornoDetalhada("Contrato em processo de envio. Aguarde a confirmação.");
            }
            return entity;
        }

        gerarDocumentoArrecadacao(entity);
        if (!documentoArrecadacaoPago(entity)) {
            return entity;
        }
        if (entity.getUfRegistro() == Uf.PE) {
            if (!contratoValidation.validarUfRegistroComUfDoDevedor(entity)) {
                entity.getVeiculos().forEach(v -> {
                    v.setMensagemRetornoDetalhada("UF de endereço do devedor difere da UF de registro");
                    v.setMensagemRetorno(mensagemRetornoDETRANRepository.findTop1ByErroPadraoAndUf(true, v.getContrato().getUfRegistro()));
                });
                entity.setSituacao(Situacao.ERRO);
                return entity;
            }
        }
        enviarParaDetran(entity);

        definirSucessoContrato(entity);

        //so deve baixar contrato se o novo registro tiver sido executado com sucesso (inclusao apenas)
        if (entity.getSituacao() == Situacao.ATIVO) {
            verificaBaixaContrato(entity);
        }
        return entity;
    }

    private boolean documentoArrecadacaoPago(Contrato entity) {
        if (entity.getUfRegistro() == Uf.BA) {
            for (Veiculo v : entity.getVeiculos()) {
                DocumentoArrecadacao documentoArrecadacao = documentoArrecadacaoService.findByVeiculo(v);
                if (documentoArrecadacao != null && documentoArrecadacao.getDataPagamento() == null) {
                    logger.info("#DOCUMENTOARRECADACAOPAGO | Colocando contrato como Pendente de Pagamento-> " + entity.getUfRegistro() + " Veículo: " + v.getNumeroChassi() + " id: " + entity.getId());
                    entity.setSituacao(Situacao.PENDENTE_PAGAMENTO);
                    entity.getVeiculos().get(0).setMensagemRetornoDetalhada(null);
                    entity.getVeiculos().get(0).setMensagemRetorno(null);
                    return false;
                }
            }
        }
        return true;
    }

    private Contrato updateDadosContratoJaEnviado(Contrato entity) throws ServiceException {
        entity = findOne(entity.getId());
        entity.setSituacao(Situacao.ATIVO);
        entity.setDataConclusaoDETRAN(new Date());
        save(entity);
        //limpa mensagens do veiculo
        for (Veiculo v : entity.getVeiculos()) {
            v.setMensagemRetorno(mensagemRetornoDETRANRepository.findTop1BySucessoAndUf(Boolean.TRUE, entity.getUfRegistro()));
            v.setMensagemRetornoDetalhada(null);
            if (v.getDataTransmissaoDETRAN() == null) {
                v.setDataTransmissaoDETRAN(entity.getDataConclusaoDETRAN());
            }
            veiculoRepository.save(v);
        }
        return entity;
    }

    private Optional<HandlerWsDetranClient> consultarWsDetran(Uf estado) {
        return listaWsDetranClient
                .stream()
                .filter(wsDetranClient -> wsDetranClient.getUf().equals(estado))
                .findFirst();
    }

    private boolean verificaExistenciaContratoDetran(Contrato entity) {
        Uf ufRegistro = entity.getUfRegistro();
        if (consultarWsDetran(ufRegistro).isPresent()) {
            for (Veiculo v : entity.getVeiculos()) {
                try {
                    RetornoContratoDetranDTO retorno = consultarWsDetran(ufRegistro).get().
                            consultarContratoChassi(
                                    v.getNumeroChassi(),
                                    entity.getFinanceira().getDocumento(),
                                    entity.getNumeroContrato());
                    if (Objects.nonNull(retorno)
                        && entity.getNumeroRegistroEletronico().toString()
                                .equals(retorno.getNumeroRegistroContratoRegistradora())) {
                        return true;
                    }
                } catch (Exception e) {
                    logger.error("Erro na consulta do contrato pela WS");
                }
            }
        }
        return false;
    }

    /**
     * Baixa o contrato existente caso o novo contrato tenha apenas um chassi e o contrato existente para o chassi tenha numero de gravame diferente
     *
     * @param entity
     * @throws ServiceException
     */
    private void verificaBaixaContrato(Contrato entity) throws ServiceException {
        if (entity.getDataAditivoContrato() != null) {
            return;
        }

        List<String> chassis = entity.getVeiculos().stream().map(Veiculo::getNumeroChassi).collect(Collectors.toList());
        //so pode baixar se nao for lote
        if (chassis.size() == 1) {

            Contrato contratoExistente = contratoRepository.findByChassiAndSituacao(chassis, Situacao.ATIVO, entity);
            Veiculo veiculoNovoContrato = entity.getVeiculos().get(0);
            if (contratoExistente != null && contratoExistente.getId() != entity.getId()) {
                for (Veiculo v : contratoExistente.getVeiculos()) {
                    if (mesmoChassi(veiculoNovoContrato, v)) {
                        baixar(contratoExistente);
                        return;
                    }
                }
            }

        }
    }

    private boolean mesmoChassi(Veiculo veiculoNovoContrato, Veiculo v) {
        return v.getNumeroChassi().toLowerCase().equals(veiculoNovoContrato.getNumeroChassi().toLowerCase());
    }

    /**
     * Define o contrato cadastrado como ATIVO e data de conclusao para o momento
     * corrente, quando todos os veiculo envolvidos estiverem transmitidos de forma
     * correta
     *
     * @param entity
     */
    private void definirSucessoContrato(Contrato entity) throws ServiceException {
        boolean sucesso = true;
        for (Veiculo v : entity.getVeiculos()) {
            if (v.getMensagemRetorno() == null || !v.getMensagemRetorno().getSucesso()) {
                sucesso = false;
                break;
            }
        }

        if (entity.getAlteracao() != null && entity.getAlteracao()) {
            if (sucesso) {
                entity.setSituacao(Situacao.ATIVO);
                entity.setDataConclusaoDETRAN(new Date());
            } else {
                entity.setSituacao(Situacao.ERRO);
                entity.setDataConclusaoDETRAN(null);
            }
        } else {
            if (sucesso) {
                entity.setDataConclusaoDETRAN(new Date());
                alterarContratoPrincipalParaAditivado(entity);
                entity.setSituacao(Situacao.ATIVO);
            } else {
                entity.setDataConclusaoDETRAN(null);
                entity.setSituacao(Situacao.ERRO);
            }
        }

        if (entity.getUfRegistro() == Uf.MS && entity.getSituacao() == Situacao.ATIVO) {
            entity.setDataConclusaoDETRAN(null);
            logger.info("#DEFINIRSUCESSOCONTRATO 1 | Colocando contrato como Pendente -> " + entity.getUfRegistro() + " id: " + entity.getId());
            entity.setSituacao(Situacao.PENDENTE);
        }

        if (entity.getUfRegistro() == Uf.RS && entity.getSituacao() == Situacao.ATIVO) {
            entity.setDataConclusaoDETRAN(null);
            logger.info("#DEFINIRSUCESSOCONTRATO 1 | Colocando contrato como Pendente -> " + entity.getUfRegistro() + " id: " + entity.getId());
            entity.setSituacao(Situacao.PENDENTE);
        }

//        if (entity.getUfRegistro() == Uf.RS && entity.getSituacao() == Situacao.ERRO) {
//            if (entity.getVeiculos().get(0).getMensagemRetornoDetalhada().contains("ERRO INESPERADO") || entity.getVeiculos().get(0).getMensagemRetornoDetalhada().contains("Elemento Signature") || entity.getVeiculos().get(0).getMensagemRetornoDetalhada().contains("is not a valid value for 'NCName'") || entity.getVeiculos().get(0).getMensagemRetornoDetalhada().contains("A assinatura digital deve conter o CNPJ do agente financeiro ou terceirizado")) {
//                for (Veiculo v : entity.getVeiculos()) {
//                    v.setMensagemRetornoDetalhada(null);
//                    v.setMensagemRetorno(null);
//                    logger.info("#DEFINIRSUCESSOCONTRATO 2 | Colocando contrato como Pendente -> " + entity.getUfRegistro() + " Veículo: " + v.getNumeroChassi() + " id: " + entity.getId());
//                }
//                entity.setDataConclusaoDETRAN(null);
//                entity.setSituacao(Situacao.PENDENTE);
//            }
//        }
    }

    private void enviarParaDetran(Contrato entity) {
        Uf ufRegistro = entity.getUfRegistro();
        logger.warn("Iniciando trasmissao");
        // envio multiplo siginifica que o detran aceita em um unico envio varios chassis do mesmo contrato ex: pr
        if (consultarWsDetran(ufRegistro).isPresent()) {
            if (envioMultiplo.contains(ufRegistro)) {
//                MensagemRetornoDTO retornoDetran = consultarWsDetran(ufRegistro).get().comunicarContratoFinanceiroVeiculo(entity);

                MensagemRetornoDTO retornoDetran = null;
                if (migracaoService.isEnviarParaWinov(ufRegistro)) {
                    retornoDetran = migracaoService.enviarContratoParaEquinix(entity, null);
                } else {
                    retornoDetran = consultarWsDetran(ufRegistro).get().comunicarContratoFinanceiroVeiculo(entity);
                }

                MensagemRetorno mensagemRetornoDETRAN = salvarNovaMensagem(ufRegistro, retornoDetran);
                for (Veiculo v : entity.getVeiculos()) {
                    v.setMensagemRetorno(mensagemRetornoDETRAN);
                    v.setDataTransmissaoDETRAN(new Date());
                    v.setMensagemRetornoDetalhada(null);

                    if (!mensagemRetornoDETRAN.getSucesso()) {
                        v.setMensagemRetornoDetalhada(retornoDetran.getCodigo() + " - " + (StringUtils.isEmpty(retornoDetran.getDescricao()) ? mensagemRetornoDETRAN.getDescricao() : retornoDetran.getDescricao()));
                    } else {
                        v.setNumeroRegistroDetran(retornoDetran.getNumeroDetran());
                    }
                }
            } else {
                for (Veiculo v : entity.getVeiculos()) {
                    if (v.getMensagemRetorno() == null || !v.getMensagemRetorno().getSucesso() || entity.getAlteracao()) {
//                        MensagemRetornoDTO retornoDetran = consultarWsDetran(ufRegistro).get().comunicarContratoFinanceiroVeiculo(entity, v);

                        MensagemRetornoDTO retornoDetran = null;
                        if (migracaoService.isEnviarParaWinov(ufRegistro)) {
                            retornoDetran = migracaoService.enviarContratoParaEquinix(entity, v);
                        } else {
                            retornoDetran = consultarWsDetran(ufRegistro).get().comunicarContratoFinanceiroVeiculo(entity, v);
                        }

                        MensagemRetorno mensagemRetornoDETRAN = salvarNovaMensagem(ufRegistro, retornoDetran);
                        v.setMensagemRetorno(mensagemRetornoDETRAN);
                        v.setDataTransmissaoDETRAN(new Date());
                        if (Objects.nonNull(mensagemRetornoDETRAN) || !mensagemRetornoDETRAN.getDescricao().isEmpty()) {
                            v.setMensagemRetornoDetalhada(mensagemRetornoDETRAN.getDescricao());
                        } else {
                            v.setMensagemRetornoDetalhada(null);
                        }

                        if (!mensagemRetornoDETRAN.getSucesso()) {
                            v.setMensagemRetornoDetalhada(retornoDetran.getCodigo() + " - " + (StringUtils.isEmpty(retornoDetran.getDescricao()) ? mensagemRetornoDETRAN.getDescricao() : retornoDetran.getDescricao()));
                        } else {
                            v.setNumeroRegistroDetran(retornoDetran.getNumeroDetran());
                        }
                    }
                }
            }
        }
    }

    private MensagemRetorno salvarNovaMensagem(Uf ufRegistro, MensagemRetornoDTO retornoDetran) {
        MensagemRetorno mensagemRetornoDETRAN = mensagemRetornoDETRANRepository.findTop1ByCodigoAndUf(retornoDetran.getCodigo(), ufRegistro);
        if (mensagemRetornoDETRAN == null) {
            mensagemRetornoDETRAN = mensagemRetornoDETRANRepository.save(new MensagemRetorno(retornoDetran.getCodigo(), ufRegistro, retornoDetran.getDescricao()));
        }
        return unproxy(mensagemRetornoDETRAN);
    }

    public void gerarNumeroRegistroEletronico(Contrato entity) {
        Long numeroRegistroEletronico = (Calendar.getInstance().get(Calendar.YEAR) * 10000000000L) + entity.getId();
        entity.setNumeroRegistroEletronico(numeroRegistroEletronico);
    }

    @Transactional
    public void gerarDocumentoArrecadacao(Contrato entity) throws ServiceException {
        if (entity.getUfRegistro() == Uf.RJ || entity.getUfRegistro() == Uf.BA) {
            for (Veiculo v : entity.getVeiculos()) {
                if (v.getDocumentoArrecadacao() == null) {
                    String documentoArrecadacao = documentoArrecadacaoService.gerarDocumentoArrecadacao(entity.getUfRegistro(), v);
                    if (entity.getUfRegistro() == Uf.BA) v.setDocumentoArrecadacao(documentoArrecadacao);
                }
            }
        }
    }

    /**
     * Em caso de aditivo, muda o contrato original para situacao de ADITIVADO
     *
     * @param entity
     */
    private void alterarContratoPrincipalParaAditivado(Contrato entity) {
        if (entity.getNumeroRegistroEletronicoOrigem() != null) {
            Contrato contratoAtual = contratoRepository.findByNumeroRegistroEletronico(entity.getNumeroRegistroEletronicoOrigem());
            if (contratoAtual.getSituacao() == Situacao.ATIVO) {
                contratoAtual.setSituacao(Situacao.ADITIVADO);
                contratoRepository.save(contratoAtual);
            }
        }
    }

    public void baixarProcessoSend(Long numeroRegistroEletronico, String currentPrincipal) throws ServiceException {
        Contrato c = findByNumeroRegistroEletronico(numeroRegistroEletronico, currentPrincipal);
        c.setTipoBaixaContrato(TipoBaixaContrato.GRAVAME_CANCELADO);
        baixar(c);
    }

    public void baixar(Long numeroRegistroEletronico, String currentPrincipal) throws ServiceException {
        Contrato c = findByNumeroRegistroEletronico(numeroRegistroEletronico, currentPrincipal);
        baixar(c);
    }

    public Contrato findByNumeroRegistroEletronico(Long numeroRegistroEletronico, String currentPrincipal) throws ServiceException {

        Contrato contrato = contratoRepository.findByNumeroRegistroEletronico(numeroRegistroEletronico);

        validaAcessoContrato(contrato, currentPrincipal);

        return contrato;
    }

    private void validaAcessoContrato(Contrato contrato, String currentPrincipal) throws ServiceException {
        Usuario usuario = usuarioService.findByCpf(currentPrincipal);

        if (usuario == null) {
            usuario = usuarioService.findByCpfApiFinanceiras(currentPrincipal);
        }

        if (contrato == null) {
            throw new ServiceException("Registro Eletrônico não encontrado.");
        }

        if ((usuario.getPerfil() == Perfil.FINANCEIRA || !usuario.getFinanceiras().isEmpty()) && !usuario.getFinanceiras().contains(contrato.getFinanceira())) {
            throw new ServiceException("Seu usuário não tem autorização para operar no contrato desse agente financeiro");
        }

        if (usuario.getPerfil() == Perfil.DETRAN && usuario.getUf() != contrato.getUfRegistro()) {
            throw new ServiceException("Seu usuário não pode visualizar contratos de outra UF");
        }
    }

    public Contrato findByCnpjAgenteFinanceiroAndNumeroContrato(String cnpjAgenteFinanceiro, String numeroContrato, String currentPrincipal) throws ServiceException {
        Usuario usuario = usuarioService.findByCpfFinanceiras(currentPrincipal);

        if (usuario == null) {
            usuario = usuarioService.findByCpfApiFinanceiras(currentPrincipal);
        }

        if ((usuario.getPerfil() == Perfil.FINANCEIRA || !usuario.getFinanceiras().isEmpty()) && !usuario.getFinanceiras().stream().map(Financeira::getDocumento).collect(Collectors.toList()).contains(cnpjAgenteFinanceiro)) {
            throw new ServiceException("Seu usuário não tem autorização para operar no contrato desse agente financeiro");
        }

        Financeira financeira = financeiraRepository.findByDocumento(cnpjAgenteFinanceiro);
        if (financeira == null) {
            throw new ServiceException("O Agente Financeiro informado é inválido.");
        }

        Contrato contrato = contratoRepository.findTop1ByFinanceiraIdAndNumeroContratoOrderByIdDesc(financeira.getId(), numeroContrato);
        if (contrato == null) {
            throw new ServiceException("Contrato não encontrado.");
        }

        return contrato;
    }

    /**
     * Baixar um contrato ativo para possibilitar um novo registro para os veiculos envolvidos
     *
     * @throws ServiceException
     */
    @AuditContrato(action = "Baixa de Contrato")
    public void baixar(Contrato entity) throws ServiceException {
        entity.setSituacao(Situacao.BAIXADO);
        contratoRepository.save(entity);

        if (ufBaixa.contains(entity.getUfRegistro())) {
            eviarParaDetranCancelaBaixa(entity);
        }
    }


    private void eviarParaDetranCancelaBaixa(Contrato entity) throws ServiceException {
        TipoBaixaContrato tipoBaixa = entity.getTipoBaixaContrato();
        if (Objects.isNull(tipoBaixa))
            tipoBaixa = TipoBaixaContrato.GRAVAME_BAIXADO;
        Uf ufRegistro = entity.getUfRegistro();

        for (Veiculo v : entity.getVeiculos()) {
            if ((v.getMensagemRetorno() == null || v.getMensagemRetorno().getSucesso()) && consultarWsDetran(ufRegistro).isPresent()) {
                MensagemRetornoDTO retornoDetran = consultarWsDetran(ufRegistro).get().cancelarBaixarContrato(entity, v, tipoBaixa.getCodDetran());
                if (!retornoDetran.getSucesso()) {
                    throw new ServiceException(retornoDetran.getCodigo() + " - " + retornoDetran.getDescricao());
                }
            }
        }

    }

    @AuditContrato(action = "Anulação de Contrato")
    public void anular(Contrato entity) throws ServiceException {
        entity.setSituacao(Situacao.ANULADO);
        contratoRepository.save(entity);
    }

    /**
     * Salvar novos arquivos
     *
     * @param entity
     * @throws ServiceException
     */
    public void salvarDocumento(Contrato entity) throws ServiceException {
        salvarArquivo(entity);
        contratoRepository.save(entity);
    }

    private void salvarArquivo(Contrato entity) throws ServiceException {
        for (Anexo a : entity.getAnexos()) {
            InputStream file = a.getFile();
            if (file != null) {
                if (a.getId() == null && entity.getAprovadoAuditoria() == SimNao.S && entity.getUfRegistro() != Uf.MS) {
                    entity.setAprovadoAuditoria(null);
                }
                try {
                    String referenciaArquivo = PlaceconUtil.formataData(new Date()) + "_" + RandomStringUtils.randomAlphanumeric(10);
                    a.setReferenciaArquivo(referenciaArquivo);
                    File targetFile = new File(FILE_DIR, referenciaArquivo);
                    FileUtils.copyInputStreamToFile(file, targetFile);
                } catch (IOException e) {
                    logger.error(e.getMessage(), e);
                    throw new ServiceException("Erro ao salvar o arquivo");
                }
            }
        }
    }

    public Contrato parseContratoRsngToContrato(ContratoRsng contratoRsng) {
        Contrato contrato = new Contrato();

        List<Veiculo> veiculos = contratoRsng.getVeiculos().stream().map(veiculoRsng -> {
            Veiculo veiculo = new Veiculo();
            BeanUtils.copyProperties(veiculoRsng, veiculo, "id");
            veiculo.setContrato(contrato);
            veiculo.setNumeroGravame(veiculoRsng.getApontamento());
            return veiculo;
        }).collect(Collectors.toList());

        BeanUtils.copyProperties(contratoRsng, contrato, "id", "veiculos", "envios");
        contrato.setVeiculos(veiculos);
        contrato.setSituacao(Situacao.PENDENTE);
        save(contrato);
        return contrato;
    }

    public void assinar(Assinatura assinatura) throws ServiceException {
        Contrato contrato = contratoRepository.findOne(assinatura.getIdContrato());
        if (contrato.getAssinado() == SimNao.S) {
            throw new ServiceException("Esse contrato já está assinado");
        }

        Usuario usuario = usuarioService.findByCpfFinanceiras(assinatura.getUsuario());
        if (usuario.isPerfilFinanceira() && !usuario.getFinanceiras().contains(contrato.getFinanceira())) {
            throw new ServiceException("Sem autorização para assinar esse contrato");
        }

        assinatura.setData(new Date());
        assinatura = assinaturaRepository.save(assinatura);
        contrato.setAssinado(SimNao.S);
    }

//    public Contrato findByNumeroRegistroEletronicoFetchAnexo(Long numeroRegistroEletronico, String currentPrincipal) throws ServiceException {
//        Contrato contrato = findByNumeroRegistroEletronico(numeroRegistroEletronico, currentPrincipal);
//        Hibernate.initialize(contrato.getAnexos());
//        return contrato;
//    }


    public Contrato findByChassiAndSituacaoAtiva(String chassi) {
        return contratoRepository.findByChassiAndSituacao(Arrays.asList(chassi), Situacao.ATIVO);
    }

    public void baixarAssinar(Contrato entity, Assinatura assinatura) throws ServiceException {
        entity.setSituacao(Situacao.BAIXADO);
        entity.setAssinado(SimNao.S);
        contratoRepository.save(entity);
        eviarParaDetranCancelaBaixa(entity);
        assinaturaRepository.save(assinatura);
    }

    public void anularAssinar(Contrato entity, Assinatura assinatura) throws ServiceException {
        entity.setSituacao(Situacao.ANULADO);
        entity.setAssinado(SimNao.S);
        contratoRepository.save(entity);
        assinaturaRepository.save(assinatura);
    }

    public Contrato findByChassi(String chassi) {
        return contratoRepository.findByChassi(Arrays.asList(chassi));
    }

    public Contrato findTop1ByChassi(String chassi) {
        return contratoRepository.findTop1ByChassi(chassi);
    }

    public Contrato findByChassiAndNumeroContrato(String chassi, String numeroContrato) {
        return contratoRepository.findTop1ByChassiAndNumeroContrato(chassi, numeroContrato);
    }

    public Contrato findByCnpjAgenteFinanceiroAndChassiAndGravame(String cnpjAgenteFinanceiro, String chassi, String gravame, String currentPrincipal) throws ServiceException {
        Usuario usuario = usuarioService.findByCpfFinanceiras(currentPrincipal);

        if (usuario == null) {
            usuario = usuarioService.findByCpfApiFinanceiras(currentPrincipal);
        }

        if ((usuario.getPerfil() == Perfil.FINANCEIRA || !usuario.getFinanceiras().isEmpty()) && !usuario.getFinanceiras().stream().map(Financeira::getDocumento).collect(Collectors.toList()).contains(cnpjAgenteFinanceiro)) {
            throw new ServiceException("Seu usuário não tem autorização para operar no contrato desse agente financeiro");
        }

        Financeira financeira = financeiraRepository.findByDocumento(cnpjAgenteFinanceiro);
        if (financeira == null) {
            throw new ServiceException("O Agente Financeiro informado é inválido.");
        }

        Contrato contrato = contratoRepository.findByFinanceiraAndChassiAndGravame(financeira, chassi, gravame);
        if (contrato == null) {
            throw new ServiceException("Contrato não encontrado.");
        }

        return contrato;
    }

    public void salvarDocumento(Anexo anexo) throws ServiceException {
        try {
            String referenciaArquivo = PlaceconUtil.formataData(new Date()) + "_" + RandomStringUtils.randomAlphanumeric(10);
            anexo.setReferenciaArquivo(referenciaArquivo);
            File targetFile = new File(FILE_DIR, referenciaArquivo);
            FileUtils.copyInputStreamToFile(anexo.getFile(), targetFile);
            anexoRepository.save(anexo);
        } catch (IOException e) {
            logger.error(e.getMessage(), e);
            throw new ServiceException("Erro ao salvar o arquivo");
        }
    }

    public void salvarDocumento(List<Anexo> anexos, InputStream inputStream) throws ServiceException {
        try {
            String referenciaArquivo = PlaceconUtil.formataData(new Date()) + "_" + RandomStringUtils.randomAlphanumeric(10);
            File targetFile = new File(FILE_DIR, referenciaArquivo);
            FileUtils.copyInputStreamToFile(inputStream, targetFile);
            for (Anexo a : anexos) {
                a.setReferenciaArquivo(referenciaArquivo);
                anexoRepository.save(a);
            }
        } catch (IOException e) {
            logger.error(e.getMessage(), e);
            throw new ServiceException("Erro ao salvar o arquivo");
        }
    }

    public void cancelarAlteracao(Contrato entity) {
        entity = findOne(entity.getId());
        entity.setSituacao(Situacao.ATIVO);

        recuperarUltimoLogAtivoContrato(entity);

        for (Veiculo v : entity.getVeiculos()) {
            v.setMensagemRetorno(mensagemRetornoDETRANRepository.findTop1BySucessoAndUf(true, entity.getUfRegistro()));
        }
        contratoRepository.save(entity);
    }

    @Override
    public void delete(Long id) throws ServiceException {
        Contrato contrato = contratoRepository.findOne(id);
        for (Veiculo v : contrato.getVeiculos()) {
            if (StringUtils.isNotEmpty(v.getDocumentoArrecadacao())) {
                documentoArrecadacaoService.deleteByVeiculo(v);
            }
        }
        contratoRepository.delete(id);
    }

    private void recuperarUltimoLogAtivoContrato(Contrato entity) {
        Object[] obj = contratoRepository.findLastAtivo(entity.getId());

        if (obj != null) {
            Object[] ret = (Object[]) obj[0];
            entity.setAlteracao((Boolean) ret[0]);
            entity.setAprovadoAuditoria(ret[1] != null ? SimNao.valueOf((String) ret[1]) : null);
            entity.setAssinado(ret[2] != null ? SimNao.valueOf((String) ret[2]) : null);
            entity.setBairroDevedor((String) ret[3]);
            entity.setBairroGarantidor((String) ret[4]);
            entity.setCepDevedor((String) ret[5]);
            entity.setCepGarantidor((String) ret[6]);
            entity.setChassiSubstituicao((String) ret[7]);
            entity.setClausulaPenalVrg((String) ret[8]);
            entity.setComentario((String) ret[9]);
            entity.setComplementoEnderecoDevedor((String) ret[10]);
            entity.setComplementoEnderecoGarantidor((String) ret[11]);
            entity.setCpfCnpjCessaoDireito((String) ret[12]);
            entity.setCpfCnpjDevedorFinanciado((String) ret[13]);
            entity.setCpfCnpjGarantidorFinanciado((String) ret[14]);
            entity.setDataAditivoContrato((Date) ret[15]);
            entity.setDataCadastro((Date) ret[16]);
            entity.setDataCompra((Date) ret[17]);
            entity.setDataConclusaoDETRAN((Date) ret[18]);
            entity.setDataContrato((Date) ret[19]);
            entity.setDataLiberacaoCredito((Date) ret[20]);
            entity.setDataVencimentoPrimeiraParcela((Date) ret[21]);
            entity.setDataVencimentoUltimaParcela((Date) ret[22]);
            entity.setDddDevedor((Integer) ret[23]);
            entity.setDddGarantidor((Integer) ret[24]);
            entity.setDescricaoPenalidade((String) ret[25]);
            entity.setEmailDevedor((String) ret[26]);
            entity.setEmailGarantidor((String) ret[27]);
            entity.setEnderecoDevedor((String) ret[28]);
            entity.setEnderecoGarantidor((String) ret[29]);
            entity.setIndicadorComissao((Boolean) ret[30]);
            entity.setIndicadorPenalidade((Boolean) ret[31]);
            entity.setIndicadorTaxaMoraDia((Boolean) ret[32]);
            entity.setIndicadorTaxaMulta((Boolean) ret[33]);
            entity.setNomeCessaoDireito((String) ret[34]);
            entity.setNomeDevedorFinanciado((String) ret[35]);
            entity.setNomeGarantidorFinanciado((String) ret[36]);
            entity.setNumeroAditivoContrato((String) ret[37]);
            entity.setNumeroContrato((String) ret[38]);
            entity.setNumeroCotaConsorcio(ret[39] != null ? ((BigInteger) ret[39]).longValue() : null);
            entity.setNumeroEnderecoDevedor((String) ret[40]);
            entity.setNumeroEnderecoGarantidor((String) ret[41]);
            entity.setNumeroGrupoConsorcio((String) ret[42]);
            entity.setNumeroRegistroEletronico(ret[43] != null ? ((BigInteger) ret[43]).longValue() : null);
            entity.setNumeroRegistroEletronicoOrigem(ret[44] != null ? ((BigInteger) ret[44]).longValue() : null);
            entity.setPercentualComissao((BigDecimal) ret[45]);
            entity.setPossuiGarantidor(ret[46] != null ? SimNao.valueOf((String) ret[46]) : null);
            entity.setQuantidadeMeses((Integer) ret[47]);
            entity.setSiglaIndiceFinaceiro(ret[48] != null ? IndiceFinanceiro.valueOf((String) ret[48]) : null);
            entity.setSituacao(Situacao.ATIVO);
//			entity.setSituacaoFinanceira(ret[50] != null ? SituacaoFinanceira.valueOf((String)ret[50]) : null);
            entity.setTelefoneDevedor((Integer) ret[51]);
            entity.setTelefoneGarantidor((Integer) ret[52]);
            entity.setTipoContrato(ret[53] != null ? TipoContrato.valueOf((String) ret[53]) : null);
            entity.setTipoRestricao(ret[54] != null ? TipoRestricao.valueOf((String) ret[54]) : null);
            entity.setTipoVrg(ret[55] != null ? TipoVrg.valueOf((String) ret[55]) : null);
            entity.setUfEnderecoDevedor(ret[56] != null ? Uf.valueOf((String) ret[56]) : null);
            entity.setUfEnderecoGarantidor(ret[57] != null ? Uf.valueOf((String) ret[57]) : null);
            entity.setUfLiberacaoCredito(ret[58] != null ? Uf.valueOf((String) ret[58]) : null);
            entity.setUfRegistro(ret[59] != null ? Uf.valueOf((String) ret[59]) : null);
            entity.setValorCredito((BigDecimal) ret[60]);
            entity.setValorIOF((BigDecimal) ret[61]);
            entity.setValorParcela((BigDecimal) ret[62]);
            entity.setValorTaxaContrato((BigDecimal) ret[63]);
            entity.setValorTaxaJurosAno((BigDecimal) ret[64]);
            entity.setValorTaxaJurosMes((BigDecimal) ret[65]);
            entity.setValorTaxaMoraDia((BigDecimal) ret[66]);
            entity.setValorTaxaMulta((BigDecimal) ret[67]);
            entity.setValorTotalDivida((BigDecimal) ret[68]);
            entity.setValorVrg((BigDecimal) ret[69]);
//			entity.setArquivoRemessa(ret[70] != null ? arquivoRemessaService.findOne(((BigInteger)ret[70]).longValue()) : null);
//			entity.setCobranca(ret[71] != null ? cobrancaService.findOne(((BigInteger)ret[71]).longValue()) : null);
            entity.setFinanceira(ret[72] != null ? financeiraRepository.findOne(((BigInteger) ret[72]).longValue()) : null);
            entity.setMunicipioDevedor(ret[73] != null ? municipioService.findOne(((BigInteger) ret[73]).longValue()) : null);
            entity.setMunicipioGarantidor(ret[74] != null ? municipioService.findOne(((BigInteger) ret[74]).longValue()) : null);
            entity.setMunicipioLiberacao(ret[75] != null ? municipioService.findOne(((BigInteger) ret[75]).longValue()) : null);
//			entity.setLivro(ret[76] != null ? livroService.findOne(((BigInteger)ret[76]).longValue()) : null);
            entity.setIdProcessoB3(ret[77] != null ? ((BigInteger) ret[77]).longValue() : null);
            entity.setMotivoBaixa((String) ret[78]);
        }
    }

    public List<Veiculo> findByCobranca(Cobranca entity) {
        List<Veiculo> cobrancas = contratoRepository.findByCobranca(entity);
        for (Veiculo v : cobrancas) {
            if (v.getContrato().getCobranca().getCredenciamento().getTipoPreco() == TipoPreco.COMPOSTO) {
                for (PrecoComposto p : v.getContrato().getCobranca().getCredenciamento().getPrecosCompostos()) {
                    Hibernate.initialize(p);
                }
            }
        }
        return cobrancas;
    }

    public List<Veiculo> findVeiculosByCobranca(Cobranca entity) {
        return contratoRepository.findVeiculosByCobranca(entity);
    }

    public List<VeiculoRsng> findVeiculosSngByCobranca(Cobranca entity) {
        return contratoRepository.findVeiculosByCobrancaSng(entity);
    }


    public List<Contrato> findContratosSemTaxa(Uf uf) {
        return contratoRepository.findContratoSemTaxa(uf);
    }

    public Contrato findByChassiGravame(String numeroChassi, String numeroGravame) {
        return contratoRepository.findByChassiGravame(numeroChassi, numeroGravame);
    }

    public Long findByProcessoB3(Long processoB3) {
        return contratoRepository.findByProcessoB3(processoB3);
    }

    public void incrementarTransacao(Contrato entity) throws ServiceException {
        Contrato contrato = findOne(entity.getId());
        if (contrato.getSituacao() != Situacao.ERRO) {
            throw new ServiceException("Só é possível incrementar o contador de contratos na situação de 'ERRO'");
        }

        for (Veiculo v : entity.getVeiculos()) {
            Veiculo veiculo = veiculoRepository.findOne(v.getId());
            veiculo.setContadorTransacao(v.getContadorTransacao());
            veiculoRepository.save(veiculo);
        }
    }

    public void resultadoAuditoria(Contrato contrato, String mensagem, SimNao resultado) throws ServiceException {
        contrato = findOne(contrato.getId());
        RegistroAuditoria registroAuditoria = new RegistroAuditoria();
        registroAuditoria.setUsuario("DETRANMS");
        registroAuditoria.setData(new Date());
        registroAuditoria.setContrato(contrato);
        registroAuditoria.setMensagem(mensagem);
        registroAuditoria.setAprovado(resultado);
        contrato.getAuditorias().add(registroAuditoria);
        contrato.setAprovadoAuditoria(resultado);
        save(contrato);
    }

    public void notificarErro(Long numeroregistro, String mensagem, String chassi) throws ServiceException {
        Contrato contrato = contratoRepository.findByNumeroRegistroEletronico(numeroregistro);
        MensagemRetorno mensagemRetorno = mensagemRetornoDETRANRepository.findTop1ByErroPadraoAndUf(true, contrato.getUfRegistro());

        contrato.setSituacao(Situacao.ERRO);
        for (Veiculo v : contrato.getVeiculos()) {
            if (chassi.equals(v.getNumeroChassi())) {
                v.setMensagemRetorno(mensagemRetorno);
                v.setDataTransmissaoDETRAN(new Date());
                v.setMensagemRetornoDetalhada(mensagem);
            }
        }
        save(contrato);
    }

    public void notificarSucesso(Long numeroregistro, String numeroRegistroDetran, String chassi) throws ServiceException {
        Contrato entity = contratoRepository.findByNumeroRegistroEletronico(numeroregistro);

        for (Veiculo v : entity.getVeiculos()) {
            if (v.getNumeroChassi().equals(chassi)) {
                v.setMensagemRetorno(mensagemRetornoDETRANRepository.findTop1BySucessoAndUf(Boolean.TRUE, entity.getUfRegistro()));
                v.setMensagemRetornoDetalhada(null);
                v.setDataTransmissaoDETRAN(new Date());
                v.setNumeroRegistroDetran(numeroRegistroDetran);
                v.setMensagemRetorno(null);
                v.setMensagemRetornoDetalhada(null);
            }
        }
        if (entity.getVeiculosComErro() == 0) {
            entity.setDataConclusaoDETRAN(new Date());
            if (isUfAtivoAposEnvio(entity.getUfRegistro())) {
                entity.setSituacao(Situacao.ATIVO);
            } else {
                entity.setSituacao(Situacao.PENDENTE_PAGAMENTO);
            }
        }
        save(entity);
    }

    public void atualizarSituacao(Contrato entity, Situacao situacaoContrato) {
        entity = contratoRepository.findOne(entity.getId());
        entity.setSituacao(situacaoContrato);
        if (situacaoContrato == Situacao.ERRO) {
            entity.setDataConclusaoDETRAN(null);
            MensagemRetorno retorno = mensagemRetornoDETRANRepository.findTop1ByErroPadraoAndUf(true, entity.getUfRegistro());
            for (Veiculo v : entity.getVeiculos()) {
                v.setDataTransmissaoDETRAN(null);
                v.setMensagemRetorno(retorno);
                v.setMensagemRetornoDetalhada(retorno.getDescricao());
            }
        }

        if (situacaoContrato == Situacao.PENDENTE) {
            entity.setDataConclusaoDETRAN(null);
            for (Veiculo v : entity.getVeiculos()) {
                logger.info("#ATUALIZAR SITUACAO | Colocando contrato como Pendente -> " + entity.getUfRegistro() + " Veículo: " + v.getNumeroChassi() + " id: " + entity.getId());
                v.setDataTransmissaoDETRAN(null);
            }
        }
        contratoRepository.save(entity);
    }

    public void alterarTipoContrato(Contrato entity) throws ServiceException {
        if (entity.getTipoContrato() == TipoContrato.CONTRATO_PRINCIPAL) {
            entity.setDataAditivoContrato(null);
            entity.setNumeroAditivoContrato(null);
        } else {
            if (entity.getDataAditivoContrato() == null || StringUtils.isEmpty(entity.getNumeroAditivoContrato())) {
                throw new ServiceException("Em caso de aditivo informe a data e o número do adtivo de contrato");
            }
        }
        contratoRepository.save(entity);
    }

    public Veiculo getVeiculoByChassi(String chassi) {
        return veiculoRepository.findFirstByNumeroChassi(chassi);
    }

    private Contrato copyContrato(Contrato contratoAntigo) {
        Contrato newContrato = new Contrato();
        BeanUtils.copyProperties(contratoAntigo, newContrato);
        newContrato.setVeiculos(limparIdVeiculos(contratoAntigo.getVeiculos()));
        newContrato.setAnexos(null);
        newContrato.setAssinaturas(null);
        newContrato.setAuditorias(null);
        newContrato.setEnvios(null);
        newContrato.setId(null);
        newContrato.setCobranca(null);
        newContrato.setDataConclusaoDETRAN(null);
        newContrato.setSituacaoFinanceira(SituacaoFinanceira.NAO_PAGO);
        newContrato.setNumeroRegistroEletronicoOrigem(Objects.nonNull(contratoAntigo.getNumeroRegistroEletronicoOrigem()) ? contratoAntigo.getNumeroRegistroEletronicoOrigem() : contratoAntigo.getNumeroRegistroEletronico());
        return newContrato;
    }

    private List<Veiculo> limparIdVeiculos(List<Veiculo> veiculos) {

        return veiculos.stream().map(veiculo -> {
            veiculo.setId(null);
            veiculo.setDataTransmissaoDETRAN(null);
            return veiculo;
        }).collect(Collectors.toList());
    }

    private Boolean isUfCobraAlteracao(Uf uf) {
        return ufCobraAlteracao.contains(uf);
    }

    private Boolean isUfAtivoAposEnvio(Uf uf) {
        return contratoAtivoAposEnviar.contains(uf);
    }

    public Contrato findById(Long id) {
        Optional<Contrato> contrato = contratoRepository.findByMarcaId(id);
        if (contrato.isPresent()) {
            return contrato.get();
        }
        return null;
    }

    public RetornoContratoDetranDTO consultaContrato(
            Uf estado,
            String documentoFinanceira,
            String numeroContrato,
            String tipo,
            String chassi,
            Date dataInicio,
            Date dataFim,
            SituacaoContrato situacao) throws Exception {

        RetornoContratoDetranDTO resultado = null;

        if (migracaoService.isEnviarParaWinov(estado)) {

            return migracaoService.consultarContratoNaEquinix(
                    estado, documentoFinanceira, numeroContrato,
                    tipo, chassi, dataInicio, dataFim, situacao
            );

        } else {
            HandlerWsDetranClient handler = consultarWsDetran(estado).get();
            if (tipo == null || tipo.equals("CHASSI")) {
                resultado = handler.consultarContratoChassi(chassi, documentoFinanceira, numeroContrato);
            } else if (tipo.equals("VEICULO_CHASSI")) {
                resultado = handler.consultarContratosPorVeiculo(documentoFinanceira, chassi, dataInicio, dataFim, situacao.toString());
            } else {
                resultado = handler.consultarContratoNumero(documentoFinanceira, numeroContrato);
            }
            return resultado;
        }
    }


    public ByteArrayResource generatePdfComprovante(Map<String, Object> variables) throws DocumentException {
        try {
            LOGGER.info("Iniciando geração do PDF...");

            Contrato contrato = (Contrato) variables.get("contrato");

            if (contrato == null) {
                throw new RuntimeException("Contrato não encontrado no contexto do template.");
            }

            Comprovante comprovante = comprovanteRepository.findByContratoId(contrato.getId());

            if (comprovante == null) {
                throw new RuntimeException("Nenhum comprovante encontrado para o contrato ID: " + contrato.getId());
            }

            Resource detranResource = resourceLoader.getResource("classpath:static/assets/images/" + contrato.getUfRegistro() + ".png");

            Resource logoResource = resourceLoader.getResource("classpath:static/assets/images/logo.png");

            File detranFile = detranResource.getFile();
            String absolutePathDetran = detranFile.getAbsolutePath().replace("\\", "/");
            String imageDetranPath = "file:///" + absolutePathDetran;
            variables.put("imageDetranPath", imageDetranPath);
            LOGGER.info("Caminho da logo detran (URI): {}", absolutePathDetran);


            File logoFile = logoResource.getFile();
            String absolutePath = logoFile.getAbsolutePath().replace("\\", "/");
            String imagePath = "file:///" + absolutePath;
            variables.put("imagePath", imagePath);
            LOGGER.info("Caminho da logo (URI): {}", imagePath);


            String baseUrl = ServletUriComponentsBuilder.fromCurrentContextPath().build().toUriString();
            variables.put("baseUrl", baseUrl);
            variables.put("comprovante", comprovante);


            byte[] qrCode = generateQRCodeAsync(comprovante.getCodigo(), String.valueOf(contrato.getNumeroRegistroEletronico())).get();
            String qrCodeBase64 = Base64.getEncoder().encodeToString(qrCode);
            variables.put("qrCodeBase64", "data:image/png;base64," + qrCodeBase64);
            LOGGER.info("QR Code gerado e convertido para Base64.");

            Context context = new Context();
            context.setVariables(variables);
            String htmlContent = templateEngine.process("print-pdf-contrato", context);
            LOGGER.info("HTML gerado: {}", htmlContent);

            if (htmlContent == null) {
                LOGGER.error("O conteúdo HTML gerado está vazio!");
                throw new RuntimeException("Erro ao processar o template Thymeleaf: conteúdo HTML vazio.");
            }

            LOGGER.info("HTML gerado com sucesso. Criando PDF...");
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();

            String baseFileUrl = "file:///" + logoFile.getParent().replace("\\", "/");
            LOGGER.info("Base URL configurada para o PDF: {}", baseFileUrl);

            PdfRendererBuilder builder = new PdfRendererBuilder();
            builder.withHtmlContent(htmlContent, baseFileUrl);
            builder.toStream(outputStream);
            builder.run();

            LOGGER.info("PDF gerado com sucesso.");
            return new ByteArrayResource(outputStream.toByteArray());

        } catch (Exception e) {
            LOGGER.error("Erro ao gerar PDF: ", e);
            throw new DocumentException("Erro ao gerar PDF", e);
        }
    }

    public byte[] generateQRCode(String codigoComprovante, String numeroRegistroEletronico) throws WriterException, IOException {
        String baseUrl = ServletUriComponentsBuilder.fromCurrentContextPath().build().toUriString();
        String url = baseUrl + "/registro/public/validate.xhtml";

        String text = url + "?codigoComprovante=" + codigoComprovante + "&numeroRegistroEletronico=" + numeroRegistroEletronico;
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        BitMatrix matrix = new MultiFormatWriter().encode(text, BarcodeFormat.QR_CODE, 256, 256);
        MatrixToImageWriter.writeToStream(matrix, MediaType.IMAGE_PNG.getSubtype(), baos, new MatrixToImageConfig());
        return baos.toByteArray();
    }

    @Async
    public ListenableFuture<byte[]> generateQRCodeAsync(String codigoComprovante, String numeroRegistroEletronico) throws Exception {
        return new AsyncResult<byte[]>(generateQRCode(codigoComprovante, numeroRegistroEletronico));
    }

}
