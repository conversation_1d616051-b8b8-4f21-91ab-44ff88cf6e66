package com.registrocontrato.registro.service.detran;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.registrocontrato.infra.email.Email;
import com.registrocontrato.infra.email.EnviaEmail;
import com.registrocontrato.infra.entity.Financeira;
import com.registrocontrato.infra.entity.Uf;
import com.registrocontrato.infra.exception.ServiceException;
import com.registrocontrato.registro.entity.*;
import com.registrocontrato.registro.enums.TipoPesquisaGravame;
import com.registrocontrato.registro.repository.MensagemRetornoRepository;
import com.registrocontrato.registro.service.dto.*;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.web.client.HttpClientErrorException;

import javax.mail.internet.InternetAddress;
import javax.xml.bind.JAXBContext;
import javax.xml.bind.JAXBException;
import javax.xml.bind.Marshaller;
import java.io.IOException;
import java.io.InputStream;
import java.io.StringWriter;
import java.util.*;

public interface HandlerWsDetranClient {

	MensagemRetornoDTO comunicarContratoFinanceiroVeiculo(Contrato contrato, Veiculo veiculo);

	default RetornoContratoDetranDTO consultarContratoChassi(String chassi, String financeira, String numeroContrato) throws Exception {
		return null;
	}

	default RetornoContratoDetranDTO consultarContratoNumero(String cnpjAgente, String numeroContrato) throws Exception {
		return null;
	}

	default RetornoGravameDTO consultarGravame(TipoPesquisaGravame tipoPesquisaGravame, Financeira financeira, String valor, String outro) {
		return null;
	}

	default MensagemRetornoDTO cancelarBaixarContrato(Contrato contrato, Veiculo veiculo, String cancelarBaixar) {
		return null;
	}

	default MensagemRetornoDTO alterarSenha(String novaSenha) {
		return null;
	}

	default MensagemRetornoDTO comunicarContratoFinanceiroVeiculo(Contrato contrato) {
		return comunicarContratoFinanceiroVeiculo(contrato, null);
	}

	default MensagemRetornoDTO consultarNumeroDetran(String chassi, int remarcado, String cnpjAgente, int gravame, int tipoRestricao) {
		return null;
	}

	default  RetornoStatusTaxaVeiculoDTO consultaStatusTaxasVeiculo(Contrato contrato, String numeroChassi) {
	    return null;
	}

	default  RetornoGadELoteTarifasDTO listaGadeLoteTarifas(Financeira financeira , Date dataInicio, Date dataFim) {
	    return null;
	}

	default RetornoContratoDetranDTO consultarContratosPorVeiculo(String financeira, String chassi, Date dataInicio, Date dataFim, String situacao) throws Exception {
		return null;
	}

	default MensagemRetornoDTO envioImagem(Veiculo veiculo, String referenciaArquivo) throws ServiceException, IOException {
		throw new ServiceException("Método não disponível para esse estado");
	}

	default void enviarEmailSuporte(EnviaEmail enviaEmail, String mensagem) {
		try {
			HashMap<Character, List<InternetAddress>> hash = new HashMap<Character, List<InternetAddress>>();
			List<InternetAddress> emails = new ArrayList<>();
			emails.add(new InternetAddress("<EMAIL>"));
			hash.put(Email.TIPO_PARA, emails);
			Email email = new Email(enviaEmail);
			email.enviarEmail("Erro no envio de Contrato", hash, mensagem);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	default Log getLogger() {
		return LogFactory.getLog(getClass());
	}

	default Object getJSON(Object object) {
		try {
			return new ObjectMapper().writeValueAsString(object);
		} catch (JsonProcessingException e) {
			getLogger().error(e.getMessage());
		}

		return null;
	}

	default Object getXml(Object object) {
		try {
			StringWriter writer = new StringWriter();
			JAXBContext context = JAXBContext.newInstance(object.getClass());
			Marshaller m = context.createMarshaller();
			m.marshal(object, writer);
			return writer.toString();
		} catch (JAXBException e) {
			getLogger().error(e.getMessage(), e);
		}
		return null;
	}

	default InputStream downloadBoleto(Cobranca cobranca) {
		return null;
	}

	MensagemRetornoRepository getMensagemRetornoRepository();

	default MensagemRetornoDTO getErroPadrao(Uf uf) {
		MensagemRetorno mensagem = getMensagemRetornoRepository().findTop1ByErroPadraoAndUf(true, uf);
		MensagemRetornoDTO retorno = new MensagemRetornoDTO();
		BeanUtils.copyProperties(mensagem, retorno);
		return retorno;
	}

	default MensagemRetornoDTO getErro(Uf uf, String codigo) {
		MensagemRetorno mensagem = getMensagemRetornoRepository().findTop1ByCodigoAndUf(codigo, uf);
		MensagemRetornoDTO retorno = new MensagemRetornoDTO();
		if (Objects.nonNull(mensagem))
			BeanUtils.copyProperties(mensagem, retorno);
		retorno.setCodigo(codigo);
		retorno.setSucesso(false);
		return retorno;
	}

	default MensagemRetornoDTO getSucesso(Uf uf) {
		MensagemRetorno mensagem = getMensagemRetornoRepository().findTop1BySucessoAndUf(true, uf);
		MensagemRetornoDTO retorno = new MensagemRetornoDTO();
		BeanUtils.copyProperties(mensagem, retorno);
		return retorno;
	}

	default MensagemRetornoDTO cancelamentoDocumentoArrecadacao(DocumentoArrecadacao documentoArrecadacao) {
		return null;
	}

	default MensagemRetornoDTO emitirDocumentoArrecadacao(DocumentoArrecadacao documentoArrecadacao) {
		return null;
	}

	default Map<String, Object> getResponseErrorBody(HttpClientErrorException e) {
		try {
			return new ObjectMapper().readValue(e.getResponseBodyAsString(), Map.class);
		} catch (IOException io) {
			return null;
		}
	}

	Uf getUf();
}
