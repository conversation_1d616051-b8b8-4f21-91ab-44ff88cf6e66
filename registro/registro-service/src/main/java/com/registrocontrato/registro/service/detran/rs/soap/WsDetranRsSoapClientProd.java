package com.registrocontrato.registro.service.detran.rs.soap;

import com.registrocontrato.infra.entity.Financeira;
import com.registrocontrato.infra.entity.SimNao;
import com.registrocontrato.infra.entity.Uf;
import com.registrocontrato.infra.exception.ServiceException;
import com.registrocontrato.infra.util.PlaceconUtil;
import com.registrocontrato.registro.entity.Contrato;
import com.registrocontrato.registro.repository.DocumentoArrecadacaoRepository;
import com.registrocontrato.registro.repository.MensagemRetornoRepository;
import com.registrocontrato.registro.service.RegistroEnvioService;
import com.registrocontrato.registro.service.detran.rs.client.*;
import com.registrocontrato.registro.service.dto.RetornoGadELoteTarifasDTO;
import com.registrocontrato.registro.service.dto.RetornoStatusTaxaVeiculoDTO;
import com.registrocontrato.seguranca.repository.FinanceiraRepository;
import com.registrocontrato.seguranca.service.AcessoSenhaService;
import com.registrocontrato.seguranca.service.UsuarioService;
import com.registrocontrato.seguranca.service.dto.AcessoSenhaDTO;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Component;
import org.springframework.ws.soap.client.SoapFaultClientException;

import javax.xml.bind.JAXBIntrospector;
import java.io.FileInputStream;
import java.math.BigInteger;
import java.security.KeyStore;
import java.security.PrivateKey;
import java.security.cert.X509Certificate;
import java.util.Date;
import java.util.List;

import static com.registrocontrato.infra.util.PlaceconUtil.*;

@Component
@Profile({"prod", "hot","prod-rj"})
public class WsDetranRsSoapClientProd extends WsDetranRS {

    public WsDetranRsSoapClientProd(MensagemRetornoRepository mensagemRetornoRepository,
                                    FinanceiraRepository financeiraRepository,
                                    DocumentoArrecadacaoRepository documentoArrecadacaoRepository,
                                    RegistroEnvioService registroEnvioService,
                                    AcessoSenhaService acessoSenhaservice,
                                    UsuarioService usuarioService) {
        super(mensagemRetornoRepository, financeiraRepository, documentoArrecadacaoRepository, registroEnvioService, acessoSenhaservice, usuarioService);
    }

    @Override
    protected Object callSoap(Object request, String cnpjFinanceira) {
        getWebServiceTemplate().setMessageSender(httpComponentsMessageSender());
        getWebServiceTemplate().setMarshaller(marshaller());
        getWebServiceTemplate().setUnmarshaller(marshaller());

        AcessoSenhaDTO acessoSenhaDTO = acessoSenhaservice.recuperarAcessoDetranFinanceira(Uf.RS, cnpjFinanceira);

        X509Certificate x509Certificate = null;
        PrivateKey privateKey = null;
        try {
            logger.info("Iniciando extração da chave pública");
            KeyStore keystore = KeyStore.getInstance("PKCS12");
            char[] password = acessoSenhaDTO.getAcessos()[1].toCharArray();
            logger.info("Vai carregar o certificado no seguinte PATH" + getCertDir() + acessoSenhaDTO.getReferenciaArquivo());
            keystore.load(new FileInputStream(getCertDir() + acessoSenhaDTO.getReferenciaArquivo()), password);
            logger.info("Carregou o certificado");
            String alias = keystore.aliases().nextElement();
            logger.info("Vai realizar a extração das chaves privadas e públicas");
            x509Certificate = (X509Certificate) keystore.getCertificate(alias);
            privateKey = (PrivateKey) keystore.getKey(alias, password);
        } catch (Exception e) {
            logger.info("Erro ao carregar chave privada e pública");
            throw new ServiceException("Erro ao carregar chave privada | publica");
        }
        getLogger().info("Vai Iniciar a requisição");
        Object jaxbElement = getWebServiceTemplate().marshalSendAndReceive(getUrl().trim(), request, webServiceMessageCallback(x509Certificate, privateKey));
        getLogger().info(jaxbElement);
        return JAXBIntrospector.getValue(jaxbElement);
    }

    @Override
    public RetornoStatusTaxaVeiculoDTO consultaStatusTaxasVeiculo(Contrato contrato, String numeroChassi) {
        StatusTaxasVeiculo request = new StatusTaxasVeiculo();

        StatusTaxaVeiculo statusTaxaVeiculo = new StatusTaxaVeiculo();
        request.setStatusTaxasVeiculoRequest(statusTaxaVeiculo);

        StatusTaxaVeiculo.InfStatusTaxasVeiculo infStatusTaxasVeiculo = new StatusTaxaVeiculo.InfStatusTaxasVeiculo();
        statusTaxaVeiculo.setInfStatusTaxasVeiculo(infStatusTaxasVeiculo);

        Financeira financeira = financeiraRepository.findOne(contrato.getFinanceira().getId());

        infStatusTaxasVeiculo.setId("contrato" + limparString(contrato.getNumeroContrato()));
        infStatusTaxasVeiculo.setCnpjAgente(retiraFormatacao(financeira.getDocumento()));
        infStatusTaxasVeiculo.setChassi(numeroChassi);

        try {
            StatusTaxasVeiculoResponse response = (StatusTaxasVeiculoResponse) callSoap(request, contrato.getFinanceira().getDocumento());
            StatusTaxaVeiculoRetorno statusTaxasVeiculoResponse = response.getStatusTaxasVeiculoResponse();
            logger.info(PlaceconUtil.objectToString(statusTaxasVeiculoResponse));
            RetornoStatusTaxaVeiculoDTO retornoDto = new RetornoStatusTaxaVeiculoDTO();
            retornoDto.setChassi(numeroChassi);
            retornoDto.setCnpjAgente(infStatusTaxasVeiculo.getCnpjAgente());
            retornoDto.setTaxaReservaGravame(statusTaxasVeiculoResponse.isTaxaReservaGravame() ? SimNao.S : SimNao.N);
            retornoDto.setTaxaReservaContrato(statusTaxasVeiculoResponse.isTaxaRegistroContrato() ? SimNao.S : SimNao.N);
            return retornoDto;
        } catch (SoapFaultClientException e) {
            logger.error(e.getFaultStringOrReason(), e);
            return null;
        } catch (Exception e) {
            logger.error(e.getMessage());
            return null;
        }
    }

    @Override
    public RetornoGadELoteTarifasDTO listaGadeLoteTarifas(Financeira financeira, Date dataInicio, Date dataFim) {
        RetornoGadELoteTarifasDTO retornoDto = null;

        ListaGadELoteTarifasRequest request = new ListaGadELoteTarifasRequest();
        GadELoteTarifasListaPedido gadELoteTarifasListaPedido = new GadELoteTarifasListaPedido();
        request.setGadELoteTarifasListaPedido(gadELoteTarifasListaPedido);
        GadELoteTarifasListaPedido.InfContrato infContrato = new GadELoteTarifasListaPedido.InfContrato();
        gadELoteTarifasListaPedido.setInfContrato(infContrato);

        String cnpjAgente = retiraFormatacao(financeira.getDocumento());
        infContrato.setCnpjAgente(cnpjAgente);
        infContrato.setDataFimPeriodo(new BigInteger(formataData(dataFim)));
        infContrato.setDataIniPeriodo(new BigInteger(formataData(dataInicio)));
        infContrato.setId("lista" + cnpjAgente);

        try {
            ListaGadELoteTarifasResponse response = (ListaGadELoteTarifasResponse) callSoap(request, financeira.getDocumento());

            List<GadELoteTarifasPagamento> listaGadEPagamentos = response.getListaGadELoteTarifasResponse().getListaGadEPagamentos();
            retornoDto = new RetornoGadELoteTarifasDTO();

            for (GadELoteTarifasPagamento gadE : listaGadEPagamentos) {
                RetornoGadELoteTarifasDTO.GadePagamentoDTO gadEDTO = new RetornoGadELoteTarifasDTO.GadePagamentoDTO();
                gadEDTO.setCodPagto(gadE.getCodPagto().toString());
                gadEDTO.setCodTarifa(gadE.getCodTarifa().toString());
                gadEDTO.setDataGeracao(formataData(gadE.getDataPagto().toString()));
                gadEDTO.setDataPagto(formataData(gadE.getDataPagto().toString()));
                gadEDTO.setExercPagto(gadE.getExercPagto().toString());
                gadEDTO.setNroGade(gadE.getNroGade().toString());
                gadEDTO.setOrigemPagto(gadE.getOrigemPagto().toString());
                gadEDTO.setQtdTarifas(gadE.getQtdTarifas().toString());
                gadEDTO.setQtdTarifasDisponiveis(gadE.getQtdTarifasDisponiveis().toString());
                retornoDto.getListaGadePagamentoDTO().add(gadEDTO);
            }
            return retornoDto;
        } catch (SoapFaultClientException e) {
            logger.error(e.getFaultStringOrReason(), e);
            return null;
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return null;
        }
    }
}
