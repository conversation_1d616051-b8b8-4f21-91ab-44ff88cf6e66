package com.registrocontrato.registro.repository;

import com.registrocontrato.infra.entity.Financeira;
import com.registrocontrato.infra.entity.Uf;
import com.registrocontrato.infra.service.BaseRepository;
import com.registrocontrato.registro.entity.Contrato;
import com.registrocontrato.registro.entity.DocumentoArrecadacao;
import com.registrocontrato.registro.entity.Veiculo;
import org.springframework.data.jpa.repository.EntityGraph;
import org.springframework.data.jpa.repository.Lock;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import javax.persistence.LockModeType;
import java.util.Date;
import java.util.List;
import java.util.Optional;

@Repository
public interface DocumentoArrecadacaoRepository extends BaseRepository<DocumentoArrecadacao> {

    @Query("select doc from DocumentoArrecadacao doc "
            + "where doc.codigo = :codigo")
    Optional<DocumentoArrecadacao> findByCodigo(@Param("codigo") String codigo);

    @Lock(LockModeType.PESSIMISTIC_WRITE)
    DocumentoArrecadacao findTop1ByEstadoAndVeiculoIsNullAndDataPagamentoIsNotNullOrderByDataPagamentoAsc(@Param("estado") Uf estado);

    Optional<DocumentoArrecadacao> findTop1ByVeiculo(@Param("veiculo") Veiculo veiculo);

    Optional<DocumentoArrecadacao> findByVeiculoAndCodigo(@Param("veiculo") Veiculo veiculo, @Param("codigo") String codigo);

    DocumentoArrecadacao findByCodigoOrLinhaDigitavel(String codigo, String linhaDigitavel);

    @Query("select da from DocumentoArrecadacao da "
            + "where da.veiculo.id = "
            + " (select v.id from Veiculo v inner join v.contrato c "
            + "    where c = :contrato and :veiculo in c.veiculos)")
    DocumentoArrecadacao findbyContratoAndVeiculoContrato(@Param("contrato") Contrato contrato, @Param("veiculo") Veiculo veiculo);

    @Query("select doc from DocumentoArrecadacao doc "
            + "inner join fetch doc.veiculo ve "
            + "inner join fetch ve.contrato con "
            + "where doc.dataPagamento is null and doc.estado = :uf "
            + "and doc.notificado = 'N'")
    List<DocumentoArrecadacao> recuperaDocumentosNaoPagoAndNaoNotificado(@Param("uf") Uf uf);

    @Query("select doc from DocumentoArrecadacao doc "
            + "inner join fetch doc.veiculo ve "
            + "inner join fetch ve.contrato con "
            + "where doc.dataPagamento is null and doc.estado = :uf "
            + "and doc.notificado = 'S' "
            + "and con.dataCadastro < :data")
    List<DocumentoArrecadacao> recuperaDocumentosNaoPagoAndNotificado(@Param("uf") Uf uf, @Param("data") Date data);


    @Query("select doc from DocumentoArrecadacao doc "
            + "inner join fetch doc.financeira f "
            + "where doc.dataPagamento is null and doc.estado = :estado "
            + "and doc.notificado = 'S'")
    List<DocumentoArrecadacao> findByEstadoAndDataPagamentoIsNull(@Param("estado") Uf estado);

    DocumentoArrecadacao findFirstByCodigoAndEstadoAndDataPagamentoIsNotNull(String codigo, Uf estado);

    DocumentoArrecadacao findByCodigoOrLinhaDigitavelAndLinhaDigitavelIsNotNull(String codigo, String linhaDigitavel);

    @Query("select max(doc.requestId) from  DocumentoArrecadacao doc")
    Integer findByMaxRequestId();

    @Query("select doc from DocumentoArrecadacao doc where doc.requestId is not null order by doc.requestId desc")
    List<DocumentoArrecadacao> findCobrancasBB();

    @Query("SELECT d FROM DocumentoArrecadacao d " +
            "left join fetch d.veiculo v " +
            "WHERE d.dataPagamento is null AND d.linhaDigitavel is not null AND " +
            "(d.confirmacaoPagamentoConsistente is null or d.confirmacaoPagamentoConsistente = 'N') AND " +
            "(d.confirmacaoPagamentoBanco is null or d.confirmacaoPagamentoBanco = 'N') AND d.estado IN :estado")
    List<DocumentoArrecadacao> buscaGuiasParaPagamento(@Param("estado") List<Uf> estado);

    @Query("SELECT d FROM DocumentoArrecadacao d " +
            "left join fetch d.veiculo v " +
            "WHERE d.linhaDigitavel is not null AND " +
            "d.dataPagamento is null AND " +
            "d.confirmacaoPagamentoConsistente = 'S' AND d.codigoAutenticacaoPagamento is null AND d.codigoIdentificadorPagamento is not null AND " +
            "(d.confirmacaoPagamentoBanco is null or d.confirmacaoPagamentoBanco = 'N') AND d.estado IN :estado")
    List<DocumentoArrecadacao> buscaGuiasParaConfirmacaoDePagamento(@Param("estado") List<Uf> estado);

    @Query("select doc from DocumentoArrecadacao doc "
            + "inner join fetch doc.financeira f "
            + "where doc.dataPagamento >= :dataPagamento and doc.estado = :estado and doc.financeira in (:financeiras)")
    List<DocumentoArrecadacao> findAllByEstadoAndFinanceiraAndDataPagamentoIsGreaterThanEqual(@Param("estado") Uf estado, @Param("financeiras") List<Financeira> financeiras, @Param("dataPagamento") Date dataPagamento);

    @EntityGraph(attributePaths = {"financeira"})
    DocumentoArrecadacao findDocumentoArrecadacaoByCodigo(@Param("codigo") String codigo);

    DocumentoArrecadacao findByCodigoBarras(String codigoBarras);

    @Query("select max(codigo) from DocumentoArrecadacao where veiculo=:veiculo")
    String findCodigoByVeiculo(@Param("veiculo") Veiculo veiculo);

    @Query("select d from DocumentoArrecadacao d where d.veiculo=:veiculo")
    Optional<DocumentoArrecadacao> findDocumentoArrecadacaoByVeiculo(@Param("veiculo") Veiculo veiculo);
}
