package com.registrocontrato.registro.service.cobranca.calculadora.nordeste;

import com.registrocontrato.infra.entity.Uf;
import com.registrocontrato.registro.entity.Cobranca;
import com.registrocontrato.registro.service.CredenciamentoService;
import com.registrocontrato.registro.service.CupomDescontoService;
import com.registrocontrato.registro.service.MigracaoService;
import com.registrocontrato.registro.service.cobranca.gravame.GravameService;
import com.registrocontrato.registro.service.detran.pi.client.ConsultaBoletoPiResponse;
import com.registrocontrato.registro.service.detran.pi.rest.WsDetranPiRest;
import com.registrocontrato.registro.service.cobranca.CobrancaService;
import com.registrocontrato.registro.service.cobranca.calculadora.CalculadoraDeCobrancaDetranIntegrado;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.Objects;

@Component
public class CalculadoraDeCobrancaPI extends CalculadoraDeCobrancaDetranIntegrado {

    private final WsDetranPiRest wsDetranPiRest;
    private final MigracaoService migracaoService;

    public CalculadoraDeCobrancaPI(@Lazy CobrancaService cobrancaService, CredenciamentoService credenciamentoService, CupomDescontoService cupomDescontoService, WsDetranPiRest wsDetranPiRest, GravameService gravameService, MigracaoService migracaoService) {
        super(cobrancaService, credenciamentoService, cupomDescontoService, gravameService);
        this.wsDetranPiRest = wsDetranPiRest;
        this.migracaoService = migracaoService;
    }

    @Override
    protected BigDecimal getValorBoletoDetran(Cobranca cobranca) {
        try {
            ConsultaBoletoPiResponse consultarBoleto;
            if (migracaoService.isEnviarParaWinov(Uf.PI)) {
                consultarBoleto = migracaoService.consultarCobrancaPi(cobranca.getDataInicio(), cobranca.getFinanceira().getDocumento());
            } else {
                consultarBoleto = wsDetranPiRest.consultarBoleto(cobranca.getDataInicio(), cobranca.getFinanceira().getDocumento());
            }

            if (Objects.nonNull(consultarBoleto) && Objects.nonNull(consultarBoleto.getValor())) {
                return BigDecimal.valueOf(consultarBoleto.getValor());
            }
        } catch (Exception e) {
            logger.error(e.getMessage());
            return BigDecimal.ZERO;
        }
        return BigDecimal.ZERO;
    }

    @Override
    public Uf getUf() {
        return Uf.PI;
    }
}
