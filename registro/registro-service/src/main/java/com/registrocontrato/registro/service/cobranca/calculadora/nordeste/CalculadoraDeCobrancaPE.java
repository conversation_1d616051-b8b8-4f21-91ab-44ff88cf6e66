package com.registrocontrato.registro.service.cobranca.calculadora.nordeste;

import com.registrocontrato.infra.entity.Uf;
import com.registrocontrato.registro.service.ArquivoCobrancaService;
import com.registrocontrato.registro.service.CredenciamentoService;
import com.registrocontrato.registro.service.CupomDescontoService;
import com.registrocontrato.registro.service.cobranca.CobrancaService;
import com.registrocontrato.registro.service.cobranca.calculadora.CalculadoraDeCobrancaArquivoFTP;
import com.registrocontrato.registro.service.cobranca.gravame.GravameService;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

@Component
public class CalculadoraDeCobrancaPE extends CalculadoraDeCobrancaArquivoFTP {

    public CalculadoraDeCobrancaPE(@Lazy CobrancaService cobrancaService, CredenciamentoService credenciamentoService, CupomDescontoService cupomDescontoService, ArquivoCobrancaService arquivoCobrancaService, GravameService gravameService) {
        super(cobrancaService, credenciamentoService, cupomDescontoService, arquivoCobrancaService, gravameService);
    }

    @Override
    public Uf getUf() {
        return Uf.PE;
    }
}
