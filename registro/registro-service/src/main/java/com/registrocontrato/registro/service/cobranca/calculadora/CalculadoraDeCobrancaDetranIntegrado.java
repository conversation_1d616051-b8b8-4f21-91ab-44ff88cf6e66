package com.registrocontrato.registro.service.cobranca.calculadora;

import com.registrocontrato.infra.exception.ServiceException;
import com.registrocontrato.registro.entity.Cobranca;
import com.registrocontrato.registro.entity.Credenciamento;
import com.registrocontrato.registro.service.CredenciamentoService;
import com.registrocontrato.registro.service.CupomDescontoService;
import com.registrocontrato.registro.service.cobranca.CobrancaService;
import com.registrocontrato.registro.service.cobranca.gravame.GravameService;

import java.math.BigDecimal;

public abstract class CalculadoraDeCobrancaDetranIntegrado extends CalculadoraDeCobrancaDefault {

    public CalculadoraDeCobrancaDetranIntegrado(CobrancaService cobrancaService, CredenciamentoService credenciamentoService, CupomDescontoService cupomDescontoService, GravameService gravameService) {
        super(cobrancaService, credenciamentoService, cupomDescontoService, gravameService);
    }

    @Override
    protected void definirValoresDaCobranca(Cobranca cobranca, Credenciamento credenciamento) throws ServiceException {
        super.definirValoresDaCobranca(cobranca, credenciamento);
        cobranca.setValorCobrancaInformadoDetran(getValorBoletoDetran(cobranca));
    }

    protected abstract BigDecimal getValorBoletoDetran(Cobranca cobranca);
}
