package com.registrocontrato.registro.service.cobranca.calculadora.nordeste;

import com.registrocontrato.infra.entity.Uf;
import com.registrocontrato.registro.entity.Cobranca;
import com.registrocontrato.registro.service.CredenciamentoService;
import com.registrocontrato.registro.service.CupomDescontoService;
import com.registrocontrato.registro.service.cobranca.CobrancaService;
import com.registrocontrato.registro.service.cobranca.calculadora.CalculadoraDeCobrancaDetranIntegrado;
import com.registrocontrato.registro.service.cobranca.gravame.GravameService;
import com.registrocontrato.registro.service.detran.se.rest.WsDetranSE;
import com.registrocontrato.registro.service.detran.se.client.response.CobrancaResponse;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.Objects;

@Component
public class CalculadoraDeCobrancaSE extends CalculadoraDeCobrancaDetranIntegrado {

    private final WsDetranSE wsDetranSeRestClient;

    public CalculadoraDeCobrancaSE(@Lazy
                                   CobrancaService cobrancaService,
                                   CredenciamentoService credenciamentoService,
                                   CupomDescontoService cupomDescontoService,
                                   WsDetranSE wsDetranSeRestClient, GravameService gravameService) {
        super(cobrancaService, credenciamentoService, cupomDescontoService, gravameService);
        this.wsDetranSeRestClient = wsDetranSeRestClient;
    }

    @Override
    protected BigDecimal getValorBoletoDetran(Cobranca cobranca) {
        try {
            CobrancaResponse cobrancaResponse = wsDetranSeRestClient.buscarDadosDeCobranca(cobranca);
            if (Objects.nonNull(cobrancaResponse)) {
                String valor = cobrancaResponse.getGuide().getTotalValue();
                BigDecimal valorTotal = new BigDecimal(valor.replace(",", ""));
                logger.info("Valor Total: " + valorTotal);
                return valorTotal;
            }
            return BigDecimal.ZERO;
        } catch (Exception e) {
            logger.error(e.getMessage());
            return BigDecimal.ZERO;
        }
    }


    @Override
    public Uf getUf() {
        return Uf.SE;
    }
}
