package com.registrocontrato.registro.service.dto;

import com.registrocontrato.infra.entity.Financeira;
import com.registrocontrato.infra.entity.Uf;
import com.registrocontrato.registro.entity.Marca;
import com.registrocontrato.registro.entity.Modelo;
import com.registrocontrato.registro.enums.SituacaoBaixaB3;
import com.registrocontrato.registro.enums.SituacaoRsng;
import com.registrocontrato.registro.enums.TipoContrato;
import org.springframework.data.domain.Sort.Direction;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class ContratoRsngFilter {

    private Long protocoloPlaceconRsng;

    private String numeroContrato;

    private Uf ufRegistro;

    private Uf ufEnderecoDevedor;

    private String cpfCnpjDevedorFinanciado;

    private String chassi;

    private String numeroApontamento;

    private String placa;

    private String usuario;

    private SituacaoRsng situacao;

    private TipoContrato tipoContrato;

    private Financeira financeira;

    private String numeroRenavam;

    private Marca marca;

    private Modelo modelo;

    private Date dataCadastro;

    private Date dataAditivo;

    private Date dataContrato;

    private Date dataInclusaoAnexo;

    private Boolean consultaRapida;

    private String valor;

    private Boolean comCobranca = false;

    private Direction direction;

    private Boolean alteracao;

    private List<Financeira> financeiras = new ArrayList<>();

    private SituacaoBaixaB3 situacaoBaixaB3;

    private Boolean findBaixa;

    public Boolean getFindBaixa() {
        return findBaixa;
    }

    public void setFindBaixa(Boolean findBaixa) {
        this.findBaixa = findBaixa;
    }

    public SituacaoBaixaB3 getSituacaoBaixaB3() {
        return situacaoBaixaB3;
    }

    public void setSituacaoBaixaB3(SituacaoBaixaB3 situacaoBaixaB3) {
        this.situacaoBaixaB3 = situacaoBaixaB3;
    }

    public Boolean getAlteracao() {
        return alteracao;
    }

    public void setAlteracao(Boolean alteracao) {
        this.alteracao = alteracao;
    }

    public Long getProtocoloPlaceconRsng() {
        return protocoloPlaceconRsng;
    }

    public void setProtocoloPlaceconRsng(Long protocoloPlaceconRsng) {
        this.protocoloPlaceconRsng = protocoloPlaceconRsng;
    }

    public String getNumeroContrato() {
        return numeroContrato;
    }

    public void setNumeroContrato(String numeroContrato) {
        this.numeroContrato = numeroContrato;
    }

    public Uf getUfRegistro() {
        return ufRegistro;
    }

    public void setUfRegistro(Uf ufRegistro) {
        this.ufRegistro = ufRegistro;
    }

    public String getCpfCnpjDevedorFinanciado() {
        return cpfCnpjDevedorFinanciado;
    }

    public void setCpfCnpjDevedorFinanciado(String cpfCnpjDevedorFinanciado) {
        this.cpfCnpjDevedorFinanciado = cpfCnpjDevedorFinanciado;
    }

    public String getChassi() {
        return chassi;
    }

    public void setChassi(String chassi) {
        this.chassi = chassi;
    }

    public String getNumeroApontamento() {
        return numeroApontamento;
    }

    public void setNumeroApontamento(String numeroApontamento) {
        this.numeroApontamento = numeroApontamento;
    }

    public String getPlaca() {
        return placa;
    }

    public void setPlaca(String placa) {
        this.placa = placa;
    }

    public String getUsuario() {
        return usuario;
    }

    public void setUsuario(String usuario) {
        this.usuario = usuario;
    }

    public SituacaoRsng getSituacao() {
        return situacao;
    }

    public void setSituacao(SituacaoRsng situacao) {
        this.situacao = situacao;
    }

    public TipoContrato getTipoContrato() {
        return tipoContrato;
    }

    public void setTipoContrato(TipoContrato tipoContrato) {
        this.tipoContrato = tipoContrato;
    }

    public Financeira getFinanceira() {
        return financeira;
    }

    public void setFinanceira(Financeira financeira) {
        this.financeira = financeira;
    }

    public Uf getUfEnderecoDevedor() {
        return ufEnderecoDevedor;
    }

    public void setUfEnderecoDevedor(Uf ufEnderecoDevedor) {
        this.ufEnderecoDevedor = ufEnderecoDevedor;
    }

    public String getNumeroRenavam() {
        return numeroRenavam;
    }

    public void setNumeroRenavam(String numeroRenavam) {
        this.numeroRenavam = numeroRenavam;
    }

    public Marca getMarca() {
        return marca;
    }

    public void setMarca(Marca marca) {
        this.marca = marca;
    }

    public Modelo getModelo() {
        return modelo;
    }

    public void setModelo(Modelo modelo) {
        this.modelo = modelo;
    }

    public Date getDataCadastro() {
        return dataCadastro;
    }

    public void setDataCadastro(Date dataCadastro) {
        this.dataCadastro = dataCadastro;
    }

    public Date getDataAditivo() {
        return dataAditivo;
    }

    public void setDataAditivo(Date dataAditivo) {
        this.dataAditivo = dataAditivo;
    }

    public Date getDataContrato() {
        return dataContrato;
    }

    public void setDataContrato(Date dataContrato) {
        this.dataContrato = dataContrato;
    }

    public Date getDataInclusaoAnexo() {
        return dataInclusaoAnexo;
    }

    public void setDataInclusaoAnexo(Date dataInclusaoAnexo) {
        this.dataInclusaoAnexo = dataInclusaoAnexo;
    }

    public String getValor() {
        return valor;
    }

    public void setValor(String valor) {
        this.valor = valor;
    }

    public Boolean getConsultaRapida() {
        return consultaRapida;
    }

    public void setConsultaRapida(Boolean consultaRapida) {
        this.consultaRapida = consultaRapida;
    }

    public Boolean getComCobranca() {
        return comCobranca;
    }

    public void setComCobranca(Boolean comCobranca) {
        this.comCobranca = comCobranca;
    }

    public Direction getDirection() {
        return direction;
    }

    public void setDirection(Direction direction) {
        this.direction = direction;
    }

    public List<Financeira> getFinanceiras() {
        return financeiras;
    }

    public void setFinanceiras(List<Financeira> financeiras) {
        this.financeiras = financeiras;
    }
}
