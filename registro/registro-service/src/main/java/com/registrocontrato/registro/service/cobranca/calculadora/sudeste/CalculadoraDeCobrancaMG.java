package com.registrocontrato.registro.service.cobranca.calculadora.sudeste;

import com.registrocontrato.infra.entity.Uf;
import com.registrocontrato.registro.service.CredenciamentoService;
import com.registrocontrato.registro.service.CupomDescontoService;
import com.registrocontrato.registro.service.cobranca.CobrancaService;
import com.registrocontrato.registro.service.cobranca.calculadora.CalculadoraDeCobrancaDefault;
import com.registrocontrato.registro.service.cobranca.gravame.GravameService;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

@Component
public class CalculadoraDeCobrancaMG extends CalculadoraDeCobrancaDefault {

    public CalculadoraDeCobrancaMG(@Lazy CobrancaService cobrancaService, CredenciamentoService credenciamentoService, CupomDescontoService cupomDescontoService, GravameService gravameService) {
        super(cobrancaService, credenciamentoService, cupomDescontoService, gravameService);
    }

    @Override
    public Uf getUf() {
        return Uf.MG;
    }
}
