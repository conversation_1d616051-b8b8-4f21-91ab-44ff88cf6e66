package com.registrocontrato.registro.service.detran.pi.soap;

import com.registrocontrato.infra.util.PlaceconUtil;
import com.registrocontrato.registro.entity.Contrato;
import com.registrocontrato.registro.entity.Veiculo;
import com.registrocontrato.registro.repository.MensagemRetornoRepository;
import com.registrocontrato.registro.service.RegistroEnvioService;
import com.registrocontrato.registro.service.detran.pi.client.InformarRegistroContrato;
import com.registrocontrato.registro.service.detran.pi.client.InformarRegistroContratoResponse;
import com.registrocontrato.registro.service.detran.pi.client.RegistroContrato;
import com.registrocontrato.registro.service.dto.MensagemRetornoDTO;
import com.registrocontrato.seguranca.service.AcessoSenhaService;
import com.registrocontrato.seguranca.service.UsuarioService;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Component;
import org.springframework.ws.soap.saaj.SaajSoapMessage;

import javax.xml.bind.JAXBIntrospector;
import java.math.BigDecimal;

import static com.registrocontrato.infra.util.PlaceconUtil.*;

@Component
@Profile({"prod", "hot","prod-rj"})
public class WsDetranPiSoapClientProd extends WsDetranPI {

    public WsDetranPiSoapClientProd(RegistroEnvioService registroEnvioService,
                                    MensagemRetornoRepository mensagemRetornoRepository,
                                    AcessoSenhaService acessoSenhaService,
                                    UsuarioService usuarioService) {
        super(registroEnvioService, mensagemRetornoRepository, acessoSenhaService, usuarioService);
    }

    @Override
    public MensagemRetornoDTO comunicarContratoFinanceiroVeiculo(Contrato contrato, Veiculo veiculo) {
        long start = System.nanoTime();

        InformarRegistroContrato request = putRequest(contrato, veiculo);
        InformarRegistroContratoResponse response = null;
        try {
            getWebServiceTemplate().setMessageSender(httpComponentsMessageSender());
            getWebServiceTemplate().setMarshaller(marshaller());
            getWebServiceTemplate().setUnmarshaller(marshaller());

            Object jaxbElement = getWebServiceTemplate().marshalSendAndReceive(
                    getUrl(),
                    request,
                    message -> {
                        SaajSoapMessage soapMessage = (SaajSoapMessage) message;
                        soapMessage.getSaajMessage().getMimeHeaders()
                                .setHeader("Content-Type", "text/xml; charset=utf-8");
                    });

            response = (InformarRegistroContratoResponse) JAXBIntrospector.getValue(jaxbElement);

            MensagemRetornoDTO retorno = new MensagemRetornoDTO();
            if (response.getReturn().getCodigo() != null && response.getReturn().getCodigo() == 0) {
                retorno = getSucesso(contrato.getUfRegistro());
                retorno.setNumeroDetran("" + response.getReturn().getNumeroRegistroDetran());
                return retorno;
            }

            retorno.setCodigo("" + response.getReturn().getCodigo());
            retorno.setDescricao(response.getReturn().getDescricao());
            return retorno;
        } catch (Exception e) {
            getLogger().error(e.getMessage(), e);
            return getErroPadrao(contrato.getUfRegistro());
        } finally {
            registrarLog(contrato, start, request, response);
        }
    }

    @Override
    protected InformarRegistroContrato putRequest(Contrato contrato, Veiculo veiculo) {
        InformarRegistroContrato request = new InformarRegistroContrato();
        request.setAutenticacao(getCredencial());

        // dados do contrato
        RegistroContrato registro = new RegistroContrato();
        request.setRegistroContrato(registro);

        if (contrato.getNumeroAditivoContrato() == null) {
            if (contrato.getAlteracao() != Boolean.TRUE) {
                registro.setFlagTransacao("1");
            } else {
                registro.setFlagTransacao("2");
            }
        } else {
            registro.setNumAditivoContrato(contrato.getNumeroAditivoContrato());
            registro.setNumDataAditivo(formataData(contrato.getDataAditivoContrato()));

            if (contrato.getAlteracao() != Boolean.TRUE) {
                registro.setFlagTransacao("3");
            } else {
                registro.setFlagTransacao("4");
            }
        }

        // veiculo
        registro.setAnoFabricacao(objectToString(veiculo.getAnoFabricacao()));
        registro.setAnoModelo(objectToString(veiculo.getAnoModelo()));
        registro.setChassi(veiculo.getNumeroChassi());
        registro.setCodigoCor("");
        registro.setCodigoTipoVeiculo("");
        registro.setMarcaModelo("");
        registro.setPlaca(veiculo.getPlaca());
        registro.setUfLicenciamento(contrato.getUfRegistro().name());
        registro.setCodigoTipoVeiculo(veiculo.getTipo() == null ? "6" : String.valueOf(Integer.parseInt(veiculo.getTipo().getCodigo())));

        if (veiculo.getUf() != null) {
            registro.setUfPlaca(veiculo.getUf().name());
        }
        registro.setRenavam(veiculo.getNumeroRenavam());
        registro.setIdentificaoRemarcacao(veiculo.getChassiRemarcado() ? "1" : "2");
        registro.setNumeroGravame(objectToString(veiculo.getNumeroGravame()));

        // devedor
        registro.setBairroImovelDevedor(contrato.getBairroDevedor());
        registro.setCepImovelDevedor(contrato.getCepDevedor());
        registro.setCodigoMunicipioDevedor(contrato.getMunicipioDevedor().getCodigoDenatran());
        registro.setComplementoImovelDevedor(contrato.getComplementoEnderecoDevedor());
        registro.setCpfCnpjDevedor(contrato.getCpfCnpjDevedorFinanciado());
        registro.setNomeDevedor(contrato.getNomeDevedorFinanciado());
        registro.setNumeroImovelDevedor(contrato.getNumeroEnderecoDevedor());
        registro.setNumeroTelefoneDevedor(objectToString(contrato.getTelefoneDevedor()));
        registro.setTipoDocumentoDevedor(contrato.getCpfCnpjDevedorFinanciado().length() == 11 ? "1" : "2");
        registro.setUfImovelDevedor(contrato.getUfEnderecoDevedor().name());
        registro.setDddImovelDevedor(objectToString(contrato.getDddDevedor()));
        registro.setEmailDevedor(objectToString(contrato.getEmailDevedor()));
        registro.setLogradouroDevedor(contrato.getEnderecoDevedor());

        // credenciada
        registro.setCredenciada(getCnpjCredenciada());

        // credor
        registro.setTipoDocumentoCredor(contrato.getFinanceira().getDocumento().length() == 11 ? "1" : "2");
        registro.setCpfCnpjCredor(contrato.getFinanceira().getDocumento());
        registro.setNomeCredor(contrato.getFinanceira().getNome());

        // contrato
        registro.setDataCadastro(formataData(contrato.getDataCadastro()));
        registro.setDataContrato(formataData(contrato.getDataContrato()));
        registro.setDataVencimentoPrimeiraParcela(formataData(contrato.getDataVencimentoPrimeiraParcela()));
        registro.setDataVencimentoUltimaParcela(formataData(contrato.getDataVencimentoUltimaParcela()));
        registro.setDataLiberacaoCredito(formataData(contrato.getDataLiberacaoCredito()));

        if (contrato.getMunicipioLiberacao() != null)
            registro.setCodigoMunicipioLiberacaoCredito(contrato.getMunicipioLiberacao().getCodigoDenatran());
        if (contrato.getUfLiberacaoCredito() != null)
            registro.setUfLiberacaoCredito(contrato.getUfLiberacaoCredito().name());

        registro.setIndicadorComissao(toSimNao(contrato.getIndicadorComissao()));
        registro.setIndicadorPenalidade(toSimNao(contrato.getIndicadorPenalidade()));
        registro.setIndicadorTaxaMora(toSimNao(contrato.getIndicadorTaxaMoraDia()));
        registro.setIndicadorTaxaMulta(toSimNao(contrato.getIndicadorTaxaMulta()));

        registro.setIndices(contrato.getSiglaIndiceFinaceiro().name());
        registro.setNumeroContrato(contrato.getNumeroContrato());
        registro.setNumeroCotaConsorcio(objectToString(contrato.getNumeroCotaConsorcio()));
        registro.setNumeroGrupoConsorcio(objectToString(contrato.getNumeroGrupoConsorcio()));
        registro.setNumeroRegistro(objectToString(contrato.getNumeroRegistroEletronico()).substring(2));
        registro.setPenalidade(contrato.getDescricaoPenalidade());
        registro.setQuantidadeParcela(objectToString(contrato.getQuantidadeMeses()));
        registro.setTaxaJuroAno(PlaceconUtil.formataValorPadraoZero(contrato.getValorTaxaJurosAno(), true));
        registro.setTaxaJuroMes(PlaceconUtil.formataValorPadraoZero(contrato.getValorTaxaJurosMes(), true));

        registro.setTipoGravame(contrato.getTipoRestricao().getCodigo());
        registro.setValorComissao(PlaceconUtil.formataValorPadraoZero(contrato.getPercentualComissao(), true));

        registro.setValorIof(PlaceconUtil.formataValorPadraoZero(contrato.getValorIOF(), true));
        registro.setValorParcela(PlaceconUtil.formataValorPadraoZero(contrato.getValorParcela(), false));
        registro.setValorTaxaMora(PlaceconUtil.formataValorPadraoZero(contrato.getValorTaxaMoraDia(), true));
        registro.setValorTaxaMulta(PlaceconUtil.formataValorPadraoZero(contrato.getValorTaxaMulta(), true));
        registro.setCorrecaoMonetaria(PlaceconUtil.formataValorPadraoZero(contrato.getValorTaxaJurosMes(), true));

        BigDecimal valorFinanciamento = contrato.getValorCredito() != null ? contrato.getValorCredito() : contrato.getValorTotalDivida();
        registro.setValorTotalFinanciamento(PlaceconUtil.formataValorPadraoZero(valorFinanciamento, false));

        registro.setNumeroRegistroDetran(veiculo.getNumeroRegistroDetran());
        registro.setTipoRegistro(contrato.getNumeroAditivoContrato() != null && contrato.getDataAditivoContrato() != null ? "2" : "1");

        return request;
    }
}
