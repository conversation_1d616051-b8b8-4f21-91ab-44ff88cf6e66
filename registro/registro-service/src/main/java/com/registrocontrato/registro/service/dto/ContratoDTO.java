package com.registrocontrato.registro.service.dto;

import com.registrocontrato.infra.entity.*;
import com.registrocontrato.registro.entity.*;
import com.registrocontrato.registro.enums.*;
import org.springframework.data.domain.Sort.Direction;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class ContratoDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long numeroRegistroEletronico;

    private String numeroContrato;

    private Uf ufRegistro;

    private Uf ufEnderecoDevedor;

    private List<Uf> ufsIgnoradas;

    private Municipio municipioEnderecoDevedor;

    private String cpfCnpjDevedorFinanciado;

    private String agenteCadastro;

    private String chassi;

    private String placa;

    private String usuario;

    private Situacao situacao;

    private String numeroGravame;

    private List<Situacao> situacoes = new ArrayList<>();

    private SituacaoFinanceira situacaoFinanceira;

    private SimNao assinado;

    private SimNao aprovadoAuditoria;

    private SimNao possuiAnexo;

    private Cobranca cobranca;

    private TipoContrato tipoContrato;

    private Financeira financeira;

    private TipoVeiculo tipoVeiculo;

    private Long anoModelo;

    private String numeroRenavam;

    private Marca marca;

    private Modelo modelo;

    private Date dataCadastro;

    private Date dataAditivo;

    private Date dataContrato;

    private Date dataInclusaoAnexo;

    private Date dataRegistroDETRAN;

    private Boolean consultaRapida;

    private String valor;

    private Boolean comCobranca = false;

    private SimNao integra;

    private Direction direction;

    // alteracao
    private Boolean alteracao;

    private Agente agente;

    private MensagemRetorno mensagemRetorno;

    private List<Financeira> financeiras = new ArrayList<>();

    private SituacaoBaixaB3 situacaoBaixaB3;

    private Boolean findBaixa;

    public Boolean getFindBaixa() {
        return findBaixa;
    }

    public void setFindBaixa(Boolean findBaixa) {
        this.findBaixa = findBaixa;
    }

    public SituacaoBaixaB3 getSituacaoBaixaB3() {
        return situacaoBaixaB3;
    }

    public void setSituacaoBaixaB3(SituacaoBaixaB3 situacaoBaixaB3) {
        this.situacaoBaixaB3 = situacaoBaixaB3;
    }

    public MensagemRetorno getMensagemRetorno() {
        return mensagemRetorno;
    }

    public void setMensagemRetorno(MensagemRetorno mensagemRetorno) {
        this.mensagemRetorno = mensagemRetorno;
    }

    public Boolean getAlteracao() {
        return alteracao;
    }

    public void setAlteracao(Boolean alteracao) {
        this.alteracao = alteracao;
    }

    public SimNao getPossuiAnexo() {
        return possuiAnexo;
    }

    public void setPossuiAnexo(SimNao possuiAnexo) {
        this.possuiAnexo = possuiAnexo;
    }

    public Long getNumeroRegistroEletronico() {
        return numeroRegistroEletronico;
    }

    public void setNumeroRegistroEletronico(Long numeroRegistroEletronico) {
        this.numeroRegistroEletronico = numeroRegistroEletronico;
    }

    public String getNumeroContrato() {
        return numeroContrato;
    }

    public void setNumeroContrato(String numeroContrato) {
        this.numeroContrato = numeroContrato;
    }

    public Uf getUfRegistro() {
        return ufRegistro;
    }

    public void setUfRegistro(Uf ufRegistro) {
        this.ufRegistro = ufRegistro;
    }

    public String getCpfCnpjDevedorFinanciado() {
        return cpfCnpjDevedorFinanciado;
    }

    public void setCpfCnpjDevedorFinanciado(String cpfCnpjDevedorFinanciado) {
        this.cpfCnpjDevedorFinanciado = cpfCnpjDevedorFinanciado;
    }

    public String getChassi() {
        return chassi;
    }

    public void setChassi(String chassi) {
        this.chassi = chassi;
    }

    public String getAgenteCadastro() {
        return agenteCadastro;
    }

    public void setAgenteCadastro(String agenteCadastro) {
        this.agenteCadastro = agenteCadastro;
    }

    public String getPlaca() {
        return placa;
    }

    public void setPlaca(String placa) {
        this.placa = placa;
    }

    public String getUsuario() {
        return usuario;
    }

    public void setUsuario(String usuario) {
        this.usuario = usuario;
    }

    public Situacao getSituacao() {
        return situacao;
    }

    public void setSituacao(Situacao situacao) {
        this.situacao = situacao;
    }

    public SituacaoFinanceira getSituacaoFinanceira() {
        return situacaoFinanceira;
    }

    public void setSituacaoFinanceira(SituacaoFinanceira situacaoFinanceira) {
        this.situacaoFinanceira = situacaoFinanceira;
    }

    public SimNao getAssinado() {
        return assinado;
    }

    public void setAssinado(SimNao assinado) {
        this.assinado = assinado;
    }

    public List<Situacao> getSituacoes() {
        return situacoes;
    }

    public void setSituacoes(List<Situacao> situacoes) {
        this.situacoes = situacoes;
    }

    public String getNumeroGravame() {
        return numeroGravame;
    }

    public void setNumeroGravame(String numeroGravame) {
        this.numeroGravame = numeroGravame;
    }

    public SimNao getAprovadoAuditoria() {
        return aprovadoAuditoria;
    }

    public void setAprovadoAuditoria(SimNao aprovadoAuditoria) {
        this.aprovadoAuditoria = aprovadoAuditoria;
    }

    public void setCobranca(Cobranca cobranca) {
        this.cobranca = cobranca;
    }

    public Cobranca getCobranca() {
        return cobranca;
    }

    public TipoContrato getTipoContrato() {
        return tipoContrato;
    }

    public void setTipoContrato(TipoContrato tipoContrato) {
        this.tipoContrato = tipoContrato;
    }

    public Financeira getFinanceira() {
        return financeira;
    }

    public void setFinanceira(Financeira financeira) {
        this.financeira = financeira;
    }

    public Uf getUfEnderecoDevedor() {
        return ufEnderecoDevedor;
    }

    public void setUfEnderecoDevedor(Uf ufEnderecoDevedor) {
        this.ufEnderecoDevedor = ufEnderecoDevedor;
    }

    public Municipio getMunicipioEnderecoDevedor() {
        return municipioEnderecoDevedor;
    }

    public void setMunicipioEnderecoDevedor(Municipio municipioEnderecoDevedor) {
        this.municipioEnderecoDevedor = municipioEnderecoDevedor;
    }

    public TipoVeiculo getTipoVeiculo() {
        return tipoVeiculo;
    }

    public void setTipoVeiculo(TipoVeiculo tipoVeiculo) {
        this.tipoVeiculo = tipoVeiculo;
    }

    public Long getAnoModelo() {
        return anoModelo;
    }

    public void setAnoModelo(Long anoModelo) {
        this.anoModelo = anoModelo;
    }

    public String  getNumeroRenavam() {
        return numeroRenavam;
    }

    public void setNumeroRenavam(String numeroRenavam) {
        this.numeroRenavam = numeroRenavam;
    }

    public Marca getMarca() {
        return marca;
    }

    public void setMarca(Marca marca) {
        this.marca = marca;
    }

    public Modelo getModelo() {
        return modelo;
    }

    public void setModelo(Modelo modelo) {
        this.modelo = modelo;
    }

    public Date getDataCadastro() {
        return dataCadastro;
    }

    public void setDataCadastro(Date dataCadastro) {
        this.dataCadastro = dataCadastro;
    }

    public Date getDataAditivo() {
        return dataAditivo;
    }

    public void setDataAditivo(Date dataAditivo) {
        this.dataAditivo = dataAditivo;
    }

    public Date getDataContrato() {
        return dataContrato;
    }

    public void setDataContrato(Date dataContrato) {
        this.dataContrato = dataContrato;
    }

    public Date getDataInclusaoAnexo() {
        return dataInclusaoAnexo;
    }

    public void setDataInclusaoAnexo(Date dataInclusaoAnexo) {
        this.dataInclusaoAnexo = dataInclusaoAnexo;
    }

    public Date getDataRegistroDETRAN() {
        return dataRegistroDETRAN;
    }

    public void setDataRegistroDETRAN(Date dataRegistroDETRAN) {
        this.dataRegistroDETRAN = dataRegistroDETRAN;
    }

    public String getValor() {
        return valor;
    }

    public void setValor(String valor) {
        this.valor = valor;
    }

    public Boolean getConsultaRapida() {
        return consultaRapida;
    }

    public void setConsultaRapida(Boolean consultaRapida) {
        this.consultaRapida = consultaRapida;
    }

    public Boolean getComCobranca() {
        return comCobranca;
    }

    public void setComCobranca(Boolean comCobranca) {
        this.comCobranca = comCobranca;
    }

    public SimNao getIntegra() {
        return integra;
    }

    public void setIntegra(SimNao integra) {
        this.integra = integra;
    }

    public List<Uf> getUfsIgnoradas() {
        return ufsIgnoradas;
    }

    public void setUfsIgnoradas(List<Uf> ufsIgnoradas) {
        this.ufsIgnoradas = ufsIgnoradas;
    }

    public Direction getDirection() {
        return direction;
    }

    public void setDirection(Direction direction) {
        this.direction = direction;
    }

    public void setAgente(Agente agente) {
        this.agente = agente;
    }

    public Agente getAgente() {
        return agente;
    }

    public List<Financeira> getFinanceiras() {
        return financeiras;
    }

    public void setFinanceiras(List<Financeira> financeiras) {
        this.financeiras = financeiras;
    }
}
