package com.registrocontrato.registro.repository;

import com.registrocontrato.infra.entity.Financeira;
import com.registrocontrato.infra.entity.Uf;
import com.registrocontrato.infra.service.BaseRepository;
import com.registrocontrato.registro.entity.BilhetagemGravame;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

@Repository
public interface GravameRepository extends BaseRepository<BilhetagemGravame> {

    @Query("select g from BilhetagemGravame g where g.uf = :uf and g.dataInicio <= :data and g.dataFim >= :data and g.financeira = :financeira")
    BilhetagemGravame findByAtivo(@Param("uf") Uf uf, @Param("data") Date data, @Param("financeira") Financeira financeira);

    @Query("SELECT c FROM BilhetagemGravame c WHERE c.uf = :uf AND c.dataInicio <= :dataInicio  AND c.dataFim >= :dataFim and c.financeira = :financeira ORDER BY c.id DESC")
    List<BilhetagemGravame> findGravameByUfAndPeriodo(@Param("uf") Uf uf, @Param("dataInicio") Date dataInicio, @Param("dataFim")Date dataFim, @Param("financeira") Financeira financeira);

    List<BilhetagemGravame> findAllByDataInicioLessThanEqualAndDataFimGreaterThanEqual(@Param("dataInicio") Date dataInicio, @Param("dataFim")Date dataFim);
}
