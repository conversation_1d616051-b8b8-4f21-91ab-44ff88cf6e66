package com.registrocontrato.registro.service;

import com.registrocontrato.infra.entity.Financeira;
import com.registrocontrato.infra.entity.SimNao;
import com.registrocontrato.infra.service.BaseService;
import com.registrocontrato.infra.util.PlaceconUtil;
import com.registrocontrato.registro.entity.ArquivoRemessa;
import com.registrocontrato.registro.entity.ItemArquivoRemessa;
import com.registrocontrato.registro.repository.ArquivoRemessaRepository;
import com.registrocontrato.registro.repository.ItemArquivoRemessaRepository;
import com.registrocontrato.registro.service.dto.ArquivoRemessaDTO;
import com.registrocontrato.seguranca.entity.Perfil;
import com.registrocontrato.seguranca.entity.Usuario;
import com.registrocontrato.seguranca.service.UsuarioService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.data.domain.Sort.Direction;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.data.repository.PagingAndSortingRepository;
import org.springframework.stereotype.Service;

import javax.persistence.criteria.*;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

@Service
public class ArquivoRemessaService extends BaseService<ArquivoRemessa, ArquivoRemessaDTO> {

    private static final long serialVersionUID = 1L;

    @Autowired
    private UsuarioService usuarioService;

    @Autowired
    private ArquivoRemessaRepository arquivoRemessaRepository;

    @Autowired
    private ItemArquivoRemessaRepository itemArquivoRemessaRepository;

    @Override
    public ArquivoRemessa findOne(Long id) {
        ArquivoRemessa ar = super.findOne(id);
        if (ar.getTemplateRemessa() != null) {
            ar.getTemplateRemessa().getId();
        }
        return ar;
    }

    public ArquivoRemessa findArquivoEcommPan(String nome) {
        List<ArquivoRemessa> list = arquivoRemessaRepository.findArquivoEcommPan(nome);
        return list == null || list.isEmpty() ? null : list.get(0);
    }

    @Override
    public Page<ArquivoRemessa> findAll(int first, int pageSize, ArquivoRemessaDTO filter) {
        Specification<ArquivoRemessa> contratoSpec = new Specification<ArquivoRemessa>() {

            @Override
            public Predicate toPredicate(Root<ArquivoRemessa> root, CriteriaQuery<?> cq, CriteriaBuilder cb) {
                Join<ArquivoRemessa, ItemArquivoRemessa> join = root.join("registros", JoinType.LEFT);
                cq.distinct(true);

                List<Predicate> predicates = new ArrayList<>();

                if (StringUtils.isNotEmpty(filter.getChassi())) {
                    predicates.add(cb.like(cb.lower(join.get("chassi")), "%" + filter.getChassi().toLowerCase() + "%"));
                }

                if (StringUtils.isNotEmpty(filter.getNumeroContrato())) {
                    predicates.add(cb.like(cb.lower(join.get("numeroContrato")), "%" + filter.getNumeroContrato().toLowerCase() + "%"));
                }

                if (StringUtils.isNotEmpty(filter.getNomeArquivo())) {
                    predicates.add(cb.like(cb.lower(root.<String>get("nome")), "%" + filter.getNomeArquivo().toLowerCase() + "%"));
                }
                if (filter.getDataInicio() != null) {
                    predicates.add(cb.greaterThanOrEqualTo(root.<Date>get("dataTransacao"), PlaceconUtil.minDateTime(filter.getDataInicio())));
                }
                if (filter.getDataFim() != null) {
                    predicates.add(cb.lessThanOrEqualTo(root.<Date>get("dataTransacao"), PlaceconUtil.maxDateTime(filter.getDataFim())));
                }

                if (filter.getTransmitida() != null) {
                    predicates.add(cb.equal(join.<SimNao>get("transmitida"), filter.getTransmitida()));
                }
//                if (filter.getTransmitida() != null) {
//                    predicates.add(cb.equal(root.<SimNao>get("transmitida"), filter.getTransmitida()));
//                }

                if (filter.getId() != null) {
                    predicates.add(cb.equal(root.get("id"), filter.getId()));
                }
//                if (filter.getId() != null) {
//                    predicates.add(cb.equal(join.get("id"), filter.getId()));
//                }

                if (filter.getValida() != null) {
                    predicates.add(cb.equal(join.<SimNao>get("valida"), filter.getValida()));
                }
//                if (filter.getValida() != null) {
//                    predicates.add(cb.equal(root.<SimNao>get("valida"), filter.getValida()));
//                }

                if (filter.getFinanceira() != null) {
                    predicates.add(cb.equal(root.join("registros").get("financeira"), filter.getFinanceira()));
                }

                // se o usuario eh financeira filtrar somente os usuario da financeira
                Usuario usuarioLogado = usuarioService.findByCpfFinanceiras(filter.getUsuario());
                if (usuarioLogado.getPerfil() == Perfil.FINANCEIRA || !usuarioLogado.getFinanceiras().isEmpty()) {
                    List<Financeira> values = usuarioLogado.getFinanceiras();
                    predicates.add(root.join("registros").get("financeira").in(values));
                }
                return andTogether(predicates, cb);
            }

            private Predicate andTogether(List<Predicate> predicates, CriteriaBuilder cb) {
                return cb.and(predicates.toArray(new Predicate[0]));
            }
        };

        return arquivoRemessaRepository.findAll(contratoSpec, new PageRequest(first / pageSize, pageSize, new Sort(Direction.DESC, "id")));
    }

    public List<ItemArquivoRemessa> findItens(ArquivoRemessaDTO filter) {
        Specification<ItemArquivoRemessa> contratoSpec = new Specification<ItemArquivoRemessa>() {

            @Override
            public Predicate toPredicate(Root<ItemArquivoRemessa> root, CriteriaQuery<?> cq, CriteriaBuilder cb) {

                Join<ItemArquivoRemessa, ArquivoRemessa> join = root.join("arquivoRemessa", JoinType.INNER);
                cq.distinct(true);

                List<Predicate> predicates = new ArrayList<>();

                if (StringUtils.isNotEmpty(filter.getDescricaoErro())) {
                    predicates.add(cb.like(cb.lower(root.get("descricaoErro")), "%" + filter.getDescricaoErro().toLowerCase() + "%"));
                }

                if (filter.getTransmitida() != null) {
                    predicates.add(cb.equal(root.<SimNao>get("transmitida"), filter.getTransmitida()));
                }

                if (filter.getValida() != null) {
                    predicates.add(cb.equal(root.<SimNao>get("valida"), filter.getValida()));
                }

                if (filter.getId() != null) {
                    predicates.add(cb.equal(join.get("id"), filter.getId()));
                }

                // se o usuario eh financeira filtrar somente os usuario da financeira
                Usuario usuarioLogado = usuarioService.findByCpfFinanceiras(filter.getUsuario());
                if (usuarioLogado.getPerfil() == Perfil.FINANCEIRA || !usuarioLogado.getFinanceiras().isEmpty()) {
                    List<Financeira> values = usuarioLogado.getFinanceiras();
                    predicates.add(root.get("financeira").in(values));
                }
                return andTogether(predicates, cb);
            }

            private Predicate andTogether(List<Predicate> predicates, CriteriaBuilder cb) {
                return cb.and(predicates.toArray(new Predicate[0]));
            }
        };

        return itemArquivoRemessaRepository.findAll(contratoSpec);
    }

    public List<ItemArquivoRemessa> getRegistros(ArquivoRemessa arq, String username) {
        Usuario usuario = usuarioService.findByCpfFinanceiras(username);
        if (Objects.nonNull(usuario))
            if (usuario.getPerfil() == Perfil.FINANCEIRA || !usuario.getFinanceiras().isEmpty()) {
                return itemArquivoRemessaRepository.findAllByArquivoRemessaAndFinanceiraInOrderByNumeroContratoAsc(arq, usuario.getFinanceiras());
            }
        return itemArquivoRemessaRepository.findAllByArquivoRemessaOrderByNumeroContratoAsc(arq);
    }

    protected PagingAndSortingRepository<ArquivoRemessa, Long> getRepository() {
        return arquivoRemessaRepository;
    }

}
