package com.registrocontrato.registro.repository;

import com.registrocontrato.infra.service.BaseRepository;
import com.registrocontrato.registro.entity.Contrato;
import com.registrocontrato.registro.entity.ContratoRsng;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;
import java.util.Optional;

public interface ContratoRsngRepository extends BaseRepository<ContratoRsng> {

    //TODO: verificar se pode cancelar apontamentos enviados para o detran
    @Query("select o from ContratoRsng o inner join fetch o.veiculos e " +
           "where o.id = (select max(c.id) from ContratoRsng c inner join c.veiculos v where v.numeroChassi = :chassi and v.apontamento = :apontamento)")
    Optional<ContratoRsng> findByChassiGravame(@Param("chassi") String numeroChassi, @Param("apontamento") String apontamento);

    Optional<ContratoRsng> findByProtocoloPlaceconRsng(Long protocolo);

    @Query("select c from ContratoRsng c inner join fetch c.veiculos v " +
           "where c.registroAutomaticoDetran = true " +
           "and c.contrato is null " +
           "and c.situacao = 'PENDENTE_ENVIO'")
    List<ContratoRsng> findPendentesEnvio();

    @Query("select o from ContratoRsng o inner join fetch o.veiculos e where o.id = (select max(c.id) from ContratoRsng c inner join c.veiculos v where v.numeroChassi = :chassi and c.numeroContrato = :numeroContrato)")
    ContratoRsng findTop1ByChassiAndNumeroContrato(@Param("chassi") String chassi, @Param("numeroContrato") String numeroContrato);

}
