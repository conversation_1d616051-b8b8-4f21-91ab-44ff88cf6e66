package com.registrocontrato.registro.service.cobranca.calculadora.sul;

import com.registrocontrato.infra.entity.Uf;
import com.registrocontrato.infra.exception.ServiceException;
import com.registrocontrato.registro.entity.Cobranca;
import com.registrocontrato.registro.service.CredenciamentoService;
import com.registrocontrato.registro.service.CupomDescontoService;
import com.registrocontrato.registro.service.MigracaoService;
import com.registrocontrato.registro.service.detran.pr.client.response.PeriodoRelatorioSituacaoBoletoMensalPrResponse;
import com.registrocontrato.registro.service.cobranca.gravame.GravameService;
import com.registrocontrato.registro.service.detran.pr.rest.WsDetranPR;
import com.registrocontrato.registro.service.cobranca.CobrancaService;
import com.registrocontrato.registro.service.cobranca.calculadora.CalculadoraDeCobrancaDetranIntegrado;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.Objects;

@Component
public class CalculadoraDeCobrancaPR extends CalculadoraDeCobrancaDetranIntegrado {

    private final WsDetranPR wsDetranPrRestClient;

    private final MigracaoService migracaoService;

    public CalculadoraDeCobrancaPR(@Lazy CobrancaService cobrancaService, CredenciamentoService credenciamentoService, CupomDescontoService cupomDescontoService, WsDetranPR wsDetranPrRestClient, MigracaoService migracaoService, GravameService gravameService) {
        super(cobrancaService, credenciamentoService, cupomDescontoService, gravameService);
        this.wsDetranPrRestClient = wsDetranPrRestClient;
        this.migracaoService = migracaoService;
    }

    @Override
    protected BigDecimal getValorBoletoDetran(Cobranca cobranca) {
        try {
            PeriodoRelatorioSituacaoBoletoMensalPrResponse[] consultarCobrancas;
            if (migracaoService.isEnviarParaWinov(Uf.PR)) {
                consultarCobrancas =
                        migracaoService.consultarCobrancasPr(cobranca.getDataInicio(), cobranca.getDataFim(), cobranca.getFinanceira());
            } else {
                consultarCobrancas =
                        wsDetranPrRestClient.consultarCobrancas(cobranca.getDataInicio(), cobranca.getDataFim(), cobranca.getFinanceira());
            }

            if (Objects.nonNull(consultarCobrancas) && Objects.nonNull(consultarCobrancas[0].getRegistradoras())) {
                Double valorBoleto = consultarCobrancas[0].getRegistradoras()[0].getAgentes()[0].getValorBoleto();
                return BigDecimal.valueOf(valorBoleto);
            }
            return BigDecimal.ZERO;
        } catch (ServiceException e) {
            return BigDecimal.ZERO;
        }
    }

    @Override
    public Uf getUf() {
        return Uf.PR;
    }
}
