package com.registrocontrato.registro.service.cobranca.boleto;

import com.registrocontrato.registro.entity.Cobranca;
import com.registrocontrato.registro.service.cobranca.BoletoService;
import com.registrocontrato.seguranca.service.FinanceiraService;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

public abstract class CobrancaComBoletoDeReembolso implements BoletoDeCobrancaStrategy {

    protected final Log logger = LogFactory.getLog(getClass());

    protected final FinanceiraService financeiraService;

    protected final BoletoService boletoService;

    public CobrancaComBoletoDeReembolso(FinanceiraService financeiraService, BoletoService boletoService) {
        this.financeiraService = financeiraService;
        this.boletoService = boletoService;
    }

    @Override
    public void emitirBoleto(Cobranca cobranca) throws Exception {

        BoletoResponse retornoBoletoPlace = boletoService.criarBoletoPlace(cobranca);
        BoletoResponse retornoBoletoReembolso = boletoService.criarBoletoReembolso(cobranca);
        BoletoResponse retornoBoletoSng = boletoService.criarBoletoSng(cobranca);
        atualizarCobranca(cobranca, retornoBoletoPlace, retornoBoletoReembolso, retornoBoletoSng);

    }

}
