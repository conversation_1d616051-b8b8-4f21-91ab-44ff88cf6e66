package com.registrocontrato.registro.repository;

import com.registrocontrato.infra.entity.Agente;
import com.registrocontrato.infra.entity.Financeira;
import com.registrocontrato.infra.entity.SimNao;
import com.registrocontrato.infra.entity.Uf;
import com.registrocontrato.infra.service.BaseRepository;
import com.registrocontrato.registro.entity.*;
import com.registrocontrato.registro.enums.Situacao;
import com.registrocontrato.registro.enums.TipoContrato;
import org.springframework.data.jpa.repository.EntityGraph;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;
import java.util.Optional;

@Repository
public interface ContratoRepository extends BaseRepository<Contrato> {

    @Query("select distinct c from Contrato c "
            + "inner join fetch c.veiculos v "
            + "where c.ufRegistro = :uf and c.situacao = :situacao")
    List<Contrato> findByUfRegistroAndSituacao(@Param("uf") Uf uf, @Param("situacao") Situacao situacao);

    @Query("select distinct c from Contrato c "
            + "inner join fetch c.veiculos v "
            + "inner join c.anexos a "
            + "where c.ufRegistro = :uf and c.situacao = :situacao "
            + "and (c.aprovadoAuditoria is null or c.aprovadoAuditoria = 'N') "
            + "and a.enviado = true")
    List<Contrato> findByUfRegistroAndSituacaoAndArquivoEnviado(@Param("uf") Uf uf, @Param("situacao") Situacao situacao);

    @Query("select c from Contrato c inner join fetch c.veiculos v where c.id = (select max(o.id) from Contrato o inner join o.veiculos e where e.numeroChassi in (:chassis) and o.situacao = :situacao)")
    Contrato findByChassiAndSituacao(@Param("chassis") List<String> chassis, @Param("situacao") Situacao situacao);

    @Query("select c from Contrato c inner join fetch c.veiculos v where c.id = (select max(o.id) from Contrato o inner join o.veiculos e where e.numeroChassi in (:chassis) and o.situacao in (:situacoes))")
    Contrato findByChassiAndSituacao(@Param("chassis") List<String> chassis, @Param("situacoes") List<Situacao> situacoes);

    @Query("select c from Contrato c inner join fetch c.veiculos v where "
            + "c.id = (select max(o.id) "
            + "	from Contrato o inner join o.veiculos e "
            + "	where e.numeroChassi in (:chassis) and o.situacao = :situacao and (:contrato is null or :contrato <> o))")
    Contrato findByChassiAndSituacao(@Param("chassis") List<String> chassis, @Param("situacao") Situacao situacao, @Param("contrato") Contrato contrato);

    @Query("select a from Contrato a where a.id = (select max(c.id) from Contrato c inner join c.veiculos v where v.numeroChassi in (:chassis))")
    Contrato findByChassi(@Param("chassis") List<String> chassis);

    @Query("select c from Contrato c where c.numeroRegistroEletronico = :numeroRegistroEletronico and c.situacao = :situacao")
    Contrato findByNumeroRegistroEletronicoAndSituacao(@Param("numeroRegistroEletronico") Long numeroRegistroEletronico, @Param("situacao") Situacao situacao);

    @Query("select c from Contrato c where c.numeroRegistroEletronico = :numeroRegistroEletronico and c.situacao in (:situacao)")
    Contrato findByNumeroRegistroEletronicoAndSituacao(@Param("numeroRegistroEletronico") Long numeroRegistroEletronico, @Param("situacao") List<Situacao> situacao);

    Contrato findTop1ByCpfCnpjDevedorFinanciadoOrderById(@Param("cpfCnpjDevedorFinanciado") String cpfCnpjDevedorFinanciado);

    Contrato findByNumeroRegistroEletronico(@Param("numeroRegistroEletronico") Long numeroRegistroEletronico);

    Contrato findTop1ByFinanceiraIdAndNumeroContratoOrderByIdDesc(@Param("financeiraId") Long idFinanceira, @Param("numeroContrato") String numeroContrato);

    @Query("SELECT c FROM Contrato c WHERE c.id IN (SELECT MAX(e) FROM Contrato e join e.veiculos v WHERE c.financeira = :financeira AND v.numeroChassi = :chassi AND v.numeroGravame = :gravame)")
    Contrato findByFinanceiraAndChassiAndGravame(@Param("financeira") Financeira financeira, @Param("chassi") String chassi, @Param("gravame") String gravame);

    Long countByAssinado(SimNao n);

    List<Contrato> findByLivroOrderByDataContratoAsc(Livro livro);

    @Query("from Contrato c "
            + "where c.dataConclusaoDETRAN >= :dataInicio "
            + "and c.dataConclusaoDETRAN <= :dataFim "
            + "and c.tipoContrato = :tipoContrato "
            + "and c.ufRegistro = :uf "
            + "and c.aprovadoAuditoria = 'S' "
            + "and c.livro is null ")
    List<Contrato> findDisponiveisLivro(@Param("tipoContrato") TipoContrato tipo, @Param("dataInicio") Date dataInicio, @Param("dataFim") Date dataFim,
                                        @Param("uf") Uf uf);

    Long countByAssinadoAndUfRegistro(SimNao n, Uf uf);

    Long countByAssinadoAndFinanceiraInAndUfRegistro(SimNao n, List<Financeira> values, Uf uf);

    Long countBySituacaoAndFinanceiraIn(Situacao rascunho, List<Financeira> values);

    @Query("select case when count(*) > 0 then true else false end from Contrato c "
            + "inner join c.veiculos v "
            + "where c.numeroAditivoContrato = :numeroAditivo and "
            + "c.dataAditivoContrato = :data and "
            + "v.numeroChassi in (:chassis) and "
            + "c.situacao = 'ATIVO'")
    boolean isAditivoJaCadastrado(@Param("chassis") List<String> chassis, @Param("numeroAditivo") String numeroAditivo, @Param("data") Date data);


    @Query("select count(*) from Contrato c where c.financeira = :financeira and c.ufRegistro = :uf "
            + "and c.situacao = 'ATIVO' and c.dataContrato between :dataInicio and :dataFim")
    Long getTotalContratosAtivosPeriodo(@Param("financeira") Financeira financeira, @Param("uf") Uf uf, @Param("dataInicio") Date dataInicio,
                                        @Param("dataFim") Date dataFim);


    Long countBySituacaoAndFinanceiraInAndUfRegistro(Situacao rascunho, List<Financeira> values, Uf uf);

    @Query("select count(c) from Contrato c where c.financeira in :financeiras "
            + "and not exists (select 1 from Anexo a where a.contrato = c) "
            + "and c.situacao='ATIVO'")
    Long countByAnexosIsNullAndFinanceiraIn(@Param("financeiras") List<Financeira> values);

    @Query("select count(c) from Contrato c where c.financeira in :financeiras "
            + "and c.ufRegistro = :uf "
            + "and not exists (select 1 from Anexo a where a.contrato = c) "
            + "and c.situacao='ATIVO'")
    Long countByAnexosIsNullAndFinanceiraInAndUfRegistro(@Param("financeiras") List<Financeira> values, @Param("uf") Uf uf);

    Long countByFinanceiraIn(List<Financeira> values);

    Long countByFinanceiraInAndUfRegistro(List<Financeira> values, Uf uf);

    @Query("select f, cast(c.dataConclusaoDETRAN as date), count(distinct c.id), count(v.id) "
            + "from Contrato c inner join c.financeira f inner join c.veiculos v "
            + "where (:uf is null or c.ufRegistro = :uf) "
            + "and (:agente is null or c.agente = :agente) "
            + "and c.dataConclusaoDETRAN between :dataInicio and :dataFim "
            + "group by f.id, cast(c.dataConclusaoDETRAN as date) "
            + "order by f.id, cast(c.dataConclusaoDETRAN as date)")
    List<Object[]> findByMesAtual(@Param("uf") Uf uf,
                                  @Param("dataInicio") Date dataInicio,
                                  @Param("dataFim") Date dataFim,
                                  @Param("agente") Agente agente);

    @Query("select f, cast(c.dataConclusaoDETRAN as date), count(distinct c.id), count(v.id) "
            + "from Contrato c inner join c.financeira f inner join c.veiculos v "
            + "where c.financeira in (:financeiras) "
            + "and (:uf is null or c.ufRegistro = :uf) "
            + "and (:agente is null or c.agente = :agente) "
            + "and c.dataConclusaoDETRAN between :dataInicio and :dataFim "
            + "group by f.id, cast(c.dataConclusaoDETRAN as date) "
            + "order by f.id, cast(c.dataConclusaoDETRAN as date)")
    List<Object[]> findByMesAtual(@Param("financeiras") List<Financeira> financeiras,
                                  @Param("uf") Uf uf,
                                  @Param("dataInicio") Date dataInicio,
                                  @Param("dataFim") Date dataFim,
                                  @Param("agente") Agente agente);

    @Query("from Contrato c left join fetch c.municipioDevedor "
            + "where c.cpfCnpjDevedorFinanciado = :cpfCnpjDevedorFinanciado "
            + "and (:id is null or c.id = :id) "
            + "order by c.id desc")
    List<Contrato> findByCpfCnpjDevedorFinanciado(@Param("cpfCnpjDevedorFinanciado") String cpfCnpjDevedorFinanciado, @Param("id") Long id);

    Long countByAssinadoAndFinanceiraIn(SimNao n, List<Financeira> financeiras);

    Long countBySituacaoAndUfRegistro(Situacao situacao, Uf uf);

    Long countBySituacao(Situacao situacao);

    Long countByAuditoriasIsNullAndSituacaoInAndFinanceiraIn(List<Situacao> situacoes, List<Financeira> financeiras);

    Long countByAuditoriasIsNullAndSituacaoInAndFinanceiraInAndUfRegistro(List<Situacao> situacoes,
                                                                          List<Financeira> financeiras, Uf uf);

    Long countByAuditoriasIsNullAndSituacaoInAndUfRegistro(List<Situacao> situacoes, Uf uf);

    Long countByAuditoriasIsNullAndSituacaoIn(List<Situacao> situacoes);

    Long countByAnexosIsNotNullAndUfRegistro(Uf uf);

    Long countByAnexosIsNotNull();

    @Query("select count(c) from Contrato c" +
            " where not exists (select 1 from Anexo a where a.contrato=c)" +
            " and c.ufRegistro = :uf" +
            " and c.situacao!='BAIXADO'")
    Long countByAnexosIsNullAndUfRegistro(@Param("uf") Uf uf);

    @Query("select count(c) from Contrato c" +
            " where not exists (select 1 from Anexo a where a.contrato=c)" +
            " and c.situacao != 'BAIXADO'")
    Long countByAnexosIsNull();

    Long countByUfRegistro(Uf uf);

    @Query(nativeQuery = true, value = "select nextval('registro.numero_seq_detran_pe_seq')")
    Long getSequencialDetranPE();

    @Query(nativeQuery = true, value = "select nextval('registro.numero_seq_detran_pb_seq')")
    Long getSequencialDetranPB();

    @Query("select count(v) from Veiculo v inner join v.contrato c "
            + "inner join v.mensagemRetorno m "
            + "where cast(c.dataCadastro as date) = current_date() "
            + "and c.situacao NOT IN ('ATIVO', 'PENDENTE_PAGAMENTO') "
            + "and m.sucesso = false and m.erroPadrao = true "
            + "and (:uf is null or c.ufRegistro = :uf) "
            + "and (:agente is null or c.agente = :agente) "
            + "and (c.financeira in :financeiras) ")
    Long countQuantidadeErroComunicacao(@Param("financeiras") List<Financeira> financeiras, @Param("uf") Uf uf, @Param("agente") Agente agente);

    @Query("select count(v) from Veiculo v inner join v.contrato c "
            + "inner join v.mensagemRetorno m "
            + "where cast(c.dataCadastro as date) = current_date() "
            + "and c.situacao NOT IN ('ATIVO', 'PENDENTE_PAGAMENTO') "
            + "and m.sucesso = false and m.erroPadrao = true "
            + "and (:agente is null or c.agente = :agente) "
            + "and (:uf is null or c.ufRegistro = :uf) ")
    Long countQuantidadeErroComunicacao(@Param("uf") Uf uf, @Param("agente") Agente agente);

    @Query("select o from Contrato o inner join fetch o.veiculos e where o.id = (select max(c.id) from Contrato c inner join c.veiculos v where v.numeroChassi = :chassi and c.numeroContrato = :numeroContrato)")
    Contrato findTop1ByChassiAndNumeroContrato(@Param("chassi") String chassi, @Param("numeroContrato") String numeroContrato);

    @Query("select o from Contrato o inner join fetch o.veiculos e where o.id = (select max(c.id) from Contrato c inner join c.veiculos v where v.numeroChassi = :chassi)")
    Contrato findTop1ByChassi(@Param("chassi") String chassi);

    @Query(nativeQuery = true,
            value = "select ca.alteracao, ca.aprovado_auditoria, ca.assinado, ca.bairro_devedor, ca.bairro_garantidor, " +
                    "ca.cep_devedor, ca.cep_garantidor, ca.chassi_substituicao, ca.clausula_penal_vrg, ca.comentario, " +
                    "ca.complemento_endereco_devedor, ca.complemento_endereco_garantidor, ca.cpf_cnpj_cessao_direito, " +
                    "ca.cpf_cnpj_devedor_financiado, ca.cpf_cnpj_garantidor_financiado, ca.data_aditivo_contrato, " +
                    "ca.data_cadastro, ca.data_compra, ca.data_conclusaodetran, ca.data_contrato, ca.data_liberacao_credito, " +
                    "ca.data_vencimento_primeira_parcela, ca.data_vencimento_ultima_parcela, ca.ddd_devedor, ca.ddd_garantidor, " +
                    "ca.descricao_penalidade, ca.email_devedor, ca.email_garantidor, ca.endereco_devedor, ca.endereco_garantidor, " +
                    "ca.indicador_comissao, ca.indicador_penalidade, ca.indicador_taxa_mora_dia, ca.indicador_taxa_multa, " +
                    "ca.nome_cessao_direito, ca.nome_devedor_financiado, ca.nome_garantidor_financiado, ca.numero_aditivo_contrato, " +
                    "ca.numero_contrato, ca.numero_cota_consorcio, ca.numero_endereco_devedor, ca.numero_endereco_garantidor, " +
                    "ca.numero_grupo_consorcio, ca.numero_registro_eletronico, ca.numero_registro_eletronico_origem, " +
                    "ca.percentual_comissao, ca.possui_garantidor, ca.quantidade_meses, ca.sigla_indice_finaceiro, ca.situacao, " +
                    "ca.situacao_financeira, ca.telefone_devedor, ca.telefone_garantidor, ca.tipo_contrato, ca.tipo_restricao, " +
                    "ca.tipo_vrg, ca.uf_endereco_devedor, ca.uf_endereco_garantidor, ca.uf_liberacao_credito, ca.uf_registro," +
                    "ca.valor_credito, ca.valoriof, ca.valor_parcela, ca.valor_taxa_contrato, ca.valor_taxa_juros_ano," +
                    "ca.valor_taxa_juros_mes, ca.valor_taxa_mora_dia, ca.valor_taxa_multa, ca.valor_total_divida, " +
                    "ca.valor_vrg, ca.arquivo_remessa_id, ca.cobranca_id, ca.financeira_id, ca.municipio_devedor_id, " +
                    "ca.municipio_garantidor_id, ca.municipio_liberacao_id, ca.livro_id, ca.id_processob3, " +
                    "ca.motivo_baixa "
                    + "from registro.contrato_aud ca join registro.revision_entity re on (re.id = ca.rev) "
                    + "where ca.situacao = 'ATIVO' and ca.id = :id "
                    + "and re.timestamp = (select max(x.timestamp) from registro.revision_entity x "
                    + "where 1 = 1 and exists (select 1 from registro.contrato_aud y "
                    + "where y.situacao = 'ATIVO' and y.id = :id and y.rev = x.id))")
    Object[] findLastAtivo(@Param("id") Long id);

    @Query("select o from Contrato o inner join fetch o.veiculos e where o.id = (select max(c.id) from Contrato c inner join c.veiculos v where v.numeroChassi = :chassi and v.numeroGravame = :gravame)")
    Contrato findByChassiGravame(@Param("chassi") String numeroChassi, @Param("gravame") String numeroGravame);

    @Query("select distinct(v) from Veiculo v left join fetch v.marca m join fetch v.contrato c join fetch c.cobranca o left join fetch c.anexos where c.cobranca = :cobranca order by c.id")
    List<Veiculo> findByCobranca(@Param("cobranca") Cobranca cobranca);

    @Query("select distinct(v) from Veiculo v join fetch v.contrato c where c.cobranca = :cobranca order by c.id")
    List<Veiculo> findVeiculosByCobranca(@Param("cobranca") Cobranca cobranca);

    @Query("select case when c.cobranca is not null then true else false end, false from Contrato c where c = :contrato")
    public Boolean possuiCobranca(@Param("contrato") Contrato contrato);

    @Query("select c from Contrato c inner join fetch c.veiculos v "
            + "where c.ufRegistro = :uf "
            + "and (select doc from DocumentoArrecadacao doc where doc.veiculo = v) is null")
    List<Contrato> findContratoSemTaxa(@Param("uf") Uf uf);

    Long countBySituacaoAndFinanceiraInAndAgente(Situacao situacao, List<Financeira> financeiras, Agente agente);

    Long countBySituacaoAndFinanceiraInAndUfRegistroAndAgente(Situacao situacao, List<Financeira> financeiras, Uf uf,
                                                              Agente agente);

    @Query("select count(c) from Contrato c" +
            " where not exists (select 1 from Anexo a where a.contrato=c)" +
            " and c.financeira in :financeiras" +
            " and c.agente = :agente" +
            " and c.situacao != 'BAIXADO'")
    Long countByAnexosIsNullAndFinanceiraInAndAgente(@Param("financeiras") List<Financeira> financeiras, @Param("agente") Agente agente);

    @Query("select count(c) from Contrato c" +
            " where not exists (select 1 from Anexo a where a.contrato=c)" +
            " and c.financeira in :financeiras" +
            " and c.agente = :agente" +
            " and c.ufRegistro = :uf" +
            " and c.situacao != 'BAIXADO'")
    Long countByAnexosIsNullAndFinanceiraInAndUfRegistroAndAgente(@Param("financeiras") List<Financeira> financeiras, @Param("uf") Uf uf, @Param("agente") Agente agente);

    @Query(nativeQuery = true, value = "select id_contrato from b3.processob3 where id_processo = :processo")
    Long findByProcessoB3(@Param("processo") Long processoB3);

    @Query(value = "from Contrato c where c.idProcessoB3 = :processo")
    Contrato findByProcessoB3Contrato(@Param("processo") Long processoB3);

    @Query(value = "select c.numeroRegistroEletronico from Contrato c "
            + "where c.situacao = 'PENDENTE' "
            + "and c.ufRegistro = :uf ")
    List<Long> listPendencias(@Param("uf") Uf uf);

    List<Contrato> findBySituacao(Situacao situacao);

    @Query(value = "select c from Contrato c "
            + "where c.situacao = 'PENDENTE_PAGAMENTO' "
            + "and c.ufRegistro = :uf ")
    List<Contrato> listPendenciasPagamento(@Param("uf") Uf uf);

    @Query(value = "SELECT DISTINCT c.financeira.documento FROM Contrato c WHERE c.ufRegistro =:uf AND c.situacao = 'PENDENTE'")
    List<String> listFinanceirasPendencias(@Param("uf") Uf uf);

    @Query(value = "SELECT c.numeroRegistroEletronico FROM Contrato c WHERE c.financeira.documento =:documento AND c.situacao =:situacao AND c.ufRegistro =:ufRegistro")
    List<Long> listContratosPendentesPorFinanceira(@Param("documento") String documento, @Param("situacao") Situacao situacao, @Param("ufRegistro") Uf ufRegistro);

    @EntityGraph(attributePaths = {"veiculos"})
    List<Contrato> findAllByDataConclusaoDETRANBetweenAndUfRegistro(Date dataInicio, Date dataFim, Uf uf);

    @Query("SELECT v FROM Veiculo v join fetch v.contrato c WHERE c.dataCadastro <= :dataLimite AND c.ufRegistro in (:ufRegistro) AND c.situacao = :situacao")
    List<Veiculo> buscaContratosPorTempoPorSituacaoEUf(@Param("dataLimite") Date dataLimite, @Param("ufRegistro") List<Uf> ufRegistro, @Param("situacao") Situacao situacao);

    @Query("SELECT c FROM Contrato c WHERE c.id = :id")
    Optional<Contrato> findByMarcaId(@Param("id") Long id);

    @Query("select distinct c from Contrato c "
            + " inner join fetch c.veiculos v "
            + " where c.situacao = :situacao "
            + " and c.ufRegistro = :uf and v not in (SELECT d.veiculo FROM DocumentoArrecadacao d WHERE d.estado=:uf)")
    List<Contrato> listarContratosPorSituacaoEUfRegistroESemDocDeArrecadacao(@Param("situacao") Situacao situacao, @Param("uf") Uf uf);


    @Query("select count (c) from Contrato c" +
            " where not exists (select 1 from Anexo a where a.contrato = c) and" +
            " c.situacao != 'BAIXADO' and" +
            " (:financeiras is null or c.financeira in :financeiras) and" +
            " (:uf is null or c.ufRegistro = :uf) and" +
            " (:financeiras is null or :agente is null or c.agente = :agente)")
    Long countBySemAnexoENaoBaixado(@Param("financeiras") List<Financeira> financeiras, @Param("uf") Uf uf, @Param("agente") Agente agente);

    @Query("SELECT DISTINCT c FROM Contrato c" +
            " join fetch c.financeira f" +
            " join c.anexos a" +
            " join fetch c.veiculos" +
            " where a.enviado != true" +
            " and c.ufRegistro in :estados" +
            " and c.financeira in :financeiras")
    List<Contrato> findContratoByAnexoNaoEnviadoAndEstadosAndFinanceiras(@Param("estados") List<Uf> estados, @Param("financeiras") List<Financeira> financeiras);

    @Query("select c from Contrato c inner join fetch c.veiculos v " +
            "where v.numeroGravame = :gravame " +
            "and c.situacao = 'ATIVO' " +
            "and c.tipoContrato = 'CESSAO_DIREITO_DEVEDOR'")
    Optional<Contrato> findByNumeroGravame(@Param("gravame") String gravame);


    @Query("select contr from Contrato contr where contr.id = " +
            "(select min(c.id) from Contrato c where c.financeira = :financeira)")
    Contrato findByMinContrato(@Param("financeira") Financeira financeira);

    @Query("select distinct(v) from VeiculoRsng v join fetch v.contratoRsng c where c.cobranca = :cobranca order by c.id")
    List<VeiculoRsng> findVeiculosByCobrancaSng(@Param("cobranca") Cobranca entity);
}


