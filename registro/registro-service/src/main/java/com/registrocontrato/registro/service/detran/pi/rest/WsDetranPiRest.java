package com.registrocontrato.registro.service.detran.pi.rest;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.registrocontrato.infra.entity.Uf;
import com.registrocontrato.infra.exception.ServiceException;
import com.registrocontrato.registro.service.detran.DefinirCredenciaisAPIDetran;
import com.registrocontrato.registro.service.detran.pi.client.ConsultaBoletoPiResponse;
import com.registrocontrato.seguranca.service.AcessoSenhaService;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;

import javax.annotation.PostConstruct;
import java.util.Date;

public abstract class WsDetranPiRest {

    protected final Log logger = LogFactory.getLog(getClass());

    @Value("${detran.pi.default.uri-boleto:null}")
    private String urlPI;

    @Value("${detran.pi.rest.token:null}")
    private String token;

    protected ObjectMapper mapper = new ObjectMapper();

    @Autowired
    private AcessoSenhaService acessoSenhaService;

    public abstract ConsultaBoletoPiResponse consultarBoleto(Date dataInicio, String cnpj) throws ServiceException;

    public String getUrl() {
        return urlPI;
    }

    public String getToken() {
        return token;
    }

    public Uf getUf() {
        return Uf.PI;
    }

}
