package com.registrocontrato.registro.repository;

import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import com.registrocontrato.infra.entity.SimNao;
import com.registrocontrato.infra.entity.Uf;
import com.registrocontrato.infra.service.BaseRepository;
import com.registrocontrato.registro.entity.ContaAzul;
import com.registrocontrato.registro.enums.TipoContaAzul;

import java.util.List;

public interface ContaAzulRepository extends BaseRepository<ContaAzul>{

	@Query("select case when count(*) > 0 then true else false end "
			+ "from ContaAzul c "
			+ "where c.codigo = :codigo and c.tipoContaAzul = 'SERVICO' "
			+ "and c.tipoContaAzul = :tipo "
			+ "and (:id is null or :id != c.id)")
	Boolean codigoServicoJaRegistrado(@Param("tipo") TipoContaAzul tipoContaAzul, @Param("codigo")String codigo, @Param("id") Long id);

	@Query("select case when count(*) > 0 then true else false end "
			+ "from ContaAzul c "
			+ "where c.uf = :uf and c.tipoContaAzul = 'SERVICO' "
			+ "and (:quatroRodas is null or c.quatroRodas = :quatroRodas) "
			+ "and (:id is null or :id != c.id)")
	Boolean tipoVeiculoServicoJaRegistrado(@Param("uf")Uf uf, @Param("quatroRodas") SimNao quatroRodas, @Param("id") Long id);

	@Query("from ContaAzul c where c.tipoContaAzul = 'SERVICO' and c.uf = :uf")
	ContaAzul findServicoByUf(@Param("uf")Uf uf);

	@Query("from ContaAzul c where c.tipoContaAzul = 'SERVICO' and c.uf = :uf and c.codigo like '%RSNG%'")
	ContaAzul findServicoByUfSng(@Param("uf")Uf uf);

	@Query("from ContaAzul c where c.uf = :uf and c.ativo = :ativo and c.codigo like '%RSNG%'")
	List<ContaAzul> findAllByUfAndAtivoSng(Uf uf, SimNao ativo);

	List<ContaAzul> findAllByUfAndAtivo(Uf uf, SimNao ativo);
}
