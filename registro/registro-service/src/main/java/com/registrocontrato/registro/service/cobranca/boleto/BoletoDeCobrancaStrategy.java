package com.registrocontrato.registro.service.cobranca.boleto;

import com.registrocontrato.infra.entity.SimNao;
import com.registrocontrato.infra.entity.Uf;
import com.registrocontrato.registro.entity.Cobranca;

import java.util.List;

public interface BoletoDeCobrancaStrategy {

    void emitirBoleto(Cobranca cobranca) throws Exception;

    default Cobranca atualizarCobranca(Cobranca cobranca, BoletoResponse retornoBoletoPlace, BoletoResponse retornoBoletoSng) {
        cobranca.setTokenSngBoleto(retornoBoletoSng.getToken());
        cobranca.setBoletoSngEmitido(true);
        cobranca.setTokenBoleto(retornoBoletoPlace.getToken());
        cobranca.setNumeroDocumentoCredenciada(retornoBoletoPlace.getNumeroDocumento());
        cobranca.setBoletoEmitido(true);
        return cobranca;
    }

    default Cobranca atualizarCobranca(Cobranca cobranca, BoletoResponse retornoBoletoPlace, BoletoResponse retornoBoletoReembolso, BoletoResponse retornoBoletoSng) {
        Boolean boletoEmitido = !(cobranca.getFinanceira().getCobrancaSemBoleto() != null && cobranca.getFinanceira().getCobrancaSemBoleto().equals(SimNao.S));

        cobranca.setTokenSngBoleto(retornoBoletoSng.getToken());
        cobranca.setBoletoSngEmitido(true);
        cobranca.setTokenReembolsoBoleto(retornoBoletoReembolso.getToken());
        cobranca.setNumeroDocumentoDetran(retornoBoletoReembolso.getNumeroDocumento());
        cobranca.setTokenBoleto(retornoBoletoPlace != null ? retornoBoletoPlace.getToken() : null);
        cobranca.setNumeroDocumentoCredenciada(retornoBoletoPlace != null ? retornoBoletoPlace.getNumeroDocumento() : null);
        cobranca.setBoletoEmitido(boletoEmitido);
        cobranca.setBoletoReembolsoEmitido(true);
        return cobranca;
    }

    Uf getUf();

    List<String> getCnpjFinanceiras();

}
