package com.registrocontrato.registro.service;

import com.registrocontrato.infra.entity.Uf;
import com.registrocontrato.infra.util.PlaceconUtil;
import com.registrocontrato.registro.entity.*;
import com.registrocontrato.registro.enums.TipoCobranca;
import com.registrocontrato.registro.enums.TipoContrato;
import com.registrocontrato.registro.enums.TipoVeiculo;
import com.registrocontrato.registro.repository.CobrancaRepository;
import com.registrocontrato.registro.service.cobranca.gravame.GravameService;
import com.registrocontrato.registro.service.detran.pr.client.response.RelatorioDeCobrancaResponse;
import com.registrocontrato.registro.service.detran.pr.client.response.ResultadoPrResponse;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang.time.DateFormatUtils;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.apache.poi.hssf.util.HSSFColor;
import org.apache.poi.openxml4j.exceptions.InvalidFormatException;
import org.apache.poi.openxml4j.opc.OPCPackage;
import org.apache.poi.openxml4j.opc.PackageAccess;
import org.apache.poi.poifs.crypt.EncryptionInfo;
import org.apache.poi.poifs.crypt.EncryptionMode;
import org.apache.poi.poifs.crypt.Encryptor;
import org.apache.poi.poifs.filesystem.POIFSFileSystem;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.util.Units;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.MalformedURLException;
import java.net.URL;
import java.security.GeneralSecurityException;
import java.sql.Timestamp;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class ManipulaRelatorioService {

    @Autowired
    private ContratoService contratoService;

    @Autowired
    private CredenciamentoService credenciamentoService;

    @Autowired
    private CupomDescontoService cupomDescontoService;

    @Autowired
    private GravameService gravameService;

    @Autowired
    CobrancaRepository cobrancaRepository;

    protected final Log logger = LogFactory.getLog(getClass());

    public CellStyle createStyle(Workbook workbook) {
        CellStyle style = workbook.createCellStyle();
        Font font = workbook.createFont();
        font.setFontName("Arial");
        style.setFillForegroundColor(HSSFColor.HSSFColorPredefined.PALE_BLUE.getIndex());
        style.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        font.setBold(true);
        font.setColor(HSSFColor.HSSFColorPredefined.BLACK.getIndex());
        style.setFont(font);
        return style;
    }

    public int createHeader(Workbook workbook, Sheet sheet, CellStyle style, int rowCount) throws MalformedURLException, IOException {
        return createHeader(workbook, sheet, style, rowCount, "PLACECON - Registro de Contrato");
    }

    public int createHeader(Workbook workbook, Sheet sheet, CellStyle style, int rowCount, String titulo) throws MalformedURLException, IOException {
        Row header = sheet.createRow(rowCount++);
        header.setHeightInPoints(45);
        header.createCell(0).setCellValue("");
        header.getCell(0).setCellStyle(style);
        header.createCell(1).setCellValue(titulo);
        header.getCell(1).setCellStyle(style);


        CreationHelper helper = workbook.getCreationHelper();
        Drawing<?> drawing = sheet.createDrawingPatriarch();

        ClientAnchor anchor = helper.createClientAnchor();
        anchor.setAnchorType(ClientAnchor.AnchorType.DONT_MOVE_AND_RESIZE);
        anchor.setDx1(20 * Units.EMU_PER_PIXEL);
        anchor.setDy1(20 * Units.EMU_PER_PIXEL);

        try {
            URL url = new URL("https://placecon.com.br/relatorio/templates/assets/images/logo.png");
            InputStream is = url.openStream();
            int pictureIndex = workbook.addPicture(IOUtils.toByteArray(is), Workbook.PICTURE_TYPE_PNG);
            final Picture pict = drawing.createPicture(anchor, pictureIndex);
            pict.resize(0.7);
        } catch (Exception e) {
            logger.error(e);
        }
        sheet.addMergedRegion(new CellRangeAddress(0, 0, 1, 26));
        return rowCount;
    }

    public Credenciamento filtrarCredenciamentoAtivoPorUf(Uf uf, List<Credenciamento> credenciamentos) {
        return credenciamentos.stream().filter(c -> c.getUf().equals(uf)).findFirst().orElse(null);
    }

    public BilhetagemGravame filtrarBilhetagemAtivoPorUf(Uf uf, List<BilhetagemGravame> gravames) {
        return gravames.stream().filter(c -> c.getUf().equals(uf)).findFirst().orElse(null);
    }

    public void createTableXLSEmail(Sheet sheet, CellStyle style, int rowCount, Cobranca cobranca) {
        Row rowTitle = sheet.createRow(rowCount++);
        rowTitle.createCell(0).setCellValue("Id registro");
        sheet.setColumnWidth(0, 21 * 256);
        rowTitle.createCell(1).setCellValue("Uf do Registro");
        sheet.setColumnWidth(1, 21 * 256);
        rowTitle.createCell(2).setCellValue("Número do Contrato");
        sheet.setColumnWidth(2, 21 * 256);
        rowTitle.createCell(3).setCellValue("Gravame");
        sheet.setColumnWidth(3, 16 * 256);
        rowTitle.createCell(4).setCellValue("CHASSI");
        sheet.setColumnWidth(4, 26 * 256);
        rowTitle.createCell(5).setCellValue("RENAVAM");
        sheet.setColumnWidth(5, 17 * 256);
        rowTitle.createCell(6).setCellValue("Placa");
        sheet.setColumnWidth(6, 11 * 256);
        rowTitle.createCell(7).setCellValue("Ano Fabricação");
        sheet.setColumnWidth(7, 19 * 256);
        rowTitle.createCell(8).setCellValue("Ano Modelo");
        sheet.setColumnWidth(8, 16 * 256);
        rowTitle.createCell(9).setCellValue("Marca");
        sheet.setColumnWidth(9, 19 * 256);
        rowTitle.createCell(10).setCellValue("Data do Contrato");
        sheet.setColumnWidth(10, 19 * 256);
        rowTitle.createCell(11).setCellValue("Situação");
        sheet.setColumnWidth(11, 14 * 256);
        rowTitle.createCell(12).setCellValue("Tipo de Restrição");
        sheet.setColumnWidth(12, 23 * 256);
        rowTitle.createCell(13).setCellValue("Credor");
        sheet.setColumnWidth(13, 21 * 256);
        rowTitle.createCell(14).setCellValue("Data do Cadastro");
        sheet.setColumnWidth(14, 23 * 256);
        rowTitle.createCell(15).setCellValue("Anexo");
        sheet.setColumnWidth(15, 12 * 256);
        rowTitle.createCell(16).setCellValue("Data do Registro no DETRAN");
        sheet.setColumnWidth(16, 32 * 256);
        rowTitle.createCell(17).setCellValue("Valor DETRAN");
        sheet.setColumnWidth(17, 19 * 256);
        rowTitle.createCell(18).setCellValue("Valor Credenciada");
        sheet.setColumnWidth(18, 23 * 256);
        rowTitle.createCell(19).setCellValue("Valor Total");
        sheet.setColumnWidth(19, 15 * 256);
        rowTitle.createCell(20).setCellValue("Tipo de Veículo");
        sheet.setColumnWidth(20, 26 * 256);
        rowTitle.createCell(21).setCellValue("Tipo de Cobrança");
        sheet.setColumnWidth(21, 26 * 256);
        for (int i = 0; i < 22; i++) {
            rowTitle.getCell(i).setCellStyle(style);
        }

        List<Veiculo> veiculos = contratoService.findByCobranca(cobranca);
        Set<Uf> estadosRegistrados = veiculos.stream().map(veiculo -> veiculo.getContrato().getUfRegistro()).collect(Collectors.toSet());

        Map<Uf, BigDecimal> valorDescontoPorEstado = new HashMap<>();
        for (Uf estado : estadosRegistrados) {
            valorDescontoPorEstado.put(estado, valorDescontoPorCobranca(cobranca, estado));
        }

        Long numeroRegistroEletronico = 0L;
        //mostra todos os veiculos do contrato
        Contrato c = null;
        Boolean unificada = veiculos.stream().map(v -> v.getContrato().getUfRegistro()).collect(Collectors.toSet()).size() > 1;
        List<Credenciamento> credenciamentos = credenciamentoService.getCredenciamentos(cobranca);
        Set<String> idRegistro = new HashSet<>();

        for (Veiculo v : veiculos) {

            c = v.getContrato();
            Credenciamento credenciamento = filtrarCredenciamentoAtivoPorUf(c.getUfRegistro(), credenciamentos);
            Row row = sheet.createRow(rowCount++);
            Cell cell = row.createCell(0);
            cell.setCellType(CellType.STRING);
            cell.setCellValue(String.valueOf(c.getNumeroRegistroEletronico()));
            row.createCell(1).setCellValue(c.getUfRegistro().toString());
            row.createCell(2).setCellValue(c.getNumeroContrato());
            row.createCell(3).setCellValue(v.getNumeroGravame());
            row.createCell(4).setCellValue(v.getNumeroChassi());
            row.createCell(5).setCellValue(v.getNumeroRenavam());
            row.createCell(6).setCellValue(v.getPlaca());
            row.createCell(7).setCellValue(v.getAnoFabricacao() == null ? "" : v.getAnoFabricacao().toString());
            row.createCell(8).setCellValue(v.getAnoModelo() == null ? "" : v.getAnoModelo().toString());
            row.createCell(9).setCellValue(v.getMarca() != null ? v.getMarca().getDescricao() : "");
            row.createCell(10).setCellValue(DateFormatUtils.format(c.getDataContrato(), "dd/MM/yyyy"));
            row.createCell(11).setCellValue(c.getSituacao().getDescricao());
            row.createCell(12).setCellValue(c.getTipoRestricao() != null ? c.getTipoRestricao().getDescricao() : "");
            row.createCell(13).setCellValue(c.getFinanceira() != null ? c.getFinanceira().getDocumento() : "");
            row.createCell(14).setCellValue(c.getDataCadastro() == null ? "-" : DateFormatUtils.format(c.getDataCadastro(), "dd/MM/yyyy HH:mm:ss"));
            row.createCell(15).setCellValue(PlaceconUtil.isListaVaziaOuNula(c.getAnexos()) ? "N" : "S");
            row.createCell(16).setCellValue(c.getDataConclusaoDETRAN() == null ? "-" : DateFormatUtils.format(c.getDataConclusaoDETRAN(), "dd/MM/yyyy HH:mm:ss"));
            double valorDETRAN = 0;
            double valorIntegraMais = 0;
            double valorTotalRegistro = 0;
            double valorDesconto = 0;
            double valorCredenciada = 0;
            if (c.getCobranca() != null) {

                valorIntegraMais = c.getCobranca().getValorIntegraMais().doubleValue() / c.getCobranca().getQuantidadeRegistros();
                valorDesconto = valorDescontoPorEstado.get(c.getUfRegistro()).doubleValue();

                if (c.getUfRegistro() == Uf.CE) {
                    if (TipoVeiculo.getQuatroRodas().contains(v.getTipo())) {
                        valorCredenciada = credenciamento.getPrecosCompostos().stream().filter(precoComposto -> (precoComposto.getValorParametro().equals("4"))).findFirst().get().getValorCredenciada().doubleValue();
                        valorDETRAN = credenciamento.getPrecosCompostos().stream().filter(precoComposto -> (precoComposto.getValorParametro().equals("4"))).findFirst().get().getValorDETRAN().doubleValue();

                    } else {
                        valorCredenciada = credenciamento.getPrecosCompostos().stream().filter(precoComposto -> (precoComposto.getValorParametro().equals("2"))).findFirst().get().getValorCredenciada().doubleValue();
                        valorDETRAN = credenciamento.getPrecosCompostos().stream().filter(precoComposto -> (precoComposto.getValorParametro().equals("2"))).findFirst().get().getValorDETRAN().doubleValue();
                    }
                    valorCredenciada = valorCredenciada + valorIntegraMais;
                } else if (unificada && c.getUfRegistro() != Uf.CE) {
                    valorCredenciada = credenciamento.getValorCredenciada().doubleValue();
                    if (c.getTipoContrato().equals(TipoContrato.CONTRATO_PRINCIPAL)) {
                        valorDETRAN = credenciamento.getValorDETRAN().doubleValue();
                    } else {
                        valorDETRAN = credenciamento.getValorAditivo().doubleValue();
                    }
                } else {
                    valorCredenciada = (c.getCobranca().getValorCredenciada().doubleValue() + valorIntegraMais) / c.getCobranca().getQuantidadeRegistros();
                    if (c.getTipoContrato().equals(TipoContrato.CONTRATO_PRINCIPAL)) {
                        valorDETRAN = c.getCobranca().getValorDetranPrincipal().doubleValue() / c.getCobranca().getQuantidadePrincipal();
                    } else {
                        valorDETRAN = c.getCobranca().getValorDetranAditivo().doubleValue() / c.getCobranca().getQuantidadeAditivo();
                    }
                }

                double valorCformatado = valorCredenciada - valorDesconto;
                double valorDetran = valorDETRAN;

                if (idRegistro.contains(c.getNumeroRegistroEletronico().toString())) {
                    valorCformatado = 0d;
                    valorDetran = 0d;
                }
                
                valorTotalRegistro = (valorDETRAN + valorCredenciada) - valorDesconto;
                row.createCell(17).setCellValue(valorDetran);
                row.createCell(18).setCellValue(valorCformatado);
                row.createCell(19).setCellValue(valorTotalRegistro);
            }
            row.createCell(20).setCellValue(Objects.nonNull(v.getTipo()) ? v.getTipo().getDescricao() : "");
            row.createCell(21).setCellValue(credenciamento.getTipoCobranca() == TipoCobranca.CONTRATO ? "Contrato" : "Veículo");
            idRegistro.add(c.getNumeroRegistroEletronico().toString());
        }
    }

    public void createTableRelatorio(Sheet sheet, CellStyle style, int rowCount, RelatorioDeCobrancaResponse relatorioDeCobrancaResponse) {

        Row rowTitle = sheet.createRow(rowCount++);
        rowTitle.createCell(0).setCellValue("Numero Contrato");
        sheet.setColumnWidth(0, 21 * 256);
        rowTitle.createCell(1).setCellValue("Data Registro");
        sheet.setColumnWidth(1, 21 * 256);
        rowTitle.createCell(2).setCellValue("Data Contrato");
        sheet.setColumnWidth(2, 16 * 256);
        rowTitle.createCell(3).setCellValue("Chassi");
        sheet.setColumnWidth(3, 26 * 256);
        rowTitle.createCell(4).setCellValue("Gravame");

        for (ResultadoPrResponse r : relatorioDeCobrancaResponse.getResultado()) {

            Row row = sheet.createRow(rowCount++);
            Cell cell = row.createCell(0);
            cell.setCellType(CellType.STRING);
            cell.setCellValue(String.valueOf(r.getNumeroContrato()));
            row.createCell(1).setCellValue(stringTimeStampToDate(r.getDataRegistro()));
            row.createCell(2).setCellValue(stringTimeStampToDate(r.getDataContrato()));
            row.createCell(3).setCellValue(r.getChassi());
            row.createCell(4).setCellValue(r.getNumeroRestricao());
        }
    }

    public void createTable(Sheet sheet, CellStyle style, int rowCount, Cobranca cobranca) {
        long start = System.currentTimeMillis();
        logger.info(String.format("Processando relatório default [%s]", cobranca.getId()));

        Row rowTitle = sheet.createRow(rowCount++);
        rowTitle.createCell(0).setCellValue("Id do registro");
        sheet.setColumnWidth(0, 21 * 256);
        rowTitle.createCell(1).setCellValue("UF do registro");
        sheet.setColumnWidth(1, 21 * 256);
        rowTitle.createCell(2).setCellValue("Número do Contrato");
        sheet.setColumnWidth(2, 21 * 256);
        rowTitle.createCell(3).setCellValue("Gravame");
        sheet.setColumnWidth(3, 16 * 256);
        rowTitle.createCell(4).setCellValue("CHASSI");
        sheet.setColumnWidth(4, 26 * 256);
        rowTitle.createCell(5).setCellValue("RENAVAM");
        sheet.setColumnWidth(5, 17 * 256);
        rowTitle.createCell(6).setCellValue("Placa");
        sheet.setColumnWidth(6, 11 * 256);
        rowTitle.createCell(7).setCellValue("Ano Fabricação");
        sheet.setColumnWidth(7, 19 * 256);
        rowTitle.createCell(8).setCellValue("Ano Modelo");
        sheet.setColumnWidth(8, 16 * 256);
        rowTitle.createCell(9).setCellValue("Marca");
        sheet.setColumnWidth(9, 19 * 256);
        rowTitle.createCell(10).setCellValue("Data do Contrato");
        sheet.setColumnWidth(10, 19 * 256);
        rowTitle.createCell(11).setCellValue("Situação");
        sheet.setColumnWidth(11, 14 * 256);
        rowTitle.createCell(12).setCellValue("Tipo de Restrição");
        sheet.setColumnWidth(12, 23 * 256);
        rowTitle.createCell(13).setCellValue("Credor");
        sheet.setColumnWidth(13, 21 * 256);
        rowTitle.createCell(14).setCellValue("CPF/CNPJ do Devedor");
        sheet.setColumnWidth(14, 28 * 256);
        rowTitle.createCell(15).setCellValue("Tipo de Pessoa");
        sheet.setColumnWidth(15, 18 * 256);
        rowTitle.createCell(16).setCellValue("Nome do Devedor");
        sheet.setColumnWidth(16, 30 * 256);
        rowTitle.createCell(17).setCellValue("Data do Cadastro");
        sheet.setColumnWidth(17, 23 * 256);
        rowTitle.createCell(18).setCellValue("Anexo");
        sheet.setColumnWidth(18, 12 * 256);
        rowTitle.createCell(19).setCellValue("Data do Registro no DETRAN");
        sheet.setColumnWidth(19, 32 * 256);
        rowTitle.createCell(20).setCellValue("Valor DETRAN");
        sheet.setColumnWidth(20, 19 * 256);
        rowTitle.createCell(21).setCellValue("Valor Credenciada");
        sheet.setColumnWidth(21, 23 * 256);
        rowTitle.createCell(22).setCellValue("Valor Total");
        sheet.setColumnWidth(22, 15 * 256);
        rowTitle.createCell(23).setCellValue("Tipo de Veículo");
        sheet.setColumnWidth(23, 26 * 256);
        rowTitle.createCell(24).setCellValue("Tipo de Cobrança");
        sheet.setColumnWidth(24, 26 * 256);
        for (int i = 0; i < 25; i++) {
            rowTitle.getCell(i).setCellStyle(style);
        }

        List<Veiculo> veiculos = contratoService.findVeiculosByCobranca(cobranca);
        Set<Uf> estadosRegistrados = veiculos.stream().map(veiculo -> veiculo.getContrato().getUfRegistro()).collect(Collectors.toSet());

        Map<Uf, BigDecimal> valorDescontoPorEstado = new HashMap<>();
        for (Uf estado : estadosRegistrados) {
            valorDescontoPorEstado.put(estado, valorDescontoPorCobranca(cobranca, estado));
        }

        //mostra todos os veiculos do contrato
        Contrato c = null;
        Boolean unificada = veiculos.stream().map(v -> v.getContrato().getUfRegistro()).collect(Collectors.toSet()).size() > 1;
        List<Credenciamento> credenciamentos = credenciamentoService.getCredenciamentos(cobranca);
        int cont = 1;
        Set<String> idRegistro = new HashSet<>();
        for (Veiculo v : veiculos) {
            long startIndividual = System.currentTimeMillis();
            logger.info("Veículo " + cont + " de: " + veiculos.size());
            cont++;
            c = v.getContrato();
            Credenciamento credenciamento = filtrarCredenciamentoAtivoPorUf(c.getUfRegistro(), credenciamentos);
            Row row = sheet.createRow(rowCount++);
            Cell cell = row.createCell(0);
            cell.setCellType(CellType.STRING);
            cell.setCellValue(String.valueOf(c.getNumeroRegistroEletronico()));
            row.createCell(1).setCellValue(c.getUfRegistro().toString());
            row.createCell(2).setCellValue(c.getNumeroContrato());
            row.createCell(3).setCellValue(v.getNumeroGravame());
            row.createCell(4).setCellValue(v.getNumeroChassi());
            row.createCell(5).setCellValue(v.getNumeroRenavam());
            row.createCell(6).setCellValue(v.getPlaca());
            row.createCell(7).setCellValue(v.getAnoFabricacao() == null ? "" : v.getAnoFabricacao().toString());
            row.createCell(8).setCellValue(v.getAnoModelo() == null ? "" : v.getAnoModelo().toString());
            row.createCell(9).setCellValue(v.getMarca() != null ? v.getMarca().getDescricao() : "");
            row.createCell(10).setCellValue(DateFormatUtils.format(c.getDataContrato(), "dd/MM/yyyy"));
            row.createCell(11).setCellValue(c.getSituacao().getDescricao());
            row.createCell(12).setCellValue(c.getTipoRestricao() != null ? c.getTipoRestricao().getDescricao() : "");
            row.createCell(13).setCellValue(c.getFinanceira() != null ? c.getFinanceira().getDocumento() : "");
            row.createCell(14).setCellValue(c.getCpfCnpjDevedorFinanciado());
            row.createCell(15).setCellValue(c.getCpfCnpjDevedorFinanciado().length() > 12 ? "PJ" : "PF");
            row.createCell(16).setCellValue(c.getNomeDevedorFinanciado());
            row.createCell(17).setCellValue(c.getDataCadastro() == null ? "-" : DateFormatUtils.format(c.getDataCadastro(), "dd/MM/yyyy HH:mm:ss"));
            row.createCell(18).setCellValue(PlaceconUtil.isListaVaziaOuNula(c.getAnexos()) ? "N" : "S");
            row.createCell(19).setCellValue(c.getDataConclusaoDETRAN() == null ? "-" : DateFormatUtils.format(c.getDataConclusaoDETRAN(), "dd/MM/yyyy HH:mm:ss"));

            double valorDETRAN = 0;
            double valorIntegraMais = 0;
            double valorTotalRegistro = 0;
            double valorDesconto = 0;
            double valorCredenciada = 0;
            if (c.getCobranca() != null) {
                valorIntegraMais = c.getCobranca().getValorIntegraMais().doubleValue() / c.getCobranca().getQuantidadeRegistros();
                valorDesconto = valorDescontoPorEstado.get(c.getUfRegistro()).doubleValue();

                if (c.getUfRegistro() == Uf.CE) {
                    if (TipoVeiculo.getQuatroRodas().contains(v.getTipo())) {
                        valorCredenciada = credenciamento.getPrecosCompostos().stream().filter(precoComposto -> (precoComposto.getValorParametro().equals("4"))).findFirst().get().getValorCredenciada().doubleValue();
                        valorDETRAN = credenciamento.getPrecosCompostos().stream().filter(precoComposto -> (precoComposto.getValorParametro().equals("4"))).findFirst().get().getValorDETRAN().doubleValue();

                    } else {
                        valorCredenciada = credenciamento.getPrecosCompostos().stream().filter(precoComposto -> (precoComposto.getValorParametro().equals("2"))).findFirst().get().getValorCredenciada().doubleValue();
                        valorDETRAN = credenciamento.getPrecosCompostos().stream().filter(precoComposto -> (precoComposto.getValorParametro().equals("2"))).findFirst().get().getValorDETRAN().doubleValue();
                    }
                    valorCredenciada = valorCredenciada + valorIntegraMais;
                } else if (unificada && c.getUfRegistro() != Uf.CE) {
                    valorCredenciada = credenciamento.getValorCredenciada().doubleValue();
                    if (c.getTipoContrato().equals(TipoContrato.CONTRATO_PRINCIPAL)) {
                        valorDETRAN = credenciamento.getValorDETRAN().doubleValue();
                    } else {
                        valorDETRAN = credenciamento.getValorAditivo().doubleValue();
                    }
                } else {
                    valorCredenciada = (c.getCobranca().getValorCredenciada().doubleValue() + valorIntegraMais) / c.getCobranca().getQuantidadeRegistros();
                    if (c.getTipoContrato().equals(TipoContrato.CONTRATO_PRINCIPAL)) {
                        valorDETRAN = c.getCobranca().getValorDetranPrincipal().doubleValue() / c.getCobranca().getQuantidadePrincipal();
                    } else {
                        valorDETRAN = c.getCobranca().getValorDetranAditivo().doubleValue() / c.getCobranca().getQuantidadeAditivo();
                    }
                }
                double valorCformatado = valorCredenciada - valorDesconto;
                double valorDetran = valorDETRAN;
                
                if (idRegistro.contains(c.getNumeroRegistroEletronico().toString())) {
                    valorCformatado = 0d;
                    valorDetran = 0d;
                }
                
                valorTotalRegistro = (valorDETRAN + valorCredenciada) - valorDesconto;
                row.createCell(20).setCellValue(valorDetran);
                row.createCell(21).setCellValue(valorCformatado);
                row.createCell(22).setCellValue(valorTotalRegistro);
            }
            row.createCell(23).setCellValue(Objects.nonNull(v.getTipo()) ? v.getTipo().getDescricao() : "");
            row.createCell(24).setCellValue(credenciamento.getTipoCobranca() == TipoCobranca.CONTRATO ? "Contrato" : "Veículo");
            long endIndividual = System.currentTimeMillis();
            logger.info(String.format("Tempo de processamento individual: %s", endIndividual - startIndividual));
            idRegistro.add(String.valueOf(c.getNumeroRegistroEletronico()));
        }
        long end = System.currentTimeMillis();
        logger.info(String.format("Tempo de processamento do relatorio default: %s", end - start));
    }

    public void createTableSng(Sheet sheet, CellStyle style, int rowCount, Cobranca cobranca) {
        long start = System.currentTimeMillis();
        logger.info(String.format("Processando relatório default [%s]", cobranca.getId()));

        Row rowTitle = sheet.createRow(rowCount++);
        rowTitle.createCell(0).setCellValue("UF do Registro");
        sheet.setColumnWidth(0, 21 * 256);
        rowTitle.createCell(1).setCellValue("Número do Contrato");
        sheet.setColumnWidth(1, 21 * 256);
        rowTitle.createCell(2).setCellValue("CHASSI");
        sheet.setColumnWidth(2, 26 * 256);
        rowTitle.createCell(3).setCellValue("RENAVAM");
        sheet.setColumnWidth(3, 17 * 256);
        rowTitle.createCell(4).setCellValue("Placa");
        sheet.setColumnWidth(4, 11 * 256);
        rowTitle.createCell(5).setCellValue("Ano Fabricação");
        sheet.setColumnWidth(5, 19 * 256);
        rowTitle.createCell(6).setCellValue("Ano Modelo");
        sheet.setColumnWidth(6, 16 * 256);
        rowTitle.createCell(7).setCellValue("Marca");
        sheet.setColumnWidth(7, 19 * 256);
        rowTitle.createCell(8).setCellValue("Data do Contrato");
        sheet.setColumnWidth(8, 19 * 256);
        rowTitle.createCell(9).setCellValue("Situação");
        sheet.setColumnWidth(9, 14 * 256);
        rowTitle.createCell(10).setCellValue("Tipo de Restrição");
        sheet.setColumnWidth(10, 23 * 256);
        rowTitle.createCell(11).setCellValue("Credor");
        sheet.setColumnWidth(11, 21 * 256);
        rowTitle.createCell(12).setCellValue("CPF/CNPJ do Devedor");
        sheet.setColumnWidth(12, 28 * 256);
        rowTitle.createCell(13).setCellValue("Tipo de Pessoa");
        sheet.setColumnWidth(13, 18 * 256);
        rowTitle.createCell(14).setCellValue("Nome do Devedor");
        sheet.setColumnWidth(14, 30 * 256);
        rowTitle.createCell(15).setCellValue("Data do Cadastro RSNG");
        sheet.setColumnWidth(15, 23 * 256);
        rowTitle.createCell(16).setCellValue("Data de Conclusão RSNG");
        sheet.setColumnWidth(16, 32 * 256);
        rowTitle.createCell(17).setCellValue("Valor Bilhetagem");
        sheet.setColumnWidth(17, 23 * 256);
        rowTitle.createCell(18).setCellValue("Valor Total RSNG");
        sheet.setColumnWidth(18, 15 * 256);
        rowTitle.createCell(19).setCellValue("Tipo de Veículo");
        sheet.setColumnWidth(19, 26 * 256);
        for (int i = 0; i < 20; i++) {
            rowTitle.getCell(i).setCellStyle(style);
        }

        List<VeiculoRsng> veiculos = contratoService.findVeiculosSngByCobranca(cobranca);
        Set<Uf> estadosRegistrados = veiculos.stream().map(veiculo -> veiculo.getContratoRsng().getUfRegistro()).collect(Collectors.toSet());

        Map<Uf, BigDecimal> valorDescontoPorEstado = new HashMap<>();
        for (Uf estado : estadosRegistrados) {
            valorDescontoPorEstado.put(estado, valorDescontoPorCobranca(cobranca, estado));
        }

        //mostra todos os veiculos do contratoRsng
        ContratoRsng c = null;
        List<BilhetagemGravame> gravames = gravameService.getBilhetagemGravames(cobranca);
        int cont = 1;
        for (VeiculoRsng v : veiculos) {
            long startIndividual = System.currentTimeMillis();
            logger.info("Veículo " + cont + " de: " + veiculos.size());
            cont++;
            c = v.getContratoRsng();
            Row row = sheet.createRow(rowCount++);
            Cell cell = row.createCell(0);
            cell.setCellType(CellType.STRING);
            row.createCell(0).setCellValue(c.getUfRegistro().toString());
            row.createCell(1).setCellValue(c.getNumeroContrato());
            row.createCell(2).setCellValue(v.getNumeroChassi());
            row.createCell(3).setCellValue(v.getNumeroRenavam());
            row.createCell(4).setCellValue(v.getPlaca());
            row.createCell(5).setCellValue(v.getAnoFabricacao() == null ? "" : v.getAnoFabricacao().toString());
            row.createCell(6).setCellValue(v.getAnoModelo() == null ? "" : v.getAnoModelo().toString());
            row.createCell(7).setCellValue(v.getMarca() != null ? v.getMarca().getDescricao() : "");
            row.createCell(8).setCellValue(DateFormatUtils.format(c.getDataContrato(), "dd/MM/yyyy"));
            row.createCell(9).setCellValue(c.getSituacao().getDescricao());
            row.createCell(10).setCellValue(c.getTipoRestricao() != null ? c.getTipoRestricao().getDescricao() : "");
            row.createCell(11).setCellValue(c.getFinanceira() != null ? c.getFinanceira().getDocumento() : "");
            row.createCell(12).setCellValue(c.getCpfCnpjDevedorFinanciado());
            row.createCell(13).setCellValue(c.getCpfCnpjDevedorFinanciado().length() > 12 ? "PJ" : "PF");
            row.createCell(14).setCellValue(c.getNomeDevedorFinanciado());
            row.createCell(15).setCellValue(c.getDataCadastroRsng() == null ? "-" : DateFormatUtils.format(c.getDataCadastroRsng(), "dd/MM/yyyy HH:mm:ss"));
            row.createCell(16).setCellValue(c.getDataConclusaoRsng() == null ? "-" : DateFormatUtils.format(c.getDataConclusaoRsng(), "dd/MM/yyyy HH:mm:ss"));

            if (c.getCobranca() != null) {
                BilhetagemGravame credenciamento = filtrarBilhetagemAtivoPorUf(c.getUfRegistro(), gravames);
                String valorTotalRegistro = c.getCobranca().getValorCobrancaSng().toPlainString();
                row.createCell(17).setCellValue(credenciamento.getValorTotal() == null ? "" : credenciamento.getValorTotal().toPlainString());
                row.createCell(18).setCellValue(valorTotalRegistro);
            }

            row.createCell(19).setCellValue(Objects.nonNull(v.getTipo()) ? v.getTipo().getDescricao() : "");
            long endIndividual = System.currentTimeMillis();
            logger.info(String.format("Tempo de processamento individual: %s", endIndividual - startIndividual));
        }
        long end = System.currentTimeMillis();
        logger.info(String.format("Tempo de processamento do relatorio default: %s", end - start));
    }

    public BigDecimal valorDescontoPorCobranca(Cobranca cobranca, Uf uf) {
        List<CupomDesconto> cupomDescontos = cupomDescontoService.findCuponsDisponiveis(cobranca, uf);
        List<FaixaDesconto> faixaDescontos = cupomDescontos.stream().filter(f -> f.getUf().equals(uf)).map(CupomDesconto::getFaixasDesconto).findFirst().orElse(null);
        if (Objects.nonNull(faixaDescontos)) {

            return faixaDescontos.stream().map(faixaDesconto -> {
                if (faixaDesconto.getQuantidadeFinal() == null)
                    return faixaDesconto.getPercentual();

                long numeroContratosComDesconto = faixaDesconto.getQuantidadeFinal() - faixaDesconto.getQuantidadeInicial() + 1;
                long quantidadeRegistrosPorEstado = cobrancaRepository.countContratosByCobrancaAndUfRegistro(cobranca, uf);

                BigDecimal desconto = faixaDesconto.getPercentual();
                BigDecimal totalDescontos = desconto.multiply(BigDecimal.valueOf(numeroContratosComDesconto));
                return totalDescontos.divide(BigDecimal.valueOf(quantidadeRegistrosPorEstado));
            }).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);

        }
        return BigDecimal.ZERO;
    }

    public void createTableGrupoSafra(Sheet sheet, CellStyle style, int rowCount, Cobranca cobranca) {

        Row rowTitle = sheet.createRow(rowCount++);
        rowTitle.createCell(0).setCellValue("Data");
        sheet.setColumnWidth(0, 23 * 256);
        rowTitle.createCell(1).setCellValue("CPF/CNPJ do Fornecedor");
        sheet.setColumnWidth(1, 28 * 256);
        rowTitle.createCell(2).setCellValue("UF do Registro");
        sheet.setColumnWidth(2, 28 * 256);
        rowTitle.createCell(3).setCellValue("Contrato");
        sheet.setColumnWidth(3, 21 * 256);
        rowTitle.createCell(4).setCellValue("Chassi");
        sheet.setColumnWidth(4, 23 * 256);
        rowTitle.createCell(5).setCellValue("Qtd");
        sheet.setColumnWidth(5, 20 * 256);
        rowTitle.createCell(6).setCellValue("Valor Unitário");
        sheet.setColumnWidth(6, 16 * 256);
        rowTitle.createCell(7).setCellValue("Valor Total");
        sheet.setColumnWidth(7, 16 * 256);
        rowTitle.createCell(8).setCellValue("Valor DETRAN");
        sheet.setColumnWidth(8, 16 * 256);
        rowTitle.createCell(9).setCellValue("Valor Credenciada");
        sheet.setColumnWidth(9, 23 * 256);
        rowTitle.createCell(10).setCellValue("Número do Registro Eletrônico");
        sheet.setColumnWidth(10, 32 * 256);
        rowTitle.createCell(11).setCellValue("Gravame");
        sheet.setColumnWidth(11, 16 * 256);
        rowTitle.createCell(12).setCellValue("RENAVAM");
        sheet.setColumnWidth(12, 17 * 256);
        rowTitle.createCell(13).setCellValue("Placa");
        sheet.setColumnWidth(13, 11 * 256);
        rowTitle.createCell(14).setCellValue("Ano Fabricação");
        sheet.setColumnWidth(14, 19 * 256);
        rowTitle.createCell(15).setCellValue("Ano Modelo");
        sheet.setColumnWidth(15, 19 * 256);
        rowTitle.createCell(16).setCellValue("Marca");
        sheet.setColumnWidth(16, 19 * 256);
        rowTitle.createCell(17).setCellValue("Data do Contrato");
        sheet.setColumnWidth(17, 19 * 256);
        rowTitle.createCell(18).setCellValue("Situação");
        sheet.setColumnWidth(18, 14 * 256);
        rowTitle.createCell(19).setCellValue("Tipo de Restrição");
        sheet.setColumnWidth(19, 23 * 256);
        rowTitle.createCell(20).setCellValue("Credor");
        sheet.setColumnWidth(20, 21 * 256);
        rowTitle.createCell(21).setCellValue("CPF/CNPJ do Devedor");
        sheet.setColumnWidth(21, 28 * 256);
        rowTitle.createCell(22).setCellValue("Tipo de Pessoa");
        sheet.setColumnWidth(22, 18 * 256);
        rowTitle.createCell(23).setCellValue("Nome do Devedor");
        sheet.setColumnWidth(23, 45 * 256);
        rowTitle.createCell(24).setCellValue("Data do Cadastro");
        sheet.setColumnWidth(24, 23 * 256);
        rowTitle.createCell(25).setCellValue("Anexo");
        sheet.setColumnWidth(25, 12 * 256);
        rowTitle.createCell(26).setCellValue("Tipo de Veículo");
        sheet.setColumnWidth(26, 26 * 256);
        rowTitle.createCell(27).setCellValue("Tipo de Veículo");
        sheet.setColumnWidth(27, 26 * 256);
        for (int i = 0; i < 28; i++) {
            rowTitle.getCell(i).setCellStyle(style);
        }

        List<Veiculo> veiculos = contratoService.findByCobranca(cobranca);
        Set<Uf> estadosRegistrados = veiculos.stream().map(veiculo -> veiculo.getContrato().getUfRegistro()).collect(Collectors.toSet());

        Map<Uf, BigDecimal> valorDescontoPorEstado = new HashMap<>();
        for (Uf estado : estadosRegistrados) {
            valorDescontoPorEstado.put(estado, valorDescontoPorCobranca(cobranca, estado));
        }

        //mostra todos os veiculos do contrato
        Contrato c = null;
        Boolean unificada = veiculos.stream().map(v -> v.getContrato().getUfRegistro()).collect(Collectors.toSet()).size() > 1;
        List<Credenciamento> credenciamentos = credenciamentoService.getCredenciamentos(cobranca);
        Set<String> idRegistro = new HashSet<>();

        for (Veiculo v : veiculos) {

            c = v.getContrato();
            Credenciamento credenciamento = filtrarCredenciamentoAtivoPorUf(c.getUfRegistro(), credenciamentos);
            Row row = sheet.createRow(rowCount++);
            Cell cell = row.createCell(0);
            cell.setCellType(CellType.STRING);
            cell.setCellValue(c.getDataConclusaoDETRAN() == null ? "-" : DateFormatUtils.format(c.getDataConclusaoDETRAN(), "dd/MM/yyyy HH:mm:ss"));
            row.createCell(1).setCellValue("06.032.507/0001-03");
            row.createCell(2).setCellValue(c.getUfRegistro().toString());
            row.createCell(3).setCellValue(c.getNumeroContrato());
            row.createCell(4).setCellValue(v.getNumeroChassi());
            row.createCell(5).setCellValue(1);
            row.createCell(10).setCellValue(String.valueOf(c.getNumeroRegistroEletronico()));
            row.createCell(11).setCellValue(v.getNumeroGravame());
            row.createCell(12).setCellValue(v.getNumeroRenavam());
            row.createCell(13).setCellValue(v.getPlaca());
            row.createCell(14).setCellValue(v.getAnoFabricacao() == null ? "" : v.getAnoFabricacao().toString());
            row.createCell(15).setCellValue(v.getAnoModelo() == null ? "" : v.getAnoModelo().toString());
            row.createCell(16).setCellValue(v.getMarca() != null ? v.getMarca().getDescricao() : "");
            row.createCell(17).setCellValue(DateFormatUtils.format(c.getDataContrato(), "dd/MM/yyyy"));
            row.createCell(18).setCellValue(c.getSituacao().getDescricao());
            row.createCell(19).setCellValue(c.getTipoRestricao() != null ? c.getTipoRestricao().getDescricao() : "");
            row.createCell(20).setCellValue(c.getFinanceira() != null ? c.getFinanceira().getDocumento() : "");
            row.createCell(21).setCellValue(c.getCpfCnpjDevedorFinanciado());
            row.createCell(22).setCellValue(c.getCpfCnpjDevedorFinanciado().length() > 12 ? "PJ" : "PF");
            row.createCell(23).setCellValue(c.getNomeDevedorFinanciado());
            row.createCell(24).setCellValue(c.getDataCadastro() == null ? "-" : DateFormatUtils.format(c.getDataCadastro(), "dd/MM/yyyy HH:mm:ss"));
            row.createCell(25).setCellValue(PlaceconUtil.isListaVaziaOuNula(c.getAnexos()) ? "N" : "S");
            row.createCell(26).setCellValue(c.getDataConclusaoDETRAN() == null ? "-" : DateFormatUtils.format(c.getDataConclusaoDETRAN(), "dd/MM/yyyy HH:mm:ss"));
            row.createCell(27).setCellValue(credenciamento.getTipoCobranca() == TipoCobranca.CONTRATO ? "Contrato" : "Veículo");

            double valorTotalRegistro = 0;
            double valorDETRAN = 0;
            double valorCredenciada = 0;
            double valorDesconto = 0;
            double valorIntegraMais = 0;
            if (c.getCobranca() != null) {
                valorIntegraMais = c.getCobranca().getValorIntegraMais().doubleValue() / c.getCobranca().getQuantidadeRegistros();
                valorDesconto = valorDescontoPorEstado.get(c.getUfRegistro()).doubleValue();

                if (c.getUfRegistro() == Uf.CE) {
                    if (TipoVeiculo.getQuatroRodas().contains(v.getTipo())) {
                        valorCredenciada = credenciamento.getPrecosCompostos().stream().filter(precoComposto -> (precoComposto.getValorParametro().equals("4"))).findFirst().get().getValorCredenciada().doubleValue();
                        valorDETRAN = credenciamento.getPrecosCompostos().stream().filter(precoComposto -> (precoComposto.getValorParametro().equals("4"))).findFirst().get().getValorDETRAN().doubleValue();

                    } else {
                        valorCredenciada = credenciamento.getPrecosCompostos().stream().filter(precoComposto -> (precoComposto.getValorParametro().equals("2"))).findFirst().get().getValorCredenciada().doubleValue();
                        valorDETRAN = credenciamento.getPrecosCompostos().stream().filter(precoComposto -> (precoComposto.getValorParametro().equals("2"))).findFirst().get().getValorDETRAN().doubleValue();
                    }
                    valorCredenciada = valorCredenciada + valorIntegraMais;
                } else if (unificada && c.getUfRegistro() != Uf.CE) {
                    valorCredenciada = credenciamento.getValorCredenciada().doubleValue();
                    if (c.getTipoContrato().equals(TipoContrato.CONTRATO_PRINCIPAL)) {
                        valorDETRAN = credenciamento.getValorDETRAN().doubleValue();
                    } else {
                        valorDETRAN = credenciamento.getValorAditivo().doubleValue();
                    }
                } else {
                    valorCredenciada = (c.getCobranca().getValorCredenciada().doubleValue()) / c.getCobranca().getQuantidadeRegistros();
                    if (c.getTipoContrato().equals(TipoContrato.CONTRATO_PRINCIPAL)) {
                        valorDETRAN = c.getCobranca().getValorDetranPrincipal().doubleValue() / c.getCobranca().getQuantidadePrincipal();
                    } else {
                        valorDETRAN = c.getCobranca().getValorDetranAditivo().doubleValue() / c.getCobranca().getQuantidadeAditivo();
                    }
                }
                double valorCformatado = valorCredenciada - valorDesconto;
                double valorDetran = valorDETRAN;

                if (idRegistro.contains(c.getNumeroRegistroEletronico().toString())) {
                    valorCformatado = 0d;
                    valorDetran = 0d;
                }
                valorTotalRegistro = (valorDETRAN + valorCredenciada) - valorDesconto;
                row.createCell(6).setCellValue(valorTotalRegistro);
                row.createCell(7).setCellValue(valorTotalRegistro);
                row.createCell(8).setCellValue(valorDetran);
                row.createCell(9).setCellValue(valorCformatado);
                idRegistro.add(c.getNumeroRegistroEletronico().toString());
            }
        }
    }

    public POIFSFileSystem encriptografaArquivo(String path, String senha) throws IOException, GeneralSecurityException, InvalidFormatException {

        POIFSFileSystem poifsFileSystem = new POIFSFileSystem();
        EncryptionInfo info = new EncryptionInfo(EncryptionMode.agile);
        Encryptor enc = info.getEncryptor();
        enc.confirmPassword(senha);
        OPCPackage opc = OPCPackage.open(new File(path), PackageAccess.READ_WRITE);
        OutputStream os = enc.getDataStream(poifsFileSystem);
        opc.save(os);
        opc.close();
        return poifsFileSystem;
    }

    private String stringTimeStampToDate(String time) {
        Timestamp timestamp = new Timestamp(Long.valueOf(time));
        return PlaceconUtil.formataData(new Date(timestamp.getTime()), "dd/MM/yyyy");
    }

    public void createTableRelatorioDetran(Sheet sheet, CellStyle style, int rowCount, List<Contrato> contratos) {

        Row rowTitle = sheet.createRow(rowCount++);
        rowTitle.createCell(0).setCellValue("Numero Contrato");
        sheet.setColumnWidth(0, 21 * 256);
        rowTitle.createCell(1).setCellValue("Data Registro");
        sheet.setColumnWidth(1, 21 * 256);
        rowTitle.createCell(2).setCellValue("Data Contrato");
        sheet.setColumnWidth(2, 16 * 256);
        rowTitle.createCell(3).setCellValue("Chassi");
        sheet.setColumnWidth(3, 26 * 256);
        rowTitle.createCell(4).setCellValue("Gravame");

        for (Contrato r : contratos) {

            for (Veiculo v : r.getVeiculos()) {
                Row row = sheet.createRow(rowCount++);
                Cell cell = row.createCell(0);
                cell.setCellType(CellType.STRING);
                cell.setCellValue(String.valueOf(r.getNumeroContrato()));
                row.createCell(1).setCellValue(PlaceconUtil.formatarDataPadrao(r.getDataConclusaoDETRAN()));
                row.createCell(2).setCellValue(PlaceconUtil.formatarDataPadrao(r.getDataContrato()));
                row.createCell(3).setCellValue(v.getNumeroChassi());
                row.createCell(4).setCellValue(v.getNumeroGravame());
            }
        }

    }

}
