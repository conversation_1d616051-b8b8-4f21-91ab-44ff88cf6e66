package com.registrocontrato.registro.service.cobranca;

import com.registrocontrato.infra.entity.SimNao;
import com.registrocontrato.infra.entity.Uf;
import com.registrocontrato.infra.exception.ServiceException;
import com.registrocontrato.infra.util.PlaceconUtil;
import com.registrocontrato.registro.entity.Cobranca;
import com.registrocontrato.registro.service.cobranca.boleto.BoletoDeCobrancaStrategy;
import com.registrocontrato.registro.service.cobranca.boleto.BoletoDetran;
import com.registrocontrato.registro.service.cobranca.boleto.BoletoDetranInterface;
import com.registrocontrato.registro.service.cobranca.boleto.BoletoResponse;
import com.registrocontrato.registro.service.cobranca.notareembolso.NotaDeReembolsoStrategy;
import com.registrocontrato.registro.service.util.NomeadorBoletosUtil;
import org.apache.commons.io.FileUtils;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.glassfish.jersey.client.authentication.HttpAuthenticationFeature;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.ws.rs.client.ClientBuilder;
import javax.ws.rs.client.Entity;
import javax.ws.rs.core.Form;
import javax.ws.rs.core.Response;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Stream;

import static com.registrocontrato.infra.util.PlaceconUtil.*;
import static javax.ws.rs.core.MediaType.WILDCARD;
import static javax.ws.rs.core.Response.Status.CREATED;
import static org.apache.commons.lang3.StringUtils.isBlank;


@Service
public class BoletoService {

    protected final Log logger = LogFactory.getLog(getClass());
    private final String url;
    private final String token;
    private final String tokenItau;
    private final String versao;
    private final String fileDir;
    private final String fileNfDir;
    private final String titulo;
    private final String instrucao1;
    private final String instrucao2;
    private final String instrucao3;
    private final List<NotaDeReembolsoStrategy> notaDeReembolsoStrategy;
    private final List<BoletoDetranInterface> boletoDetranInterface;
    private final List<String> cnpjsBancoBrasil = Arrays.asList("06043050000132", "31546476000156", "00000000000191");

    public BoletoService(
            @Value("${boleto.url:null}") String url,
            @Value("${boleto.token:null}") String token,
            @Value("${boleto.token.conta.itau:null}") String tokenItau,
            @Value("${boleto.versao:null}") String versao,
            @Value("${file-boleto.dir:null}") String fileDir,
            @Value("${file-nf.dir:null}") String fileNfDir,
            @Value("${boleto.titulo:null}") String titulo,
            @Value("${boleto.instrucao1:null}") String instrucao1,
            @Value("${boleto.instrucao2:null}") String instrucao2,
            @Value("${boleto.instrucao3:null}") String instrucao3,
            List<NotaDeReembolsoStrategy> notaDeReembolsoStrategy,
            List<BoletoDetranInterface> boletoDetranInterface) {
        this.url = url;
        this.token = token;
        this.tokenItau = tokenItau;
        this.versao = versao;
        this.fileDir = fileDir;
        this.fileNfDir = fileNfDir;
        this.titulo = titulo;
        this.instrucao1 = instrucao1;
        this.instrucao2 = instrucao2;
        this.instrucao3 = instrucao3;
        this.notaDeReembolsoStrategy = notaDeReembolsoStrategy;
        this.boletoDetranInterface = boletoDetranInterface;
    }

    public BoletoResponse criarBoletoPlace(Cobranca cobranca) throws Exception {
        String numeroDocumento = NomeadorBoletosUtil.gerarNumeroBoletoCredenciada(cobranca.getId());
        BigDecimal valorCobranca = cobranca.getValorCobranca();
        valorCobranca = valorCobranca.subtract(cobranca.getValorDetran());
        logger.info("Valor cobrança - boleto Place: " + valorCobranca);

        if (isMaiorQueZero(valorCobranca) && (cobranca.getFinanceira().getCobrancaSemBoleto() == null || cobranca.getFinanceira().getCobrancaSemBoleto().equals(SimNao.N)))
            return gerarBoleto(cobranca, valorCobranca, numeroDocumento, cobranca.getDataVencimentoBoleto());
        return null;
    }

    public BoletoResponse criarBoletoSng(Cobranca cobranca) throws Exception {
        String numeroDocumento = NomeadorBoletosUtil.gerarNumeroBoletoSng(cobranca.getId());
        BigDecimal valorCobranca = cobranca.getValorCobrancaSng();

        if (isMaiorQueZero(valorCobranca))
            return gerarBoleto(cobranca, valorCobranca, numeroDocumento, cobranca.getDataVencimentoBoleto());
        return null;
    }

    public BoletoResponse criarBoletoReembolso(Cobranca cobranca) throws Exception {
        String numDocumento = NomeadorBoletosUtil.gerarNumeroBoletoReembolso(cobranca.getDataInicio(), cobranca.getId());
        criarNotaDeReembolso(cobranca, numDocumento);

        if (cobranca.isUnificada()) {
            if (isMaiorQueZero(cobranca.getValorDetranReembolsavel())) {
                return gerarBoleto(cobranca, cobranca.getValorDetranReembolsavel(), numDocumento, cobranca.getDataVencimentoReembolsoBoleto());
            }
            return null;
        }
        if (isMaiorQueZero(cobranca.getValorDetran()))
            return gerarBoleto(cobranca, cobranca.getValorDetran(), numDocumento, cobranca.getDataVencimentoReembolsoBoleto());
        return null;
    }

    private Boolean isMaiorQueZero(BigDecimal valor) {
        return valor.compareTo(BigDecimal.ZERO) > 0;
    }

    public void criarNotaDeReembolso(Cobranca cobranca, String numeroDocumento) throws ServiceException {

        Optional<NotaDeReembolsoStrategy> notaDeReembolso = filtrarInstanciaNotaDeReembolsoPorEstado(cobranca);
        NotaDeReembolsoStrategy notaDeReembolsoStrategy =
                notaDeReembolso
                        .orElseThrow(() -> new ServiceException("Não foi encontrado gerador de NR para o estado"));
        notaDeReembolsoStrategy.emitirNotaDeReembolso(cobranca, numeroDocumento);

    }

    private Optional<NotaDeReembolsoStrategy> filtrarInstanciaNotaDeReembolsoPorEstado(Cobranca cobranca) {
        Stream<NotaDeReembolsoStrategy> strategy = notaDeReembolsoStrategy.stream()
                .filter(c -> c.getUf().equals(cobranca.getEstado()));

        return strategy
                .filter(c -> {
                    if (isBancoDoBrasil(cobranca.getFinanceira().getDocumento()))
                        return strategyBB(c.getCnpjFinanceiras(), cobranca.getFinanceira().getDocumento());
                    return c.getCnpjFinanceiras().isEmpty();
                })
                .findFirst();
    }

    private boolean strategyBB(List<String> cnpjs, String docValido) {
        Optional<String> busca = cnpjs.stream()
                .filter(s -> s.equals(docValido))
                .findAny();
        return busca.isPresent();
    }

    private boolean isBancoDoBrasil(String cnpj) {
        Optional<String> busca = cnpjsBancoBrasil.stream()
                .filter(s -> s.equals(cnpj))
                .findFirst();
        return busca.isPresent();
    }

    /**
     * retorna a data de vencimento dos boletos que nao sao gerados pela place, mas pelo proprio Detran
     *
     * @param cobranca
     * @return1
     */
    public Date criarBoletoDetran(Cobranca cobranca, BoletoDetran boletoDetran) {
        String numDocumento = NomeadorBoletosUtil.getNumeroDocumentoBoletoDetran(cobranca.getDataInicio(), cobranca.getId());
        if (cobranca.isUnificada())
            numDocumento += cobranca.getEstado();
        Optional<BoletoDetranInterface> boleto = filtrarInstanciaBoletoDetranPorEstado(cobranca.getEstado());
        BoletoDetranInterface boletoDetranInterface =
                boleto
                        .orElseThrow(() -> new ServiceException(String.format("Não foi encontrado Gerador de Boleto para o estado %s", cobranca.getEstado())));
        return boletoDetranInterface.emitirBoletoDetran(numDocumento, cobranca, boletoDetran);
    }

    private Optional<BoletoDetranInterface> filtrarInstanciaBoletoDetranPorEstado(Uf uf) {
        return boletoDetranInterface.stream()
                .filter(c -> c.getUf().equals(uf))
                .findFirst();
    }

    public BoletoResponse gerarBoleto(Cobranca cobranca, BigDecimal valorCobranca, String numeroDocumento, Date dataVencimento) throws IOException, ServiceException {

        Form formData = getFormDataBoleto(cobranca, valorCobranca, numeroDocumento, dataVencimento);
        Response response = ClientBuilder.newClient().target(String.format("%s/%s", url, versao)).path("/boletos")
                .register(HttpAuthenticationFeature.basic(token, "token")).request(WILDCARD)
                .post(Entity.form(formData));

        String tokenBoleto = response.getHeaderString("X-BoletoCloud-Token");
        InputStream file;

        if (Objects.isNull(tokenBoleto)) {
            logger.error("Não foi possível gerar o token do boleto");
            throw new ServiceException("Não foi possível gerar o boleto.");
        }

        if (response.getStatus() == CREATED.getStatusCode()) {
            logger.info("Novo Boleto criado " + CREATED.getStatusCode());
            file = response.readEntity(InputStream.class);
        } else {
            String retorno = response.readEntity(String.class);
            if (retorno.contains(tokenBoleto)) {

                logger.info("Buscando Boleto existente " + tokenBoleto);

                response = ClientBuilder.newClient().target(String.format("%s/%s", url, versao)).path("/boletos/" + tokenBoleto)
                        .register(HttpAuthenticationFeature.basic(token, "token")).request(WILDCARD)
                        .get();
                file = response.readEntity(InputStream.class);
            } else {
                throw new ServiceException(retorno);
            }
        }

        logger.info("Vai salvar: " + fileDir + numeroDocumento + ".pdf");

        File targetFile = new File(fileDir, numeroDocumento + ".pdf");
        FileUtils.copyInputStreamToFile(file, targetFile);
        return new BoletoResponse(tokenBoleto, numeroDocumento);
    }

    private Form getFormDataBoleto(Cobranca cobranca, BigDecimal valorCobranca, String numeroDocumento, Date dataVencimento) {

        String sequencial = null;

        if (numeroDocumento.startsWith("0")) {
            sequencial = "9" + numeroDocumento.substring(4);
        }else{
            sequencial = numeroDocumento;
        }

        logger.info(sequencial);


        Form formData = new Form();
        boolean possuiMatriz = cobranca.getFinanceira().getPossuiMatriz() == SimNao.S;
        formData.param("boleto.conta.token", tokenItau);
        formData.param("boleto.emissao", getDataHojeFormatada(DATAYYYYMMDD));
        formData.param("boleto.vencimento", getDataFormatada(dataVencimento, DATAYYYYMMDD));
        formData.param("boleto.documento", numeroDocumento);
        formData.param("boleto.sequencial", sequencial);
        formData.param("boleto.titulo", titulo);
        formData.param("boleto.valor", valorCobranca.toString());
        formData.param("boleto.pagador.nome", !possuiMatriz ? cobranca.getFinanceira().getNome() : cobranca.getFinanceira().getNomeMatriz());
        formData.param("boleto.pagador.cprf", !possuiMatriz ? PlaceconUtil.formatarCNPJ(cobranca.getFinanceira().getDocumento()) : PlaceconUtil.formatarCNPJ(cobranca.getFinanceira().getDocumentoMatriz()));
        formData.param("boleto.pagador.endereco.cep", !possuiMatriz ? PlaceconUtil.getCepFormatado(cobranca.getFinanceira().getCep()) : PlaceconUtil.getCepFormatado(cobranca.getFinanceira().getCepMatriz()));
        formData.param("boleto.pagador.endereco.uf", !possuiMatriz ? cobranca.getFinanceira().getUfEndereco().toString() : cobranca.getFinanceira().getUfEnderecoMatriz().toString());
        formData.param("boleto.pagador.endereco.localidade", !possuiMatriz ? cobranca.getFinanceira().getMunicipio().getDescricao() : cobranca.getFinanceira().getMunicipioMatriz().getDescricao());
        formData.param("boleto.pagador.endereco.bairro", !possuiMatriz ? cobranca.getFinanceira().getBairro() : cobranca.getFinanceira().getBairroMatriz());
        formData.param("boleto.pagador.endereco.logradouro", !possuiMatriz ? cobranca.getFinanceira().getEndereco() : cobranca.getFinanceira().getEnderecoMatriz());
        formData.param("boleto.pagador.endereco.numero", !possuiMatriz ? isBlank(cobranca.getFinanceira().getNumero()) ? "S/N" : cobranca.getFinanceira().getNumero() : isBlank(cobranca.getFinanceira().getNumeroMatriz()) ? "S/N" : cobranca.getFinanceira().getNumeroMatriz());
        formData.param("boleto.pagador.endereco.complemento", !possuiMatriz ? cobranca.getFinanceira().getComplemento() : cobranca.getFinanceira().getComplementoMatriz());
        if (!isBlank(instrucao1)) {
            formData.param("boleto.instrucao", instrucao1);
        }
        if (!isBlank(instrucao2)) {
            formData.param("boleto.instrucao", instrucao2);
        }
        if (!isBlank(instrucao3)) {
            formData.param("boleto.instrucao", instrucao3);
        }
        return formData;
    }


}
