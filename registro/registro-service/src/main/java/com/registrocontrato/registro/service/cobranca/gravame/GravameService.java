package com.registrocontrato.registro.service.cobranca.gravame;

import com.registrocontrato.infra.entity.Financeira;
import com.registrocontrato.infra.entity.Uf;
import com.registrocontrato.infra.service.BaseService;
import com.registrocontrato.infra.util.PlaceconUtil;
import com.registrocontrato.registro.entity.Cobranca;
import com.registrocontrato.registro.dto.BilhetagemGravameDTO;
import com.registrocontrato.registro.entity.BilhetagemGravame;
import com.registrocontrato.registro.repository.GravameRepository;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Example;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.data.repository.PagingAndSortingRepository;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Optional;

@Service
public class GravameService extends BaseService<BilhetagemGravame, BilhetagemGravameDTO> {

    @Autowired
    private GravameRepository gravameRepository;

    @Override
    public Page<BilhetagemGravame> findAll(int first, int pageSize, BilhetagemGravameDTO filter) {
        BilhetagemGravame entity = new BilhetagemGravame();
        BeanUtils.copyProperties(filter, entity);
        return gravameRepository.findAll(Example.of(entity), new PageRequest(first / pageSize, pageSize, new Sort(Sort.Direction.ASC, "dataInicio", "dataFim")));
    }

    public BilhetagemGravame findByAtivo(Uf uf, Date date, Financeira financeira) {
        return gravameRepository.findByAtivo(uf, date, financeira);
    }

    public List<BilhetagemGravame> getBilhetagemGravames(Cobranca cobranca) {
        return gravameRepository.findAllByDataInicioLessThanEqualAndDataFimGreaterThanEqual(cobranca.getDataGeracao(), cobranca.getDataFim());
    }

    public Optional<BilhetagemGravame> findByUfAndDataInicioLessThanEqualAndDataFimGreaterThanEqual(Cobranca cobranca) {
        Date dataInicio = PlaceconUtil.minDateTime(cobranca.getDataInicio());
        Date dataFim = PlaceconUtil.maxDateTime(cobranca.getDataFim());

        return gravameRepository
                .findGravameByUfAndPeriodo(cobranca.getEstado(), dataInicio, dataFim, cobranca.getFinanceira()).stream().findFirst();
    }

    @Override
    protected PagingAndSortingRepository<BilhetagemGravame, Long> getRepository() {
        return gravameRepository;
    }
}
