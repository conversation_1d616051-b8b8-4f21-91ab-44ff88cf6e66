package com.registrocontrato.registro.service.cobranca.boleto;

import com.registrocontrato.infra.entity.SimNao;
import com.registrocontrato.infra.entity.SituacaoFinanceiraEstado;
import com.registrocontrato.registro.entity.Cobranca;
import com.registrocontrato.registro.service.cobranca.BoletoService;
import com.registrocontrato.seguranca.service.FinanceiraService;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

public abstract class CobrancaComBoletoDefault implements BoletoDeCobrancaStrategy {

    protected final Log logger = LogFactory.getLog(getClass());

    protected final BoletoService boletoService;

    protected final FinanceiraService financeiraService;

    public CobrancaComBoletoDefault(BoletoService boletoService, FinanceiraService financeiraService) {
        this.boletoService = boletoService;
        this.financeiraService = financeiraService;
    }

    @Override
    public void emitirBoleto(Cobranca cobranca) throws Exception {

        BoletoResponse retornoBoletoPlace = boletoService.criarBoletoPlace(cobranca);
        BoletoResponse retornoBoletoSng = boletoService.criarBoletoSng(cobranca);
        atualizarCobranca(cobranca, retornoBoletoPlace, retornoBoletoSng);
        processarBoletoDeReembolso(cobranca, retornoBoletoPlace, retornoBoletoSng);

    }

    private void processarBoletoDeReembolso(Cobranca cobranca, BoletoResponse retornoBoletoPlace, BoletoResponse retornoBoletoSng) throws Exception {
        if (cobrancaPossuiReembolsoDoBoletoDetran(cobranca)) {
            BoletoResponse retornoBoletoReembolso = boletoService.criarBoletoReembolso(cobranca);
            atualizarCobranca(cobranca, retornoBoletoPlace, retornoBoletoReembolso, retornoBoletoSng);
        }
    }

    protected Boolean cobrancaPossuiReembolsoDoBoletoDetran(Cobranca cobranca) {
        return getSituacaoFinanceiraEstado(cobranca).getReembolsoLinhaDigitavel() == SimNao.S;
    }

    protected SituacaoFinanceiraEstado getSituacaoFinanceiraEstado(Cobranca cobranca) {
        return financeiraService.findSituacaoFinanceiraEstado(getUf(), cobranca.getFinanceira());
    }

}
