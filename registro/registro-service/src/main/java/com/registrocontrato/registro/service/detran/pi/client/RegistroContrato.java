//
// Este arquivo foi gerado pela Arquitetura JavaTM para Implementação de Referência (JAXB) de Bind XML, v2.2.11 
// Consulte <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Todas as modificações neste arquivo serão perdidas após a recompilação do esquema de origem. 
// Gerado em: 2019.07.26 às 03:43:09 PM BRT 
//


package com.registrocontrato.registro.service.detran.pi.client;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>Classe Java de registroContrato complex type.
 * 
 * <p>O seguinte fragmento do esquema especifica o conteúdo esperado contido dentro desta classe.
 * 
 * <pre>
 * &lt;complexType name="registroContrato"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="anoFabricacao" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
 *         &lt;element name="anoModelo" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
 *         &lt;element name="bairroImovelDevedor" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
 *         &lt;element name="cepImovelDevedor" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
 *         &lt;element name="chassi" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
 *         &lt;element name="codigoCor" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
 *         &lt;element name="codigoMunicipioDevedor" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
 *         &lt;element name="codigoMunicipioLiberacaoCredito" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
 *         &lt;element name="codigoTipoVeiculo" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
 *         &lt;element name="complementoImovelDevedor" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
 *         &lt;element name="correcaoMonetaria" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
 *         &lt;element name="cpfCnpjCredor" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
 *         &lt;element name="cpfCnpjDevedor" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
 *         &lt;element name="credenciada" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
 *         &lt;element name="dataCadastro" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
 *         &lt;element name="dataContrato" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
 *         &lt;element name="dataLiberacaoCredito" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
 *         &lt;element name="dataVencimentoPrimeiraParcela" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
 *         &lt;element name="dataVencimentoUltimaParcela" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
 *         &lt;element name="dddImovelDevedor" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
 *         &lt;element name="emailDevedor" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
 *         &lt;element name="flagTransacao" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
 *         &lt;element name="identificaoRemarcacao" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
 *         &lt;element name="indicadorComissao" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
 *         &lt;element name="indicadorPenalidade" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
 *         &lt;element name="indicadorTaxaMora" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
 *         &lt;element name="indicadorTaxaMulta" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
 *         &lt;element name="indices" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
 *         &lt;element name="logradouroDevedor" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
 *         &lt;element name="marcaModelo" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
 *         &lt;element name="nomeCredor" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
 *         &lt;element name="nomeDevedor" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
 *         &lt;element name="numeroContrato" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
 *         &lt;element name="numeroCotaConsorcio" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
 *         &lt;element name="numeroGravame" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
 *         &lt;element name="numeroGrupoConsorcio" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
 *         &lt;element name="numeroImovelDevedor" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
 *         &lt;element name="numeroRegistro" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
 *         &lt;element name="numeroRegistroDetran" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
 *         &lt;element name="numeroTelefoneDevedor" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
 *         &lt;element name="penalidade" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
 *         &lt;element name="placa" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
 *         &lt;element name="quantidadeParcela" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
 *         &lt;element name="renavam" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
 *         &lt;element name="taxaJuroAno" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
 *         &lt;element name="taxaJuroMes" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
 *         &lt;element name="tipoDocumentoCredor" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
 *         &lt;element name="tipoDocumentoDevedor" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
 *         &lt;element name="tipoGravame" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
 *         &lt;element name="tipoRegistro" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
 *         &lt;element name="ufImovelDevedor" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
 *         &lt;element name="ufLiberacaoCredito" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
 *         &lt;element name="ufLicenciamento" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
 *         &lt;element name="ufPlaca" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
 *         &lt;element name="valorComissao" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
 *         &lt;element name="valorIof" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
 *         &lt;element name="valorParcela" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
 *         &lt;element name="valorTaxaMora" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
 *         &lt;element name="valorTaxaMulta" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
 *         &lt;element name="valorTotalFinanciamento" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "registroContrato", propOrder = {
    "anoFabricacao",
    "anoModelo",
    "bairroImovelDevedor",
    "cepImovelDevedor",
    "chassi",
    "codigoCor",
    "codigoMunicipioDevedor",
    "codigoMunicipioLiberacaoCredito",
    "codigoTipoVeiculo",
    "complementoImovelDevedor",
    "correcaoMonetaria",
    "cpfCnpjCredor",
    "cpfCnpjDevedor",
    "credenciada",
    "dataCadastro",
    "dataContrato",
    "dataLiberacaoCredito",
    "dataVencimentoPrimeiraParcela",
    "dataVencimentoUltimaParcela",
    "dddImovelDevedor",
    "emailDevedor",
    "flagTransacao",
    "identificaoRemarcacao",
    "indicadorComissao",
    "indicadorPenalidade",
    "indicadorTaxaMora",
    "indicadorTaxaMulta",
    "indices",
    "logradouroDevedor",
    "marcaModelo",
    "nomeCredor",
    "nomeDevedor",
    "numeroContrato",
    "numeroCotaConsorcio",
    "numeroGravame",
    "numeroGrupoConsorcio",
    "numeroImovelDevedor",
    "numeroRegistro",
    "numeroRegistroDetran",
    "numeroTelefoneDevedor",
    "penalidade",
    "placa",
    "quantidadeParcela",
    "renavam",
    "taxaJuroAno",
    "taxaJuroMes",
    "tipoDocumentoCredor",
    "tipoDocumentoDevedor",
    "tipoGravame",
    "tipoRegistro",
    "ufImovelDevedor",
    "ufLiberacaoCredito",
    "ufLicenciamento",
    "ufPlaca",
    "valorComissao",
    "valorIof",
    "valorParcela",
    "valorTaxaMora",
    "valorTaxaMulta",
    "valorTotalFinanciamento",
    "numAditivoContrato",
    "numDataAditivo"
})
public class RegistroContrato {

    protected String anoFabricacao;
    protected String anoModelo;
    protected String bairroImovelDevedor;
    protected String cepImovelDevedor;
    protected String chassi;
    protected String codigoCor;
    protected String codigoMunicipioDevedor;
    protected String codigoMunicipioLiberacaoCredito;
    protected String codigoTipoVeiculo;
    protected String complementoImovelDevedor;
    protected String correcaoMonetaria;
    protected String cpfCnpjCredor;
    protected String cpfCnpjDevedor;
    protected String credenciada;
    protected String dataCadastro;
    protected String dataContrato;
    protected String dataLiberacaoCredito;
    protected String dataVencimentoPrimeiraParcela;
    protected String dataVencimentoUltimaParcela;
    protected String dddImovelDevedor;
    protected String emailDevedor;
    protected String flagTransacao;
    protected String identificaoRemarcacao;
    protected String indicadorComissao;
    protected String indicadorPenalidade;
    protected String indicadorTaxaMora;
    protected String indicadorTaxaMulta;
    protected String indices;
    protected String logradouroDevedor;
    protected String marcaModelo;
    protected String nomeCredor;
    protected String nomeDevedor;
    protected String numeroContrato;
    protected String numeroCotaConsorcio;
    protected String numeroGravame;
    protected String numeroGrupoConsorcio;
    protected String numeroImovelDevedor;
    protected String numeroRegistro;
    protected String numeroRegistroDetran;
    protected String numeroTelefoneDevedor;
    protected String penalidade;
    protected String placa;
    protected String quantidadeParcela;
    protected String renavam;
    protected String taxaJuroAno;
    protected String taxaJuroMes;
    protected String tipoDocumentoCredor;
    protected String tipoDocumentoDevedor;
    protected String tipoGravame;
    protected String tipoRegistro;
    protected String ufImovelDevedor;
    protected String ufLiberacaoCredito;
    protected String ufLicenciamento;
    protected String ufPlaca;
    protected String valorComissao;
    protected String valorIof;
    protected String valorParcela;
    protected String valorTaxaMora;
    protected String valorTaxaMulta;
    protected String valorTotalFinanciamento;
    protected String numAditivoContrato;
    protected String numDataAditivo;

    /**
     * Obtém o valor da propriedade anoFabricacao.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getAnoFabricacao() {
        return anoFabricacao;
    }

    /**
     * Define o valor da propriedade anoFabricacao.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setAnoFabricacao(String value) {
        this.anoFabricacao = value;
    }

    /**
     * Obtém o valor da propriedade anoModelo.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getAnoModelo() {
        return anoModelo;
    }

    /**
     * Define o valor da propriedade anoModelo.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setAnoModelo(String value) {
        this.anoModelo = value;
    }

    /**
     * Obtém o valor da propriedade bairroImovelDevedor.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getBairroImovelDevedor() {
        return bairroImovelDevedor;
    }

    /**
     * Define o valor da propriedade bairroImovelDevedor.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setBairroImovelDevedor(String value) {
        this.bairroImovelDevedor = value;
    }

    /**
     * Obtém o valor da propriedade cepImovelDevedor.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCepImovelDevedor() {
        return cepImovelDevedor;
    }

    /**
     * Define o valor da propriedade cepImovelDevedor.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCepImovelDevedor(String value) {
        this.cepImovelDevedor = value;
    }

    /**
     * Obtém o valor da propriedade chassi.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getChassi() {
        return chassi;
    }

    /**
     * Define o valor da propriedade chassi.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setChassi(String value) {
        this.chassi = value;
    }

    /**
     * Obtém o valor da propriedade codigoCor.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCodigoCor() {
        return codigoCor;
    }

    /**
     * Define o valor da propriedade codigoCor.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCodigoCor(String value) {
        this.codigoCor = value;
    }

    /**
     * Obtém o valor da propriedade codigoMunicipioDevedor.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCodigoMunicipioDevedor() {
        return codigoMunicipioDevedor;
    }

    /**
     * Define o valor da propriedade codigoMunicipioDevedor.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCodigoMunicipioDevedor(String value) {
        this.codigoMunicipioDevedor = value;
    }

    /**
     * Obtém o valor da propriedade codigoMunicipioLiberacaoCredito.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCodigoMunicipioLiberacaoCredito() {
        return codigoMunicipioLiberacaoCredito;
    }

    /**
     * Define o valor da propriedade codigoMunicipioLiberacaoCredito.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCodigoMunicipioLiberacaoCredito(String value) {
        this.codigoMunicipioLiberacaoCredito = value;
    }

    /**
     * Obtém o valor da propriedade codigoTipoVeiculo.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCodigoTipoVeiculo() {
        return codigoTipoVeiculo;
    }

    /**
     * Define o valor da propriedade codigoTipoVeiculo.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCodigoTipoVeiculo(String value) {
        this.codigoTipoVeiculo = value;
    }

    /**
     * Obtém o valor da propriedade complementoImovelDevedor.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getComplementoImovelDevedor() {
        return complementoImovelDevedor;
    }

    /**
     * Define o valor da propriedade complementoImovelDevedor.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setComplementoImovelDevedor(String value) {
        this.complementoImovelDevedor = value;
    }

    /**
     * Obtém o valor da propriedade correcaoMonetaria.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCorrecaoMonetaria() {
        return correcaoMonetaria;
    }

    /**
     * Define o valor da propriedade correcaoMonetaria.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCorrecaoMonetaria(String value) {
        this.correcaoMonetaria = value;
    }

    /**
     * Obtém o valor da propriedade cpfCnpjCredor.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCpfCnpjCredor() {
        return cpfCnpjCredor;
    }

    /**
     * Define o valor da propriedade cpfCnpjCredor.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCpfCnpjCredor(String value) {
        this.cpfCnpjCredor = value;
    }

    /**
     * Obtém o valor da propriedade cpfCnpjDevedor.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCpfCnpjDevedor() {
        return cpfCnpjDevedor;
    }

    /**
     * Define o valor da propriedade cpfCnpjDevedor.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCpfCnpjDevedor(String value) {
        this.cpfCnpjDevedor = value;
    }

    /**
     * Obtém o valor da propriedade credenciada.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCredenciada() {
        return credenciada;
    }

    /**
     * Define o valor da propriedade credenciada.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCredenciada(String value) {
        this.credenciada = value;
    }

    /**
     * Obtém o valor da propriedade dataCadastro.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getDataCadastro() {
        return dataCadastro;
    }

    /**
     * Define o valor da propriedade dataCadastro.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setDataCadastro(String value) {
        this.dataCadastro = value;
    }

    /**
     * Obtém o valor da propriedade dataContrato.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getDataContrato() {
        return dataContrato;
    }

    /**
     * Define o valor da propriedade dataContrato.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setDataContrato(String value) {
        this.dataContrato = value;
    }

    /**
     * Obtém o valor da propriedade dataLiberacaoCredito.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getDataLiberacaoCredito() {
        return dataLiberacaoCredito;
    }

    /**
     * Define o valor da propriedade dataLiberacaoCredito.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setDataLiberacaoCredito(String value) {
        this.dataLiberacaoCredito = value;
    }

    /**
     * Obtém o valor da propriedade dataVencimentoPrimeiraParcela.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getDataVencimentoPrimeiraParcela() {
        return dataVencimentoPrimeiraParcela;
    }

    /**
     * Define o valor da propriedade dataVencimentoPrimeiraParcela.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setDataVencimentoPrimeiraParcela(String value) {
        this.dataVencimentoPrimeiraParcela = value;
    }

    /**
     * Obtém o valor da propriedade dataVencimentoUltimaParcela.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getDataVencimentoUltimaParcela() {
        return dataVencimentoUltimaParcela;
    }

    /**
     * Define o valor da propriedade dataVencimentoUltimaParcela.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setDataVencimentoUltimaParcela(String value) {
        this.dataVencimentoUltimaParcela = value;
    }

    /**
     * Obtém o valor da propriedade dddImovelDevedor.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getDddImovelDevedor() {
        return dddImovelDevedor;
    }

    /**
     * Define o valor da propriedade dddImovelDevedor.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setDddImovelDevedor(String value) {
        this.dddImovelDevedor = value;
    }

    /**
     * Obtém o valor da propriedade emailDevedor.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getEmailDevedor() {
        return emailDevedor;
    }

    /**
     * Define o valor da propriedade emailDevedor.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setEmailDevedor(String value) {
        this.emailDevedor = value;
    }

    /**
     * Obtém o valor da propriedade flagTransacao.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getFlagTransacao() {
        return flagTransacao;
    }

    /**
     * Define o valor da propriedade flagTransacao.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setFlagTransacao(String value) {
        this.flagTransacao = value;
    }

    /**
     * Obtém o valor da propriedade identificaoRemarcacao.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getIdentificaoRemarcacao() {
        return identificaoRemarcacao;
    }

    /**
     * Define o valor da propriedade identificaoRemarcacao.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setIdentificaoRemarcacao(String value) {
        this.identificaoRemarcacao = value;
    }

    /**
     * Obtém o valor da propriedade indicadorComissao.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getIndicadorComissao() {
        return indicadorComissao;
    }

    /**
     * Define o valor da propriedade indicadorComissao.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setIndicadorComissao(String value) {
        this.indicadorComissao = value;
    }

    /**
     * Obtém o valor da propriedade indicadorPenalidade.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getIndicadorPenalidade() {
        return indicadorPenalidade;
    }

    /**
     * Define o valor da propriedade indicadorPenalidade.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setIndicadorPenalidade(String value) {
        this.indicadorPenalidade = value;
    }

    /**
     * Obtém o valor da propriedade indicadorTaxaMora.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getIndicadorTaxaMora() {
        return indicadorTaxaMora;
    }

    /**
     * Define o valor da propriedade indicadorTaxaMora.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setIndicadorTaxaMora(String value) {
        this.indicadorTaxaMora = value;
    }

    /**
     * Obtém o valor da propriedade indicadorTaxaMulta.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getIndicadorTaxaMulta() {
        return indicadorTaxaMulta;
    }

    /**
     * Define o valor da propriedade indicadorTaxaMulta.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setIndicadorTaxaMulta(String value) {
        this.indicadorTaxaMulta = value;
    }

    /**
     * Obtém o valor da propriedade indices.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getIndices() {
        return indices;
    }

    /**
     * Define o valor da propriedade indices.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setIndices(String value) {
        this.indices = value;
    }

    /**
     * Obtém o valor da propriedade logradouroDevedor.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getLogradouroDevedor() {
        return logradouroDevedor;
    }

    /**
     * Define o valor da propriedade logradouroDevedor.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setLogradouroDevedor(String value) {
        this.logradouroDevedor = value;
    }

    /**
     * Obtém o valor da propriedade marcaModelo.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getMarcaModelo() {
        return marcaModelo;
    }

    /**
     * Define o valor da propriedade marcaModelo.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setMarcaModelo(String value) {
        this.marcaModelo = value;
    }

    /**
     * Obtém o valor da propriedade nomeCredor.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getNomeCredor() {
        return nomeCredor;
    }

    /**
     * Define o valor da propriedade nomeCredor.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setNomeCredor(String value) {
        this.nomeCredor = value;
    }

    /**
     * Obtém o valor da propriedade nomeDevedor.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getNomeDevedor() {
        return nomeDevedor;
    }

    /**
     * Define o valor da propriedade nomeDevedor.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setNomeDevedor(String value) {
        this.nomeDevedor = value;
    }

    /**
     * Obtém o valor da propriedade numeroContrato.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getNumeroContrato() {
        return numeroContrato;
    }

    /**
     * Define o valor da propriedade numeroContrato.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setNumeroContrato(String value) {
        this.numeroContrato = value;
    }

    /**
     * Obtém o valor da propriedade numeroCotaConsorcio.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getNumeroCotaConsorcio() {
        return numeroCotaConsorcio;
    }

    /**
     * Define o valor da propriedade numeroCotaConsorcio.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setNumeroCotaConsorcio(String value) {
        this.numeroCotaConsorcio = value;
    }

    /**
     * Obtém o valor da propriedade numeroGravame.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getNumeroGravame() {
        return numeroGravame;
    }

    /**
     * Define o valor da propriedade numeroGravame.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setNumeroGravame(String value) {
        this.numeroGravame = value;
    }

    /**
     * Obtém o valor da propriedade numeroGrupoConsorcio.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getNumeroGrupoConsorcio() {
        return numeroGrupoConsorcio;
    }

    /**
     * Define o valor da propriedade numeroGrupoConsorcio.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setNumeroGrupoConsorcio(String value) {
        this.numeroGrupoConsorcio = value;
    }

    /**
     * Obtém o valor da propriedade numeroImovelDevedor.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getNumeroImovelDevedor() {
        return numeroImovelDevedor;
    }

    /**
     * Define o valor da propriedade numeroImovelDevedor.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setNumeroImovelDevedor(String value) {
        this.numeroImovelDevedor = value;
    }

    /**
     * Obtém o valor da propriedade numeroRegistro.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getNumeroRegistro() {
        return numeroRegistro;
    }

    /**
     * Define o valor da propriedade numeroRegistro.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setNumeroRegistro(String value) {
        this.numeroRegistro = value;
    }

    /**
     * Obtém o valor da propriedade numeroRegistroDetran.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getNumeroRegistroDetran() {
        return numeroRegistroDetran;
    }

    /**
     * Define o valor da propriedade numeroRegistroDetran.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setNumeroRegistroDetran(String value) {
        this.numeroRegistroDetran = value;
    }

    /**
     * Obtém o valor da propriedade numeroTelefoneDevedor.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getNumeroTelefoneDevedor() {
        return numeroTelefoneDevedor;
    }

    /**
     * Define o valor da propriedade numeroTelefoneDevedor.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setNumeroTelefoneDevedor(String value) {
        this.numeroTelefoneDevedor = value;
    }

    /**
     * Obtém o valor da propriedade penalidade.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getPenalidade() {
        return penalidade;
    }

    /**
     * Define o valor da propriedade penalidade.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setPenalidade(String value) {
        this.penalidade = value;
    }

    /**
     * Obtém o valor da propriedade placa.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getPlaca() {
        return placa;
    }

    /**
     * Define o valor da propriedade placa.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setPlaca(String value) {
        this.placa = value;
    }

    /**
     * Obtém o valor da propriedade quantidadeParcela.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getQuantidadeParcela() {
        return quantidadeParcela;
    }

    /**
     * Define o valor da propriedade quantidadeParcela.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setQuantidadeParcela(String value) {
        this.quantidadeParcela = value;
    }

    /**
     * Obtém o valor da propriedade renavam.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getRenavam() {
        return renavam;
    }

    /**
     * Define o valor da propriedade renavam.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setRenavam(String value) {
        this.renavam = value;
    }

    /**
     * Obtém o valor da propriedade taxaJuroAno.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getTaxaJuroAno() {
        return taxaJuroAno;
    }

    /**
     * Define o valor da propriedade taxaJuroAno.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setTaxaJuroAno(String value) {
        this.taxaJuroAno = value;
    }

    /**
     * Obtém o valor da propriedade taxaJuroMes.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getTaxaJuroMes() {
        return taxaJuroMes;
    }

    /**
     * Define o valor da propriedade taxaJuroMes.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setTaxaJuroMes(String value) {
        this.taxaJuroMes = value;
    }

    /**
     * Obtém o valor da propriedade tipoDocumentoCredor.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getTipoDocumentoCredor() {
        return tipoDocumentoCredor;
    }

    /**
     * Define o valor da propriedade tipoDocumentoCredor.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setTipoDocumentoCredor(String value) {
        this.tipoDocumentoCredor = value;
    }

    /**
     * Obtém o valor da propriedade tipoDocumentoDevedor.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getTipoDocumentoDevedor() {
        return tipoDocumentoDevedor;
    }

    /**
     * Define o valor da propriedade tipoDocumentoDevedor.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setTipoDocumentoDevedor(String value) {
        this.tipoDocumentoDevedor = value;
    }

    /**
     * Obtém o valor da propriedade tipoGravame.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getTipoGravame() {
        return tipoGravame;
    }

    /**
     * Define o valor da propriedade tipoGravame.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setTipoGravame(String value) {
        this.tipoGravame = value;
    }

    /**
     * Obtém o valor da propriedade tipoRegistro.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getTipoRegistro() {
        return tipoRegistro;
    }

    /**
     * Define o valor da propriedade tipoRegistro.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setTipoRegistro(String value) {
        this.tipoRegistro = value;
    }

    /**
     * Obtém o valor da propriedade ufImovelDevedor.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getUfImovelDevedor() {
        return ufImovelDevedor;
    }

    /**
     * Define o valor da propriedade ufImovelDevedor.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setUfImovelDevedor(String value) {
        this.ufImovelDevedor = value;
    }

    /**
     * Obtém o valor da propriedade ufLiberacaoCredito.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getUfLiberacaoCredito() {
        return ufLiberacaoCredito;
    }

    /**
     * Define o valor da propriedade ufLiberacaoCredito.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setUfLiberacaoCredito(String value) {
        this.ufLiberacaoCredito = value;
    }

    /**
     * Obtém o valor da propriedade ufLicenciamento.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getUfLicenciamento() {
        return ufLicenciamento;
    }

    /**
     * Define o valor da propriedade ufLicenciamento.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setUfLicenciamento(String value) {
        this.ufLicenciamento = value;
    }

    /**
     * Obtém o valor da propriedade ufPlaca.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getUfPlaca() {
        return ufPlaca;
    }

    /**
     * Define o valor da propriedade ufPlaca.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setUfPlaca(String value) {
        this.ufPlaca = value;
    }

    /**
     * Obtém o valor da propriedade valorComissao.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getValorComissao() {
        return valorComissao;
    }

    /**
     * Define o valor da propriedade valorComissao.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setValorComissao(String value) {
        this.valorComissao = value;
    }

    /**
     * Obtém o valor da propriedade valorIof.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getValorIof() {
        return valorIof;
    }

    /**
     * Define o valor da propriedade valorIof.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setValorIof(String value) {
        this.valorIof = value;
    }

    /**
     * Obtém o valor da propriedade valorParcela.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getValorParcela() {
        return valorParcela;
    }

    /**
     * Define o valor da propriedade valorParcela.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setValorParcela(String value) {
        this.valorParcela = value;
    }

    /**
     * Obtém o valor da propriedade valorTaxaMora.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getValorTaxaMora() {
        return valorTaxaMora;
    }

    /**
     * Define o valor da propriedade valorTaxaMora.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setValorTaxaMora(String value) {
        this.valorTaxaMora = value;
    }

    /**
     * Obtém o valor da propriedade valorTaxaMulta.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getValorTaxaMulta() {
        return valorTaxaMulta;
    }

    /**
     * Define o valor da propriedade valorTaxaMulta.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setValorTaxaMulta(String value) {
        this.valorTaxaMulta = value;
    }

    /**
     * Obtém o valor da propriedade valorTotalFinanciamento.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getValorTotalFinanciamento() {
        return valorTotalFinanciamento;
    }

    /**
     * Define o valor da propriedade valorTotalFinanciamento.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setValorTotalFinanciamento(String value) {
        this.valorTotalFinanciamento = value;
    }

    public String getNumAditivoContrato() {
        return numAditivoContrato;
    }

    public void setNumAditivoContrato(String numAditivoContrato) {
        this.numAditivoContrato = numAditivoContrato;
    }

    public String getNumDataAditivo() {
        return numDataAditivo;
    }

    public void setNumDataAditivo(String numDataAditivo) {
        this.numDataAditivo = numDataAditivo;
    }
}
