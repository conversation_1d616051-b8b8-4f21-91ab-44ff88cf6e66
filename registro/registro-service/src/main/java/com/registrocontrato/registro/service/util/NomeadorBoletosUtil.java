package com.registrocontrato.registro.service.util;

import com.registrocontrato.infra.util.PlaceconUtil;

import java.util.Calendar;
import java.util.Date;

public class NomeadorBoletosUtil {

    private static final String LINHA_DIGITAVEL = "LD-PLACE-";

    public static String gerarNumeroBoletoReembolso(Date data, Long id) {
        Calendar prefixo = Calendar.getInstance();
        prefixo.setTime(data);
        int numero = Integer.parseInt(String.valueOf(prefixo.get(Calendar.YEAR)).substring(2));
        return String.valueOf(numero * 1000000L + id);
    }

    public static String getNumeroDocumentoBoletoDetran(Date data, Long id) {
        return LINHA_DIGITAVEL + gerarNumeroBoletoReembolso(data, id);
    }

    public static String gerarNumeroBoletoCredenciada(Long id) {
        return PlaceconUtil.leftPad(String.valueOf(id), 11, "0");
    }

    public static String gerarNumeroBoletoDetranCarregadoManualmente(Date data, Long id) {
        return gerarNumeroBoletoReembolso(data, id) + "b";
    }

    public static String gerarNumeroBoletoDetranCarregadoManualmenteParaCliente(Date data, Long id) {
        return gerarNumeroBoletoReembolso(data, id) + "-cliente";
    }

    public static String gerarNumeroBoletoDetranCarregadoManualmenteParaFinanceiro(Date data, Long id) {
        return gerarNumeroBoletoReembolso(data, id) + "-financeiro-place";
    }

    public static String gerarNumeroBoletoSng(Long id) {
        return PlaceconUtil.leftPad(String.valueOf(id), 9, "0");
    }

}
