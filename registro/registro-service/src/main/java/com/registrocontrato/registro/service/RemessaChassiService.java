package com.registrocontrato.registro.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.registrocontrato.infra.email.Email;
import com.registrocontrato.infra.email.EnviaEmail;
import com.registrocontrato.infra.entity.Financeira;
import com.registrocontrato.infra.entity.SimNao;
import com.registrocontrato.infra.entity.StatusProcessamento;
import com.registrocontrato.infra.entity.rsng.ArquivoRemessaRsng;
import com.registrocontrato.infra.exception.ServiceException;
import com.registrocontrato.infra.service.BaseService;
import com.registrocontrato.infra.util.PlaceconUtil;
import com.registrocontrato.registro.entity.ArquivoRemessa;
import com.registrocontrato.registro.entity.RemessaChassi;
import com.registrocontrato.registro.entity.RemessaChassiMensagens;
import com.registrocontrato.registro.repository.RemessaChassiRepository;
import com.registrocontrato.registro.service.dto.RemessaChassiDTO;
import com.registrocontrato.seguranca.entity.AcessoSenha;
import com.registrocontrato.seguranca.entity.Perfil;
import com.registrocontrato.seguranca.entity.Usuario;
import com.registrocontrato.seguranca.repository.AcessoSenhaRepository;
import com.registrocontrato.seguranca.service.UsuarioService;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.data.domain.Sort.Direction;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.data.repository.PagingAndSortingRepository;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;

import javax.mail.internet.InternetAddress;
import javax.persistence.criteria.*;
import java.io.*;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class RemessaChassiService extends BaseService<RemessaChassi, RemessaChassiDTO> {

    private static final String TITULO_EMAIL_REMESSA_CHASSI = "Processamento de Remessa de Chassi";

    private static final long serialVersionUID = 1L;

    @Autowired
    private EnviaEmail enviaEmail;

    @Value("${spring.mail.destinarios.suporte:null}")
    private String emailContato;

    @Value("${file.remessa.chassi.dir:null}")
    private String FILE_DIR;

    @Value("${viper.url.base:null}")
    private String URL_VIPER;

    @Value("${viper.user:null}")
    private String USER_VIPER;
    @Value("${viper.password:null}")
    private String PASSWORD_VIPER;

    @Autowired
    private ContratoService contratoService;

    @Autowired
    private RemessaChassiRepository remessaChassiRepository;

    @Autowired
    private UsuarioService usuarioService;

    @Autowired
    private AcessoSenhaRepository acessoSenhaRepository;

    public void save(String nomeArquivo, InputStream input, String usuario, Financeira financeira) throws ServiceException {
        String referenciaArquivo = gravarArquivoUpload(input);
        RemessaChassi entity = new RemessaChassi();
        entity.setDataTransacao(new Date());
        entity.setHash(referenciaArquivo);
        entity.setNome(nomeArquivo);
        entity.setStatus(StatusProcessamento.AGUARDANDO);
        entity.setUsuario(usuario);
        entity.setFinanceira(financeira);
        entity = remessaChassiRepository.save(entity);
        save(entity);
        enviarParaProcessamento(entity);
        sendEmail(entity);
    }

    public void finalizarRemessa(RemessaChassi remessaChassi, StatusProcessamento statusProcessamento) {
        remessaChassi.setStatus(statusProcessamento);
        enviarResultadoDoProcessamento(remessaChassi);
    }

    private void enviarResultadoDoProcessamento(RemessaChassi remessaChassi) {
        Usuario usuario = usuarioService.findByCpf(remessaChassi.getUsuario());
        try {
            HashMap<Character, List<InternetAddress>> hash = new HashMap<Character, List<InternetAddress>>();
            List<InternetAddress> emails = new ArrayList<>();
            if (Objects.nonNull(usuario))
                emails.add(new InternetAddress(Objects.nonNull(usuario.getEmail()) ? usuario.getEmail() : null));
            emails.add(new InternetAddress(emailContato));
            hash.put(Email.TIPO_PARA, emails);
            Map<String, String> params = new HashMap<String, String>();
            params.put("USUARIO", remessaChassi.getUsuario());
            params.put("ARQUIVO", remessaChassi.getNome());
            aplicaObservacao(remessaChassi, params);
            String chassis = getListaProcessamentoChassis(remessaChassi);
            params.put("CHASSIS", chassis);
            params.put("DATA", PlaceconUtil.formatarDataHora(remessaChassi.getDataTransacao()));
            params.put("FINANCEIRA", remessaChassi.getFinanceira().getNome());
            Email email = new Email(enviaEmail);
            email.enviarEmail(TITULO_EMAIL_REMESSA_CHASSI, params, hash, "/email/processamentoremessachassi.xhtml");
        } catch (Exception e) {
            e.printStackTrace();
        }


    }

    private void enviarParaProcessamento(RemessaChassi entity) {
        if (!(entity.getStatus() == StatusProcessamento.PROCESSADO && !(entity.getStatus() == StatusProcessamento.PROCESSADO_ERRO))) {
            entity.setStatus(StatusProcessamento.EM_PROCESSAMENTO);
            RemessaChassiDTO remessaRest = new RemessaChassiDTO().mapperRemessaChassiToDTO(entity);
            if (!enviarRequisicaoParaProcessamento(remessaRest)) {
                entity.setStatus(StatusProcessamento.AGUARDANDO);
            }
        }
    }

    private Boolean enviarRequisicaoParaProcessamento(RemessaChassiDTO remessaChassiDTO) {
        try {

            ResponseEntity<String> response = enviaTransacao(URL_VIPER, HttpMethod.POST, remessaChassiDTO, getBasicPass(), "Basic");
            logger.info(response.getBody());
            if (response.getStatusCode() == HttpStatus.OK)
                return true;
        } catch (RestClientException e) {
            logger.error(e.getMessage());
        }
        return false;
    }

    private String getBasicPass() {
        String basic = USER_VIPER + ":" + PASSWORD_VIPER;
        return Base64.getEncoder().encodeToString(basic.getBytes());
    }

    private ResponseEntity<String> enviaTransacao(String urlRequisicao, HttpMethod httpMethod, Object request, String token, String tokenType) {

        return new RestTemplate().exchange(urlRequisicao, httpMethod, new HttpEntity<>(getJSON(request), getHttpHeaders(token, MediaType.APPLICATION_JSON_UTF8, tokenType)), String.class);

    }

    private HttpHeaders getHttpHeaders(String token, MediaType mediaType, String tipoAuthorization) {

        HttpHeaders headers = new HttpHeaders();
        if (token != null) {
            headers.add("Authorization", tipoAuthorization + " " + token);
        }
        if (mediaType != null) {
            headers.setContentType(mediaType);
        }
        return headers;

    }

    private Object getJSON(Object object) {
        try {
            return new ObjectMapper().writeValueAsString(object);
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
    }

    public RemessaChassi salvarRemessaChassi(String nomeArquivo, File input, String usuario, Financeira financeira) throws ServiceException, FileNotFoundException {
        String referenciaArquivo = gravarArquivoUpload(new FileInputStream(input));
        RemessaChassi entity = new RemessaChassi();
        entity.setDataTransacao(new Date());
        entity.setHash(referenciaArquivo);
        entity.setNome(nomeArquivo);
        entity.setStatus(StatusProcessamento.AGUARDANDO);
        entity.setUsuario(usuario);
        entity.setFinanceira(financeira);
        entity = remessaChassiRepository.save(entity);
        sendEmail(entity);
        return entity;
    }

    public void associaRemessaPadraoParaRemessaChassi(Long id, ArquivoRemessa arquivoRemessa) throws ServiceException {
        RemessaChassi entity = findOne(id);
        entity.setArquivoRemessa(arquivoRemessa);
        save(entity);
    }

    public List<RemessaChassi> getArquivosRemessaNaoProcessados() {
        return remessaChassiRepository.findNaoProcessados();
    }

    public List<RemessaChassi> getArquivosInativos1Hora() {
        Calendar cal = Calendar.getInstance();
        cal.add(Calendar.MINUTE, -20);
        Date fim = cal.getTime();
        return remessaChassiRepository.buscaInativo1Hora(fim);
    }

    public List<RemessaChassi> getArquivosRemessaEmProcessamento() {
        return remessaChassiRepository.findEmProcessamentos();
    }

    private void sendEmail(RemessaChassi entity) {
        try {
            HashMap<Character, List<InternetAddress>> hash = new HashMap<Character, List<InternetAddress>>();
            List<InternetAddress> emails = new ArrayList<>();
            emails.add(new InternetAddress(emailContato));
            hash.put(Email.TIPO_PARA, emails);
            Map<String, String> params = new HashMap<String, String>();
            params.put("ARQUIVO", entity.getNome());
            params.put("USUARIO", entity.getUsuario());
            params.put("DATA", PlaceconUtil.formatarDataHora(entity.getDataTransacao()));
            aplicaObservacao(entity, params);
            Email email = new Email(enviaEmail);
            email.enviarEmail(TITULO_EMAIL_REMESSA_CHASSI + " - CHEGADA", params, hash, "/email/remessachassi.xhtml");
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void aplicaObservacao(RemessaChassi entity, Map<String, String> params) {
        if (entity.getObservacao() == null || entity.getObservacao().trim().isEmpty() || entity.getStatus() == StatusProcessamento.PROCESSADO) {
            params.put("HASOBSERVACAO", "none");
            params.put("OBSERVACAO", "");
        } else {
            params.put("HASOBSERVACAO", "block");
            params.put("OBSERVACAO", entity.getObservacao());
        }
    }

    private String gravarArquivoUpload(InputStream inputStream) throws ServiceException {
        String referenciaArquivo = PlaceconUtil.formataData(new Date()) + "_" + RandomStringUtils.randomAlphanumeric(5);
        try {
            File targetFile = new File(FILE_DIR, referenciaArquivo);
            FileUtils.copyInputStreamToFile(inputStream, targetFile);
        } catch (IOException e) {
            throw new ServiceException(e.getMessage());
        }
        return referenciaArquivo;
    }

    @Override
    public Page<RemessaChassi> findAll(int first, int pageSize, RemessaChassiDTO filter) {
        Specification<RemessaChassi> contratoSpec = new Specification<RemessaChassi>() {

            @Override
            public Predicate toPredicate(Root<RemessaChassi> root, CriteriaQuery<?> cq, CriteriaBuilder cb) {
                Join<RemessaChassi, ArquivoRemessa> joinArquivoRemessa = root.join("arquivoRemessa", JoinType.LEFT);
                cq.distinct(true);

                List<Predicate> predicates = new ArrayList<>();

                if (StringUtils.isNotEmpty(filter.getChassi())) {
                    predicates.add(cb.like(cb.lower(joinArquivoRemessa.join("registros").get("chassi")), "%" + filter.getChassi().toLowerCase() + "%"));
                }

                if (StringUtils.isNotEmpty(filter.getNomeArquivo())) {
                    predicates.add(cb.like(cb.lower(root.<String>get("nome")), "%" + filter.getNomeArquivo().toLowerCase() + "%"));
                }
                if (filter.getDataInicio() != null) {
                    predicates.add(cb.greaterThanOrEqualTo(root.<Date>get("dataTransacao"), PlaceconUtil.minDateTime(filter.getDataInicio())));
                }
                if (filter.getDataFim() != null) {
                    predicates.add(cb.lessThanOrEqualTo(root.<Date>get("dataTransacao"), PlaceconUtil.maxDateTime(filter.getDataFim())));
                }

                if (filter.getFinanceira() != null) {
                    predicates.add(cb.equal(root.get("financeira"), filter.getFinanceira()));
                }

                // se o usuario eh financeira filtrar somente os usuario da financeira
                Usuario usuarioLogado = usuarioService.findByCpfFinanceiras(filter.getUsuario());
                if (usuarioLogado.getPerfil() == Perfil.FINANCEIRA || !usuarioLogado.getFinanceiras().isEmpty()) {
                    List<Financeira> values = usuarioLogado.getFinanceiras();
                    predicates.add(root.get("financeira").in(values));
                }

                return andTogether(predicates, cb);
            }

            private Predicate andTogether(List<Predicate> predicates, CriteriaBuilder cb) {
                return cb.and(predicates.toArray(new Predicate[0]));
            }
        };

        return remessaChassiRepository.findAll(contratoSpec, new PageRequest(first / pageSize, pageSize, new Sort(Direction.DESC, "id")));
    }

    @Override
    protected PagingAndSortingRepository<RemessaChassi, Long> getRepository() {
        return remessaChassiRepository;
    }

    public void save(RemessaChassi entity, StatusProcessamento status) throws ServiceException {
        entity.setStatus(status);
        if (StatusProcessamento.PROCESSADO == status)
            finalizarRemessa(entity, status);
        if (StatusProcessamento.PROCESSADO_ERRO == status) {
            finalizarRemessaComErro(entity.getId(), status, "Remessa Finalizada com Erro");
        }
        if (StatusProcessamento.AGUARDANDO == status || StatusProcessamento.EM_PROCESSAMENTO == status)
            enviarParaProcessamento(entity);

        save(entity);
    }

    public void finalizarRemessaComErro(Long id, StatusProcessamento status, String erro) throws ServiceException {
        RemessaChassi entity = remessaChassiRepository.findOne(id);
        entity.setStatus(status);
        entity.setObservacao(erro);
        save(entity);
        enviarEmailErroLogin(entity);
    }

    private void enviarEmailErroLogin(RemessaChassi entity) {
        Usuario usuario = usuarioService.findByCpf(entity.getUsuario());
        try {
            HashMap<Character, List<InternetAddress>> hash = new HashMap<Character, List<InternetAddress>>();
            List<InternetAddress> emails = new ArrayList<>();
            emails.add(new InternetAddress(usuario.getEmail()));
            hash.put(Email.TIPO_OCULTO, Arrays.asList(new InternetAddress("<EMAIL>")));
            hash.put(Email.TIPO_PARA, emails);
            Map<String, String> params = new HashMap<String, String>();
            params.put("USUARIO", entity.getUsuario());
            params.put("ARQUIVO", entity.getNome());
            logger.info("Data: " + PlaceconUtil.formatarDataHora(entity.getDataTransacao()));
            params.put("DATA", PlaceconUtil.formatarDataHora(entity.getDataTransacao()));
            params.put("FINANCEIRA", entity.getFinanceira().getNome());
            params.put("ERROR", entity.getObservacao());
            Email email = new Email(enviaEmail);
            email.enviarEmail("=> " + TITULO_EMAIL_REMESSA_CHASSI, params, hash, "/email/remessaerrologin.xhtml");
        } catch (Exception e) {
            e.printStackTrace();
        }

    }

    private String getListaProcessamentoChassis(RemessaChassi entity) {
        StringBuilder chassiProcessado = new StringBuilder();
        StringBuilder chassiErroGRAVAME = new StringBuilder();
        entity.setMensagens(remessaChassiRepository.findMensagensRelatorioById(entity));
        if (!PlaceconUtil.isListaVaziaOuNula(entity.getMensagens())) {
            List<RemessaChassiMensagens> mensagensSucesso = entity.getMensagens().stream().filter(r -> r.getSucesso() == SimNao.S).collect(Collectors.toList());
            List<RemessaChassiMensagens> mensagensErro = entity.getMensagens().stream().filter(r -> r.getSucesso() == SimNao.N).collect(Collectors.toList());

            if (!PlaceconUtil.isListaVaziaOuNula(mensagensSucesso)) {
                chassiProcessado.append("<h2>Chassi processado com Sucesso</h2><table>");
                mensagensSucesso.forEach(item -> {
                    montaItemChassi(chassiProcessado, item.getChassi(), item.getMensagen());
                });
                chassiProcessado.append("</table>");
            }

            if (!PlaceconUtil.isListaVaziaOuNula(mensagensErro)) {
                chassiProcessado.append("<h2>Chassi com erro no GRAVAME</h2><table>");
                mensagensErro.forEach(item -> {
                    montaItemChassi(chassiErroGRAVAME, item.getChassi(), item.getMensagen());
                });
                chassiErroGRAVAME.append("</table>");
            }
            return chassiProcessado + chassiErroGRAVAME.toString();
        }

        return retornoQuandoNaoHouverChassiProcessado();

    }

    private String retornoQuandoNaoHouverChassiProcessado() {
        StringBuilder s = new StringBuilder();
        s.append("<table>");
        s.append("<tr><td>");
        s.append("Não houve chassis processados nessa remessa, verifique a disponibilidade da Plataforma B3.");
        s.append("</td></tr>");
        s.append("</table>");
        return s.toString();
    }

    public void montaItemChassi(StringBuilder chassis, String numeroChassi, String mensagem) {
        chassis.append("<tr>");
        chassis.append("<td>");
        chassis.append(numeroChassi);
        chassis.append("</td>");
        chassis.append("<td>:&nbsp;&nbsp;");
        chassis.append(mensagem);
        chassis.append("</td></tr>");

    }

    public ArquivoRemessa findArquivoRemessaById(Long id) {
        Optional<RemessaChassi> remessaChassi = remessaChassiRepository.arquivoRemessaById(id);
        if (remessaChassi.isPresent()) {
            return remessaChassi.get().getArquivoRemessa();
        }
        return null;
    }

    public List<String> getCpfsRegistradosRemessa(Financeira financeira) {
        return acessoSenhaRepository.findByFinanceiraAcessoSNG(financeira).stream().map(AcessoSenha::getUsuario).collect(Collectors.toList());
    }


}
