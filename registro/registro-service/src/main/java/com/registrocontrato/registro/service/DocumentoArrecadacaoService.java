package com.registrocontrato.registro.service;

import com.registrocontrato.infra.entity.EstadoRequisicao;
import com.registrocontrato.infra.entity.Financeira;
import com.registrocontrato.infra.entity.Uf;
import com.registrocontrato.infra.exception.ServiceException;
import com.registrocontrato.infra.service.BaseService;
import com.registrocontrato.infra.util.PlaceconUtil;
import com.registrocontrato.registro.entity.DocumentoArrecadacao;
import com.registrocontrato.registro.entity.MensagemRetorno;
import com.registrocontrato.registro.entity.Veiculo;
import com.registrocontrato.registro.repository.DocumentoArrecadacaoRepository;
import com.registrocontrato.registro.repository.MensagemRetornoRepository;
import com.registrocontrato.registro.repository.VeiculoRepository;
import com.registrocontrato.registro.service.detran.HandlerWsDetranClient;
import com.registrocontrato.registro.service.detran.ba.enums.CodigoOperacaoEnum;
import com.registrocontrato.registro.service.detran.ba.soap.WsDetranBA;
import com.registrocontrato.registro.service.detran.ce.rest.WsDetranCE;
import com.registrocontrato.registro.service.detran.ce.client.response.dae.estoque.EstoqueDaesResponse;
import com.registrocontrato.registro.service.dto.DocumentoArrecadacaoDTO;
import com.registrocontrato.registro.service.dto.MensagemRetornoDTO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.data.domain.Sort.Direction;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.data.repository.PagingAndSortingRepository;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.faces.application.FacesMessage;
import javax.faces.context.FacesContext;
import javax.persistence.criteria.*;
import java.util.*;

@Service
public class DocumentoArrecadacaoService extends BaseService<DocumentoArrecadacao, DocumentoArrecadacaoDTO> {

    private static final long serialVersionUID = 1L;

    @Autowired
    private DocumentoArrecadacaoRepository repository;

    @Autowired
    private VeiculoRepository veiculoRepository;

    @Autowired
    private List<HandlerWsDetranClient> listaWsDetranClient;

    @Autowired
    private MensagemRetornoRepository mensagemRetornoDETRANRepository;

    @Autowired
    private WsDetranCE wsDetranCeRestClientProd;

    @Override
    public Page<DocumentoArrecadacao> findAll(int first, int pageSize, DocumentoArrecadacaoDTO filter) {

        Specification<DocumentoArrecadacao> specification = new Specification<DocumentoArrecadacao>() {

            @Override
            public Predicate toPredicate(Root<DocumentoArrecadacao> root, CriteriaQuery<?> cq, CriteriaBuilder cb) {
                cq.distinct(true);
                List<Predicate> predicates = new ArrayList<>();

                if (StringUtils.isNotEmpty(filter.getChassi())) {
                    predicates.add(cb.like(cb.lower(root.join("veiculo", JoinType.INNER).<String>get("numeroChassi")), "%" + filter.getChassi().toLowerCase() + "%"));
                }

                if (!StringUtils.isBlank(filter.getCodigo())) {
                    predicates.add(cb.like(cb.lower(root.<String>get("codigo")), "%" + filter.getCodigo() + "%"));
                }
                if (filter.getEstado() != null) {
                    predicates.add(cb.equal(root.<Uf>get("estado"), filter.getEstado()));
                }
                if (filter.getEstadoRequisicao() != null) {
                    predicates.add(cb.equal(root.<EstadoRequisicao>get("estadoRequisicao"), filter.getEstadoRequisicao()));
                }
                if (filter.getDataInicio() != null) {
                    predicates.add(cb.greaterThanOrEqualTo(root.<Date>get("dataPagamento"),
                            PlaceconUtil.minDateTime(filter.getDataInicio())));
                }
                if (filter.getDataFim() != null) {
                    predicates.add(cb.lessThanOrEqualTo(root.<Date>get("dataPagamento"),
                            PlaceconUtil.maxDateTime(filter.getDataFim())));
                }
                if (StringUtils.isNotEmpty(filter.getCodigoIdentificadorPagamento())) {
                    predicates.add(cb.like(cb.lower(root.<String>get("codigoIdentificadorPagamento")), "%" + filter.getCodigoIdentificadorPagamento() + "%"));
                }

                return andTogether(predicates, cb);
            }

            private Predicate andTogether(List<Predicate> predicates, CriteriaBuilder cb) {
                return cb.and(predicates.toArray(new Predicate[0]));
            }
        };
        return repository.findAll(specification, new PageRequest(first / pageSize, pageSize, new Sort(Direction.DESC, "id")));
    }

    private Optional<HandlerWsDetranClient> consultarWsDetran(Uf estado) {
        return listaWsDetranClient
                .stream()
                .filter(wsDetranClient -> wsDetranClient.getUf().equals(estado))
                .findFirst();
    }

    @Override
    public void save(DocumentoArrecadacao entity) throws ServiceException {
        repository.findByCodigo(entity.getCodigo()).ifPresent((doc) -> {
            throw new ServiceException(String.format("Já existe um Documento de Arrecadação cadastrado com o número %s.", entity.getCodigo()));
        });
        super.save(entity);
    }

    @Override
    protected PagingAndSortingRepository<DocumentoArrecadacao, Long> getRepository() {
        return repository;
    }

    @Transactional
    public String gerarDocumentoArrecadacao(Uf estado, Veiculo veiculo) throws ServiceException {
        DocumentoArrecadacao doc = new DocumentoArrecadacao();

        if (estado == Uf.RJ) {
            doc = repository.findTop1ByEstadoAndVeiculoIsNullAndDataPagamentoIsNotNullOrderByDataPagamentoAsc(estado);
            if (doc == null) {
                throw new ServiceException("Sem documento de arrecadação disponível. Não se preocupe, já estamos atuando para solucionar.");
            }
            if (Objects.isNull(doc.getVeiculo())) {
                doc.setVeiculo(veiculo);
                veiculo.setDocumentoArrecadacao(doc.getCodigo());
                repository.save(doc);
            } else {
                gerarDocumentoArrecadacao(estado, veiculo);
            }
        }

        if (estado == Uf.BA) {
            doc.setVeiculo(veiculo);

            Optional<HandlerWsDetranClient> wsDetran = consultarWsDetran(estado);
            MensagemRetornoDTO retornoDetran = null;
            MensagemRetorno mensagemRetornoDETRAN = null;
            if (wsDetran.isPresent()) {
                retornoDetran = wsDetran.get().emitirDocumentoArrecadacao(doc);
                mensagemRetornoDETRAN = mensagemRetornoDETRANRepository.findTop1ByCodigoAndUf(retornoDetran.getCodigo(), estado);
            }

            if (mensagemRetornoDETRAN == null) {
                mensagemRetornoDETRAN = mensagemRetornoDETRANRepository.save(new MensagemRetorno(retornoDetran.getCodigo(), estado, retornoDetran.getDescricao()));
            }
            unproxy(mensagemRetornoDETRAN);

            if (!retornoDetran.getSucesso()) {
                veiculo.setMensagemRetorno(mensagemRetornoDETRAN);
                veiculo.setMensagemRetornoDetalhada(null);
                veiculo.setMensagemRetornoDetalhada(retornoDetran.getCodigo() + " - " + (StringUtils.isEmpty(retornoDetran.getDescricao()) ? mensagemRetornoDETRAN.getDescricao() : retornoDetran.getDescricao()));
            } else {
                veiculo.setDocumentoArrecadacao(doc.getCodigo());
                repository.save(doc);
            }
        }

        return doc.getCodigo();
    }

    public List<DocumentoArrecadacao> findCobrancasBB() {
        return repository.findCobrancasBB();
    }

    public DocumentoArrecadacao gerarDocumentoArrecadacao(String cpf, Long quantidade, Integer tipoServico, Financeira financeira) throws Exception {
        DocumentoArrecadacao retornoDetran = wsDetranCeRestClientProd.gerarDocumentoArrecadacao(cpf, quantidade, tipoServico, financeira);
        return retornoDetran;
    }

    public String buscarBase64BoletoPdf(String codigo, Financeira financeira) throws Exception {
        return wsDetranCeRestClientProd.emitirExtratoPdf(codigo, financeira).getObjetoRetorno().getArquivoDaeBase64();
    }


    public Optional<EstoqueDaesResponse> buscarEstoqueDae(Financeira financeira) throws Exception {
        if (wsDetranCeRestClientProd.buscarEstoqueDae(financeira).isPresent())
            return Optional.of(wsDetranCeRestClientProd.buscarEstoqueDae(financeira).get());
        return Optional.empty();
    }

    public DocumentoArrecadacao findByVeiculo(Veiculo veiculo) {
        return repository.findTop1ByVeiculo(veiculo)
                .orElse(null);
    }

    public DocumentoArrecadacao findByVeiculoAndCodigoDocArrecadacao(Veiculo veiculo, String codDocArrecadacao) {
        return repository.findByVeiculoAndCodigo(veiculo, codDocArrecadacao)
                .orElseThrow(() -> new ServiceException("Documento de arrecadação não encontrado"));
    }

    public DocumentoArrecadacao findByCodigoBarras(String codigoBarras) {
        return repository.findByCodigoBarras(codigoBarras);
    }

    @Override
    public void delete(Long id) throws ServiceException {
        DocumentoArrecadacao documentoArrecadacao = repository.findOne(id);
        if (documentoArrecadacao.getDataPagamento() != null) {
            throw new ServiceException("Esse documento não pode ser excluido pois está pago.");
        }
        if (documentoArrecadacao.getEstado() == Uf.BA) {
           cancelarDocArrecadacao(documentoArrecadacao);
        }
        documentoArrecadacao.getVeiculo().setDocumentoArrecadacao(null);
        veiculoRepository.save(documentoArrecadacao.getVeiculo());

        repository.delete(documentoArrecadacao.getId());
    }

    public void deleteByVeiculo(Veiculo v) throws ServiceException {
        DocumentoArrecadacao documentoArrecadacao = findByVeiculo(v);
        if (documentoArrecadacao.getEstado() == Uf.BA) {
            cancelarDocArrecadacao(documentoArrecadacao);
        } else {
            documentoArrecadacao.setVeiculo(null);
            save(documentoArrecadacao);
        }
    }

    private void cancelarDocArrecadacao(DocumentoArrecadacao documentoArrecadacao) throws ServiceException {
        Optional<HandlerWsDetranClient> wsDetran = consultarWsDetran(documentoArrecadacao.getEstado());
        if (wsDetran.isPresent()) {
            MensagemRetornoDTO retorno = wsDetran.get().cancelamentoDocumentoArrecadacao(documentoArrecadacao);
            if (!retorno.getSucesso()) {
                throw new ServiceException(retorno.getCodigo() + " - " + retorno.getDescricao());
            }
        }
    }

    public void renovarTaxa(DocumentoArrecadacao documentoArrecadacao) {
        consultarWsDetran(documentoArrecadacao.getEstado()).ifPresent(wsDetranClient ->
            ((WsDetranBA) wsDetranClient).emitirDocumentoArrecadacao(documentoArrecadacao, CodigoOperacaoEnum.RGE)
        );
       repository.save(documentoArrecadacao);
    }

    public Financeira findFinanceiraByCodigo(String codigo) {
        DocumentoArrecadacao doc = repository.findDocumentoArrecadacaoByCodigo(codigo);
        return doc.getFinanceira();
    }

    public void erroCobranca(String codigoDeBarras) {
        FacesContext.getCurrentInstance()
                .addMessage("messages", new FacesMessage(FacesMessage.SEVERITY_WARN,
                        "O código de barras " + codigoDeBarras + " já foi processado", null));
    }

    public boolean verificaEstado(DocumentoArrecadacao documentoArrecadacao, Uf estado) {
        return documentoArrecadacao.getEstado().equals(estado);
    }

    public DocumentoArrecadacao findById(Long id) {
        return repository.findOne(id);
    }
}
