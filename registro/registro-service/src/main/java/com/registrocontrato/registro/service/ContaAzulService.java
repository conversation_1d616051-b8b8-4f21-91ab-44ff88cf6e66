package com.registrocontrato.registro.service;

import com.registrocontrato.infra.entity.Financeira;
import com.registrocontrato.infra.entity.SimNao;
import com.registrocontrato.infra.entity.SituacaoFinanceiraEstado;
import com.registrocontrato.infra.entity.Uf;
import com.registrocontrato.infra.exception.ServiceException;
import com.registrocontrato.infra.service.BaseService;
import com.registrocontrato.infra.util.PlaceconUtil;
import com.registrocontrato.registro.dto.*;
import com.registrocontrato.registro.entity.*;
import com.registrocontrato.registro.enums.TipoContaAzul;
import com.registrocontrato.registro.repository.CobrancaRepository;
import com.registrocontrato.registro.repository.ContaAzulRepository;
import com.registrocontrato.registro.repository.SituacaoFinanceiraEstadoRepository;
import com.registrocontrato.registro.service.cobranca.CobrancaService;
import com.registrocontrato.registro.service.cobranca.calculadora.CalculadoraDeCobrancaStrategy;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.data.domain.Sort.Direction;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.data.repository.PagingAndSortingRepository;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.util.MultiValueMap;
import org.springframework.util.StringUtils;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URLEncoder;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

import static com.registrocontrato.infra.util.PlaceconUtil.formatarDataPadrao;
import static com.registrocontrato.infra.util.PlaceconUtil.rightPad;

@Service
public class ContaAzulService extends BaseService<ContaAzul, ContaAzulDTO> {

    private static final long serialVersionUID = 1L;

    @Autowired
    private CobrancaService cobrancaService;

    @Autowired
    private CupomDescontoService cupomDescontoService;

    @Autowired
    private CredenciamentoService credenciamentoService;

    @Autowired
    private List<CalculadoraDeCobrancaStrategy> calculadoras;

    @Autowired
    private SituacaoFinanceiraEstadoRepository situacaoFinanceiraEstadoRepository;

    private List<String> cnpjsNumeroPedido = Arrays.asList("02916265000160");

    @Autowired
    private ContaAzulRepository repository;
    @Value("${contaazul.id:null}")
    private String ID;
    @Value("${contaazul.secret:null}")
    private String secret;
    @Value("${contaazul.url:null}")
    private String CONTAAZUL_AUTH_URL;
    @Value("${contaazul.redirect:null}")
    private String REDIRECT_URL;
    @Value("${contaazul.url-token:null}")
    private String CONTAAZUL_TOKEN_URL;
    @Value("${contaazul.url-servicos:null}")
    private String CONTAAZUL_SERVICES_URL;
    @Value("${contaazul.url-clientes:null}")
    private String CONTAAZUL_CLIENTS_URL;
    @Value("${contaazul.url-vendas:null}")
    private String CONTAAZUL_CREATE_SALE_URL;
    @Autowired
    private CobrancaRepository cobrancaRepository;

    /*
     * Builds ContaAzul OAuth 2 Authorization URL
     */
    public String getRedirectUrl(HttpServletRequest request, String contaazulState) throws UnsupportedEncodingException {
        System.out.println("ID " + ID);
        UriComponentsBuilder builder = UriComponentsBuilder.fromUriString(CONTAAZUL_AUTH_URL).queryParam("client_id", ID).queryParam("client_secret", secret).queryParam("state", contaazulState).queryParam("redirect_uri", URLEncoder.encode(REDIRECT_URL, "UTF-8"));
        return builder.build().toUri().toString();
    }

    /*
     * Request ContaAzul OAuth 2 tokens
     */
    public TokenContaAzulDTO getContaAzulTokens(String url) throws IOException {
        HttpEntity<MultiValueMap<String, String>> httpEntity;
        httpEntity = getTokenRequestConfig();
        RestTemplate restTemplate = new RestTemplate();
        TokenContaAzulDTO token = restTemplate.postForObject(url, httpEntity, TokenContaAzulDTO.class);
        return token;
    }

    /*
     * Builds the request body and headers for the post request to be made, in this
     * case it's a HttpEntity class, expected by RestTemplate
     */
    private HttpEntity<MultiValueMap<String, String>> getTokenRequestConfig() {
        return new HttpEntity<MultiValueMap<String, String>>(null, getTokenRequestHeaders());
    }

    /*
     * Builds the token request body
     */
    public String getRefreshTokenUrl(String refreshToken) {
        UriComponentsBuilder builder = UriComponentsBuilder.fromUriString(CONTAAZUL_TOKEN_URL).queryParam("refresh_token", refreshToken).queryParam("grant_type", "refresh_token");
        return builder.build().toUri().toString();
    }

    /*
     * Builds the token request headers
     */
    private HttpHeaders getTokenRequestHeaders() {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.add("Authorization", getBasicAuthenticationHeader());
        return headers;
    }

    public ContaAzul findServico(Uf uf) {
        if (uf == Uf.CE) return repository.findAllByUfAndAtivo(uf, SimNao.S).get(0);
        return repository.findServicoByUf(uf);
    }

    public ContaAzul findServicoSng(Uf uf) {
        if (uf == Uf.CE) return repository.findAllByUfAndAtivoSng(uf, SimNao.S).get(0);
        return repository.findServicoByUfSng(uf);
    }

    /**
     * Generates a random string with 16 chars
     **/
    public String generateRandomString() {
        Random r = new Random();
        StringBuilder b = new StringBuilder();
        char[] stringOptions = "abcdefghijklmnopqrstuvwxyz".toCharArray();

        for (int i = 0; i < 16; i++) {
            b.append(stringOptions[r.nextInt(stringOptions.length)]);
        }
        return b.toString();
    }

    /*
     * Builds the basic authentication header
     */
    private String getBasicAuthenticationHeader() {
        String credentialsToBeEncoded = ID + ':' + secret;
        return "Basic " + Base64.getEncoder().encodeToString(credentialsToBeEncoded.getBytes());
    }

    /*
     * Builds the token request body
     */
    public String getTokenUrl(String authorizationCode) throws UnsupportedEncodingException {
        UriComponentsBuilder builder = UriComponentsBuilder.fromUriString(CONTAAZUL_TOKEN_URL).queryParam("code", authorizationCode).queryParam("redirect_uri", REDIRECT_URL).queryParam("grant_type", "authorization_code");
        String url = builder.build().toUri().toString();
        System.out.println("URL GET TOKEN " + url);
        return url;
    }

    public ResponseEntity<ServicoContaAzulDTO[]> listServices(String accessToken, String codigo) {
        RestTemplate restTemplate = new RestTemplate();

        return restTemplate.exchange(getServiceListUrl(codigo), HttpMethod.GET, getAuthorizedRequestConfig(accessToken), ServicoContaAzulDTO[].class);
    }

    public ResponseEntity<ClienteContaAzulDTO[]> listClients(String accessToken, String documento) {
        RestTemplate restTemplate = new RestTemplate();

        return restTemplate.exchange(getClientListUrl(documento), HttpMethod.GET, getAuthorizedRequestConfig(accessToken), ClienteContaAzulDTO[].class);
    }

    @Override
    public void save(ContaAzul entity) throws ServiceException {
        validate(entity);
        super.save(entity);
    }

    private void validate(ContaAzul entity) throws ServiceException {
        if (entity.getAtivo() == null || StringUtils.isEmpty(entity.getCodigo()) || entity.getTipoContaAzul() == null) {
            throw new ServiceException("Preencha os campos obrigários para cadastro Conta Azul: ativo, código e tipo");
        }
        if (entity.getUf() == Uf.CE && entity.getQuatroRodas() == null && entity.getTipoContaAzul() == TipoContaAzul.SERVICO) {
            throw new ServiceException("Serviço para uf AP precisam possuir a informação de quatro rodas (sim ou não)");
        }
        // apenas um código por tipo (servico, venda, cliente etc)
        if (repository.codigoServicoJaRegistrado(entity.getTipoContaAzul(), entity.getCodigo(), entity.getId())) {
            throw new ServiceException(String.format("Código %s já registrado para o tipo %s", entity.getCodigo(), entity.getTipoContaAzul().getDescricao()));
        }
        if (entity.getTipoContaAzul() == TipoContaAzul.SERVICO && repository.tipoVeiculoServicoJaRegistrado(entity.getUf(), entity.getQuatroRodas(), entity.getId())) {
            throw new ServiceException(String.format("UF %s já possui código registrado para o tipo de veículo informado", entity.getUf()));
        }
    }

    public void createVenda(ServicoContaAzulDTO servico, ClienteContaAzulDTO cliente, Cobranca cobranca, String accessToken, String descricao, Date dataEmissao) throws ServiceException {
        RestTemplate restTemplate = new RestTemplate();

        HttpHeaders headers = new HttpHeaders();
        VendaContaAzulRequest request = new VendaContaAzulRequest();
        headers.setContentType(MediaType.APPLICATION_JSON);

        BigDecimal valorCredenciada = cobranca.getValorCredenciada();

        if (cobranca.getValorIntegraMais() != null) {
            valorCredenciada = valorCredenciada.add(cobranca.getValorIntegraMais());
        }

        if (cobranca.getValorDesconto() != null) {
            if (cobranca.getEstado() != Uf.CE)
                valorCredenciada = valorCredenciada.subtract(cobranca.getValorDesconto());

            logger.info("Valor da Credenciada dentro do desconto " + valorCredenciada);

            /*
             * enviado o valor já descontado conforme solicitado pelo nilton, futuramente,
             * se desejar que seja valor cheio e aplicar o desconto na conta azul
             * descomentar a linha abaixo e ajustar valor da cobranca para ignorar o
             * desconto (mandar valor cheio)
             *
             * DescontoVendaDTO desconto = new DescontoVendaDTO();
             * desconto.setMeasure_unit("VALUE");
             * desconto.setRate(cobranca.getValorDesconto()); request.setDiscount(desconto);
             */
        }

        request.setEmission(PlaceconUtil.getDateStringFromPattern(dataEmissao, "yyyy-MM-dd'T'HH:mm:ss.SSSXXX"));
        request.setStatus("COMMITTED");
        // request.setNumber(String.valueOf(cobranca.getId()));
        request.setCustomer_id(cliente.getId());
        request.setPayment(new PagamentoVendaContaAzulRequest());
        request.getPayment().setType("CASH");
        ParcelaVendaContaAzulRequest parc = new ParcelaVendaContaAzulRequest();

        Calendar c = Calendar.getInstance();
        // se estiver gerando no dia 10 ou posterior empurra para o proximo mes
        if (c.get(Calendar.DAY_OF_MONTH) >= 10) {
            c.add(Calendar.MONTH, 1);
        }
        c.set(Calendar.DAY_OF_MONTH, 10);

        parc.setDue_date(PlaceconUtil.getDateStringFromPattern(c.getTime(), "yyyy-MM-dd'T'HH:mm:ss.SSSXXX"));
        parc.setNumber(1);
        parc.setValue(valorCredenciada);
        request.getPayment().setInstallments(new ParcelaVendaContaAzulRequest[]{parc});

        if (cobranca.getEstado() == Uf.CE) {
            processaCeara(cobranca, request, valorCredenciada, parc);
        } else if (cobrancaService.isBancoDoBrasil(cobranca.getFinanceira().getDocumento())) {
            processarNotaDeCobrancaAgrupada(accessToken, cobranca, request);
        } else if (cobranca.getEstado() == Uf.BR) {
            processarNotaDeCobrancaUnificada(accessToken, cobranca, request);
        } else {
            ServicoVendaContaAzulRequest ser = new ServicoVendaContaAzulRequest();
            ser.setQuantity(cobranca.getQuantidadeRegistros().intValue());
            ser.setService_id(servico.getId());
            if (isNotaFiscalComNumeroDePedido(cobranca))
                ser.setDescription(descricao.concat("\n Pedido nº: ").concat(cobranca.getNumeroPedido()));
            else if (descricao != null && !descricao.trim().equals(""))
                ser.setDescription(descricao);

            BigDecimal valorEnvio = valorCredenciada.divide(new BigDecimal(cobranca.getQuantidadeRegistros()), 6, RoundingMode.HALF_UP);
            logger.info("Valor de envio " + valorEnvio);
            ser.setValue(valorEnvio);
            request.setServices(new ServicoVendaContaAzulRequest[]{ser});
        }

        try {
            ResponseEntity<VendaContaAzulResponse> responseEntity = restTemplate.postForEntity(CONTAAZUL_CREATE_SALE_URL, new HttpEntity<>(request, getAuthorizationHeader(accessToken)), VendaContaAzulResponse.class);
            if (responseEntity.getStatusCodeValue() != 201) {
                throw new ServiceException("Venda não gerada: status " + responseEntity.getStatusCodeValue());
            }

            if (cobrancaService.isBancoDoBrasil(cobranca.getFinanceira().getDocumento())) {
                cobrancaService.getCnpjsBancoBrasil()
                        .forEach(cnpj -> cobrancaService.buscarCobrancasBancoDoBrasil(cobranca).forEach(cob ->
                                cobrancaService.sentToContaAzul(cob, responseEntity.getBody().getNumber())));
            } else {
                cobrancaService.sentToContaAzul(cobranca, responseEntity.getBody().getNumber());
            }
        } catch (HttpClientErrorException e) {
            throw new ServiceException("Venda não gerada: " + e.getStatusCode() + ": " + e.getResponseBodyAsString());
        }
    }

    public void createVendaSng(ServicoContaAzulDTO servico, ClienteContaAzulDTO cliente, Cobranca cobranca, String accessToken, String descricao, Date dataEmissao) throws ServiceException {
        RestTemplate restTemplate = new RestTemplate();

        HttpHeaders headers = new HttpHeaders();
        VendaContaAzulRequest request = new VendaContaAzulRequest();
        headers.setContentType(MediaType.APPLICATION_JSON);

        BigDecimal valorCredenciada = cobranca.getValorCobrancaSng();

        request.setEmission(PlaceconUtil.getDateStringFromPattern(dataEmissao, "yyyy-MM-dd'T'HH:mm:ss.SSSXXX"));
        request.setStatus("COMMITTED");
        // request.setNumber(String.valueOf(cobranca.getId()));
        request.setCustomer_id(cliente.getId());
        request.setPayment(new PagamentoVendaContaAzulRequest());
        request.getPayment().setType("CASH");
        ParcelaVendaContaAzulRequest parc = new ParcelaVendaContaAzulRequest();

        Calendar c = Calendar.getInstance();
        // se estiver gerando no dia 10 ou posterior empurra para o proximo mes
        if (c.get(Calendar.DAY_OF_MONTH) >= 10) {
            c.add(Calendar.MONTH, 1);
        }
        c.set(Calendar.DAY_OF_MONTH, 10);

        parc.setDue_date(PlaceconUtil.getDateStringFromPattern(c.getTime(), "yyyy-MM-dd'T'HH:mm:ss.SSSXXX"));
        parc.setNumber(1);
        parc.setValue(valorCredenciada);
        request.getPayment().setInstallments(new ParcelaVendaContaAzulRequest[]{parc});

        if (cobranca.getEstado() == Uf.BR) {
            processarNotaDeCobrancaUnificadaSng(accessToken, cobranca, request);
        } else {
            ServicoVendaContaAzulRequest ser = new ServicoVendaContaAzulRequest();
            ser.setQuantity(cobranca.getQuantidadeRegistrosSng().intValue());
            ser.setService_id(servico.getId());
            if (isNotaFiscalComNumeroDePedido(cobranca))
                ser.setDescription(descricao.concat("\n Pedido nº: ").concat(cobranca.getNumeroPedido()));
            else if (descricao != null && !descricao.trim().equals(""))
                ser.setDescription(descricao);

            BigDecimal valorEnvio = valorCredenciada.divide(new BigDecimal(cobranca.getQuantidadeRegistrosSng()), 6, RoundingMode.HALF_UP);
            logger.info("Valor de envio " + valorEnvio);
            ser.setValue(valorEnvio);
            request.setServices(new ServicoVendaContaAzulRequest[]{ser});
        }

        try {
            ResponseEntity<VendaContaAzulResponse> responseEntity = restTemplate.postForEntity(CONTAAZUL_CREATE_SALE_URL, new HttpEntity<>(request, getAuthorizationHeader(accessToken)), VendaContaAzulResponse.class);
            if (responseEntity.getStatusCodeValue() != 201) {
                throw new ServiceException("Venda não gerada: status " + responseEntity.getStatusCodeValue());
            }

            cobrancaService.sentToContaAzulSng(cobranca, responseEntity.getBody().getNumber());
        } catch (HttpClientErrorException e) {
            throw new ServiceException("Venda não gerada: " + e.getStatusCode() + ": " + e.getResponseBodyAsString());
        }
    }

    private void processarNotaDeCobrancaUnificadaSng(String token, Cobranca cobranca, VendaContaAzulRequest request) {
        List<Cobranca> cobrancas = realizarCalculos(cobranca, cobranca.getFinanceira(), buscarListaDeUfs(cobranca));
        List<ServicoVendaContaAzulRequest> servicoVendaContaAzulRequestList = new ArrayList<>();
        for (Cobranca c : cobrancas) {
            logger.info("Iterando no estado: " + c.getEstado());
            ServicoContaAzulDTO servico = buscarServico(token, c.getEstado()).orElseThrow(() -> new ServiceException("Falha na busca do serviço do estado: " + c.getEstado()));
            ServicoVendaContaAzulRequest ser = new ServicoVendaContaAzulRequest();
            ser.setDescription("Registro eletrônico de contratos"
                    .concat(" - Detran/" + c.getEstado())
                    .concat(" \n Competência: " + formatarDataPadrao(cobranca.getDataInicio()) + " a " + formatarDataPadrao(cobranca.getDataFim())));
            if (isNotaFiscalComNumeroDePedido(cobranca))
                ser.setDescription(ser.getDescription().concat("\n Pedido nº: ").concat(c.getNumeroPedido()));

            ser.setQuantity(c.getQuantidadeRegistrosSng().intValue());
            ser.setService_id(servico.getId());
            logger.info("Valor Credenciada: " + (c.getValorCobrancaSng()));
            BigDecimal valorEnvio = (c.getValorCobrancaSng().divide(new BigDecimal(c.getQuantidadeRegistrosSng()), 6, RoundingMode.HALF_UP));
            logger.info("Quantidade de registros: " + c.getQuantidadeRegistrosSng());
            logger.info("Valor dividido pela quantidade de registros " + valorEnvio);
            ser.setValue(valorEnvio);
            if (valorEnvio.compareTo(BigDecimal.ZERO) > 0) {
                logger.info("Valor Maior que 0: " + valorEnvio);
                servicoVendaContaAzulRequestList.add(ser);
            }
        }
        servicoVendaContaAzulRequestList.forEach(s ->
                logger.info("ValorxQuantidade: " + s.getValue() + "*" + s.getQuantity() + " = " + s.getValue().multiply(new BigDecimal(s.getQuantity())))
        );
        ServicoVendaContaAzulRequest[] servicoArray = new ServicoVendaContaAzulRequest[servicoVendaContaAzulRequestList.size()];
        servicoArray = servicoVendaContaAzulRequestList.toArray(servicoArray);
        request.setServices(servicoArray);
    }

    private Set<Uf> buscarListaDeUfs(Cobranca cobranca) {
        Set<Uf> ufs = getUfsCredenciadasNaFinanceira(cobranca);
        Set<Uf> ufsCobranca = cobrancaRepository.findContratosByCobranca(cobranca).stream().map(Contrato::getUfRegistro).collect(Collectors.toSet());
        return ufs.stream().filter(ufsCobranca::contains).collect(Collectors.toSet());
    }

    private void processarNotaDeCobrancaAgrupada(String token, Cobranca cobranca, VendaContaAzulRequest request) {
        List<Cobranca> cobrancasBB = cobrancaService.buscarCobrancasBancoDoBrasil(cobranca);
        Cobranca c = cobrancaService.gerarCobrancaAgrupada(cobrancasBB);

        List<ServicoVendaContaAzulRequest> servicoVendaContaAzulRequestList = new ArrayList<>();

        ServicoContaAzulDTO servico = buscarServico(token, c.getEstado())
                .orElseThrow(() -> new ServiceException("Falha na busca do serviço do estado: " + c.getEstado()));

        ServicoVendaContaAzulRequest ser = new ServicoVendaContaAzulRequest();
        ser.setDescription(definirObservacaoCobrancaAgrupada(c, cobrancasBB));
        ser.setQuantity(c.getQuantidadeRegistros().intValue());
        ser.setService_id(servico.getId());

        logger.info("Valor Credenciada: " + (c.getValorCredenciada().subtract(c.getValorDesconto())));
        BigDecimal valorEnvio = (c.getValorCredenciada().subtract(c.getValorDesconto())).divide(new BigDecimal(c.getQuantidadeRegistros()), 6, RoundingMode.HALF_UP);
        logger.info("Quantidade de registros: " + c.getQuantidadeRegistros());
        logger.info("Valor dividido pela quantidade de registros " + valorEnvio);
        ser.setValue(valorEnvio);
        if (valorEnvio.compareTo(BigDecimal.ZERO) > 0) {
            logger.info("Valor Maior que 0: " + valorEnvio);
            servicoVendaContaAzulRequestList.add(ser);
        }

        servicoVendaContaAzulRequestList.forEach(s ->
            logger.info("ValorxQuantidade: " + s.getValue() + "*" + s.getQuantity() + " = " + s.getValue().multiply(new BigDecimal(s.getQuantity())))
        );

        ServicoVendaContaAzulRequest[] servicoArray = new ServicoVendaContaAzulRequest[servicoVendaContaAzulRequestList.size()];
        servicoArray = servicoVendaContaAzulRequestList.toArray(servicoArray);
        request.setServices(servicoArray);
        ParcelaVendaContaAzulRequest[] parcelas = request.getPayment().getInstallments();
        parcelas[0].setValue(c.getValorCredenciada().subtract(c.getValorDesconto()));
    }

    private String definirObservacaoCobrancaAgrupada(Cobranca cobranca, List<Cobranca> cobrancas) {
        StringBuilder obs = new StringBuilder();
        LocalDate dataAtual = LocalDate.now();
        DateTimeFormatter formato = DateTimeFormatter.ofPattern("MM/yyyy");
        Set<Uf> ufs = cobrancas.stream().map(Cobranca::getEstado).collect(Collectors.toSet());

        obs.append("Número do contrato: 202474213124;\n");
        obs.append("Objeto contratual: prestação de serviços para anotação de gravame;\n");
        obs.append("Mês da prestação dos serviços: ").append(dataAtual.format(formato)).append("\n \n");

        for (Financeira f : cobrancas.stream().map(Cobranca::getFinanceira).collect(Collectors.toSet())) {
            Cobranca cob = cobrancas.stream()
                    .filter(c -> c.getFinanceira().equals(f))
                    .findFirst()
                    .get();

            obs.append(rightPad(f.getNome() + ":", 32)).append("R$ ").append(cob.getValorCredenciada()).append("\n");

            long quantidadeRegistros = cobrancaRepository.setContratoCobrado(cobranca, f, new ArrayList<>(ufs), cobranca.getDataInicio(), cobranca.getDataFim());
            logger.info("Quantidade de Contratos da Cobranca -> " + quantidadeRegistros);
        }
        obs.append(rightPad("TOTAL:", 32)).append("R$ ").append(cobranca.getValorCredenciada()).append("\n\n");

        obs.append("Banco: 001 - Banco do Brasil\n");
        obs.append("Agência: 1004-9\n");
        obs.append("Conta Corrente: 24557-2\n");
        obs.append("Favorecido: Place Tecnologia e Inovação S.A.\n\n");

        obs.append(rightPad("IRRF:", 8)).append("4,80%\n");
        obs.append(rightPad("PIS:", 8)).append("0,65%\n");
        obs.append(rightPad("COFINS:", 8)).append("3,00%\n");
        obs.append(rightPad("CSLL:", 8)).append("1,00%");

        cobrancas.forEach(c -> c.setObservacao(obs.toString()));

        return obs.toString();
    }

    private void processarNotaDeCobrancaUnificada(String token, Cobranca cobranca, VendaContaAzulRequest request) {
        List<Cobranca> cobrancas = realizarCalculos(cobranca, cobranca.getFinanceira(), buscarListaDeUfs(cobranca));
        List<ServicoVendaContaAzulRequest> servicoVendaContaAzulRequestList = new ArrayList<>();
        for (Cobranca c : cobrancas) {
            logger.info("Iterando no estado: " + c.getEstado());
            ServicoContaAzulDTO servico = buscarServico(token, c.getEstado()).orElseThrow(() -> new ServiceException("Falha na busca do serviço do estado: " + c.getEstado()));
            ServicoVendaContaAzulRequest ser = new ServicoVendaContaAzulRequest();
            ser.setDescription("Registro eletrônico de contratos"
                    .concat(" - Detran/" + c.getEstado())
                    .concat(" \n Competência: " + formatarDataPadrao(cobranca.getDataInicio()) + " a " + formatarDataPadrao(cobranca.getDataFim())));
            if (isNotaFiscalComNumeroDePedido(cobranca))
                ser.setDescription(ser.getDescription().concat("\n Pedido nº: ").concat(c.getNumeroPedido()));

            ser.setQuantity(c.getQuantidadeRegistros().intValue());
            ser.setService_id(servico.getId());
            logger.info("Valor Credenciada: " + (c.getValorCredenciada().subtract(c.getValorDesconto())));
            BigDecimal valorEnvio = (c.getValorCredenciada().subtract(c.getValorDesconto())).divide(new BigDecimal(c.getQuantidadeRegistros()), 6, RoundingMode.HALF_UP);
            logger.info("Quantidade de registros: " + c.getQuantidadeRegistros());
            logger.info("Valor dividido pela quantidade de registros " + valorEnvio);
            ser.setValue(valorEnvio);
            if (valorEnvio.compareTo(BigDecimal.ZERO) > 0) {
                logger.info("Valor Maior que 0: " + valorEnvio);
                servicoVendaContaAzulRequestList.add(ser);
            }
        }
        servicoVendaContaAzulRequestList.forEach(s ->
            logger.info("ValorxQuantidade: " + s.getValue() + "*" + s.getQuantity() + " = " + s.getValue().multiply(new BigDecimal(s.getQuantity())))
        );
        ServicoVendaContaAzulRequest[] servicoArray = new ServicoVendaContaAzulRequest[servicoVendaContaAzulRequestList.size()];
        servicoArray = servicoVendaContaAzulRequestList.toArray(servicoArray);
        request.setServices(servicoArray);
    }

    private Optional<ServicoContaAzulDTO> buscarServico(String token, Uf uf) {
        ContaAzul conta = findServico(uf);
        ResponseEntity<ServicoContaAzulDTO[]> servico = listServices(token, conta.getCodigo());
        if (servico.getStatusCode() == HttpStatus.OK)
            return Objects.nonNull(servico.getBody()) && servico.getBody().length > 0 && Objects.nonNull(servico.getBody()[0]) ? Optional.of(servico.getBody()[0]) : Optional.empty();
        return Optional.empty();
    }

    private void processaCeara(Cobranca cobranca, VendaContaAzulRequest request, BigDecimal b, ParcelaVendaContaAzulRequest parc) {
        BigDecimal descontoMoto = new BigDecimal(0l);
        BigDecimal descontoCarro = new BigDecimal(0l);
        if (cobranca.getValorDesconto() != null && !cobranca.getValorDesconto().equals(BigDecimal.ZERO.setScale(2))) {
            CupomDesconto cupom = cupomDescontoService.findCuponsDisponiveis(cobranca).get(0);
            for (FaixaDesconto faixaDesconto : cupom.getFaixasDesconto()) {
                if (faixaDesconto.getPrecoComposto().getValorParametro().equals("2")) {
                    descontoMoto = faixaDesconto.getPercentual();
                } else if (faixaDesconto.getPrecoComposto().getValorParametro().equals("4")) {
                    descontoCarro = faixaDesconto.getPercentual();
                }
            }
        }

        ServicoVendaContaAzulRequest moto = new ServicoVendaContaAzulRequest();
        moto.setQuantity(cobrancaRepository.count2RodasByCobranca(cobranca).intValue());
        moto.setService_id("67384c76-5d23-414a-8714-dc9f00228baf");
        moto.setDescription("Registro de Contratos de Moto \n Competência: " + formatarDataPadrao(cobranca.getDataInicio()) + " a " + formatarDataPadrao(cobranca.getDataFim()));
        if (isNotaFiscalComNumeroDePedido(cobranca))
            moto.setDescription(moto.getDescription().concat(" - Detran/" + cobranca.getEstado()).concat(" Pedido: nº: ").concat(cobranca.getNumeroPedido()));
        BigDecimal valorMoto = credenciamentoService.findPrecoCompostoByCobrancaAndParametro(cobranca, "2").getValorCredenciada();
        moto.setValue(valorMoto.subtract(descontoMoto));

        ServicoVendaContaAzulRequest carro = new ServicoVendaContaAzulRequest();
        carro.setQuantity(cobrancaRepository.count4RodasByCobranca(cobranca).intValue());
        carro.setService_id("f78d1cab-9b37-4f80-a1ec-adb491d0e6a2");
        carro.setDescription("Registro de Contratos de Automóvel \n Competência: " + formatarDataPadrao(cobranca.getDataInicio()) + " a " + formatarDataPadrao(cobranca.getDataFim()));
        if (isNotaFiscalComNumeroDePedido(cobranca))
            carro.setDescription(carro.getDescription().concat(" - Detran/" + cobranca.getEstado()).concat(" Pedido: nº: ").concat(cobranca.getNumeroPedido()));
        BigDecimal valorCarro = credenciamentoService.findPrecoCompostoByCobrancaAndParametro(cobranca, "4").getValorCredenciada();
        carro.setValue(valorCarro.subtract(descontoCarro));

        if (carro.getQuantity() >= 1 && moto.getQuantity() >= 1) {
            logger.info("Carro e Moto maior que 1");
            request.setServices(new ServicoVendaContaAzulRequest[]{carro, moto});
            b = valorCarro.add(valorMoto);
        } else if (carro.getQuantity() >= 1 && moto.getQuantity() <= 0) {
            logger.info("Carro maior que 1");
            request.setServices(new ServicoVendaContaAzulRequest[]{carro});
            b = valorCarro;
        } else if (moto.getQuantity() >= 1 && carro.getQuantity() <= 0) {
            logger.info("Moto maior que 1");
            request.setServices(new ServicoVendaContaAzulRequest[]{moto});
            b = valorMoto;
        }

        parc.setValue(moto.getValue().multiply(new BigDecimal(moto.getQuantity())).add(carro.getValue().multiply(new BigDecimal(carro.getQuantity()))));
        logger.info("Valor Parcela" + parc.getValue());
    }

    /*
     * Builds the service list url
     */
    private String getServiceListUrl(String codigo) {
        UriComponentsBuilder builder = UriComponentsBuilder.fromUriString(CONTAAZUL_SERVICES_URL).queryParam("code", codigo).queryParam("size", "50");
        return builder.build().toUri().toString();
    }

    private String getClientListUrl(String documento) {
        UriComponentsBuilder builder = UriComponentsBuilder.fromUriString(CONTAAZUL_CLIENTS_URL).queryParam("search", documento).queryParam("size", "50");
        return builder.build().toUri().toString();
    }

    /*
     * In our sample application we redirect the user to the initial page, but you
     * don't need to to it. After getting the OAuth2 tokens, you can store them and
     * redirect the user to any of your application's page.
     */
    public String getHomeUrlWithTokens(HttpServletRequest request, TokenContaAzulDTO token) {
        String homeUrl = request.getScheme() + "://" + request.getServerName() + ":" + request.getServerPort();

        UriComponentsBuilder builder = UriComponentsBuilder.fromUriString(homeUrl).queryParam("access_token", token.getAccessToken()).queryParam("refresh_token", token.getRefreshToken());
        return builder.build().toUri().toString();
    }

    /*
     * Builds the get request for product list call
     */
    private HttpEntity<MultiValueMap<String, String>> getAuthorizedRequestConfig(String accessToken) {
        return new HttpEntity<MultiValueMap<String, String>>(null, getAuthorizationHeader(accessToken));
    }

    /*
     * Create authorization header for API calls
     */
    private MultiValueMap<String, String> getAuthorizationHeader(String accessToken) {
        HttpHeaders headers = new HttpHeaders();

        List<MediaType> acceptedTypes = new ArrayList<MediaType>();
        acceptedTypes.add(MediaType.APPLICATION_JSON);
        headers.setAccept(acceptedTypes);
        headers.add("Authorization", "Bearer " + accessToken);

        return headers;
    }

    private Boolean isNotaFiscalComNumeroDePedido(Cobranca cobranca) {
        return cnpjsNumeroPedido.contains(cobranca.getFinanceira().getDocumento()) && Objects.nonNull(cobranca.getNumeroPedido());
    }

    @Override
    public Page<ContaAzul> findAll(int first, int pageSize, ContaAzulDTO filter) {

        Specification<ContaAzul> spec = new Specification<ContaAzul>() {

            @Override
            public Predicate toPredicate(Root<ContaAzul> root, CriteriaQuery<?> cq, CriteriaBuilder cb) {
                cq.distinct(true);
                List<Predicate> predicates = new ArrayList<>();

                if (filter.getUf() != null) {
                    predicates.add(cb.equal(root.<Uf>get("uf"), filter.getUf()));
                }

                if (filter.getTipo() != null) {
                    predicates.add(cb.equal(root.<TipoContaAzul>get("tipoContaAzul"), filter.getTipo()));
                }

                if (filter.getDocumento() != null) {
                    predicates.add(cb.equal(root.<String>get("documento"), filter.getTipo()));
                }

                if (filter.getAtivo() != null) {
                    predicates.add(cb.equal(root.<SimNao>get("ativo"), filter.getAtivo()));
                }

                return andTogether(predicates, cb);
            }

            private Predicate andTogether(List<Predicate> predicates, CriteriaBuilder cb) {
                return cb.and(predicates.toArray(new Predicate[0]));
            }
        };
        return repository.findAll(spec, new PageRequest(first / pageSize, pageSize, new Sort(Direction.ASC, "codigo")));

    }

    private Set<Uf> getUfsCredenciadasNaFinanceira(Cobranca cobranca) {

        List<SituacaoFinanceiraEstado> situacoes = getSituacoes(cobranca);

        if (!PlaceconUtil.isListaVaziaOuNula(situacoes))
            return situacoes.stream().map(SituacaoFinanceiraEstado::getUf).collect(Collectors.toSet());

        return Collections.emptySet();

    }

    private List<SituacaoFinanceiraEstado> getSituacoes(Cobranca cobranca) {
        return situacaoFinanceiraEstadoRepository
                .findAllByFinanceira(
                        cobranca.getFinanceira()
                );
    }

    private List<Cobranca> realizarCalculos(Cobranca cobranca, Financeira financeira, Set<Uf> ufs) {
        return ufs.stream().map(uf -> {
            Cobranca cobrancaNova = new Cobranca(cobranca.getId(), uf, cobranca.getDataInicio(), cobranca.getDataFim(), financeira, SimNao.S);
            Optional<Cobranca> cobrancaOptional =
                    calcular(cobrancaNova);
            return cobrancaOptional.orElse(null);
        }).collect(Collectors.toList());
    }

    private Optional<Cobranca> calcular(Cobranca cobranca) {
        try {
            return Optional.ofNullable(calcularCobranca(cobranca));
        } catch (ServiceException e) {
            throw new RuntimeException(e);
        }
    }

    public Cobranca calcularCobranca(Cobranca cobranca) throws ServiceException {

        Optional<CalculadoraDeCobrancaStrategy> calculadora = filtrarInstanciaCalculadoraPorEstado(cobranca.getEstado());

        CalculadoraDeCobrancaStrategy calculadoraDeCobranca =
                calculadora
                        .orElseThrow(() -> new ServiceException("Não foi encontrado calculadora para o estado da Cobrança"));

        return calculadoraDeCobranca.calcularCobrancaUnificada(cobranca);

    }

    private Optional<CalculadoraDeCobrancaStrategy> filtrarInstanciaCalculadoraPorEstado(Uf uf) {
        return calculadoras.stream()
                .filter(c -> c.getUf().equals(uf))
                .findFirst();
    }


    @Override
    protected PagingAndSortingRepository<ContaAzul, Long> getRepository() {
        return repository;
    }

}
