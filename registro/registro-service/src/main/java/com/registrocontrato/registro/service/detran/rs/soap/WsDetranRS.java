package com.registrocontrato.registro.service.detran.rs.soap;

import com.registrocontrato.infra.entity.Financeira;
import com.registrocontrato.infra.entity.Uf;
import com.registrocontrato.infra.exception.ServiceException;
import com.registrocontrato.registro.entity.Contrato;
import com.registrocontrato.registro.entity.DocumentoArrecadacao;
import com.registrocontrato.registro.entity.TipoMensagem;
import com.registrocontrato.registro.entity.Veiculo;
import com.registrocontrato.registro.enums.TipoRestricao;
import com.registrocontrato.registro.repository.DocumentoArrecadacaoRepository;
import com.registrocontrato.registro.repository.MensagemRetornoRepository;
import com.registrocontrato.registro.service.RegistroEnvioService;
import com.registrocontrato.registro.service.detran.WsDetranDefault;
import com.registrocontrato.registro.service.detran.rs.client.ContratoRetorno;
import com.registrocontrato.registro.service.detran.rs.client.IncluirContrato;
import com.registrocontrato.registro.service.detran.rs.client.IncluirContratoResponse;
import com.registrocontrato.registro.service.detran.rs.client.TUf;
import com.registrocontrato.registro.service.dto.MensagemRetornoDTO;
import com.registrocontrato.registro.service.dto.RetornoGadELoteTarifasDTO;
import com.registrocontrato.registro.service.dto.RetornoStatusTaxaVeiculoDTO;
import com.registrocontrato.seguranca.repository.FinanceiraRepository;
import com.registrocontrato.seguranca.service.AcessoSenhaService;
import com.registrocontrato.seguranca.service.UsuarioService;
import com.registrocontrato.seguranca.service.dto.AcessoSenhaDTO;
import org.apache.http.client.HttpClient;
import org.apache.http.conn.ssl.NoopHostnameVerifier;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.ssl.SSLContextBuilder;
import org.apache.http.ssl.TrustStrategy;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.core.io.FileSystemResource;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.http.client.ClientHttpRequestFactory;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.oxm.jaxb.Jaxb2Marshaller;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.HttpServerErrorException;
import org.springframework.web.client.RestTemplate;
import org.springframework.ws.WebServiceMessage;
import org.springframework.ws.client.core.WebServiceMessageCallback;
import org.springframework.ws.client.core.support.WebServiceGatewaySupport;
import org.springframework.ws.soap.SoapMessage;
import org.springframework.ws.soap.client.SoapFaultClientException;
import org.springframework.ws.transport.http.HttpComponentsMessageSender;
import org.w3c.dom.Node;
import org.w3c.dom.NodeList;

import javax.net.ssl.HostnameVerifier;
import javax.net.ssl.SSLContext;
import javax.net.ssl.SSLSession;
import javax.xml.crypto.dsig.*;
import javax.xml.crypto.dsig.dom.DOMSignContext;
import javax.xml.crypto.dsig.keyinfo.KeyInfo;
import javax.xml.crypto.dsig.keyinfo.KeyInfoFactory;
import javax.xml.crypto.dsig.keyinfo.X509Data;
import javax.xml.crypto.dsig.spec.C14NMethodParameterSpec;
import javax.xml.crypto.dsig.spec.TransformParameterSpec;
import javax.xml.namespace.QName;
import javax.xml.soap.MessageFactory;
import javax.xml.soap.SOAPBody;
import javax.xml.soap.SOAPBodyElement;
import javax.xml.soap.SOAPMessage;
import javax.xml.transform.Transformer;
import javax.xml.transform.TransformerException;
import javax.xml.transform.TransformerFactory;
import javax.xml.transform.dom.DOMResult;
import javax.xml.transform.dom.DOMSource;
import javax.xml.xpath.XPath;
import javax.xml.xpath.XPathConstants;
import javax.xml.xpath.XPathFactory;
import java.io.*;
import java.security.*;
import java.security.cert.CertificateException;
import java.security.cert.X509Certificate;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.Objects;

import static com.registrocontrato.infra.util.PlaceconUtil.*;

public abstract class WsDetranRS extends WebServiceGatewaySupport implements WsDetranDefault {

    private static final String SOAP_SHEMA_LOCATION = "http://www.w3.org/2000/09/xmldsig#";

//    private static final Long COD_TARIFA = 7978L;

    @Value("${detran.rs.default.uri:null}")
    private String DEFAULT_URI;

    @Value("${detran.rs.default.uri.imagem:null}")
    private String URL_IMAGEM;

    @Value("${detran.rs.context.path:null}")
    private String CLIENT_PATH;

    @Value("${detran.readTimeout:20000}")
    private Integer readTimeout;

    @Value("${detran.connectionTimeout:20000}")
    private Integer connectionTimeout;

    @Value("${detran.certificados:null}")
    private String CERT_DIR;

    @Value("${file.dir-read:null}")
    private String FILE_DIR_READ;

    protected final MensagemRetornoRepository mensagemRetornoRepository;
    protected final FinanceiraRepository financeiraRepository;
    protected final DocumentoArrecadacaoRepository documentoArrecadacaoRepository;
    protected final RegistroEnvioService registroEnvioService;
    protected final AcessoSenhaService acessoSenhaservice;
    private final UsuarioService usuarioService;

    public WsDetranRS(MensagemRetornoRepository mensagemRetornoRepository,
                      FinanceiraRepository financeiraRepository,
                      DocumentoArrecadacaoRepository documentoArrecadacaoRepository,
                      RegistroEnvioService registroEnvioService,
                      AcessoSenhaService acessoSenhaservice, UsuarioService usuarioService) {
        this.mensagemRetornoRepository = mensagemRetornoRepository;
        this.financeiraRepository = financeiraRepository;
        this.documentoArrecadacaoRepository = documentoArrecadacaoRepository;
        this.registroEnvioService = registroEnvioService;
        this.acessoSenhaservice = acessoSenhaservice;
        this.usuarioService = usuarioService;
    }

    public abstract RetornoGadELoteTarifasDTO listaGadeLoteTarifas(Financeira financeira, Date dataInicio, Date dataFim);

    public abstract RetornoStatusTaxaVeiculoDTO consultaStatusTaxasVeiculo(Contrato contrato, String numeroChassi);

    abstract Object callSoap(Object request, String cnpjFinanceira);

    @Bean
    public HttpComponentsMessageSender httpComponentsMessageSender() {
        HttpComponentsMessageSender sender = new HttpComponentsMessageSender();
        sender.setReadTimeout(readTimeout);
        sender.setConnectionTimeout(connectionTimeout);
        return sender;
    }

    class NullHostnameVerifier implements HostnameVerifier {
        public boolean verify(String hostname, SSLSession session) {
            return true;
        }
    }

    protected Jaxb2Marshaller marshaller() {
        Jaxb2Marshaller marshaller = new Jaxb2Marshaller();
        marshaller.setContextPath(CLIENT_PATH);
        return marshaller;
    }

    @Override
    public MensagemRetornoDTO comunicarContratoFinanceiroVeiculo(Contrato contrato, Veiculo veiculo) {
        long start = System.nanoTime();
        com.registrocontrato.registro.service.detran.rs.client.Contrato contratoEnvio = null;
        IncluirContratoResponse response = null;

        IncluirContrato incluirContrato = new IncluirContrato();
        try {
            contratoEnvio = putRequest(contrato, veiculo);
            incluirContrato.setContrato(contratoEnvio);
            getWebServiceTemplate().setMessageSender(httpComponentsMessageSender());
            getWebServiceTemplate().setMarshaller(marshaller());
            getWebServiceTemplate().setUnmarshaller(marshaller());

            response = (IncluirContratoResponse) callSoap(incluirContrato, contrato.getFinanceira().getDocumento());
            MensagemRetornoDTO mensagemRetorno = getSucesso(Uf.RS);

            ContratoRetorno contratoResponse = response.getContratoResponse();
            if (contratoResponse.getLinhaDigitavel() != null && contratoResponse.getDataValidade() != null) {
                DocumentoArrecadacao doc = new DocumentoArrecadacao();
                doc.setEstado(contrato.getUfRegistro());
                doc.setVencimento(stringToDateDataPadrao(contratoResponse.getDataValidade()));
                doc.setLinhaDigitavel(String.valueOf(contratoResponse.getLinhaDigitavel()));
                doc.setVeiculo(veiculo);
                doc.setNossoNumero(contratoResponse.getChaveInterna());
                documentoArrecadacaoRepository.save(doc);
            }

            veiculo.setNumeroRegistroDetran(String.valueOf(contratoResponse.getChaveInterna()));
            mensagemRetorno.setNumeroDetran(veiculo.getNumeroRegistroDetran());
            return mensagemRetorno;
        } catch (SoapFaultClientException e) {
            logger.error(e.getFaultStringOrReason(), e);
            MensagemRetornoDTO mensagemRetorno = getErroPadrao(contrato.getUfRegistro());
            mensagemRetorno.setDescricao(e.getFaultStringOrReason());
            return mensagemRetorno;
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return getErroPadrao(contrato.getUfRegistro());
        } finally {
            registrarLog(contrato, start, incluirContrato, response);
        }
    }

    protected com.registrocontrato.registro.service.detran.rs.client.Contrato putRequest(
            Contrato contrato, Veiculo veiculo) {
        com.registrocontrato.registro.service.detran.rs.client.Contrato contratoEnvio = new com.registrocontrato.registro.service.detran.rs.client.Contrato();
        com.registrocontrato.registro.service.detran.rs.client.Contrato.InfContrato infContrato = new com.registrocontrato.registro.service.detran.rs.client.Contrato.InfContrato();
        Financeira financeira = financeiraRepository.findOne(contrato.getFinanceira().getId());


        infContrato.setId("contrato" + limparString(contrato.getNumeroContrato()));
        infContrato.setCnpjAgente(retiraFormatacao(financeira.getDocumento()));

        //TODO adiciona controle para aditivo e alterarcao caso necessário
        infContrato.setNroContrato(contrato.getNumeroContrato());

        //Dados Devedor
        infContrato.setCpfCnpjFinanciado(contrato.getCpfCnpjDevedorFinanciado());
        infContrato.setNomeFinanciado(contrato.getNomeDevedorFinanciado());
        infContrato.setEndRuaFinanciado(contrato.getEnderecoDevedor()); //está certo ?
        infContrato.setEndNroFinanciado(contrato.getNumeroEnderecoDevedor());
        infContrato.setEndComplFinanciado(contrato.getComplementoEnderecoDevedor());
        infContrato.setEndBairroFinanciado(contrato.getBairroDevedor());
        infContrato.setCepFinanciado(contrato.getCepDevedor());
        infContrato.setMunicipioFinanciado(contrato.getMunicipioDevedor().getDescricao());
        infContrato.setUfFinanciado(TUf.fromValue(contrato.getUfEnderecoDevedor().name()));
        infContrato.setDddFinanciado(String.valueOf(contrato.getDddDevedor()));
        infContrato.setTelefoneFinanciado(String.valueOf(contrato.getTelefoneDevedor()));


        //Veiculo
        infContrato.setChassi(veiculo.getNumeroChassi());
        if (veiculo.getUf() != null) {
            infContrato.setUfPlaca(TUf.fromValue(veiculo.getUf().name()));
        }
        if (veiculo.getPlaca() != null) {
            infContrato.setPlaca(veiculo.getPlaca());
        }

        String tipoRestricao = geraCodTipoRestricao(contrato);
        infContrato.setTipoRestricao(tipoRestricao);
        infContrato.setSamdContrato(formataData(contrato.getDataContrato()));
        infContrato.setQtdParcela(String.valueOf(contrato.getQuantidadeMeses()));
        infContrato.setTxJuroAno(contrato.getValorTaxaJurosMes() != null ? contrato.getValorTaxaJurosMes().setScale(3).toString() : null);
        infContrato.setTxJuroAno(contrato.getValorTaxaJurosAno() != null ? contrato.getValorTaxaJurosAno().setScale(3).toString() : null);
        infContrato.setVlrTxContrato(contrato.getValorTaxaContrato() != null ? contrato.getValorTaxaContrato().setScale(2).toString() : null);
        infContrato.setVlrFinanciado(contrato.getValorTotalDivida().setScale(2).toString());
        infContrato.setSamdVenctoParcelaInicial(formataData(contrato.getDataVencimentoPrimeiraParcela()));
        infContrato.setSamdVenctoParcelaFinal(formataData(contrato.getDataVencimentoUltimaParcela()));
        infContrato.setSamdLibCredito(formataData(contrato.getDataLiberacaoCredito()));
        infContrato.setUfLibCredito(TUf.fromValue(contrato.getUfLiberacaoCredito().name()));
        if (contrato.getMunicipioLiberacao() != null)
            infContrato.setMunLibCredito(contrato.getMunicipioLiberacao().getCodigoDenatran().substring(0, 4));
        infContrato.setIndices(contrato.getSiglaIndiceFinaceiro().getSigla());
        infContrato.setNroGrupoCons(contrato.getNumeroGrupoConsorcio());
        if (contrato.getNumeroCotaConsorcio() != null) {
            infContrato.setNroCotaCons(String.valueOf(contrato.getNumeroCotaConsorcio()));
        }
//        infContrato.setNroAdtvCons(null);
        infContrato.setSamdAditivo(formataData(contrato.getDataAditivoContrato()));
        infContrato.setTxMulta(contrato.getValorTaxaMulta() != null ? contrato.getValorTaxaMulta().setScale(3).toString() : null);
        infContrato.setTxMora(contrato.getValorTaxaMoraDia() != null ? contrato.getValorTaxaMoraDia().setScale(3).toString() : null);
        infContrato.setPenalidade(contrato.getDescricaoPenalidade());
        infContrato.setComissao(contrato.getIndicadorComissao() ? contrato.getPercentualComissao().setScale(3).toString() : null);
//        infContrato.setNumeroCND(null);
        infContrato.setEmailAgente(financeira.getEmailRepresentante());
        infContrato.setEmailFinanciado(contrato.getEmailDevedor());

        contratoEnvio.setInfContrato(infContrato);

        return contratoEnvio;
    }

    private String geraCodTipoRestricao(Contrato contrato) {
        String tipoRestricao = null;
        if (TipoRestricao.ARRENDAMENTO.equals(contrato.getTipoRestricao())) {
            tipoRestricao = "1";
        } else if (TipoRestricao.RESERVA_DOMINIO.equals(contrato.getTipoRestricao())) {
            tipoRestricao = "2";
        } else if (TipoRestricao.PENHOR.equals(contrato.getTipoRestricao())) {
            // Bloquear na Tela ?
        } else {
            tipoRestricao = "3";
        }
        return tipoRestricao;
    }

    protected WebServiceMessageCallback webServiceMessageCallback(X509Certificate x509Certificate, PrivateKey privateKey) {
        return new WebServiceMessageCallback() {
            @Override
            public void doWithMessage(final WebServiceMessage message) throws IOException, TransformerException {
                try {
                    try (ByteArrayOutputStream stream = new ByteArrayOutputStream()) {
                        ((SoapMessage) message).writeTo(stream);

                        InputStream is = new ByteArrayInputStream(stream.toByteArray());
                        SOAPMessage request = MessageFactory.newInstance().createMessage(null, is);

                        SOAPMessage soapMessage = signSoap(request, x509Certificate, privateKey);

                        Transformer identityTransform = TransformerFactory.newInstance().newTransformer();
                        DOMResult domResult = new DOMResult();
                        identityTransform.transform(new DOMSource(soapMessage.getSOAPBody().getFirstChild()), domResult);

                        Node bodyContent = domResult.getNode();
                        identityTransform.transform(new DOMSource(bodyContent), message.getPayloadResult());
                    }
                } catch (final Exception e) {
                    logger.error(e);
                }
            }
        };
    }

    private SOAPMessage signSoap(SOAPMessage soapMessage, X509Certificate cert, PrivateKey privateKey) throws Exception {
        SOAPBody soapBody = soapMessage.getSOAPBody();

        XPath xpath = XPathFactory.newInstance().newXPath();
        NodeList nodeList = (NodeList) xpath.compile("//*[@Id]")
                .evaluate(soapBody, XPathConstants.NODESET);
        if (nodeList.getLength() != 1) {
            throw new Exception("None or multiple elements found");
        }

        Node nodeToSign = nodeList.item(0);
        SOAPBodyElement soapBodyElement = (SOAPBodyElement) nodeToSign;
        soapBodyElement.setIdAttributeNode(soapBodyElement.getAttributeNode("Id"), true);
        soapBodyElement.addAttribute(
                new QName(
                        "http://www.w3.org/2001/XMLSchema-instance",
                        "schemaLocation",
                        "xsi"
                ),
                SOAP_SHEMA_LOCATION);

        XMLSignatureFactory sigFactory = XMLSignatureFactory.getInstance("DOM", new org.apache.jcp.xml.dsig.internal.dom.XMLDSigRI());

        ArrayList<Transform> transforms = new ArrayList<>();
        transforms.add(sigFactory.newTransform(Transform.ENVELOPED, (TransformParameterSpec) null));
        transforms.add(sigFactory.newTransform(CanonicalizationMethod.INCLUSIVE, (TransformParameterSpec) null));

        Reference ref = sigFactory.newReference(
                "#" + soapBodyElement.getAttributeNode("Id").getTextContent(),
                sigFactory.newDigestMethod(DigestMethod.SHA1, null),
                transforms, null, null
        );

        SignedInfo signedInfo = sigFactory.newSignedInfo(
                sigFactory.newCanonicalizationMethod(CanonicalizationMethod.INCLUSIVE, (C14NMethodParameterSpec) null),
                sigFactory.newSignatureMethod(SignatureMethod.RSA_SHA1, null),
                Collections.singletonList(ref)
        );

        KeyInfoFactory keyinfoFactory = sigFactory.getKeyInfoFactory();

        ArrayList<Object> x509Content = new ArrayList<>();
        x509Content.add(cert);
        X509Data x509Data = keyinfoFactory.newX509Data(x509Content);
        KeyInfo keyInfo = keyinfoFactory.newKeyInfo(Collections.singletonList(x509Data));
        DOMSignContext dsc = new DOMSignContext(privateKey, nodeToSign.getParentNode());
        XMLSignature signature = sigFactory.newXMLSignature(signedInfo, keyInfo);
        signature.sign(dsc);
        soapMessage.saveChanges();

        return soapMessage;
    }

    @Override
    public MensagemRetornoDTO envioImagem(Veiculo veiculo, String referenciaArquivo) {
        Long start = System.currentTimeMillis();
        Contrato contrato = veiculo.getContrato();
        MensagemRetornoDTO mensagemRetorno = new MensagemRetornoDTO();
        mensagemRetorno.setUf(Uf.RS);
        mensagemRetorno.setTipoMensagem(TipoMensagem.DETRAN);

        MultiValueMap<String, Object> body = new LinkedMultiValueMap<>();
        String responseEntity = null;

        try {
            RestTemplate restTemplate = new RestTemplate(getClientHttpRequestFactoryWithSSL(contrato.getFinanceira()));

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.MULTIPART_FORM_DATA);

            body.add("cnpjAgente", contrato.getFinanceira().getDocumento());
            body.add("nroContrato", contrato.getNumeroContrato());
            body.add("chassi", veiculo.getNumeroChassi());
            body.add("arquivo", new FileSystemResource(getFileDirRead() + referenciaArquivo));

            HttpEntity<MultiValueMap<String, Object>> request = new HttpEntity<>(body, headers);
            ResponseEntity<String> response = restTemplate.postForEntity(URL_IMAGEM, request, String.class);
            responseEntity = response.getBody();

            mensagemRetorno.setSucesso(response.getStatusCode().is2xxSuccessful());
            mensagemRetorno.setDescricao(response.getBody());
            return mensagemRetorno;

        } catch (HttpClientErrorException | HttpServerErrorException e) {
            logger.error("Falha ao enviar imagem - Detran RS: " + e.getMessage());

            responseEntity = e.getResponseBodyAsString();
            mensagemRetorno.setSucesso(e.getStatusCode().is2xxSuccessful());
            mensagemRetorno.setDescricao(e.getResponseBodyAsString());
            return mensagemRetorno;
        } finally {
            body.remove("arquivo");
            registrarLog(contrato, start, body, responseEntity);
        }

    }

    protected ClientHttpRequestFactory getClientHttpRequestFactoryWithSSL(Financeira fin) {
        try {
            AcessoSenhaDTO acessoSenhaDTO = acessoSenhaservice.recuperarAcessoDetranFinanceira(Uf.CE, fin);
            if (Objects.isNull(acessoSenhaDTO) || Objects.isNull(acessoSenhaDTO.getReferenciaArquivo())) {
                throw new ServiceException("Não existem credenciais configuradas para o agente financeiro.");
            }

            String password = acessoSenhaDTO.getAcessos()[1];
            File certKeyStore = new File(CERT_DIR + acessoSenhaDTO.getReferenciaArquivo());
            TrustStrategy acceptingTrustStrategy = (X509Certificate[] chain, String authType) -> true;
            SSLContext sslContext = SSLContextBuilder.create()
                    .loadKeyMaterial(certKeyStore, password.toCharArray(), password.toCharArray())
                    .loadTrustMaterial(acceptingTrustStrategy).build();

            HttpClient client = HttpClients.custom().setSSLContext(sslContext).setSSLHostnameVerifier(NoopHostnameVerifier.INSTANCE).build();
            HttpComponentsClientHttpRequestFactory requestFactory = new HttpComponentsClientHttpRequestFactory(client);
            requestFactory.setConnectTimeout(connectionTimeout);
            requestFactory.setReadTimeout(connectionTimeout);
            return requestFactory;
        } catch (UnrecoverableKeyException | CertificateException | NoSuchAlgorithmException | KeyStoreException | IOException | KeyManagementException e) {
            throw new ServiceException("Falha na leitura do certificado.");
        }
    }

    @Override
    public RegistroEnvioService getRegistroEnvioService() {
        return registroEnvioService;
    }

    @Override
    public UsuarioService getUsuarioService() {
        return usuarioService;
    }

    @Override
    public String getUrl() {
        return DEFAULT_URI;
    }

    public String getClientPath() {
        return CLIENT_PATH;
    }

    public Integer getReadTimeout() {
        return readTimeout;
    }

    public Integer getConnectionTimeout() {
        return connectionTimeout;
    }

    public String getCertDir() {
        return CERT_DIR;
    }

    public String getFileDirRead() {
        return FILE_DIR_READ;
    }

    public AcessoSenhaService getAcessoSenhaService() {
        return acessoSenhaservice;
    }

    @Override
    public MensagemRetornoRepository getMensagemRetornoRepository() {
        return mensagemRetornoRepository;
    }

    @Override
    public Uf getUf() {
        return Uf.RS;
    }
}
