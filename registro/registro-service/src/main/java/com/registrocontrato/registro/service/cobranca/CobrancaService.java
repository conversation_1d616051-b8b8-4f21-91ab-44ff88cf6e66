package com.registrocontrato.registro.service.cobranca;

import com.registrocontrato.infra.entity.*;
import com.registrocontrato.infra.exception.ServiceException;
import com.registrocontrato.infra.relatorio.RelatorioData;
import com.registrocontrato.infra.relatorio.RelatorioDefault;
import com.registrocontrato.infra.service.BaseService;
import com.registrocontrato.infra.util.PlaceconUtil;
import com.registrocontrato.registro.entity.*;
import com.registrocontrato.registro.enums.PagamentoCobranca;
import com.registrocontrato.registro.enums.SituacaoCobranca;
import com.registrocontrato.registro.enums.TipoCobranca;
import com.registrocontrato.registro.enums.TipoVeiculo;
import com.registrocontrato.registro.repository.CobrancaJobRepository;
import com.registrocontrato.registro.repository.CobrancaRepository;
import com.registrocontrato.registro.repository.CredenciamentoRepository;
import com.registrocontrato.registro.repository.SituacaoFinanceiraEstadoRepository;
import com.registrocontrato.registro.service.ContratoService;
import com.registrocontrato.registro.service.CredenciamentoService;
import com.registrocontrato.registro.service.CupomDescontoService;
import com.registrocontrato.registro.service.ManipulaRelatorioService;
import com.registrocontrato.registro.service.cobranca.boleto.BoletoDeCobrancaStrategy;
import com.registrocontrato.registro.service.cobranca.boleto.CobrancaComBoletoDeReembolso;
import com.registrocontrato.registro.service.cobranca.calculadora.CalculadoraDeCobrancaStrategy;
import com.registrocontrato.registro.service.dto.CobrancaDTO;
import com.registrocontrato.registro.service.util.NomeadorBoletosUtil;
import com.registrocontrato.seguranca.entity.Perfil;
import com.registrocontrato.seguranca.entity.Usuario;
import com.registrocontrato.seguranca.service.FinanceiraService;
import com.registrocontrato.seguranca.service.UsuarioService;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.time.DateFormatUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.apache.poi.hssf.util.HSSFColor;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.data.repository.PagingAndSortingRepository;
import org.springframework.stereotype.Service;

import javax.persistence.criteria.*;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.*;
import java.util.stream.Collectors;

import static com.registrocontrato.infra.util.PlaceconUtil.formataValorMonetario;
import static com.registrocontrato.registro.service.util.NomeadorBoletosUtil.gerarNumeroBoletoSng;
import static javax.faces.context.FacesContext.getCurrentInstance;

@Service
public class CobrancaService extends BaseService<Cobranca, CobrancaDTO> {

    private static final long serialVersionUID = 1L;

    private final String fileNfDir;

    private final String fileDir;

    private final String fileNfDirRead;

    private final CobrancaRepository cobrancaRepository;

    private final UsuarioService usuarioService;

    private final CobrancaValidator cobrancaValidator;

    private final EnviaCobrancaEmail enviaCobrancaEmail;

    private final BoletoService boletoService;

    private final FinanceiraService financeiraService;

    private final ManipulaRelatorioService manipulaRelatorioService;

    private final ContratoService contratoService;

    private final CredenciamentoRepository credenciamentoRepository;

    private final CupomDescontoService cupomDescontoService;

    private final List<CalculadoraDeCobrancaStrategy> calculadoras;

    private final List<BoletoDeCobrancaStrategy> geradorDeBoletos;

    private final List<String> cnpjsNumeroPedido = Arrays.asList("02916265000160");

    private final List<String> cnpjsBancoBrasil = Arrays.asList("06043050000132", "31546476000156", "00000000000191");

    private final SituacaoFinanceiraEstadoRepository situacaoFinanceiraEstadoRepository;

    private final RelatorioDefault relatorioDefault;

    private final CredenciamentoService credenciamentoService;

    private List<Uf> estadosLinhaDigitavel = Arrays.asList(Uf.SC, Uf.PE, Uf.PI, Uf.MS, Uf.PR, Uf.SE);

    public CobrancaService(
            @Value("${file-nf.dir:null}") String fileNfDir,
            @Value("${file-boleto.dir:null}") String fileDir,
            @Value("${file-nf.dir-read:null}") String fileNfDirRead,
            EnviaCobrancaEmail enviaCobrancaEmail,
            CobrancaRepository cobrancaRepository,
            CobrancaValidator cobrancaValidator,
            UsuarioService usuarioService,
            BoletoService boletoService,
            FinanceiraService financeiraService,
            ManipulaRelatorioService manipulaRelatorioService,
            List<BoletoDeCobrancaStrategy> geradorDeBoletos,
            ContratoService contratoService,
            CredenciamentoRepository credenciamentoRepository,
            CupomDescontoService cupomDescontoService,
            List<CalculadoraDeCobrancaStrategy> calculadoras,
            SituacaoFinanceiraEstadoRepository situacaoFinanceiraEstadoRepository,
            RelatorioDefault relatorioDefault,
            CredenciamentoService credenciamentoService) {
        this.fileNfDir = fileNfDir;
        this.fileDir = fileDir;
        this.fileNfDirRead = fileNfDirRead;
        this.enviaCobrancaEmail = enviaCobrancaEmail;
        this.cobrancaRepository = cobrancaRepository;
        this.cobrancaValidator = cobrancaValidator;
        this.usuarioService = usuarioService;
        this.boletoService = boletoService;
        this.financeiraService = financeiraService;
        this.manipulaRelatorioService = manipulaRelatorioService;
        this.geradorDeBoletos = geradorDeBoletos;
        this.contratoService = contratoService;
        this.credenciamentoRepository = credenciamentoRepository;
        this.cupomDescontoService = cupomDescontoService;
        this.calculadoras = calculadoras;
        this.situacaoFinanceiraEstadoRepository = situacaoFinanceiraEstadoRepository;
        this.relatorioDefault = relatorioDefault;
        this.credenciamentoService = credenciamentoService;
    }


    public void saveFile(InputStream stream, String numeroDocumento, String fileDir) throws ServiceException {
        File targetFile = new File(fileDir, numeroDocumento);
        if (targetFile.exists()) {
            targetFile.delete();
        }
        try {
            FileUtils.copyInputStreamToFile(stream, targetFile);
        } catch (IOException e) {
            logger.error(e);
            throw new ServiceException(e);
        }
    }

    public void saveNotaFiscal(Cobranca cobranca, InputStream stream) throws ServiceException {
        String numeroDocumento = "NF_" + PlaceconUtil.leftPad(String.valueOf(cobranca.getId()), 10, "0") + ".pdf";
        saveFile(stream, numeroDocumento, fileNfDir);
        cobranca.setNotaFiscal(numeroDocumento);
        cobrancaRepository.save(cobranca);
    }

    public void saveXmlNotaFiscal(Cobranca cobranca, InputStream stream) throws ServiceException {
        String numeroDocumento = "XMLNF_" + PlaceconUtil.leftPad(String.valueOf(cobranca.getId()), 10, "0") + ".xml";
        saveFile(stream, numeroDocumento, fileNfDir);
        cobranca.setXmlNotaFiscal(numeroDocumento);
        cobrancaRepository.save(cobranca);
    }

    public void saveNotaReembolso(Cobranca cobranca, InputStream stream) throws ServiceException {
        String numeroDocumento = "NF_RE_" + PlaceconUtil.leftPad(String.valueOf(cobranca.getId()), 10, "0") + ".pdf";
        saveFile(stream, numeroDocumento, fileNfDir);
        cobranca.setNotaReembolso(numeroDocumento);
        cobrancaRepository.save(cobranca);
    }

    public void saveBoletoCredenciada(Cobranca cobranca, InputStream stream) throws ServiceException {
        String numeroDocumento = PlaceconUtil.leftPad(String.valueOf(cobranca.getId()), 11, "0") + ".pdf";
        saveFile(stream, numeroDocumento, fileDir);
        cobranca.setBoletoEmitido(true);
        cobrancaRepository.save(cobranca);
    }

    public void saveBoletoSng(Cobranca cobranca, InputStream stream) throws ServiceException {
        String numeroDocumento = gerarNumeroBoletoSng(cobranca.getId()) + ".pdf";
        saveFile(stream, numeroDocumento, fileDir);
        cobranca.setBoletoSngEmitido(true);
        cobrancaRepository.save(cobranca);
    }

    public void saveNFSng(Cobranca cobranca, InputStream stream) throws ServiceException {
        String numeroDocumento = "NF_SNG_" + PlaceconUtil.leftPad(String.valueOf(cobranca.getId()), 10, "0") + ".pdf";
        saveFile(stream, numeroDocumento, fileNfDir);
        cobranca.setNotaFiascalSng(numeroDocumento);
        cobrancaRepository.save(cobranca);
    }

    public void saveBoletoDetran(Cobranca cobranca, InputStream stream, Boolean segundoBoleto) throws ServiceException {
        String numDocumento;
        if (segundoBoleto == Boolean.TRUE) {
            cobranca.setBoletoDetranManual(true);
            numDocumento = NomeadorBoletosUtil.
                    gerarNumeroBoletoDetranCarregadoManualmente(cobranca.getDataInicio(), cobranca.getId());
        } else {
            cobranca.setBoletoReembolsoEmitido(true);
            numDocumento = NomeadorBoletosUtil.gerarNumeroBoletoReembolso(cobranca.getDataInicio(), cobranca.getId());
        }

        saveFile(stream, numDocumento + ".pdf", fileDir);
        cobrancaRepository.save(cobranca);
    }

    public void saveBoletoDetranCobrancaUnificada(Cobranca cobranca, InputStream stream, Boolean boletoFinanceiroPlace) throws ServiceException {

        String numDocumento;
        if (boletoFinanceiroPlace) {
            cobranca.setBoletoDetranUnificadaFinanceiroPlace(true);
            numDocumento = NomeadorBoletosUtil.
                    gerarNumeroBoletoDetranCarregadoManualmenteParaFinanceiro(cobranca.getDataInicio(), cobranca.getId());
        } else {
            cobranca.setBoletoDetranUnificadaCliente(true);
            numDocumento = NomeadorBoletosUtil.
                    gerarNumeroBoletoDetranCarregadoManualmenteParaCliente(cobranca.getDataInicio(), cobranca.getId());
        }

        saveFile(stream, numDocumento + ".pdf", fileDir);
        cobrancaRepository.save(cobranca);
    }

    public void save(List<Cobranca> cobrancas) throws ServiceException {
        for (Cobranca cobranca : cobrancas) {
            cobrancaValidator.validateValoresCobranca(cobranca, cnpjsNumeroPedido);
            marcarRegistrosDeContratoComCobranca(cobrancaRepository.save(cobranca));
        }
    }

    /**
     * marca a cobranca como enviada para a conta azul
     *
     * @param cobranca
     */
    public void sentToContaAzul(Cobranca cobranca, String numeroVenda) {
        cobranca.setNumeroVenda(numeroVenda);
        cobranca.setNotaAzul(Boolean.TRUE);
        getRepository().save(cobranca);
    }

    public void sentToContaAzulSng(Cobranca cobranca, String numeroVenda) {
        cobranca.setNumeroVendaSng(numeroVenda);
        cobranca.setNotaAzulSng(Boolean.TRUE);
        getRepository().save(cobranca);
    }

    private void marcarRegistrosDeContratoComCobranca(Cobranca cobranca) throws ServiceException {

        cobrancaValidator.validaQuantidadeDeRegistrosNaGeracao(cobranca);

        long quantidadeRegistros = cobrancaRepository.setContratoCobrado(cobranca, cobranca.getFinanceira(), Arrays.asList(cobranca.getEstado()), cobranca.getDataInicio(), cobranca.getDataFim());
        long quantidadeRegistrosSng = cobrancaRepository.setContratoCobradoSng(cobranca, cobranca.getFinanceira(), Arrays.asList(cobranca.getEstado()), cobranca.getDataInicio(), cobranca.getDataFim());

        logger.info("Quantidade de Contratos da Cobranca -> " + quantidadeRegistros);
        logger.info("Quantidade de ContratosRsng da Cobranca -> " + quantidadeRegistrosSng);

        if (cobranca.getTipoCobranca() == TipoCobranca.CONTRATO) {
            quantidadeRegistros = cobranca.getContratos().size();
        }

        cobrancaValidator.validaQuantidadeDeRegistros(cobranca, quantidadeRegistros);
    }

    @Override
    public void save(Cobranca cobranca) throws ServiceException {
        if (cobranca.isUnificada()) {
            salvarCobrancaUnificada(cobranca);
        } else {
            salvarCobrancaIndividual(cobranca);
        }
    }

    private void salvarCobrancaIndividual(Cobranca cobranca) {
        cobrancaValidator.validarGravacao(cobranca, cnpjsNumeroPedido);
        setarInformacoesAdicionaisCobranca(cobranca);
        emitirBoletos(cobranca);
        setCobrancaPaga(cobranca);
        atributosCobrancaSeGerada(cobranca);
        if (cobranca.isEnviada())
            enviarEmail(cobranca);
        super.save(cobranca);
        setCobrancaPaga(cobranca);
        atributosCobrancaSeGerada(cobranca);
    }

    public void setCobrancaPaga(Cobranca cobranca) {
        if (cobranca.isPaga()) {
            cobrancaRepository.setContratoPago(cobranca);
        }
    }

    private void gerarBoleto(BoletoDeCobrancaStrategy gerador, Cobranca cobranca) {
        try {
            gerador.emitirBoleto(cobranca);
        } catch (Exception e) {
            logger.error("Erro ao gerar o boleto: " + cobranca.getEstado());
            e.printStackTrace();
            throw new RuntimeException(e);
        }
    }

    private File getFileNF(Cobranca entity) {
        File nf = null;
        if (entity.getNotaFiscal() != null) {
            nf = new File(fileNfDirRead + entity.getNotaFiscal());
        }
        return nf;
    }

    private File getFileXmlNF(Cobranca entity) {
        File nf = null;
        if (entity.getXmlNotaFiscal() != null) {
            nf = new File(fileNfDirRead + entity.getXmlNotaFiscal());
        }
        return nf;
    }

    private File getFileNotaReembolso(Cobranca entity) {
        File nr = null;
        if (entity.getNotaReembolso() != null) {
            //precisa utilizar o diretorio de escrita pois será enviado por email imediatamente após a criação da mesma
            nr = new File(fileNfDir + entity.getNotaReembolso());
        }
        return nr;
    }

    private Date getDataVencimentoBoleto(SituacaoFinanceiraEstado situacaoFinanceiraEstado) throws ServiceException {
        Integer dia = situacaoFinanceiraEstado.getDiaCobranca();
        if (Objects.isNull(dia)) dia = 10;
        return extrairDataDaCobranca(dia);
    }

    private Date extrairDataDaCobranca(Integer dia) {

        Calendar c = Calendar.getInstance();
        if (c.get(Calendar.DAY_OF_MONTH) >= dia) {
            c.add(Calendar.MONTH, 1);
        }

        c.set(Calendar.DAY_OF_MONTH, dia);

        return c.getTime();
    }

    protected void setarInformacoesAdicionaisCobranca(Cobranca cobranca) throws ServiceException {

        SituacaoFinanceiraEstado situacaoFinanceiraEstado = getSituacaoFinanceiraByEstadoAndFinanceira(cobranca);
        Date dataVencimento = getDataVencimentoBoleto(situacaoFinanceiraEstado);
        cobranca.setDataVencimentoBoleto(dataVencimento);
        cobranca.setDataVencimentoReembolsoBoleto(dataVencimento);
        cobranca.setReembolsoLinhaDigitavel(situacaoFinanceiraEstado.getReembolsoLinhaDigitavel());

    }

    public void enviaEmailCobrancaEmitidoFinanceiroPlace(Cobranca entity) {
        boolean cobrancaSemBoleto = (entity.getFinanceira().getCobrancaSemBoleto() != null && entity.getFinanceira().getCobrancaSemBoleto().equals(SimNao.S));

        if (cobrancaSemBoleto || entity.getBoletoEmitido()) {
            //boleto place
            File fileBoletoRegistradora = extractFileBoletoRegistradora(entity);
            Calendar.getInstance().setTime(entity.getDataInicio());
            File fileBoletoReembolso = null;

            if (entity.getTokenReembolsoBoleto() != null) {
                fileBoletoReembolso = extractBoletoReembolso(entity);
            }

            enviaCobrancaEmail.sendResumoEmail(entity, fileBoletoRegistradora, fileBoletoReembolso, null, getFileNF(entity), getFileNotaReembolso(entity), null, getFileXmlNF(entity), SimNao.N, true);
        } else {
            logger.info("E-mail não enviado, porque o boleto não foi emitido.");
        }
    }

    private void enviarEmail(Cobranca cobranca) {
        try {
            logger.info("Iniciando envio de email...");
            enviaEmailCobrancaEmitidoFinanceiras(cobranca);
        } catch (Exception e) {
            logger.error("Não foi possível encaminhar e-mail");
            logger.error(e.getMessage());
        }
    }

    private void enviarEmailUnificada(Cobranca cobranca) {
        boolean cobrancaSemBoleto = (cobranca.getFinanceira().getCobrancaSemBoleto() != null && cobranca.getFinanceira().getCobrancaSemBoleto().equals(SimNao.S));

        if (cobranca != null && (cobrancaSemBoleto || cobranca.getBoletoEmitido() || isBancoDoBrasil(cobranca.getFinanceira().getDocumento()))) {
            Calendar.getInstance().setTime(cobranca.getDataInicio());
            File fileBoletoRegistradora = extractFileBoletoRegistradora(cobranca);
            File fileBoletoReembolso = null;
            File fileBoletoDetranFinanceiroPlace = null;
            File fileBoletoDetranCliente = null;

            if (cobranca.getTokenReembolsoBoleto() != null) {
                fileBoletoReembolso = extractBoletoReembolso(cobranca);
            }

            if (Objects.nonNull(cobranca.getBoletoDetranUnificadaCliente()) && cobranca.getBoletoDetranUnificadaCliente()) {
                logger.info("Vai extrair e enviar boleto para o cliente realizar o pagamento");
                fileBoletoDetranCliente = extractBoletoDetranUnificadaCliente(cobranca);
            }

            if (Objects.nonNull(cobranca.getBoletoDetranUnificadaFinanceiroPlace()) && cobranca.getBoletoDetranUnificadaFinanceiroPlace()) {
                logger.info("Vai extrair e enviar boleto para o financeiro da place realizar o pagamento");
                fileBoletoDetranFinanceiroPlace = extractBoletoDetranUnificadaFinanceiroPlace(cobranca);
            }


            if (isBancoDoBrasil(cobranca.getFinanceira().getDocumento())) {
                logger.info("Enviar email para o banco do Brasil");

                 enviaCobrancaEmail.sendResumoEmailCobrancaBancoDoBrasil
                        (cobranca,
                                fileBoletoRegistradora,
                                fileBoletoReembolso,
                                fileBoletoDetranCliente,
                                getFileNF(cobranca),
                                getFileNotaReembolso(cobranca),
                                fileBoletoDetranFinanceiroPlace,
                                getFileXmlNF(cobranca)

                        );
            } else {
                logger.info("Enviar email de cobrança unificada");

                enviaCobrancaEmail.
                        sendResumoEmailCobrancaUnificada
                                (cobranca,
                                        fileBoletoRegistradora,
                                        fileBoletoReembolso,
                                        fileBoletoDetranCliente,
                                        getFileNF(cobranca),
                                        getFileNotaReembolso(cobranca),
                                        fileBoletoDetranFinanceiroPlace,
                                        getFileXmlNF(cobranca)
                                );
            }

        } else {
            logger.info("E-mail não enviado, porque o boleto não foi emitido.");
        }
    }


    public void enviaEmailCobrancaEmitido(Cobranca entity, TipoDestinatarioCobranca tipoDestinatario) throws IOException {
        if (tipoDestinatario.equals(TipoDestinatarioCobranca.FINANCEIRAS)) {
            enviaEmailCobrancaEmitidoFinanceiras(entity);
        } else if (tipoDestinatario.equals(TipoDestinatarioCobranca.FINANCEIRO_PLACE)) {
            enviaEmailCobrancaEmitidoFinanceiroPlace(entity);
        }
    }


    public void enviaEmailCobrancaEmitidoFinanceiras(Cobranca entity) throws IOException {
        boolean cobrancaSemBoleto = (entity.getFinanceira().getCobrancaSemBoleto() != null && entity.getFinanceira().getCobrancaSemBoleto().equals(SimNao.S));

        if (cobrancaSemBoleto || entity.getBoletoEmitido() || isBancoDoBrasil(entity.getFinanceira().getDocumento())) {
            File fileBoletoRegistradora = extractFileBoletoRegistradora(entity);
            Calendar.getInstance().setTime(entity.getDataInicio());
            File fileBoletoReembolso = null;
            File fileBoletoLinhaDigitavel = null;
            File fileBoletoDetran = null;

            if (entity.getTokenReembolsoBoleto() != null) {
                fileBoletoReembolso = extractBoletoReembolso(entity);
            }

            if (estadosLinhaDigitavel.contains(entity.getEstado())) {
                fileBoletoLinhaDigitavel = extractBoletoLinhaDigitavel(entity);
            }

            if (entity.getBoletoDetranManual() == Boolean.TRUE) {
                fileBoletoDetran = extractBoletoDetran(entity);
            }

            enviaCobrancaEmail.sendResumoEmail(entity, fileBoletoRegistradora, fileBoletoReembolso, fileBoletoDetran, getFileNF(entity), getFileNotaReembolso(entity), fileBoletoLinhaDigitavel, getFileXmlNF(entity), entity.getReembolsoLinhaDigitavel(), false);
        } else {
            logger.info("E-mail não enviado, porque o boleto não foi emitido.");
        }
    }

    protected SituacaoFinanceiraEstado getSituacaoFinanceiraByEstadoAndFinanceira(Cobranca cobranca) throws ServiceException {
        SituacaoFinanceiraEstado situacao = financeiraService.findSituacaoFinanceiraEstado(cobranca.getEstado(), cobranca.getFinanceira());
        if (Objects.isNull(situacao)) {
            throw new ServiceException(String.format("Financeira %s não possui uma situação financeira para a uf %s", cobranca.getFinanceira().getNome(), cobranca.getEstado()));
        }
        return situacao;
    }

    public void atributosCobrancaSeGerada(Cobranca entity) {
        if (entity.isGerada()) {
            entity.setTokenBoleto(null);
            entity.setBoletoEmitido(Boolean.FALSE);
            entity.setBoletoReembolsoEmitido(Boolean.FALSE);
            entity.setNotaRembolsoEmitida(Boolean.FALSE);
            entity.setNotaReembolso(null);
        }
    }

    private File extractFileBoletoRegistradora(Cobranca entity) {
        File targetFile = null;
        try {
            InputStream stream = Files.newInputStream(Paths.get(fileDir + PlaceconUtil.leftPad(String.valueOf(entity.getId()), 11, "0") + ".pdf"));
            if (stream != null) {
                targetFile = File.createTempFile(PlaceconUtil.leftPad(String.valueOf(entity.getId()), 11, "0"), ".pdf");
                FileUtils.copyInputStreamToFile(stream, targetFile);
            }
        } catch (Exception e) {
            logger.error("Arquivo do boleto nao encontrato.");
        }
        return targetFile;
    }

    private File extractBoletoLinhaDigitavel(Cobranca entity) {
        File fileBoletoLinhaDigitavel = null;
        String numDocumento = NomeadorBoletosUtil.getNumeroDocumentoBoletoDetran(entity.getDataInicio(), entity.getId());
        try {
            InputStream streamLinhaDigitavel = Files.newInputStream(Paths.get(fileDir + numDocumento + ".pdf"));
            fileBoletoLinhaDigitavel = File.createTempFile(numDocumento, ".pdf");
            FileUtils.copyInputStreamToFile(streamLinhaDigitavel, fileBoletoLinhaDigitavel);
            return fileBoletoLinhaDigitavel;
        } catch (IOException ioException) {
            logger.error("Não foi possível encontrar o arquivo da linha digitável: " + fileDir + numDocumento + ".pdf da cobrança: " + entity.getId());
        }
        return null;
    }

    private File extractBoletoReembolso(Cobranca entity) {
        try {
            String numDocumento = NomeadorBoletosUtil.gerarNumeroBoletoReembolso(entity.getDataInicio(), entity.getId());
            InputStream streamReembolso = Files.newInputStream(Paths.get(fileDir + numDocumento + ".pdf"));
            File fileBoletoReembolso = File.createTempFile(numDocumento, ".pdf");
            FileUtils.copyInputStreamToFile(streamReembolso, fileBoletoReembolso);
            return fileBoletoReembolso;
        } catch (IOException e) {
            return null;
        }
    }

    private File extractBoletoDetran(Cobranca entity) {
        try {
            String numDocumento = NomeadorBoletosUtil.gerarNumeroBoletoReembolso(entity.getDataInicio(), entity.getId()) + "b";
            InputStream streamReembolso = Files.newInputStream(Paths.get(fileDir + numDocumento + ".pdf"));
            File fileBoletoReembolso = File.createTempFile(numDocumento, ".pdf");
            FileUtils.copyInputStreamToFile(streamReembolso, fileBoletoReembolso);
            return fileBoletoReembolso;
        } catch (IOException e) {
            return null;
        }
    }

    private File extractBoletoDetranUnificadaFinanceiroPlace(Cobranca entity) {
        try {
            String numDocumento = NomeadorBoletosUtil.gerarNumeroBoletoDetranCarregadoManualmenteParaFinanceiro(entity.getDataInicio(), entity.getId());
            InputStream streamReembolso = Files.newInputStream(Paths.get(fileDir + numDocumento + ".pdf"));
            File fileBoletoReembolso = File.createTempFile(numDocumento, ".pdf");
            FileUtils.copyInputStreamToFile(streamReembolso, fileBoletoReembolso);
            return fileBoletoReembolso;
        } catch (IOException e) {
            return null;
        }
    }

    private File extractBoletoDetranUnificadaCliente(Cobranca entity) {
        try {
            String numDocumento = NomeadorBoletosUtil.gerarNumeroBoletoDetranCarregadoManualmenteParaCliente(entity.getDataInicio(), entity.getId());
            InputStream streamReembolso = Files.newInputStream(Paths.get(fileDir + numDocumento + ".pdf"));
            File fileBoletoReembolso = File.createTempFile(numDocumento, ".pdf");
            FileUtils.copyInputStreamToFile(streamReembolso, fileBoletoReembolso);
            return fileBoletoReembolso;
        } catch (IOException e) {
            return null;
        }
    }

    public Cobranca calcularCobranca(Cobranca cobranca) throws ServiceException {

        Optional<CalculadoraDeCobrancaStrategy> calculadora = filtrarInstanciaCalculadoraPorEstado(cobranca.getEstado());

        CalculadoraDeCobrancaStrategy calculadoraDeCobranca =
                calculadora
                        .orElseThrow(() -> new ServiceException("Não foi encontrado calculadora para o estado da Cobrança"));

        return calculadoraDeCobranca.calcularCobranca(cobranca);

    }

    private Optional<CalculadoraDeCobrancaStrategy> filtrarInstanciaCalculadoraPorEstado(Uf uf) {
        return calculadoras.stream()
                .filter(c -> c.getUf().equals(uf))
                .findFirst();
    }

    private Optional<BoletoDeCobrancaStrategy> filtrarInstanciaGeradorDeBoletoPorEstado(Cobranca cobranca) {
        return geradorDeBoletos.stream()
                .filter(c -> {
                    if (!isBancoDoBrasil(cobranca.getFinanceira().getDocumento()))
                        return c.getUf().equals(cobranca.getEstado());
                    return strategyBB(c.getCnpjFinanceiras(), cobranca.getFinanceira().getDocumento());
                })
                .findFirst();
    }

    private boolean strategyBB(List<String> cnpjs, String docValido) {
        Optional<String> busca = cnpjs.stream()
                .filter(s -> s.equals(docValido))
                .findAny();
        return busca.isPresent();
    }

    public Long count2RodasByCobrancaAndUf(Cobranca cobranca) {
        return cobrancaRepository.count2RodasByCobrancaAndUf(cobranca, cobranca.getEstado());
    }

    public Long count4RodasByCobrancaAndUf(Cobranca cobranca) {
        return cobrancaRepository.count4RodasByCobrancaAndUf(cobranca, cobranca.getEstado());
    }

    public Long countByFinanceirasVeiculos2Rodas(Financeira financeira, Uf uf, Date dataInicio, Date dataFim) {
        dataInicio = PlaceconUtil.minDateTime(dataInicio);
        dataFim = PlaceconUtil.maxDateTime(dataFim);
        return cobrancaRepository.countByFinanceirasVeiculos2Rodas(financeira, uf, dataInicio, dataFim);
    }

    public Long countByFinanceirasVeiculos4Rodas(Financeira financeira, Uf uf, Date dataInicio, Date dataFim) {
        dataInicio = PlaceconUtil.minDateTime(dataInicio);
        dataFim = PlaceconUtil.maxDateTime(dataFim);
        return cobrancaRepository.countByFinanceirasVeiculos4Rodas(financeira, uf, dataInicio, dataFim);
    }

    public Long countByFinanceirasVeiculos2RodasOfCe(Financeira financeira, Uf uf, Date dataInicio, Date dataFim) {
        dataInicio = PlaceconUtil.minDateTime(dataInicio);
        dataFim = PlaceconUtil.maxDateTime(dataFim);
        return cobrancaRepository.countByFinanceirasVeiculos2Rodas(financeira, uf, dataInicio, dataFim);
    }

    public Long countByFinanceirasVeiculos4RodasOfCe(Financeira financeira, Uf uf, Date dataInicio, Date dataFim) {
        dataInicio = PlaceconUtil.minDateTime(dataInicio);
        dataFim = PlaceconUtil.maxDateTime(dataFim);
        return cobrancaRepository.countByFinanceirasVeiculos4Rodas(financeira, uf, dataInicio, dataFim);
    }

    public List<PrecoComposto> buscaPrecoComposto(Uf uf) {
        return credenciamentoRepository.findPrecoCompostoByUf(uf);
    }

    public void cancelar(Long id) throws ServiceException {
        Cobranca cobranca = findOne(id);
        if (cobranca.getSituacaoCobranca() != SituacaoCobranca.GERADA) {
            throw new ServiceException("Não é possível cancelar uma cobrança faturada, enviada ou paga.");
        }

        if (!cobranca.getContratos().isEmpty()) {
            int quantidadeRegistros = cobrancaRepository.setCobrancaContratoCancelada(id);

            if (cobranca.getContratos().size() != quantidadeRegistros) {
                throw new ServiceException("Erro no cancelamento da cobrança.");
            }
        }
        cobrancaRepository.delete(id);
    }

    public Long countContratosByFinanceiraAndUfRegistro(Cobranca cobranca, Financeira financeira, Uf uf, Date dataInicio, Date dataFim) {
        dataInicio = PlaceconUtil.minDateTime(dataInicio);
        dataFim = PlaceconUtil.maxDateTime(dataFim);
        return cobrancaRepository.countContratosByFinanceiraAndUfRegistro(cobranca, financeira, uf, dataInicio, dataFim);
    }

    public Long countContratosByFinanceiraAndUfRegistroAditivo(Cobranca cobranca, Financeira financeira, Uf uf, Date dataInicio, Date dataFim) {
        dataInicio = PlaceconUtil.minDateTime(dataInicio);
        dataFim = PlaceconUtil.maxDateTime(dataFim);
        return cobrancaRepository.countContratosByFinanceiraAndUfRegistroAditivo(cobranca, financeira, uf, dataInicio, dataFim);
    }

    public Long countVeiculosByFinanceiraAndUfRegistro(Cobranca cobranca, Financeira financeira, Uf uf, Date dataInicio, Date dataFim) {
        dataInicio = PlaceconUtil.minDateTime(dataInicio);
        dataFim = PlaceconUtil.maxDateTime(dataFim);
        return cobrancaRepository.countVeiculosByFinanceiraAndUfRegistro(cobranca, financeira, uf, dataInicio, dataFim);
    }

    public Long countVeiculosByFinanceiraAndUfRegistroAditivo(Cobranca cobranca, Financeira financeira, Uf uf, Date dataInicio, Date dataFim) {
        dataInicio = PlaceconUtil.minDateTime(dataInicio);
        dataFim = PlaceconUtil.maxDateTime(dataFim);
        return cobrancaRepository.countVeiculosByFinanceiraAndUfRegistroAditivo(cobranca, financeira, uf, dataInicio, dataFim);
    }

    public Optional<List<Financeira>> findByFinanceirasCobrancaAberta(Uf uf, Date dataInicio, Date dataFim) {
        dataInicio = PlaceconUtil.minDateTime(dataInicio);
        dataFim = PlaceconUtil.maxDateTime(dataFim);
        return Optional.of(cobrancaRepository.findByFinanceirasCobrancaAberta(uf, dataInicio, dataFim));
    }

    public Long countVeiculosBaixaSngByFinanceiraAndUfRegistro(Financeira financeira, Uf uf, Date dataInicio, Date dataFim) {
        return cobrancaRepository.countVeiculosBaixaSngByFinanceiraAndUfRegistro(financeira, uf, dataInicio, dataFim) +
                cobrancaRepository.countVeiculosBaixaByFinanceiraAndUfRegistro(financeira, uf, dataInicio, dataFim);
    }

    public Long countVeiculosCancelamentoBaixaSngByFinanceiraAndUfRegistro(Financeira financeira, Uf uf, Date dataInicio, Date dataFim) {
        return cobrancaRepository.countVeiculosCancelamentoBaixaSngByFinanceiraAndUfRegistro(financeira, uf, dataInicio, dataFim) +
                cobrancaRepository.countVeiculosCancelamentoBaixaByFinanceiraAndUfRegistro(financeira, uf, dataInicio, dataFim);
    }

    public void removeItem(Long idCobranca, Long numeroRegistroEletronico, String usuario) throws ServiceException {
        Cobranca cobranca = findOne(idCobranca);

        if (cobranca.getContratos().size() == 1) {
            throw new ServiceException("Essa cobrança só possui um contrato. Exclua a cobrança por completo.");
        }

        Contrato contrato = contratoService.findByNumeroRegistroEletronico(numeroRegistroEletronico, usuario);
        if (!cobranca.getContratos().contains(contrato)) {
            throw new ServiceException("Essa cobrança não possui o registro " + numeroRegistroEletronico);
        }
        //TODO: Cálculos só funcionam quando o TipoCobranca = VEICULO
        BigDecimal valorTotal = cobranca.getCredenciamento().getValorTotal();
        BigDecimal valorCredenciada = cobranca.getCredenciamento().getValorCredenciada();
        BigDecimal valorDETRAN = cobranca.getCredenciamento().getValorDETRAN();
        BigDecimal valorDETRANAditivo = cobranca.getCredenciamento().getValorAditivo();
        int qtdChassis = contrato.getVeiculos().size();
        BigDecimal qtdRegistros = new BigDecimal(qtdChassis);

        if (cobranca.getValorDesconto() != null && !cobranca.getValorDesconto().equals(BigDecimal.ZERO)) {
            BigDecimal valorDescontoUnitario = cobranca.getValorDesconto().divide(new BigDecimal(cobranca.getQuantidadeRegistros()));
            cobranca.setValorDesconto(cobranca.getValorDesconto().subtract(valorDescontoUnitario.multiply(qtdRegistros)));
        }

        if (contrato.getDataAditivoContrato() == null) {
            cobranca.setQuantidadePrincipal(cobranca.getQuantidadePrincipal() - qtdChassis);
            cobranca.setValorDetranPrincipal(valorDETRAN.multiply(new BigDecimal(cobranca.getQuantidadePrincipal())));
        } else {
            cobranca.setQuantidadeAditivo(cobranca.getQuantidadeAditivo() - qtdChassis);
            cobranca.setValorDetranAditivo(valorDETRANAditivo.multiply(new BigDecimal(cobranca.getQuantidadeAditivo())));
        }
        cobranca.setQuantidadeRegistros(cobranca.getQuantidadeRegistros() - qtdChassis);

        cobranca.setValorCobranca(cobranca.getValorCobranca().subtract(valorTotal.multiply(qtdRegistros)));
        cobranca.setValorCredenciada(cobranca.getValorCredenciada().subtract(valorCredenciada.multiply(qtdRegistros)));
        cobranca.setValorDetran(cobranca.getValorDetran().subtract(valorDETRAN.multiply(qtdRegistros)));

        cobrancaRepository.save(cobranca);
        contrato.setCobranca(null);
        contratoService.save(contrato);
    }


    public Long countContratosByUfRegistro(List<Financeira> financeiras, Uf uf) {
        Date dataFim = new Date();
        Date dataInicio = DateUtils.truncate(dataFim, Calendar.MONTH);
        if (financeiras != null) {
            return cobrancaRepository.countContratosByFinanceirasUfRegistro(financeiras, uf, dataInicio, dataFim);
        }
        return cobrancaRepository.countContratosByUfRegistro(uf, dataInicio, dataFim);
    }

    public Long countVeiculosByUfRegistro(List<Financeira> financeiras, Uf uf) {
        Date dataFim = new Date();
        Date dataInicio = DateUtils.truncate(dataFim, Calendar.MONTH);
        if (financeiras != null) {
            return cobrancaRepository.countVeiculosByFinanceirasUfRegistro(financeiras, uf, dataInicio, dataFim);
        }
        return cobrancaRepository.countVeiculosByUfRegistro(uf, dataInicio, dataFim);
    }

    public BigDecimal calcularCobranca(List<Financeira> financeiras, Uf uf, String tipoValorCobranca) {
        Date dataFim = new Date();
        Date dataInicio = DateUtils.truncate(dataFim, Calendar.MONTH);
        Credenciamento credenciamento = credenciamentoRepository.findByAtivo(uf, dataFim);
        Long qtdVeiculos = 0L;

        if (credenciamento == null) {
            return BigDecimal.ZERO;
        }

        //Desconto
        BigDecimal valorDesconto = cupomDescontoService.getValorDescontoPeriodo(uf, dataInicio, dataFim);

        if (credenciamento.getTipoCobranca() == TipoCobranca.CONTRATO) {
            qtdVeiculos = countContratosByUfRegistro(financeiras, uf);
        } else {
            qtdVeiculos = countVeiculosByUfRegistro(financeiras, uf);
        }
        BigDecimal valor = calcularCobrancaTipoValor(tipoValorCobranca, credenciamento, qtdVeiculos);
        if (valorDesconto.doubleValue() > 0) {
            valor = valor.subtract(valorDesconto);
        }
        return valor;
    }

    private BigDecimal calcularCobrancaTipoValor(String tipoValorCobranca, ValoresCobranca credenciamento, Long qtdVeiculos) {
        if (tipoValorCobranca == null) {
            return credenciamento.getValorTotal().multiply(new BigDecimal(qtdVeiculos));
        } else if (tipoValorCobranca.startsWith("D")) {
            return credenciamento.getValorDETRAN().multiply(new BigDecimal(qtdVeiculos));
        } else if (tipoValorCobranca.startsWith("C")) {
            return credenciamento.getValorCredenciada().multiply(new BigDecimal(qtdVeiculos));
        }
        return BigDecimal.ZERO;
    }

    public Cobranca findByFinanceiraQtdRegistrosEstado(String cnpj, Uf uf, Long qtdRegistros, BigDecimal valor) {
        return cobrancaRepository.findByFinanceiraQtdRegistrosEstado(cnpj, uf, qtdRegistros, valor);
    }

    public void enviarCobranca(Cobranca c) throws ServiceException {
        c.setSituacaoCobranca(SituacaoCobranca.ENVIADA);
        save(c);
    }

    public List<String> getCnpjsNumeroPedido() {
        return cnpjsNumeroPedido;
    }

    private void fecharLoading() {
        String viewId = getCurrentInstance().getViewRoot().getViewId().replace("/", "_");
        getCurrentInstance().getExternalContext().addResponseCookie(
                org.primefaces.util.Constants.DOWNLOAD_COOKIE + viewId,
                "true",
                Collections.<String, Object>emptyMap()
        );
    }

    private void fecharPlanilha(XSSFWorkbook workbook, HttpServletResponse response) throws IOException {
        ServletOutputStream out = response.getOutputStream();
        workbook.write(out);
        workbook.close();

        getCurrentInstance().responseComplete();
    }

    public File relatorioGrupoSafraPorEmail(Cobranca cobranca, XSSFWorkbook workbook) throws IOException {
        FileOutputStream outputStream = null;
        File arquivo = new File(cobranca.getId() + "-RegistrosCobranca.xlsx");
        try {
            outputStream = new FileOutputStream(arquivo);
            planilhaSafra(cobranca, workbook);
            workbook.write(outputStream);
        } catch (Exception ex) {
            ex.printStackTrace();
        } finally {
            outputStream.close();
            workbook.close();
        }
        return arquivo;
    }

    public void relatorioGrupoSafra(Cobranca cobranca, HttpServletResponse response) throws IOException {
        try (XSSFWorkbook workbook = new XSSFWorkbook()) {
            planilhaSafra(cobranca, workbook);
            fecharLoading();
            fecharPlanilha(workbook, response);
        }
    }

    private void planilhaSafra(Cobranca cobranca, XSSFWorkbook workbook) throws IOException {
        // Usa o método createUniqueSheet para garantir um nome único para a planilha
        Sheet sheet = createUniqueSheet(workbook, "PLACECON - Registro de Contrato");
        sheet.setDefaultColumnWidth(23);

        CellStyle style = manipulaRelatorioService.createStyle(workbook);
        int rowCount = 0;
        rowCount = manipulaRelatorioService.createHeader(workbook, sheet, style, rowCount);

        style = manipulaRelatorioService.createStyle(workbook);
        style.setFillForegroundColor(HSSFColor.HSSFColorPredefined.GREY_25_PERCENT.getIndex());
        manipulaRelatorioService.createTableGrupoSafra(sheet, style, rowCount, cobranca);
    }

    public List<Cobranca> buscarCobrancasBancoDoBrasil(Cobranca cobranca) {
        return getCnpjsBancoBrasil().stream().map(documento -> {
            Optional<Cobranca> cob = cobrancaRepository.findByFinanceiraEstadoData(documento, cobranca.getEstado(), cobranca.getDataInicio());
            return cob.orElseThrow(() -> new ServiceException("Gerar todas as cobranças do Banco do Brasil para gerar a NF"));
        }).collect(Collectors.toList());
    }

    public void relatorioAgrupadoBancoDoBrasil(Cobranca cobranca) throws IOException {
        long start = System.currentTimeMillis();
        List<RelatorioData> relatorios = buscarCobrancasBancoDoBrasil(cobranca).stream()
                .map(this::relatorioBancoDoBrasil)
                .collect(Collectors.toList());

        String filePath = relatorioDefault.excelComVariasAbas(
                "PLACECON - Relatório de notas de serviço - Banco do Brasil.xlsx",
                relatorios);

        relatorioDefault.downloadExcel(filePath);
        long end = System.currentTimeMillis();
        getCurrentInstance().responseComplete();
        logger.info(String.format("Tempo de processamento do relatorio do Banco do Brasil: %s", end - start));
    }

    public RelatorioData relatorioBancoDoBrasil(Cobranca cobranca) {
        Map<String, Integer> headerNames = new LinkedHashMap<>();
        headerNames.put("Nome do Devedor", 45);
        headerNames.put("Chassi", 20);
        headerNames.put("Número do Contrato", 20);
        headerNames.put("UF do registro", 15);
        headerNames.put("Data do Registro no DETRAN", 30);
        headerNames.put("Valor Credenciada", 22);

        List<Veiculo> veiculos = contratoService.findByCobranca(cobranca);
        Set<Uf> estadosRegistrados = veiculos.stream().map(veiculo -> veiculo.getContrato().getUfRegistro()).collect(Collectors.toSet());

        Map<Uf, BigDecimal> valorDescontoPorEstado = new HashMap<>();
        for (Uf estado : estadosRegistrados) {
            valorDescontoPorEstado.put(estado, manipulaRelatorioService.valorDescontoPorCobranca(cobranca, estado));
        }

        List<Credenciamento> credenciamentos = credenciamentoService.getCredenciamentos(cobranca);
        boolean unificada = veiculos.stream().map(v -> v.getContrato().getUfRegistro()).collect(Collectors.toSet()).size() > 1;

        List<String[]> relatorio = new ArrayList<>();

        double valorTotal = 0;
        for (Veiculo v : veiculos) {
            long startIndividual = System.currentTimeMillis();
            logger.info(String.format("Processando veiculo %s", v.getId()));
            Contrato c = v.getContrato();
            Credenciamento credenciamento = manipulaRelatorioService.filtrarCredenciamentoAtivoPorUf(c.getUfRegistro(), credenciamentos);

            double valorDesconto = 0;
            double valorCredenciada = 0;

            if (!(c.getNumeroRegistroEletronico() == 0L &&
                    c.getCobranca().getCredenciamento().getTipoCobranca() == TipoCobranca.CONTRATO)) {

                valorDesconto = valorDescontoPorEstado.get(c.getUfRegistro()).doubleValue();

                if (c.getUfRegistro() == Uf.CE) {
                    if (TipoVeiculo.getQuatroRodas().contains(v.getTipo())) {
                        valorCredenciada = credenciamento.getPrecosCompostos().stream().filter(precoComposto -> (precoComposto.getValorParametro().equals("4"))).findFirst().get().getValorCredenciada().doubleValue();
                    } else {
                        valorCredenciada = credenciamento.getPrecosCompostos().stream().filter(precoComposto -> (precoComposto.getValorParametro().equals("2"))).findFirst().get().getValorCredenciada().doubleValue();
                    }
                } else if (unificada && c.getUfRegistro() != Uf.CE) {
                    valorCredenciada = credenciamento.getValorCredenciada().doubleValue();
                } else {
                    valorCredenciada = (c.getCobranca().getValorCredenciada().doubleValue()) / c.getCobranca().getQuantidadeRegistros();
                }
            }

            valorTotal += valorCredenciada - valorDesconto;
            String[] rel = new String[]{
                    c.getNomeDevedorFinanciado(),
                    v.getNumeroChassi(),
                    c.getNumeroContrato(),
                    c.getUfRegistro().toString(),
                    c.getDataConclusaoDETRAN() == null ? "-" : DateFormatUtils.format(c.getDataConclusaoDETRAN(), "dd/MM/yyyy HH:mm:ss"),
                    formataValorMonetario(valorCredenciada - valorDesconto)
            };
            relatorio.add(rel);
            long endIndividual = System.currentTimeMillis();
            logger.info(String.format("Tempo de processamento do relatorio do Banco do Brasil: %s", endIndividual - startIndividual));
        }

        Map<String, String> total = new HashMap<>();
        total.put("Valor Total da Nota de Serviço", formataValorMonetario(valorTotal));

        return new RelatorioData(
                headerNames,
                relatorio,
                cobranca.getFinanceira().getNome(),
                total
        );
    }

    /**
     * Cria uma planilha com nome único no workbook
     * @param workbook O workbook onde a planilha será criada
     * @param baseName Nome base para a planilha
     * @return A planilha criada
     */
    private Sheet createUniqueSheet(XSSFWorkbook workbook, String baseName) {
        String sheetName = baseName;
        int counter = 1;

        // Verifica se já existe uma planilha com esse nome
        while (sheetExists(workbook, sheetName)) {
            // Se existir, adiciona um número ao final do nome
            sheetName = baseName + " " + counter;
            counter++;
        }

        return workbook.createSheet(sheetName);
    }

    /**
     * Verifica se uma planilha com o nome especificado já existe no workbook
     * @param workbook O workbook a ser verificado
     * @param sheetName O nome da planilha a ser verificado
     * @return true se a planilha existir, false caso contrário
     */
    private boolean sheetExists(XSSFWorkbook workbook, String sheetName) {
        return workbook.getSheetIndex(sheetName) >= 0;
    }

    public void relatorioDefault(Cobranca cobranca, HttpServletResponse response) throws IOException {
        try (XSSFWorkbook workbook = new XSSFWorkbook()) {

            // Garante que os nomes são únicos dentro do workbook
            String nomeSheet1 = "PLACECON - Registro de Contrato";
            String nomeSheet2 = "PLACECON - Registro de Gravame RSNG";

            if (workbook.getSheet(nomeSheet1) != null) {
                workbook.removeSheetAt(workbook.getSheetIndex(nomeSheet1));
            }
            Sheet sheet = workbook.createSheet(nomeSheet1);
            sheet.setDefaultColumnWidth(26);
            CellStyle style = manipulaRelatorioService.createStyle(workbook);
            CellStyle styleSng = manipulaRelatorioService.createStyle(workbook);
            int rowCount = manipulaRelatorioService.createHeader(workbook, sheet, style, 0);
            manipulaRelatorioService.createTable(sheet, style, rowCount, cobranca);


            generateSngSheet(cobranca, workbook, nomeSheet2, styleSng);

            fecharLoading();
            fecharPlanilha(workbook, response);
        }
    }

    private void generateSngSheet(Cobranca cobranca, XSSFWorkbook workbook, String nomeSheet2, CellStyle style) throws IOException {
        if (cobranca.getQuantidadeRegistrosSng() == null || cobranca.getQuantidadeRegistrosSng() <= 0 )
            return;

        if (workbook.getSheet(nomeSheet2) != null) {
            workbook.removeSheetAt(workbook.getSheetIndex(nomeSheet2));
        }

        Sheet sheetSng = workbook.createSheet(nomeSheet2);

        sheetSng.setDefaultColumnWidth(26);


        int rowCountSng = manipulaRelatorioService.createHeader(workbook, sheetSng, style, 0);

        style.setFillForegroundColor(HSSFColor.HSSFColorPredefined.GREY_25_PERCENT.getIndex());

        manipulaRelatorioService.createTableSng(sheetSng, style, rowCountSng, cobranca);
    }


    public List<Cobranca> previsao(Cobranca cobranca, Set<Uf> ufs) {
        Set<Uf> ufsComCobrancaAberta = ufsCobrancaAbertaPorFinanceira(cobranca.getDataInicio(), cobranca.getDataFim(), ufs, cobranca.getFinanceira());
        Set<Credenciamento> credenciamentos = filtrarUfsComCredenciamento(cobranca.getDataInicio(), cobranca.getDataFim(), ufsComCobrancaAberta);
        List<Cobranca> cobrancas = getCobrancas(cobranca, credenciamentos);
        cobrancas = cobrancas.stream().map(this::calcularCobrancas).filter(Objects::nonNull).collect(Collectors.toList());
        return cobrancas;
    }

    private Cobranca calcularCobrancas(Cobranca cobranca) {
        try {
            return calcularCobranca(cobranca);
        } catch (ServiceException e) {
            logger.error(e.getMessage());
        }
        return null;
    }

    private List<Cobranca> getCobrancas(Cobranca cobranca, Set<Credenciamento> credenciamentos) {

        return credenciamentos.stream().map((credenciamento) -> {
                            Cobranca c = new Cobranca();
                            BeanUtils.copyProperties(cobranca, c, "notaRembolsoEmitida");
                            c.setCredenciamento(credenciamento);
                            c.setEstado(credenciamento.getUf());
                            return c;
                        }
                ).
                collect(Collectors.toList());

    }

    private Set<Uf> ufsCobrancaAbertaPorFinanceira(Date dataInicio, Date dataFim, Set<Uf> ufs, Financeira financeira) {
        return cobrancaRepository
                .findUfsCobrancaAbertaPorFinanceira(
                        financeira, dataInicio, dataFim)
                .stream()
                .filter(uf -> ufs.contains(uf))
                .collect(Collectors.toSet());
    }

    private Set<Credenciamento> filtrarUfsComCredenciamento(Date dataInicio, Date dataFim, Set<Uf> ufs) {

        return ufs.
                stream().
                map(uf -> credenciamentoRepository.
                        findCredenciamentoByUfAndPeriodo(
                                uf, dataInicio, dataFim).stream().findFirst().orElse(null))
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());
    }

    public void salvarCobrancaUnificada(Cobranca cobranca) throws ServiceException {
        emitirBoletos(cobranca);
        setCobrancaPaga(cobranca);
        atributosCobrancaSeGerada(cobranca);
        cobrancaRepository.save(cobranca);
        if (cobranca.isEnviada())
            enviarEmailUnificada(cobranca);
    }

    public void gerarCobrancaUnificada(List<Cobranca> cobrancas) throws ServiceException {

        List<Uf> ufs = cobrancas.stream().map(Cobranca::getEstado).collect(Collectors.toList());
        Cobranca cobranca = getCobrancaCopy(cobrancas);
        somarQuantidades(cobrancas, cobranca);
        realizarCalculosCobrancaUnificada(cobrancas, cobranca);
        setarInformacoesAdicionaisCobrancaUnificada(cobranca);
        marcarRegistrosUnificada(cobrancaRepository.save(cobranca), ufs);

    }

    public Cobranca gerarCobrancaAgrupada(List<Cobranca> cobrancas) throws ServiceException {

        Set<Financeira> financeiras = cobrancas.stream().map(Cobranca::getFinanceira).collect(Collectors.toSet());
        Financeira financeiraPrinciapal = financeiras.stream()
                .filter(f -> f.getDocumento().equals("00000000000191"))
                .findFirst()
                .get();

        Cobranca cobranca = getCobrancaCopy(cobrancas);

        somarQuantidades(cobrancas, cobranca);
        realizarCalculosCobrancaUnificada(cobrancas, cobranca);

        cobranca.setFinanceira(financeiraPrinciapal);
        cobranca.setEstado(Uf.BR);
        cobranca.setReembolsoLinhaDigitavel(SimNao.N);
        cobranca.setSituacaoCobranca(SituacaoCobranca.GERADA);

        return cobranca;
    }

    public boolean isBancoDoBrasil(String cnpj) {
        Optional<String> busca = cnpjsBancoBrasil.stream()
                .filter(s -> s.equals(cnpj))
                .findFirst();
        return busca.isPresent();
    }

    private void emitirBoletos(Cobranca cobranca) {
        if (cobranca.isFaturada()) {
            Optional<BoletoDeCobrancaStrategy> geradorDeBoleto = filtrarInstanciaGeradorDeBoletoPorEstado(cobranca);

            geradorDeBoleto.ifPresent(gerador -> {
                gerarBoleto(gerador, cobranca);
            });
        }
    }

    private void setarInformacoesAdicionaisCobrancaUnificada(Cobranca cobranca) throws ServiceException {

        SituacaoFinanceiraEstado situacaoFinanceiraEstado = buscarSituacaoComMaiorDiaDaCobranca(cobranca);
        Date dataVencimento = getDataVencimentoBoleto(situacaoFinanceiraEstado);
        cobranca.setEstado(Uf.BR);
        cobranca.setDataVencimentoBoleto(dataVencimento);
        cobranca.setDataVencimentoReembolsoBoleto(dataVencimento);
        cobranca.setReembolsoLinhaDigitavel(SimNao.N);
        cobranca.setSituacaoCobranca(SituacaoCobranca.GERADA);
    }

    private SituacaoFinanceiraEstado buscarSituacaoComMaiorDiaDaCobranca(Cobranca cobranca) throws ServiceException {
        return getSituacoes(cobranca)
                .stream().max(Comparator.comparing(SituacaoFinanceiraEstado::getDiaCobranca))
                .orElseThrow(() -> new ServiceException("Não existe nenhuma situação cadastrada"));
    }

    private List<SituacaoFinanceiraEstado> getSituacoes(Cobranca cobranca) {
        return situacaoFinanceiraEstadoRepository
                .findAllByFinanceira(
                        cobranca.getFinanceira()
                );
    }

    public Set<Uf> getUfsCredenciadasNaFinanceira(Cobranca cobranca) {

        List<SituacaoFinanceiraEstado> situacoes = getSituacoes(cobranca);

        if (!PlaceconUtil.isListaVaziaOuNula(situacoes))
            return situacoes.stream().map(SituacaoFinanceiraEstado::getUf).collect(Collectors.toSet());

        return Collections.emptySet();

    }

    private void marcarRegistrosUnificada(Cobranca cobranca, List<Uf> ufs) throws ServiceException {

        cobrancaValidator.validaQuantidadeDeRegistrosNaGeracao(cobranca);

        long quantidadeRegistros = cobrancaRepository.setContratoCobrado(cobranca, cobranca.getFinanceira(), ufs, cobranca.getDataInicio(), cobranca.getDataFim());

        logger.info("Quantidade de Contratos da Cobranca -> " + quantidadeRegistros);

    }

    private void somarQuantidades(List<Cobranca> cobrancas, Cobranca cobranca) {
        cobranca.setQuantidadeAditivo(cobrancas.stream().map(Cobranca::getQuantidadeAditivo).reduce(Long::sum).orElse(0l));
        cobranca.setQuantidadePrincipal(cobrancas.stream().map(Cobranca::getQuantidadePrincipal).reduce(Long::sum).orElse(0l));
        cobranca.setQuantidadeRegistros(cobrancas.stream().map(Cobranca::getQuantidadeRegistros).reduce(Long::sum).orElse(0l));
    }

    private void realizarCalculosCobrancaUnificada(List<Cobranca> cobrancas, Cobranca cobranca) {

        cobranca.setValorCobranca(cobrancas.stream().map(Cobranca::getValorCobranca).reduce(BigDecimal::add).orElse(BigDecimal.ZERO));
        cobranca.setValorCredenciada(cobrancas.stream().map(Cobranca::getValorCredenciada).reduce(BigDecimal::add).orElse(BigDecimal.ZERO));
        cobranca.setValorDesconto(cobrancas.stream().map(Cobranca::getValorDesconto).reduce(BigDecimal::add).orElse(BigDecimal.ZERO));
        cobranca.setValorDetran(cobrancas.stream().map(Cobranca::getValorDetran).reduce(BigDecimal::add).orElse(BigDecimal.ZERO));
        cobranca.setValorDetranAditivo(cobrancas.stream().map(Cobranca::getValorDetranAditivo).reduce(BigDecimal::add).orElse(BigDecimal.ZERO));
        cobranca.setValorDetranPrincipal(cobrancas.stream().map(Cobranca::getValorDetranPrincipal).reduce(BigDecimal::add).orElse(BigDecimal.ZERO));
        cobranca.setValorDetranReembolsavel(calcularTotalReembolsavel(cobrancas));

    }

    private BigDecimal calcularTotalReembolsavel(List<Cobranca> cobrancas) {

        Set<Uf> situacoes = buscarUfsComBoletoDeReembolsoByCobrancaExistente(cobrancas);

        return cobrancas
                .stream()
                .filter(cobranca -> situacoes.contains(cobranca.getEstado()))
                .map(Cobranca::getValorDetran)
                .reduce(BigDecimal::add)
                .orElse(BigDecimal.ZERO);

    }

    public Set<Uf> buscarUfsComBoletoDeReembolsoByCobrancaExistente(List<Cobranca> cobrancas) {
        Set<Uf> situacoes =
                getSituacoes(cobrancas.get(0))
                        .stream()
                        .filter(s -> s.getReembolsoLinhaDigitavel() == SimNao.S)
                        .map(SituacaoFinanceiraEstado::getUf).collect(Collectors.toSet());

        situacoes.addAll(buscarUfsComInstanciaQueGeraBoletoDeReembolso());

        return situacoes;
    }

    public Set<Uf> buscarUfsComBoletoDeReembolso(Cobranca cobranca) {

        Set<Uf> situacoes =
                getSituacoes(cobranca)
                        .stream()
                        .filter(s -> s.getReembolsoLinhaDigitavel() == SimNao.S)
                        .map(SituacaoFinanceiraEstado::getUf)
                        .collect(Collectors.toSet());
        situacoes.addAll(buscarUfsComInstanciaQueGeraBoletoDeReembolso());

        return situacoes.stream().filter(getUfsByCobranca(cobranca)::contains).collect(Collectors.toSet());
    }

    public Set<Uf> buscarUfsSemBoletoDeReembolso(Cobranca cobranca) {

        Set<Uf> ufsCobranca = getUfsByCobranca(cobranca);

        Set<Uf> situacoes =
                getSituacoes(cobranca)
                        .stream()
                        .filter(s -> s.getReembolsoLinhaDigitavel() == SimNao.N)
                        .map(SituacaoFinanceiraEstado::getUf)
                        .filter(ufsCobranca::contains)
                        .collect(Collectors.toSet());

        situacoes.removeAll(buscarUfsComInstanciaQueGeraBoletoDeReembolso());

        return situacoes;
    }

    public Set<Uf> getUfsByCobranca(Cobranca cobranca) {
        List<Contrato> contratos = cobrancaRepository.findContratosByCobranca(cobranca);
        if (!PlaceconUtil.isListaVaziaOuNula(contratos))
            return contratos.stream().map(Contrato::getUfRegistro).collect(Collectors.toSet());

        return Collections.emptySet();
    }

    private Set<Uf> buscarUfsComInstanciaQueGeraBoletoDeReembolso() {
        return geradorDeBoletos
                .stream()
                .filter(c -> c instanceof CobrancaComBoletoDeReembolso)
                .map(BoletoDeCobrancaStrategy::getUf)
                .collect(Collectors.toSet());
    }

    private Cobranca getCobrancaCopy(List<Cobranca> cobrancas) {
        Cobranca cobranca = new Cobranca();
        BeanUtils.copyProperties(cobrancas.get(0), cobranca);
        return cobranca;
    }

    public Boolean financeiraPossuiCobranca(Financeira financeira, List<Cobranca> cobrancas) {
        for (Cobranca c : cobrancas) {
            if (c.getFinanceira().equals(financeira)) {
                return c.getQuantidadeRegistros() > 0;
            }
        }
        return false;
    }

    public Long countCobrancasByPeriodoAndUf(Date dataInicio, Date dataFim, Uf uf) {
        dataInicio = PlaceconUtil.minDateTime(dataInicio);
        dataFim = PlaceconUtil.maxDateTime(dataFim);
        return cobrancaRepository.countCobrancasByPeriodoAndUf(dataInicio, dataFim, uf);
    }

    public void enviaEmailFinanceirasSemFaturamento(List<Cobranca> cobrancas, Uf uf, Date dataInicio, Date dataFim, Financeira financeira) {
        Long quantidadeCobrancasGeradasPeriodoUf = countCobrancasByPeriodoAndUf(dataInicio, dataFim, uf);
        if (quantidadeCobrancasGeradasPeriodoUf > 0) {
            return;
        }

        List<Financeira> financeirasAvisoFaturamento;
        if (financeira != null) {
            financeira = financeiraService.findOne(financeira.getId());
            if (Objects.isNull(financeira.getEnviarEmailSemFaturamento()) || financeira.getEnviarEmailSemFaturamento().equals(SimNao.N)) {
                return;
            }
            financeirasAvisoFaturamento = Collections.singletonList(financeira);
        } else {
            financeirasAvisoFaturamento = financeiraService.findFinanceirasAvisoFaturamentoAtivoECredenciamentoAtivo(uf, dataInicio, dataFim);
        }

        financeirasAvisoFaturamento
                .stream()
                .filter(f -> !financeiraPossuiCobranca(f, cobrancas))
                .forEach(f -> enviaCobrancaEmail.enviaEmailFinanceiraEstadoSemFaturamento(f, uf, dataInicio, dataFim));
    }

    public boolean cnpjsNumeroPedidoContains(String documento) {
        return cnpjsNumeroPedido.contains(documento);
    }

    public Long countContratosByFinanceiraAndUfRegistro(Financeira financeira, Uf uf, Date dataInicio, Date dataFim) {
        dataInicio = PlaceconUtil.minDateTime(dataInicio);
        dataFim = PlaceconUtil.maxDateTime(dataFim);
        return cobrancaRepository.countContratosByFinanceiraAndUfRegistro(financeira, uf, dataInicio, dataFim);
    }

    public Long countContratosByFinanceiraAndUfRegistroAditivo(Financeira financeira, Uf uf, Date dataInicio, Date dataFim) {
        dataInicio = PlaceconUtil.minDateTime(dataInicio);
        dataFim = PlaceconUtil.maxDateTime(dataFim);
        return cobrancaRepository.countContratosByFinanceiraAndUfRegistroAditivo(financeira, uf, dataInicio, dataFim);
    }

    public Long countVeiculosByFinanceiraAndUfRegistro(Financeira financeira, Uf uf, Date dataInicio, Date dataFim) {
        dataInicio = PlaceconUtil.minDateTime(dataInicio);
        dataFim = PlaceconUtil.maxDateTime(dataFim);
        return cobrancaRepository.countVeiculosByFinanceiraAndUfRegistro(financeira, uf, dataInicio, dataFim);
    }

    public Long countVeiculosRsngByFinanceiraAndUfRegistro(Financeira financeira, Uf uf, Date dataInicio, Date dataFim) {
        dataInicio = PlaceconUtil.minDateTime(dataInicio);
        dataFim = PlaceconUtil.maxDateTime(dataFim);
        return cobrancaRepository.countVeiculosRnsgByFinanceiraAndUfRegistro(financeira, uf, dataInicio, dataFim);
    }

    private Calendar getDataInicio(int periodo) {
        Calendar dataInicio = Calendar.getInstance();
        dataInicio.add(Calendar.MONTH, periodo);
        dataInicio.set(Calendar.DAY_OF_MONTH, 1);
        dataInicio = DateUtils.truncate(dataInicio, Calendar.DATE);
        return dataInicio;
    }

    public Long countVeiculosByFinanceiraAndUfRegistroAditivo(Financeira financeira, Uf uf, Date dataInicio, Date dataFim) {
        dataInicio = PlaceconUtil.minDateTime(dataInicio);
        dataFim = PlaceconUtil.maxDateTime(dataFim);
        return cobrancaRepository.countVeiculosByFinanceiraAndUfRegistroAditivo(financeira, uf, dataInicio, dataFim);
    }

    public List<Object[]> findPagasByPeriodo(List<Financeira> financeiras, Uf uf, int periodo) {
        return cobrancaRepository.findPagasByPeriodo(financeiras, uf, getDataInicio(periodo).getTime(), Calendar.getInstance().getTime());
    }

    public List<Object[]> findRegistrosByPeriodo(List<Financeira> financeiras, Uf uf, int periodo) {
        return cobrancaRepository.findRegistrosByPeriodo(financeiras, uf, getDataInicio(periodo).getTime(), Calendar.getInstance().getTime());
    }

    public List<Object[]> findContratosByPeriodo(List<Financeira> financeiras, Uf uf, int periodo) {
        return cobrancaRepository.findContratosByPeriodo(financeiras, uf, getDataInicio(periodo).getTime(), Calendar.getInstance().getTime());
    }

    @Override
    public Page<Cobranca> findAll(int first, int pageSize, CobrancaDTO filter) {
        Specification<Cobranca> cobrancaSpecification = new Specification<Cobranca>() {

            @Override
            public Predicate toPredicate(Root<Cobranca> root, CriteriaQuery<?> cq, CriteriaBuilder cb) {
                cq.distinct(true);
                List<Predicate> predicates = new ArrayList<>();

                if (filter.getDataInicio() != null) {
                    predicates.add(cb.greaterThanOrEqualTo(root.<Date>get("dataInicio"), filter.getDataInicio()));
                }

                if (filter.getDataFim() != null) {
                    predicates.add(cb.lessThanOrEqualTo(root.<Date>get("dataFim"), filter.getDataFim()));
                }

                if (filter.getEstado() != null) {
                    predicates.add(cb.equal(root.<Uf>get("estado"), filter.getEstado()));
                }

                if (filter.getSituacaoCobranca() != null) {
                    predicates.add(cb.equal(root.<SituacaoCobranca>get("situacaoCobranca"), filter.getSituacaoCobranca()));
                }

                if (!filter.getSituacoesCobranca().isEmpty()) {
                    predicates.add(root.<SituacaoCobranca>get("situacaoCobranca").in(filter.getSituacoesCobranca()));
                }

                if (filter.getFinanceira() != null) {
                    predicates.add(cb.equal(root.<Financeira>get("financeira"), filter.getFinanceira()));
                }

                if (filter.getNumeroVenda() != null && !filter.getNumeroVenda().isEmpty()) {
                    predicates.add(cb.equal(root.get("numeroVenda"), filter.getNumeroVenda()));
                }

                if (filter.getNotaAzul() != null) {
                    if (filter.getNotaAzul() == Boolean.TRUE) {
                        predicates.add(cb.equal(root.<Boolean>get("notaAzul"), Boolean.TRUE));
                    } else {
                        predicates.add(cb.or(cb.isNull(root.<Boolean>get("notaAzul")), cb.equal(root.<Boolean>get("notaAzul"), Boolean.FALSE)));
                    }
                }

                if (filter.getNotaAzulSng() != null) {
                    if (filter.getNotaAzulSng() == Boolean.TRUE) {
                        predicates.add(cb.equal(root.<Boolean>get("notaAzulSng"), Boolean.TRUE));
                    } else {
                        predicates.add(cb.or(cb.isNull(root.<Boolean>get("notaAzulSng")), cb.equal(root.<Boolean>get("notaAzulSng"), Boolean.FALSE)));
                    }
                }

                if (filter.getNotaQuery() != null) {
                    if (filter.getNotaQuery() == Boolean.TRUE) {
                        predicates.add(
                                cb.or(
                                        cb.or(cb.and(cb.isNull(root.<Boolean>get("notaAzulSng")), cb.equal(root.<Boolean>get("notaAzulSng"), Boolean.FALSE), cb.greaterThan(root.<Long>get("quantidadeRegistrosSng"), 0L), cb.isNotNull(root.<Long>get("quantidadeRegistrosSng")))),
                                        cb.or(cb.isNull(root.<Boolean>get("notaAzul")), cb.equal(root.<Boolean>get("notaAzul"), Boolean.FALSE))
                                ));
                        predicates.add(cb.or(cb.equal(root.<SituacaoCobranca>get("situacaoCobranca"), SituacaoCobranca.ENVIADA), cb.equal(root.<SituacaoCobranca>get("situacaoCobranca"), SituacaoCobranca.GERADA)));
                    }
                }

                // se o usuario eh financeira filtrar somente os usuario da financeira
                Usuario usuarioLogado = usuarioService.findByCpfFinanceiras(filter.getUsuario());
                if (usuarioLogado.getPerfil() == Perfil.FINANCEIRA || !usuarioLogado.getFinanceiras().isEmpty()) {
                    List<Financeira> values = usuarioLogado.getFinanceiras();
                    predicates.add(root.<Financeira>get("financeira").in(values));
                }

                if (usuarioLogado.getUf() != null) {
                    predicates.add(cb.equal(root.<Uf>get("estado"), usuarioLogado.getUf()));
                }
                return andTogether(predicates, cb);
            }

            private Predicate andTogether(List<Predicate> predicates, CriteriaBuilder cb) {
                return cb.and(predicates.toArray(new Predicate[0]));
            }
        };

        if (filter.getNotaAzul() != null) {
            return cobrancaRepository.findAll(cobrancaSpecification, new PageRequest(first / pageSize, pageSize, new Sort(Sort.Direction.DESC, "valorCobranca")));
        }
        return cobrancaRepository.findAll(cobrancaSpecification, new PageRequest(first / pageSize, pageSize, new Sort(Sort.Direction.DESC, "id")));
    }

    public List<String> getCnpjsBancoBrasil() {
        return cnpjsBancoBrasil;
    }

    @Override
    protected PagingAndSortingRepository<Cobranca, Long> getRepository() {
        return cobrancaRepository;
    }


}
