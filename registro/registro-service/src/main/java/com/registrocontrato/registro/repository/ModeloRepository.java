package com.registrocontrato.registro.repository;

import java.util.List;
import java.util.Optional;

import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import com.registrocontrato.infra.entity.SimNao;
import com.registrocontrato.infra.service.BaseRepository;
import com.registrocontrato.registro.entity.Marca;
import com.registrocontrato.registro.entity.Modelo;

@Repository
public interface ModeloRepository extends BaseRepository<Modelo> {

	Modelo findByCodigoDenatran(String codigoDenatran);

	List<Modelo>findAllByMarca(Marca marca);

	Modelo findTop1ByDescricaoAndAnoAndMarcaOrderById(String descricao, Integer ano, Marca marca);

	Modelo findTop1ByDescricaoIgnoreCaseAndAnoAndMarcaOrderById(String descricao, Integer ano, Marca marca);

	List<Modelo> findByMarcaAndAnoAndAtivoOrderByDescricao(Marca marca, Integer ano, SimNao simNao);

	@Override
	@Query("select m from Modelo m left join fetch m.marca c where m.id = :id")
	Modelo findOne(@Param("id") Long id);

	Modelo findTop1ByDescricaoIgnoreCaseOrderById(String descricao);

	Modelo findTop1ByDescricaoIgnoreCaseAndAnoOrderById(String descricao, Integer ano);

	@Query("SELECT m FROM Modelo m WHERE m.id = :id")
	Optional<Modelo> findByMarcaId(@Param("id") Long id);
}
