package com.registrocontrato.registro.service.cobranca.calculadora;

import com.registrocontrato.infra.exception.ServiceException;
import com.registrocontrato.infra.util.PlaceconUtil;
import com.registrocontrato.registro.entity.*;
import com.registrocontrato.registro.enums.TipoCobranca;
import com.registrocontrato.registro.service.CredenciamentoService;
import com.registrocontrato.registro.service.CupomDescontoService;
import com.registrocontrato.registro.service.cobranca.CobrancaService;
import com.registrocontrato.registro.service.cobranca.gravame.GravameService;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.context.annotation.Lazy;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

public abstract class CalculadoraDeCobrancaDefault implements CalculadoraDeCobrancaStrategy {

    protected final Log logger = LogFactory.getLog(getClass());

    protected final CobrancaService cobrancaService;

    protected final CredenciamentoService credenciamentoService;

    protected final GravameService gravameService;

    protected final CupomDescontoService cupomDescontoService;

    public CalculadoraDeCobrancaDefault(@Lazy CobrancaService cobrancaService, CredenciamentoService credenciamentoService, CupomDescontoService cupomDescontoService, GravameService gravameService) {
        this.cobrancaService = cobrancaService;
        this.credenciamentoService = credenciamentoService;
        this.gravameService = gravameService;
        this.cupomDescontoService = cupomDescontoService;
    }

    @Override
    public Cobranca calcularCobranca(Cobranca cobranca) throws ServiceException {
        Optional<Credenciamento> optionalCredenciamento = buscarCredenciamento(cobranca);
        Optional<BilhetagemGravame> optionalGravame = buscarBilhetagem(cobranca);
        if (!optionalCredenciamento.isPresent() && !optionalGravame.isPresent()) {
            throw new ServiceException("Não foi encontrado credenciamento e bilhetagem para o estado da Cobranca");
        }
        Credenciamento credenciamento = optionalCredenciamento.orElse(null);
        BilhetagemGravame gravame = optionalGravame.orElse(null);
        cobranca.setCredenciamento(credenciamento);
        cobranca.setGravame(gravame);
        definirValoresDaCobranca(cobranca, credenciamento);
        definirValoresSng(cobranca, gravame);
        aplicarDesconto(cobranca, cupomDescontoService.findCuponsDisponiveis(cobranca));
        return cobranca;
    }

    @Override
    public Cobranca calcularCobrancaUnificada(Cobranca cobranca) throws ServiceException {
        Optional<Credenciamento> optionalCredenciamento = buscarCredenciamento(cobranca);
        Optional<BilhetagemGravame> optionalGravame = buscarBilhetagem(cobranca);
        if (!optionalCredenciamento.isPresent() && !optionalGravame.isPresent()) {
            throw new ServiceException("Não foi encontrado credenciamento e bilhetagem para o estado da Cobranca");
        }
        Credenciamento credenciamento = optionalCredenciamento.orElse(null);
        BilhetagemGravame gravame = optionalGravame.orElse(null);
        cobranca.setCredenciamento(credenciamento);
        cobranca.setGravame(gravame);
        definirValoresDaCobrancaUnificada(cobranca, credenciamento);
        definirValoresSng(cobranca, gravame);
        aplicarDesconto(cobranca, cupomDescontoService.findCuponsDisponiveis(cobranca));
        return cobranca;
    }

    protected void definirValoresDaCobranca(Cobranca cobranca, Credenciamento credenciamento) throws ServiceException {
        if (credenciamento != null) {
            cobranca.setQuantidadeRegistros(buscarQuantidadeContratosPrincipal(cobranca));
            logger.info(cobranca.getQuantidadeRegistros());
            cobranca.setQuantidadeAditivo(buscarQuantidadeContratosAditivos(cobranca));
            cobranca.setQuantidadePrincipal(cobranca.getQuantidadeRegistros() - cobranca.getQuantidadeAditivo());
            cobranca.setValorDetranPrincipal(credenciamento.getValorDETRAN().multiply(new BigDecimal(cobranca.getQuantidadePrincipal())));
            cobranca.setValorDetranAditivo(credenciamento.getValorAditivo().multiply(new BigDecimal(cobranca.getQuantidadeAditivo())));
            cobranca.setValorCredenciada(credenciamento.getValorCredenciada().multiply(new BigDecimal(cobranca.getQuantidadeRegistros())));
            cobranca.setValorDetran(cobranca.getValorDetranPrincipal().add(cobranca.getValorDetranAditivo()));
            cobranca.setValorCobranca(cobranca.getValorCredenciada().add(cobranca.getValorDetran()));
        }
    }

    protected void definirValoresDaCobrancaUnificada(Cobranca cobranca, Credenciamento credenciamento) throws ServiceException {
        if (credenciamento != null) {
            cobranca.setQuantidadeRegistros(buscarQuantidadeContratosPrincipalUnificada(cobranca));
            cobranca.setQuantidadeAditivo(buscarQuantidadeContratosAditivosUnificada(cobranca));
            cobranca.setQuantidadePrincipal(cobranca.getQuantidadeRegistros() - cobranca.getQuantidadeAditivo());
            cobranca.setValorDetranPrincipal(credenciamento.getValorDETRAN().multiply(new BigDecimal(cobranca.getQuantidadePrincipal())));
            cobranca.setValorDetranAditivo(credenciamento.getValorAditivo().multiply(new BigDecimal(cobranca.getQuantidadeAditivo())));
            cobranca.setValorCredenciada(credenciamento.getValorCredenciada().multiply(new BigDecimal(cobranca.getQuantidadeRegistros())));
            cobranca.setValorDetran(cobranca.getValorDetranPrincipal().add(cobranca.getValorDetranAditivo()));
            cobranca.setValorCobranca(cobranca.getValorCredenciada().add(cobranca.getValorDetran()));
        }
    }

    protected void definirValoresSng(Cobranca cobranca, BilhetagemGravame gravame) throws ServiceException {
        if (gravame != null) {
            cobranca.setQuantidadeRegistrosSng(buscarQuantidadeContratosSngAditivos(cobranca));
            cobranca.setQuantidadeBaixaSng(buscarQuantidadeBaixaSng(cobranca));
            cobranca.setQuantidadeCancelamentoBaixaSng(buscarQuantidadeCancelamentoBaixaSng(cobranca));
            cobranca.setValorCobrancaSng(gravame.getValorTotal().multiply(new BigDecimal(
                    cobranca.getQuantidadeRegistrosSng() +
                            cobranca.getQuantidadeBaixaSng() +
                            cobranca.getQuantidadeCancelamentoBaixaSng())));
        }
    }

    protected Long buscarQuantidadeBaixaSng(Cobranca cobranca) {
        return cobrancaService
                .countVeiculosBaixaSngByFinanceiraAndUfRegistro(
                        cobranca.getFinanceira(), cobranca.getEstado(), cobranca.getDataInicio(), cobranca.getDataFim());
    }

    protected Long buscarQuantidadeCancelamentoBaixaSng(Cobranca cobranca) {
        return cobrancaService
                .countVeiculosCancelamentoBaixaSngByFinanceiraAndUfRegistro(
                        cobranca.getFinanceira(), cobranca.getEstado(), cobranca.getDataInicio(), cobranca.getDataFim());
    }

    protected Long buscarQuantidadeContratosSngAditivos(Cobranca cobranca) {
        return cobrancaService
                .countVeiculosRsngByFinanceiraAndUfRegistro(
                        cobranca.getFinanceira(), cobranca.getEstado(), cobranca.getDataInicio(), cobranca.getDataFim());
    }

    protected Long buscarQuantidadeContratosAditivos(Cobranca cobranca) {
        if (cobranca.getCredenciamento().getTipoCobranca() == TipoCobranca.CONTRATO)
            return cobrancaService
                    .countContratosByFinanceiraAndUfRegistroAditivo(
                            cobranca.getFinanceira(), cobranca.getEstado(), cobranca.getDataInicio(), cobranca.getDataFim());

        return cobrancaService
                .countVeiculosByFinanceiraAndUfRegistroAditivo(
                        cobranca.getFinanceira(), cobranca.getEstado(), cobranca.getDataInicio(), cobranca.getDataFim());
    }

    protected Long buscarQuantidadeContratosAditivosUnificada(Cobranca cobranca) {
        if (cobranca.getCredenciamento().getTipoCobranca() == TipoCobranca.CONTRATO)
            return cobrancaService
                    .countContratosByFinanceiraAndUfRegistroAditivo(cobranca,
                            cobranca.getFinanceira(), cobranca.getEstado(), cobranca.getDataInicio(), cobranca.getDataFim());

        return cobrancaService
                .countVeiculosByFinanceiraAndUfRegistroAditivo(cobranca,
                        cobranca.getFinanceira(), cobranca.getEstado(), cobranca.getDataInicio(), cobranca.getDataFim());
    }

    protected Long buscarQuantidadeContratosPrincipal(Cobranca cobranca) {
        if (cobranca.getCredenciamento().getTipoCobranca() == TipoCobranca.CONTRATO)
            return cobrancaService
                    .countContratosByFinanceiraAndUfRegistro(
                            cobranca.getFinanceira(), cobranca.getEstado(), cobranca.getDataInicio(), cobranca.getDataFim());
        return cobrancaService
                .countVeiculosByFinanceiraAndUfRegistro(
                        cobranca.getFinanceira(), cobranca.getEstado(), cobranca.getDataInicio(), cobranca.getDataFim());
    }

    protected Long buscarQuantidadeContratosPrincipalUnificada(Cobranca cobranca) {
        if (cobranca.getCredenciamento().getTipoCobranca() == TipoCobranca.CONTRATO)
            return cobrancaService
                    .countContratosByFinanceiraAndUfRegistro(cobranca,
                            cobranca.getFinanceira(), cobranca.getEstado(), cobranca.getDataInicio(), cobranca.getDataFim());
        return cobrancaService
                .countVeiculosByFinanceiraAndUfRegistro(cobranca,
                        cobranca.getFinanceira(), cobranca.getEstado(), cobranca.getDataInicio(), cobranca.getDataFim());
    }

    protected void aplicarDesconto(Cobranca c, List<CupomDesconto> cupons) {
        if (!PlaceconUtil.isListaVaziaOuNula(cupons)) {
            CupomDesconto cupom = new CupomDesconto();
            List<FaixaDesconto> listaFaixas = new ArrayList<>();
            cupons.forEach(cupomDesconto -> {
                cupomDesconto.getFaixasDesconto().forEach(faixaDesconto -> {
                    listaFaixas.add(faixaDesconto);
                });
                c.setCupomDesconto(cupomDesconto);
            });
            cupom.setFaixasDesconto(listaFaixas);
            int indice = 0;
            BigDecimal valorDesconto = new BigDecimal(Long.valueOf(0l));
            BigDecimal previaDesconto = new BigDecimal(Long.valueOf(0l));
            Long qtdSemDesconto = c.getQuantidadeRegistros();
            for (indice = 0; indice < cupom.getFaixasDesconto().size(); indice++) {
                FaixaDesconto faixa = cupom.getFaixasDesconto().get(indice);
                if ((faixa.getIndice() + 1) == cupom.getFaixasDesconto().size() && faixa.getQuantidadeFinal() == null) {
                    previaDesconto = faixa.getPercentual().multiply(new BigDecimal(qtdSemDesconto));
                    valorDesconto = valorDesconto.add(previaDesconto);
                    break;
                }
//                ESSA LÓGICA SERVE PARA MANTER O ANTIGO SISTEMA DE COBRANÇA
                long qtdInicial;
                if (faixa.getQuantidadeInicial() == null) {
                    qtdInicial = cupom.getQuantidadeInicial(indice);
                } else {
                    qtdInicial = faixa.getQuantidadeInicial();
                }
                long qtdFinal = faixa.getQuantidadeFinal();
                if (faixa.getIndice() == 0 && qtdFinal >= qtdSemDesconto) {
                    valorDesconto = faixa.getPercentual().multiply(new BigDecimal(qtdSemDesconto));
                } else {
                    if (qtdFinal <= qtdSemDesconto) {
                        previaDesconto = faixa.getPercentual().multiply(new BigDecimal(qtdFinal - qtdInicial + 1));
                    } else if (qtdSemDesconto > 0) {
                        previaDesconto = faixa.getPercentual().multiply(new BigDecimal(qtdSemDesconto));
                    }
                }
                qtdSemDesconto = c.getQuantidadeRegistros() - qtdFinal;
                valorDesconto = valorDesconto.add(previaDesconto);
            }
            c.setValorDesconto(valorDesconto);
            c.setValorCobranca(c.getValorCobranca().subtract(c.getValorDesconto()));
        }
    }

    protected Optional<Credenciamento> buscarCredenciamento(Cobranca cobranca) throws ServiceException {
        return credenciamentoService.findByUfAndDataInicioLessThanEqualAndDataFimGreaterThanEqual(cobranca);
    }

    protected Optional<BilhetagemGravame> buscarBilhetagem(Cobranca cobranca) throws ServiceException {
        return gravameService.findByUfAndDataInicioLessThanEqualAndDataFimGreaterThanEqual(cobranca);
    }
}
