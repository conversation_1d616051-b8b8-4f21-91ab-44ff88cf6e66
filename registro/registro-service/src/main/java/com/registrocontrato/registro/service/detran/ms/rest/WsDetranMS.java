package com.registrocontrato.registro.service.detran.ms.rest;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.registrocontrato.infra.entity.Financeira;
import com.registrocontrato.infra.entity.Municipio;
import com.registrocontrato.infra.entity.Uf;
import com.registrocontrato.infra.exception.ServiceException;
import com.registrocontrato.infra.util.PlaceconUtil;
import com.registrocontrato.registro.entity.Contrato;
import com.registrocontrato.registro.entity.Veiculo;
import com.registrocontrato.registro.repository.MensagemRetornoRepository;
import com.registrocontrato.registro.repository.MunicipioRepository;
import com.registrocontrato.registro.service.RegistroEnvioService;
import com.registrocontrato.registro.service.detran.WsDetranDefault;
import com.registrocontrato.registro.service.detran.ms.client.*;
import com.registrocontrato.registro.service.dto.MensagemRetornoDTO;
import com.registrocontrato.registro.service.dto.RetornoContratoDetranDTO;
import com.registrocontrato.seguranca.repository.FinanceiraRepository;
import com.registrocontrato.seguranca.service.AcessoSenhaService;
import com.registrocontrato.seguranca.service.UsuarioService;
import com.registrocontrato.seguranca.service.dto.AcessoSenhaDTO;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.conn.ssl.NoopHostnameVerifier;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.pdmodel.PDPage;
import org.apache.pdfbox.pdmodel.PDPageContentStream;
import org.apache.pdfbox.pdmodel.common.PDRectangle;
import org.apache.pdfbox.pdmodel.graphics.image.JPEGFactory;
import org.apache.pdfbox.pdmodel.graphics.image.PDImageXObject;
import org.apache.pdfbox.rendering.ImageType;
import org.apache.pdfbox.rendering.PDFRenderer;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.FileSystemResource;
import org.springframework.http.*;
import org.springframework.http.client.ClientHttpRequestFactory;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.RestTemplate;

import java.awt.image.BufferedImage;
import java.io.*;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

import static com.registrocontrato.infra.util.PlaceconUtil.*;

public abstract class WsDetranMS implements WsDetranDefault {

    private static final String TOKEN_INVALIDO = "1004";

    @Value("${detran.ms.default.uri:null}")
    private String DEFAULT_URI;

    @Value("${detran.ms.url.cadastrar:null}")
    private String URL_CADASTRAR;

    @Value("${detran.ms.url.autenticacao:null}")
    private String URL_AUTENTICACAO;

    @Value("${detran.ms.url.desbloquear.contrato:null}")
    private String URL_DESBLOQUEAR_CONTRATO;

    @Value("${detran.ms.url.envio.imagem:null}")
    private String URL_ENVIO_IMAGEM;

    @Value("${detran.ms.url.corrigir.imagem:null}")
    private String URL_CORRECAO_IMAGEM;

    @Value("${detran.ms.url.busca.andamento:null}")
    private String URL_BUSCA_ANDAMENTO;

    @Value("${detran.ms.url.consulta.boleto:null}")
    private String URL_CONSULTA_BOLETO;

    @Value("${detran.ms.url.consulta.boleto.byte:null}")
    private String URL_CONSULTA_BOLETO_BYTE;

    @Value("${detran.connectionTimeout:20000}")
    private Integer connectionTimeout;

    @Value("${file.dir-read:null}")
    private String FILE_DIR_READ;

    @Value("${detran.ms.usuario:null}")
    private String USUARIO;

    @Value("${detran.ms.senha:null}")
    private String SENHA;

    protected final RegistroEnvioService registroEnvioService;
    protected final MensagemRetornoRepository mensagemRetornoRepository;
    protected final FinanceiraRepository financeiraRepository;
    protected final MunicipioRepository municipioRepository;
    protected final AcessoSenhaService acessoSenhaservice;
    private final UsuarioService usuarioService;

    protected WsDetranMS(RegistroEnvioService registroEnvioService,
                         MensagemRetornoRepository mensagemRetornoRepository,
                         FinanceiraRepository financeiraRepository,
                         MunicipioRepository municipioRepository,
                         AcessoSenhaService acessoSenhaservice, UsuarioService usuarioService) {
        this.registroEnvioService = registroEnvioService;
        this.mensagemRetornoRepository = mensagemRetornoRepository;
        this.financeiraRepository = financeiraRepository;
        this.municipioRepository = municipioRepository;
        this.acessoSenhaservice = acessoSenhaservice;
        this.usuarioService = usuarioService;
    }

    @Override
    public abstract MensagemRetornoDTO envioImagem(Veiculo veiculo, String referenciaArquivo) throws ServiceException, IOException;

    @Override
    public MensagemRetornoDTO comunicarContratoFinanceiroVeiculo(Contrato contrato, Veiculo veiculo) {
        SolicitarRegistroRequest request = null;
        ResponsePadraoMs response = new ResponsePadraoMs();

        long start = System.nanoTime();

        try {
            request = putRequest(contrato, veiculo);
            return solicitarRegistro(request, response, contrato.getFinanceira());
        } catch (Exception e) {
            getLogger().error(e.getMessage(), e);
            return getErroPadrao(contrato.getUfRegistro());
        } finally {
            registrarLog(contrato, start, request, response);
        }
    }

    @Override
    public RetornoContratoDetranDTO consultarContratoChassi(String chassi, String financeira, String numeroContrato)
            throws Exception {
        Contrato contrato = new Contrato();
        contrato.setFinanceira(financeiraRepository.findByDocumento(financeira));
        Veiculo veiculo = new Veiculo();
        veiculo.setNumeroChassi(chassi);
        veiculo.setNumeroGravame(numeroContrato);
        try {
            BuscaAndamentoResponse retorno = buscaAndamento(contrato, veiculo).getRespostaSucesso();
            return new RetornoContratoDetranDTO(PlaceconUtil.toJson(retorno));
        } catch (Exception e) {
            getLogger().error(e.getMessage(), e);
            return null;
        }
    }

    public RetornoBuscaAndamentoDTO buscaAndamento(Contrato contrato, Veiculo veiculo) {
        boolean gravaTransacao = false;
        long end = System.nanoTime();
        StringBuilder complementoRequisicao = new StringBuilder();
        complementoRequisicao.append("?chassi=");
        complementoRequisicao.append(veiculo.getNumeroChassi());
        if (veiculo.getNumeroGravame() != null && !veiculo.getNumeroGravame().trim().isEmpty()) {
            complementoRequisicao.append("&numeroGravame=");
            complementoRequisicao.append(veiculo.getNumeroGravame());
        }

        RetornoBuscaAndamentoDTO retorno = new RetornoBuscaAndamentoDTO();
        try {
            retorno = buscaAndamento(complementoRequisicao.toString(), contrato.getFinanceira());

            String etapaAtual = Objects.nonNull(retorno.getRespostaSucesso().getEtapaAtual()) ? retorno.getRespostaSucesso().getEtapaAtual() : null;
            String resultadoConferencia = Objects.nonNull(retorno.getRespostaSucesso().getResultadoConferenciaDetran()) ? retorno.getRespostaSucesso().getResultadoConferenciaDetran() : null;

            if (isUltimaEtapa(etapaAtual))
                gravaTransacao = true;
        } catch (Exception e) {
            retorno = new RetornoBuscaAndamentoDTO();
            retorno.setMensagemRetorno(new MensagemRetornoDTO());
            retorno.getMensagemRetorno().setUf(Uf.MS);
            retorno.getMensagemRetorno().setSucesso(Boolean.TRUE);
        } finally {
            if (gravaTransacao)
                registrarLog(contrato, end, getDefaultUri() + getUrlBuscaAndamento() + complementoRequisicao, retorno);
        }
        return retorno;
    }

    private RetornoBuscaAndamentoDTO buscaAndamento(String complementoRequisicao, Financeira financeira) throws ServiceException, IOException {
        ResponseEntity<BuscaAndamentoResponse> responseEntity = null;
        RetornoBuscaAndamentoDTO retorno = null;
        ResponsePadraoMs responseError;

        try {
            String chaveAcesso = getAutenticacao(financeira);
            if (chaveAcesso == null) {
                retorno = new RetornoBuscaAndamentoDTO();
                retorno.getMensagemRetorno().setCodigo(TOKEN_INVALIDO);
                retorno.getMensagemRetorno().setDescricao("Cliente não autorizado: Token de acesso inválido.");
                retorno.getMensagemRetorno().setSucesso(Boolean.FALSE);
                retorno.getMensagemRetorno().setUf(Uf.MS);
                return retorno;
            }

            RestTemplate restTemplate = new RestTemplate(getClientHttpRequestFactory());
            HttpHeaders headers = getHttpHeaders(chaveAcesso);
            HttpEntity<String> entity = new HttpEntity<String>("parameters", headers);
            responseEntity = restTemplate.exchange(getDefaultUri() + getUrlBuscaAndamento() + complementoRequisicao, HttpMethod.GET, entity, BuscaAndamentoResponse.class);
            getLogger().info("Busca Andamento - Detran MS: " + responseEntity.getStatusCode());
            if (responseEntity.getStatusCode() == HttpStatus.OK) {
                retorno = new RetornoBuscaAndamentoDTO();
                retorno.setMensagemRetorno(new MensagemRetornoDTO());
                retorno.setRespostaSucesso(responseEntity.getBody());
                retorno.getMensagemRetorno().setUf(Uf.MS);
                retorno.getMensagemRetorno().setSucesso(Boolean.TRUE);
                return retorno;
            }
        } catch (HttpClientErrorException e) {
            if (e.getStatusCode() == HttpStatus.BAD_REQUEST || e.getStatusCode() == HttpStatus.INTERNAL_SERVER_ERROR) {
                responseError = new ObjectMapper().readValue(new String(e.getResponseBodyAsByteArray(), "UTF-8"), ResponsePadraoMs.class);
                retorno = new RetornoBuscaAndamentoDTO();
                retorno.setMensagemRetorno(new MensagemRetornoDTO());
                retorno.getMensagemRetorno().setUf(Uf.MS);
                retorno.getMensagemRetorno().setSucesso(Boolean.FALSE);
                retorno.getMensagemRetorno().setCodigo(responseError.getStatus());
                retorno.getMensagemRetorno().setDescricao(stringMax(responseError.getMessage(), 255));
                return retorno;
            }
        }
        return retorno;
    }

    private MensagemRetornoDTO solicitarRegistro(SolicitarRegistroRequest request, ResponsePadraoMs response, Financeira financeira) throws IOException, ServiceException {
        ResponseEntity<ResponsePadraoMs> responseEntity = null;
        try {
            String chaveAcesso = getAutenticacao(financeira);
            if (chaveAcesso == null) {
                return getMensagemRetornoSemAcesso();
            }

            RestTemplate restTemplate = new RestTemplate(getClientHttpRequestFactory());
            responseEntity = restTemplate.postForEntity(getDefaultUri() + getUrlCadastrar(), new HttpEntity<>(getJSON(request), getHttpHeaders(chaveAcesso)), ResponsePadraoMs.class);
            if (responseEntity.getStatusCode() == HttpStatus.OK) {
                BeanUtils.copyProperties(responseEntity.getBody(), response);
                MensagemRetornoDTO sucesso = getSucesso(Uf.MS);
                sucesso.setNumeroDetran(response.getNumeroSequencial());
                return sucesso;
            }
        } catch (HttpClientErrorException e) {
            if (e.getStatusCode() == HttpStatus.BAD_REQUEST || e.getStatusCode() == HttpStatus.INTERNAL_SERVER_ERROR) {
                BeanUtils.copyProperties(new ObjectMapper().readValue(new String(e.getResponseBodyAsByteArray(), "UTF-8"), ResponsePadraoMs.class), response);
                MensagemRetornoDTO retorno = getErroPadrao(Uf.MS);
                retorno.setDescricao(response.getStatus() + " - " + stringMax(response.getMessage(), 255));
                return retorno;
            }
        }
        return getErroPadrao(Uf.MS);
    }

    protected String compressaoArquivo(Veiculo veiculo, String referenciaArquivo) throws IOException {
        // reducao arquivos > 6mb
        long length = new File(getFileDirRead() + referenciaArquivo).length();
        if (length > 6 * 1000000) {
            PDDocument pdDocument = new PDDocument();
            PDDocument oDocument = PDDocument.load(new File(getFileDirRead() + referenciaArquivo));
            PDFRenderer pdfRenderer = new PDFRenderer(oDocument);
            int numberOfPages = oDocument.getNumberOfPages();
            PDPage page = null;
            int dpi = 100;
            if (length > 7 * 1000000)
                dpi = 90;
            for (int i = 0; i < numberOfPages; i++) {
                page = new PDPage(PDRectangle.A4);
                BufferedImage bim = pdfRenderer.renderImageWithDPI(i, dpi, ImageType.RGB);
                PDImageXObject pdImage = JPEGFactory.createFromImage(pdDocument, bim);
                PDPageContentStream contentStream = new PDPageContentStream(pdDocument, page);
                contentStream.drawImage(pdImage, 0.5f, 0.5f, PDRectangle.A4.getWidth(), PDRectangle.A4.getHeight());
                contentStream.close();
                pdDocument.addPage(page);
            }

            referenciaArquivo = veiculo.getContrato().getId() + "-compress.pdf";
            pdDocument.save(getFileDirRead() + referenciaArquivo);
            pdDocument.close();
            oDocument.close();
        }
        return referenciaArquivo;
    }

    public BoletoCobrancaDTO[] consultarBoleto(String documento) throws ServiceException {
        try {
            String chaveAcesso = getAutenticacao(financeiraRepository.findByDocumento(documento));
            if (chaveAcesso == null) {
                return null;
            }
            RestTemplate restTemplate = new RestTemplate(getClientHttpRequestFactory());
            HttpHeaders headers = getHttpHeaders(chaveAcesso);
            HttpEntity<String> entity = new HttpEntity<String>("parameters", headers);
            ResponseEntity<BoletoCobrancaDTO[]> responseEntity = restTemplate.exchange(getDefaultUri() + getUrlConsultaBoleto() + "?financeiraCnpj=" + documento, HttpMethod.GET, entity, BoletoCobrancaDTO[].class);
            if (responseEntity.getStatusCode() != HttpStatus.OK) {
                throw new ServiceException("Não foi possível completar a requisição, com o erro: " + responseEntity.getStatusCode());
            }
            return responseEntity.getBody();
        } catch (HttpClientErrorException e) {
            throw new ServiceException(e.getMessage());
        }
    }

    public MensagemRetornoDTO envioImagemDesbloqueio(Veiculo veiculo, String referenciaArquivo) throws ServiceException, IOException {
        long end = System.nanoTime();
        ResponsePadraoMs response = new ResponsePadraoMs();
        EnvioImagemDesbloqueioRequest request = new EnvioImagemDesbloqueioRequest();
        ResponseEntity<ResponsePadraoMs> responseEntity = null;
        try {
            String chaveAcesso = getAutenticacao(veiculo.getContrato().getFinanceira());

            if (chaveAcesso == null) {
                return getMensagemRetornoSemAcesso();
            }

            referenciaArquivo = compressaoArquivo(veiculo, referenciaArquivo);

            FileSystemResource f = new FileSystemResource(getFileDirRead() + referenciaArquivo);
            byte[] arquivo = new byte[(int) f.contentLength()];

            FileInputStream fis = new FileInputStream(f.getFile());
            fis.read(arquivo);
            fis.close();

            request.setImagemPendente(Base64.encodeBase64String(arquivo));
            request.setNumeroSequencial(veiculo.getNumeroRegistroDetran());

            RestTemplate restTemplate = new RestTemplate(getClientHttpRequestFactory());
            responseEntity = restTemplate.postForEntity(getDefaultUri() + getUrlDesbloquearContrato(), new HttpEntity<>(getJSON(request), getHttpHeaders(chaveAcesso)), ResponsePadraoMs.class);
            if (responseEntity.getStatusCode() == HttpStatus.OK) {
                BeanUtils.copyProperties(responseEntity.getBody(), response);
                return getSucesso(Uf.MS);
            }
        } catch (HttpClientErrorException e) {
            if (e.getStatusCode() == HttpStatus.BAD_REQUEST || e.getStatusCode() == HttpStatus.INTERNAL_SERVER_ERROR) {
                BeanUtils.copyProperties(new ObjectMapper().readValue(new String(e.getResponseBodyAsByteArray(), "UTF-8"), ResponsePadraoMs.class), response);
                MensagemRetornoDTO retorno = getErroPadrao(Uf.MS);
                retorno.setDescricao(response.getStatus() + " - " + stringMax(response.getMessage(), 255));
                return retorno;
            }
        } finally {
            registrarLog(veiculo.getContrato(), end, request, response);
        }
        return getErroPadrao(Uf.MS);
    }

    private Boolean isUltimaEtapa(String etapa) {
        for (Etapa etapaDeFinalizacao : Etapa.values()) {
            if (etapaDeFinalizacao.getEtapa().equals("final") && etapaDeFinalizacao.name().equals(etapa))
                return true;
        }
        return false;
    }

    private Boolean isResultadoConferenciaDetranSucesso(String resultado) {
        return resultado.contains("Sucesso na conferencia do contrato");
    }

    protected ClientHttpRequestFactory getClientHttpRequestFactory() {
        CloseableHttpClient httpClient = HttpClients.custom().setSSLHostnameVerifier(new NoopHostnameVerifier()).build();
        HttpComponentsClientHttpRequestFactory requestFactory = new HttpComponentsClientHttpRequestFactory();
        requestFactory.setHttpClient(httpClient);
        requestFactory.setConnectTimeout(getConnectionTimeout());
        return requestFactory;
    }

    protected SolicitarRegistroRequest putRequest(Contrato contrato, Veiculo veiculo) {

        Financeira financeira = financeiraRepository.findOne(contrato.getFinanceira().getId());

        SolicitarRegistroRequest request = new SolicitarRegistroRequest();

        request.setFinanceiraCnpj(financeira.getDocumento());
        request.setCpfCliente(contrato.getCpfCnpjDevedorFinanciado());
        request.setNomeCliente(deAccent(contrato.getNomeDevedorFinanciado()));
        request.setCepEnder(contrato.getCepDevedor());
        request.setBairroEnder(deAccent(contrato.getBairroDevedor()));
        request.setEndereco(deAccent(contrato.getEnderecoDevedor()));
        request.setNumEnder(StringUtils.isNotBlank(contrato.getNumeroEnderecoDevedor()) ? contrato.getNumeroEnderecoDevedor() : "0");
        request.setBairroEnder(deAccent(contrato.getBairroDevedor()));
        Municipio municipio = municipioRepository.findOne(contrato.getMunicipioDevedor().getId());
        request.setCodMunicipio(Integer.valueOf(municipio.getCodigoDenatran()));
        request.setDddTel(contrato.getDddDevedor());
        request.setNumTel(String.valueOf(contrato.getTelefoneDevedor()));
        request.setChassi(veiculo.getNumeroChassi());
        request.setTipoGravame(Integer.valueOf(contrato.getTipoRestricao().getCodigo()));
        request.setNumeroGravame(Integer.valueOf(veiculo.getNumeroGravame()));
        request.setDataContrato(getDateStringFromPattern(contrato.getDataContrato(), "yyyy-MM-dd"));
        request.setDataLiberCred(getDateStringFromPattern(contrato.getDataLiberacaoCredito(), "yyyy-MM-dd"));
        request.setValorFinanc(contrato.getValorCredito().doubleValue());
        request.setValorParcela(contrato.getValorParcela().doubleValue());
        request.setQtdParcelas(contrato.getQuantidadeMeses());

        request.setTxJurosAno(contrato.getValorTaxaJurosAno() != null ? contrato.getValorTaxaJurosAno().doubleValue() : 0.00);
        request.setTxJurosMes(contrato.getValorTaxaJurosMes() != null ? contrato.getValorTaxaJurosMes().doubleValue() : 0.00);
        request.setTaxaMulta(contrato.getValorTaxaMulta() != null ? contrato.getValorTaxaMulta().doubleValue() : 0.00);
        request.setTaxaMora(contrato.getValorTaxaMoraDia() != null ? contrato.getValorTaxaMoraDia().doubleValue() : 0.00);
        request.setIndices(contrato.getSiglaIndiceFinaceiro().getSigla());
        request.setValorIof(contrato.getValorIOF() != null ? contrato.getValorIOF().doubleValue() : 0.00);
        request.setDataPrimParc(getDateStringFromPattern(contrato.getDataVencimentoPrimeiraParcela(), "yyyy-MM-dd"));
        request.setDataUltmParc(getDateStringFromPattern(contrato.getDataVencimentoUltimaParcela(), "yyyy-MM-dd"));
        request.setNumGrupo(contrato.getNumeroGrupoConsorcio());
        if (contrato.getNumeroCotaConsorcio() != null) {
            request.setNumCota(contrato.getNumeroCotaConsorcio().intValue());
        }
        request.setClausulas(deAccent(contrato.getClausulaPenalVrg()));
        request.setUfContrato(contrato.getUfRegistro().name());
        request.setSeqContrato(String.valueOf(0));
        request.setAnoContrato(getDateStringFromPattern(contrato.getDataContrato(), "yyyy"));

        if (StringUtils.isBlank(contrato.getNumeroAditivoContrato())) {
            request.setFlagOperacao(1);
        } else {
            request.setFlagOperacao(3);
            if (contrato.getAlteracao() == Boolean.TRUE) {
                request.setFlagOperacao(4);
            }
            request.setSeqAditivo(0);
            request.setUfAditivo(contrato.getUfRegistro().name());
            request.setAnoAditivo(Integer.valueOf(getDateStringFromPattern(contrato.getDataAditivoContrato(), "yyyy")));
        }

        request.setNumContrato(contrato.getNumeroContrato());
        request.setDataDeRegistro(getDateStringFromPattern(new Date(), "yyy-MM-dd"));

        return request;
    }

    protected String getAutenticacao(Financeira fin) throws ServiceException {
//        AUTENTICAÇÃO ANTIGA
//        AcessoSenhaDTO acessoSenhaDTO = acessoSenhaservice.recuperarAcessoDetranFinanceira(Uf.MS, fin);
//        if (acessoSenhaDTO == null) {
//            throw new ServiceException("Não existem credenciais configuradas para o agente financeiro.");
//        }
//        AutenticacaoRequest request = new AutenticacaoRequest(acessoSenhaDTO.getAcessos()[0],
//                acessoSenhaDTO.getAcessos()[1]);

        AutenticacaoRequest request = new AutenticacaoRequest(getUsuario(), getSenha());
        RestTemplate restTemplate = new RestTemplate(getClientHttpRequestFactory());
        try {
            ResponseEntity<AutenticacaoResponse> responseEntity = restTemplate
                    .postForEntity(
                            getDefaultUri() + getUrlAutenticacao(),
                            new HttpEntity<>(request, getHttpHeaders()),
                            AutenticacaoResponse.class
                    );
            if (responseEntity.getStatusCode() == HttpStatus.OK)
                return responseEntity.getBody().getToken();
        } catch (Exception e) {
            e.printStackTrace();
            getLogger().error("FALHA NA AUTENTICAÇÃO DA API DETRAN MS" + e.getMessage());
        }
        return null;
    }

    protected HttpHeaders getHttpHeaders(String token) {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        if (token != null) {
            headers.add("Authorization", "Bearer " + token);
        }
        return headers;
    }

    private HttpHeaders getHttpHeaders() {
        return getHttpHeaders(null);
    }

    protected MensagemRetornoDTO getMensagemRetornoSemAcesso() {
        MensagemRetornoDTO retorno = new MensagemRetornoDTO();
        retorno.setCodigo(TOKEN_INVALIDO);
        retorno.setDescricao("Cliente não autorizado: Token de acesso inválido.");
        return retorno;
    }

    public InputStream buscarBoletoStream(String numeroBoleto, String cnpjFinanceira) throws ServiceException {
        String chaveAcesso = getAutenticacao(financeiraRepository.findByDocumento(cnpjFinanceira));
        if (chaveAcesso == null)
            return null;

        Map<String, String> numeroDocumento = new HashMap<>();
        numeroDocumento.put("numeroDocumento", numeroBoleto);

        RestTemplate restTemplate = new RestTemplate(getClientHttpRequestFactory());
        ResponseEntity<BoletoStreamDTO> responseEntity = restTemplate
                .postForEntity(
                        getDefaultUri() + getUrlConsultaBoletoByte(),
                        new HttpEntity<>(getJSON(numeroDocumento), getHttpHeaders(chaveAcesso)),
                        BoletoStreamDTO.class
                );
        if (responseEntity.getStatusCode() != HttpStatus.OK)
            throw new ServiceException("Não foi possível completar a requisição, com o erro: " + responseEntity.getStatusCode());
        return new ByteArrayInputStream(responseEntity.getBody().getArquivo());
    }

    public String buscaBoletoByte(String boleto, String documento) throws ServiceException, IOException {
        try {
            String chaveAcesso = getAutenticacao(financeiraRepository.findByDocumento(documento));
            if (chaveAcesso == null) {
                return null;
            }

            Map<String, String> numeroDocumento = new HashMap<>();
            numeroDocumento.put("numeroDocumento", boleto);

            RestTemplate restTemplate = new RestTemplate(getClientHttpRequestFactory());
            ResponseEntity<String> responseEntity = restTemplate.postForEntity(getDefaultUri() + getUrlConsultaBoletoByte(), new HttpEntity<>(getJSON(numeroDocumento), getHttpHeaders(chaveAcesso)), String.class);
            if (responseEntity.getStatusCode() != HttpStatus.OK) {
                throw new ServiceException("Não foi possível completar a requisição, com o erro: " + responseEntity.getStatusCode());
            }
            String retorno = responseEntity.getBody();
            JsonNode jsonNode = new ObjectMapper().readTree(retorno);
            return jsonNode.get("arquivo").asText();
        } catch (HttpClientErrorException e) {
            throw new ServiceException(e.getMessage());
        }
    }

    @Override
    public String getUrl() {
        return DEFAULT_URI + URL_CADASTRAR;
    }

    public String getDefaultUri() {
        return DEFAULT_URI;
    }

    public String getUrlCadastrar() {
        return URL_CADASTRAR;
    }

    public String getUrlAutenticacao() {
        return URL_AUTENTICACAO;
    }

    public String getUrlDesbloquearContrato() {
        return URL_DESBLOQUEAR_CONTRATO;
    }

    public String getUrlEnvioImagem() {
        return URL_ENVIO_IMAGEM;
    }

    public String getUrlCorrecaoImagem() {
        return URL_CORRECAO_IMAGEM;
    }

    public String getUrlBuscaAndamento() {
        return URL_BUSCA_ANDAMENTO;
    }

    public String getUrlConsultaBoleto() {
        return URL_CONSULTA_BOLETO;
    }

    public String getUrlConsultaBoletoByte() {
        return URL_CONSULTA_BOLETO_BYTE;
    }

    public Integer getConnectionTimeout() {
        return connectionTimeout;
    }

    public String getFileDirRead() {
        return FILE_DIR_READ;
    }

    @Override
    public RegistroEnvioService getRegistroEnvioService() {
        return registroEnvioService;
    }

    @Override
    public UsuarioService getUsuarioService() {
        return usuarioService;
    }

    @Override
    public MensagemRetornoRepository getMensagemRetornoRepository() {
        return mensagemRetornoRepository;
    }

    public AcessoSenhaService getAcessoSenhaService() {
        return acessoSenhaservice;
    }

    public String getUsuario() {
        return USUARIO;
    }

    public void setUsuario(String USUARIO) {
        this.USUARIO = USUARIO;
    }

    public String getSenha() {
        return SENHA;
    }

    public void setSenha(String SENHA) {
        this.SENHA = SENHA;
    }

    @Override
    public Uf getUf() {
        return Uf.MS;
    }
}
