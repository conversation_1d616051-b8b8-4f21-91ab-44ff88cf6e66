package com.registrocontrato.registro.service.cobranca;

import com.registrocontrato.infra.entity.SimNao;
import com.registrocontrato.infra.entity.SituacaoFinanceiraEstado;
import com.registrocontrato.infra.entity.Uf;
import com.registrocontrato.infra.exception.ServiceException;
import com.registrocontrato.registro.entity.Cobranca;
import com.registrocontrato.registro.enums.PagamentoCobranca;
import com.registrocontrato.registro.enums.SituacaoCobranca;
import com.registrocontrato.registro.repository.CobrancaRepository;
import com.registrocontrato.registro.service.CredenciamentoService;
import com.registrocontrato.seguranca.service.FinanceiraService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.faces.FacesException;
import java.util.Date;
import java.util.List;
import java.util.Objects;

@Component
public class CobrancaValidator {

    private static Logger logger = LoggerFactory.getLogger(CobrancaValidator.class);


    private final FinanceiraService financeiraService;

    private final CredenciamentoService credenciamentoService;

    private final CobrancaRepository cobrancaRepository;

    public CobrancaValidator(FinanceiraService financeiraService, CredenciamentoService credenciamentoService, CobrancaRepository cobrancaRepository) {
        this.financeiraService = financeiraService;
        this.credenciamentoService = credenciamentoService;
        this.cobrancaRepository = cobrancaRepository;
    }

    public void validacaoInicial(Cobranca cobranca) throws ServiceException {
        validarDatas(cobranca);
        validarUf(cobranca);
        validarCredenciamento(cobranca);
    }

    private void validarUf(Cobranca cobranca) throws ServiceException {
        if (Objects.isNull(cobranca.getEstado()))
            throw new ServiceException("Necessário Informar a UF");
    }

    private void validarCredenciamento(Cobranca cobranca) throws ServiceException {
        if (cobranca.getEstado() != Uf.BR && !credenciamentoService.findByUfAndDataInicioLessThanEqualAndDataFimGreaterThanEqual(cobranca).isPresent())
            throw new ServiceException("Não existe credenciamento ativo na UF: " + cobranca.getEstado() + " e no período informado.");
    }

    private void validarDatas(Cobranca cobranca) throws ServiceException {
        Date dataFim = cobranca.getDataFim();
        Date dataInicio = cobranca.getDataInicio();
        if (dataFim.before(dataInicio))
            throw new ServiceException("A data inicial deve ser anterior a data final");
    }

    public void validateValoresCobranca(Cobranca c, List<String> cnpjsNumeroPedido) throws ServiceException {
        if (cnpjsNumeroPedido.contains(c.getFinanceira().getDocumento()) && (Objects.isNull(c.getNumeroPedido()) || c.getNumeroPedido().isEmpty())) {
            throw new ServiceException("Cobrança para o cliente " + c.getFinanceira().getDocumento() + " - " + c.getFinanceira().getNome() + " não pode ser enviada sem o número do pedido");
        }
        if (c.getValorCobranca() == null || c.getValorCredenciada() == null || c.getValorDetran() == null) {
            throw new ServiceException("Informar todos os valor da cobrança: (valor total, valor da credenciada e valor do Detran)");
        }
    }

    public void verificaNumeroPedidoCobrancas(List<Cobranca> cobrancas, List<String> cnpjRequerNumeroPedido) {
        for (Cobranca cobranca : cobrancas) {
            if (cnpjRequerNumeroPedido.contains(cobranca.getFinanceira().getDocumento())) {
                if (Objects.isNull(cobranca.getNumeroPedido()) || cobranca.getNumeroPedido().isEmpty()) {
                    throw new FacesException("Cobrança para o cliente " + cobranca.getFinanceira().getDocumento() + " - " + cobranca.getFinanceira().getNome() + " não pode ser enviada sem o número do pedido");
                }
            }
        }
    }

    public void validaQuantidadeDeRegistrosNaGeracao(Cobranca cobranca) throws ServiceException {
        if (cobranca.getQuantidadeRegistros() <= 0 && cobranca.getQuantidadeRegistrosSng() <= 0 && cobranca.getQuantidadeBaixaSng() <= 0 && cobranca.getQuantidadeCancelamentoBaixaSng() <= 0) {
            throw new ServiceException(String.format("Erro ao Gerar a Cobrança para: %s, %s - deve possuir ao menos uma operação a ser cobrada", cobranca.getFinanceira(), cobranca.getEstado()));
        }
    }

    public void validaQuantidadeDeRegistros(Cobranca cobranca, Long quantidadeAlterada) throws ServiceException {
        if (cobranca.getQuantidadeRegistros() < quantidadeAlterada) {
            throw new ServiceException(String.format("Erro na Geração da Cobrança: %s, %s", cobranca.getQuantidadeRegistros(), cobranca.getEstado()));
        }
    }

    public void validarGravacao(Cobranca cobranca, List<String> cnpjsNumeroPedido) throws ServiceException {

        verificaExistenciaDaCobranca(cobranca);
        Cobranca entity = cobrancaRepository.findOne(cobranca.getId());
        validaAlteracaoDaCobranca(cobranca, entity, cnpjsNumeroPedido);

    }

    public void validarSituacaoFinanceiraEstado(Cobranca cobranca) throws ServiceException {
        if (cobranca.getEstado() != Uf.BR) {
            SituacaoFinanceiraEstado situacao = financeiraService.findSituacaoFinanceiraEstado(cobranca.getEstado(), cobranca.getFinanceira());
            if (Objects.isNull(situacao)) {
                throw new ServiceException("A financeira não possui credenciamento para esse estado");
            }
        }
    }

    public void verificaExistenciaDaCobranca(Cobranca cobranca) throws ServiceException {
        if (Objects.isNull(cobranca.getId())) {
            throw new ServiceException("Cobrança inexistente, não é possível fazer a verificação se a mesma está paga");
        }
        if (Objects.isNull(cobrancaRepository.findOne(cobranca.getId()))) {
            throw new ServiceException("Cobrança inexistente, não é possível fazer a verificação");
        }
    }

    private void validaAlteracaoDaCobranca(Cobranca cobranca, Cobranca cobrancaExistente, List<String> cnpjsNumeroPedido) throws ServiceException {
        if (Objects.nonNull(cobranca.getPagamentoCobranca()) && cobranca.getSituacaoCobranca() != SituacaoCobranca.PAGA) {
            throw new ServiceException("Só é possível incluir/alterar pagamento com cobrança paga");
        }

        if (Objects.isNull(cobranca.getDataRegistroPagamento()) && Objects.nonNull(cobranca.getPagamentoCobranca())) {
            throw new ServiceException("Necessário informar pagamento");
        }

        if (Objects.nonNull(cobranca.getPagamentoCobranca()) && Objects.isNull(cobranca.getDataRegistroPagamento())) {
            throw new ServiceException("Necessário informar data de registro do pagamento");
        }

        if (Objects.isNull(cobranca.getPagamentoCobranca()) && cobranca.getSituacaoCobranca() == SituacaoCobranca.PAGA) {
            throw new ServiceException("Não é possível colocar cobrança como paga sem o pagamento");
        }

        if (cobrancaExistente.isPaga() && cobrancaExistente.getPagamentoCobranca() == PagamentoCobranca.PAGO_OK) {
            throw new ServiceException("Não é possível alterar a situação de um cobrança paga");
        }

        if (cobranca.isFaturada() && cnpjsNumeroPedido.contains(cobrancaExistente.getFinanceira().getDocumento()) && (Objects.isNull(cobranca.getNumeroPedido()) || cobranca.getNumeroPedido().isEmpty())) {
            throw new ServiceException("Cobrança para o cliente " + cobranca.getFinanceira().getDocumento() + " - " + cobranca.getFinanceira().getNome() + " não pode ser enviada sem o número do pedido");
        }

        boolean cobrancaSemBoleto = (cobranca.getFinanceira().getCobrancaSemBoleto() != null && cobranca.getFinanceira().getCobrancaSemBoleto().equals(SimNao.S));
        if (cobranca.isEnviada() && !(cobrancaSemBoleto || cobranca.getBoletoEmitido())) {
            throw new ServiceException("A cobrança precisa estar na situação: FATURADA");
        }
        if (cobranca.isEnviada() && cobranca.getNotaFiscal() == null) {
            throw new ServiceException("Cobrança Não pode ser enviada sem Nota Fiscal");
        }
    }
}
