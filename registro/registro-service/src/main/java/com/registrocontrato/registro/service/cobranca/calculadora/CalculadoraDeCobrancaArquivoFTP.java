package com.registrocontrato.registro.service.cobranca.calculadora;

import com.registrocontrato.infra.exception.ServiceException;
import com.registrocontrato.registro.entity.Cobranca;
import com.registrocontrato.registro.entity.ConteudoArquivoCobranca;
import com.registrocontrato.registro.entity.Credenciamento;
import com.registrocontrato.registro.service.ArquivoCobrancaService;
import com.registrocontrato.registro.service.CredenciamentoService;
import com.registrocontrato.registro.service.CupomDescontoService;
import com.registrocontrato.registro.service.cobranca.CobrancaService;
import com.registrocontrato.registro.service.cobranca.gravame.GravameService;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

import java.math.BigDecimal;
import java.util.Objects;

public abstract class CalculadoraDeCobrancaArquivoFTP extends CalculadoraDeCobrancaDefault {

    protected final Log logger = LogFactory.getLog(getClass());

    protected final ArquivoCobrancaService arquivoCobrancaService;

    public CalculadoraDeCobrancaArquivoFTP(CobrancaService cobrancaService, CredenciamentoService credenciamentoService, CupomDescontoService cupomDescontoService, ArquivoCobrancaService arquivoCobrancaService, GravameService gravameService) {
        super(cobrancaService, credenciamentoService, cupomDescontoService, gravameService);
        this.arquivoCobrancaService = arquivoCobrancaService;
    }

    @Override
    protected void definirValoresDaCobranca(Cobranca cobranca, Credenciamento credenciamento) throws ServiceException {
        super.definirValoresDaCobranca(cobranca, credenciamento);
        cobranca.setValorCobrancaInformadoDetran(getValorBoletoDetran(cobranca));
    }

    protected BigDecimal getValorBoletoDetran(Cobranca cobranca) {
        ConteudoArquivoCobranca conteudoArquivoCobranca =
                arquivoCobrancaService.findByUfAndFinanceiraAndPeriodo(cobranca.getEstado(), cobranca.getFinanceira().getDocumento(), cobranca.getDataInicio(), cobranca.getDataFim());
        if (Objects.nonNull(conteudoArquivoCobranca))
            return conteudoArquivoCobranca.getValor();
        return BigDecimal.ZERO;
    }

}
