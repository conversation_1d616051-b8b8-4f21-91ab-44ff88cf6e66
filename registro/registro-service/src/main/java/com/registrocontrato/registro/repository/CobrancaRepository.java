package com.registrocontrato.registro.repository;

import com.registrocontrato.infra.entity.Financeira;
import com.registrocontrato.infra.entity.Uf;
import com.registrocontrato.infra.service.BaseRepository;
import com.registrocontrato.registro.entity.Cobranca;
import com.registrocontrato.registro.entity.ConteudoArquivoCobranca;
import com.registrocontrato.registro.entity.Contrato;
import com.registrocontrato.registro.enums.SituacaoCobranca;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Optional;

@Repository
public interface CobrancaRepository extends BaseRepository<Cobranca> {

    @Query("SELECT DISTINCT c.financeira from Contrato c WHERE c.cobranca = :cobranca")
    List<Financeira> contarNumeroDeFinanceirasVinculadaNaCobranca(@Param("cobranca") Cobranca cobranca);

    @Modifying
    @Query("update Contrato c set c.cobranca = :cobranca, c.situacaoFinanceira = 'COBRADO' "
            + "where c.cobranca is null "
            + "and c.situacao != 'ERRO' "
            + "and c.financeira = :financeira "
            + "and c.ufRegistro in (:uf) "
            + "and c.situacaoFinanceira = 'NAO_PAGO' "
            + "and (c.ufRegistro != 'CE' or (c.situacao = 'ATIVO')) "
            + "and (c.ufRegistro != 'AP' or (c.situacao = 'ATIVO')) "
            + "and (c.ufRegistro != 'MS' or (c.situacao = 'ATIVO')) "
            + "and (c.ufRegistro != 'PR' or (c.tipoContrato != 'CESSAO_DIREITO_DEVEDOR')) "
            + "and c.dataConclusaoDETRAN between :dataInicio and :dataFim ")
    int setContratoCobrado(@Param("cobranca") Cobranca cobranca,
                           @Param("financeira") Financeira financeira,
                           @Param("uf") List<Uf> uf,
                           @Param("dataInicio") Date dataInicio,
                           @Param("dataFim") Date dataFim);

    @Modifying
    @Query("update ContratoRsng c set c.cobranca = :cobranca, c.situacaoFinanceira = 'COBRADO' "
            + "where c.cobranca is null "
            + "and c.situacao != 'ERRO' "
            + "and c.financeira = :financeira "
            + "and c.ufRegistro in (:uf) "
            + "and c.situacaoFinanceira = 'NAO_PAGO' "
            + "and c.dataConclusaoRsng between :dataInicio and :dataFim ")
    int setContratoCobradoSng(@Param("cobranca") Cobranca cobranca,
                           @Param("financeira") Financeira financeira,
                           @Param("uf") List<Uf> uf,
                           @Param("dataInicio") Date dataInicio,
                           @Param("dataFim") Date dataFim);

    @Modifying
    @Query("update Contrato c set c.situacaoFinanceira = 'PAGO' where c.cobranca = :cobranca")
    int setContratoPago(@Param("cobranca") Cobranca cobranca);

    @Query("select count(c) from Contrato c "
            + "where c.financeira = :financeira "
            + "and c.cobranca is null "
            + "and c.situacao != 'ERRO' "
            + "and c.ufRegistro = :uf "
            + "and c.situacaoFinanceira = 'NAO_PAGO' "
            + "and (c.ufRegistro != 'CE' or (c.situacao = 'ATIVO')) "
            + "and (c.ufRegistro != 'AP' or (c.situacao = 'ATIVO')) "
            + "and (c.ufRegistro != 'MS' or (c.situacao = 'ATIVO')) "
            + "and (c.ufRegistro != 'PR' or (c.tipoContrato != 'CESSAO_DIREITO_DEVEDOR')) "
            + "and c.dataConclusaoDETRAN between :dataInicio and :dataFim")
    Long countContratosByFinanceiraAndUfRegistro(@Param("financeira") Financeira financeira, @Param("uf") Uf uf, @Param("dataInicio") Date dataInicio, @Param("dataFim") Date dataFim);

    @Query("select count(c) from Contrato c "
           + "where c.cobranca = :cobranca "
           + "and c.ufRegistro = :uf")
    Long countContratosByCobrancaAndUfRegistro(@Param("cobranca") Cobranca cobranca, @Param("uf") Uf uf);

    @Query("select count(c) from Contrato c "
            + "where c.financeira = :financeira "
            + "and c.cobranca = :cobranca "
            + "and c.situacao != 'ERRO' "
            + "and c.ufRegistro = :uf "
            + "and c.situacaoFinanceira = 'COBRADO' "
            + "and (c.ufRegistro != 'CE' or (c.situacao = 'ATIVO')) "
            + "and (c.ufRegistro != 'AP' or (c.situacao = 'ATIVO')) "
            + "and (c.ufRegistro != 'MS' or (c.situacao = 'ATIVO')) "
            + "and (c.ufRegistro != 'PR' or (c.tipoContrato != 'CESSAO_DIREITO_DEVEDOR')) "
            + "and c.dataConclusaoDETRAN between :dataInicio and :dataFim")
    Long countContratosByFinanceiraAndUfRegistro(@Param("cobranca") Cobranca cobranca, @Param("financeira") Financeira financeira, @Param("uf") Uf uf, @Param("dataInicio") Date dataInicio, @Param("dataFim") Date dataFim);

    @Query("select count(c) from Contrato c "
            + "where c.financeira = :financeira "
            + "and c.cobranca is null "
            + "and c.situacao != 'ERRO' "
            + "and c.ufRegistro = :uf "
            + "and c.situacaoFinanceira = 'NAO_PAGO' "
            + "and c.dataAditivoContrato is not null "
            + "and (c.ufRegistro != 'CE' or (c.situacao = 'ATIVO')) "
            + "and (c.ufRegistro != 'AP' or (c.situacao = 'ATIVO')) "
            + "and (c.ufRegistro != 'MS' or (c.situacao = 'ATIVO')) "
            + "and (c.ufRegistro != 'PR' or (c.tipoContrato != 'CESSAO_DIREITO_DEVEDOR')) "
            + "and c.dataConclusaoDETRAN between :dataInicio and :dataFim")
    Long countContratosByFinanceiraAndUfRegistroAditivo(@Param("financeira") Financeira financeira, @Param("uf") Uf uf, @Param("dataInicio") Date dataInicio, @Param("dataFim") Date dataFim);

    @Query("select count(c) from Contrato c "
            + "where c.financeira = :financeira "
            + "and c.cobranca = :cobranca "
            + "and c.situacao != 'ERRO' "
            + "and c.ufRegistro = :uf "
            + "and c.situacaoFinanceira = 'COBRADO' "
            + "and c.dataAditivoContrato is not null "
            + "and (c.ufRegistro != 'CE' or (c.situacao = 'ATIVO')) "
            + "and (c.ufRegistro != 'AP' or (c.situacao = 'ATIVO')) "
            + "and (c.ufRegistro != 'MS' or (c.situacao = 'ATIVO')) "
            + "and (c.ufRegistro != 'PR' or (c.tipoContrato != 'CESSAO_DIREITO_DEVEDOR')) "
            + "and c.dataConclusaoDETRAN between :dataInicio and :dataFim")
    Long countContratosByFinanceiraAndUfRegistroAditivo(@Param("cobranca") Cobranca cobranca, @Param("financeira") Financeira financeira, @Param("uf") Uf uf, @Param("dataInicio") Date dataInicio, @Param("dataFim") Date dataFim);

    @Query("select count(c) from Contrato c "
            + "where c.cobranca is null "
            + "and c.situacao != 'ERRO' "
            + "and c.ufRegistro = :uf "
            + "and c.situacaoFinanceira = 'NAO_PAGO' "
            + "and (c.ufRegistro != 'CE' or (c.situacao = 'ATIVO')) "
            + "and (c.ufRegistro != 'AP' or (c.situacao = 'ATIVO')) "
            + "and (c.ufRegistro != 'MS' or (c.situacao = 'ATIVO')) "
            + "and (c.ufRegistro != 'PR' or (c.tipoContrato != 'CESSAO_DIREITO_DEVEDOR')) "
            + "and c.dataConclusaoDETRAN between :dataInicio and :dataFim")
    Long countContratosByUfRegistro(@Param("uf") Uf uf, @Param("dataInicio") Date dataInicio, @Param("dataFim") Date dataFim);


    @Query("select count(c) from Contrato c "
            + "where c.cobranca is null "
            + "and c.situacao != 'ERRO' "
            + "and c.ufRegistro = :uf "
            + "and c.situacaoFinanceira = 'NAO_PAGO' "
            + "and c.financeira in (:financeiras) "
            + "and (c.ufRegistro != 'CE' or (c.situacao = 'ATIVO')) "
            + "and (c.ufRegistro != 'AP' or (c.situacao = 'ATIVO')) "
            + "and (c.ufRegistro != 'MS' or (c.situacao = 'ATIVO')) "
            + "and (c.ufRegistro != 'PR' or (c.tipoContrato != 'CESSAO_DIREITO_DEVEDOR')) "
            + "and c.dataConclusaoDETRAN between :dataInicio and :dataFim")
    Long countContratosByFinanceirasUfRegistro(@Param("financeiras") List<Financeira> financeiras, @Param("uf") Uf uf, @Param("dataInicio") Date dataInicio, @Param("dataFim") Date dataFim);

    @Query("select count(v) from Contrato c inner join c.veiculos v "
            + "where c.financeira = :financeira "
            + "and c.cobranca is null "
            + "and c.situacao != 'ERRO' "
            + "and c.ufRegistro = :uf "
            + "and c.dataAditivoContrato is not null "
            + "and c.situacaoFinanceira = 'NAO_PAGO' "
            + "and (c.ufRegistro != 'CE' or (c.situacao = 'ATIVO')) "
            + "and (c.ufRegistro != 'AP' or (c.situacao = 'ATIVO')) "
            + "and (c.ufRegistro != 'MS' or (c.situacao = 'ATIVO')) "
            + "and (c.ufRegistro != 'PR' or (c.tipoContrato != 'CESSAO_DIREITO_DEVEDOR')) "
            + "and c.dataConclusaoDETRAN between :dataInicio and :dataFim")
    Long countVeiculosByFinanceiraAndUfRegistroAditivo(@Param("financeira") Financeira financeira, @Param("uf") Uf uf, @Param("dataInicio") Date dataInicio, @Param("dataFim") Date dataFim);

    @Query("select count(v) from Contrato c inner join c.veiculos v "
            + "where c.financeira = :financeira "
            + "and c.cobranca = :cobranca "
            + "and c.situacao != 'ERRO' "
            + "and c.ufRegistro = :uf "
            + "and c.dataAditivoContrato is not null "
            + "and c.situacaoFinanceira = 'COBRADO' "
            + "and (c.ufRegistro != 'CE' or (c.situacao = 'ATIVO')) "
            + "and (c.ufRegistro != 'AP' or (c.situacao = 'ATIVO')) "
            + "and (c.ufRegistro != 'MS' or (c.situacao = 'ATIVO')) "
            + "and (c.ufRegistro != 'PR' or (c.tipoContrato != 'CESSAO_DIREITO_DEVEDOR')) "
            + "and c.dataConclusaoDETRAN between :dataInicio and :dataFim")
    Long countVeiculosByFinanceiraAndUfRegistroAditivo(@Param("cobranca") Cobranca cobranca, @Param("financeira") Financeira financeira, @Param("uf") Uf uf, @Param("dataInicio") Date dataInicio, @Param("dataFim") Date dataFim);

    @Query("select count(v) from Contrato c inner join c.veiculos v "
            + "where c.financeira = :financeira "
            + "and c.cobranca is null "
            + "and c.situacao != 'ERRO' "
            + "and c.ufRegistro = :uf "
            + "and c.situacaoFinanceira = 'NAO_PAGO' "
            + "and (c.ufRegistro != 'CE' or (c.situacao = 'ATIVO')) "
            + "and (c.ufRegistro != 'AP' or (c.situacao = 'ATIVO')) "
            + "and (c.ufRegistro != 'MS' or (c.situacao = 'ATIVO')) "
            + "and (c.ufRegistro != 'PR' or (c.tipoContrato != 'CESSAO_DIREITO_DEVEDOR')) "
            + "and c.dataConclusaoDETRAN between :dataInicio and :dataFim")
    Long countVeiculosByFinanceiraAndUfRegistro(@Param("financeira") Financeira financeira, @Param("uf") Uf uf, @Param("dataInicio") Date dataInicio, @Param("dataFim") Date dataFim);

    @Query("select count(v) from Contrato c inner join c.veiculos v "
            + "where c.financeira = :financeira "
            + "and c.cobranca = :cobranca "
            + "and c.situacao != 'ERRO' "
            + "and c.ufRegistro = :uf "
            + "and c.situacaoFinanceira = 'COBRADO' "
            + "and (c.ufRegistro != 'CE' or (c.situacao = 'ATIVO')) "
            + "and (c.ufRegistro != 'AP' or (c.situacao = 'ATIVO')) "
            + "and (c.ufRegistro != 'MS' or (c.situacao = 'ATIVO')) "
            + "and (c.ufRegistro != 'PR' or (c.tipoContrato != 'CESSAO_DIREITO_DEVEDOR')) "
            + "and c.dataConclusaoDETRAN between :dataInicio and :dataFim")
    Long countVeiculosByFinanceiraAndUfRegistro(@Param("cobranca") Cobranca cobranca, @Param("financeira") Financeira financeira, @Param("uf") Uf uf, @Param("dataInicio") Date dataInicio, @Param("dataFim") Date dataFim);

    @Query("select count(v) from Contrato c inner join c.veiculos v "
            + "where c.cobranca is null "
            + "and c.situacao != 'ERRO' "
            + "and c.situacaoFinanceira = 'NAO_PAGO' "
            + "and c.ufRegistro = :uf "
            + "and (c.ufRegistro != 'CE' or (c.situacao = 'ATIVO')) "
            + "and (c.ufRegistro != 'AP' or (c.situacao = 'ATIVO')) "
            + "and (c.ufRegistro != 'MS' or (c.situacao = 'ATIVO')) "
            + "and (c.ufRegistro != 'PR' or (c.tipoContrato != 'CESSAO_DIREITO_DEVEDOR')) "
            + "and c.dataConclusaoDETRAN between :dataInicio and :dataFim")
    Long countVeiculosByUfRegistro(@Param("uf") Uf uf, @Param("dataInicio") Date dataInicio, @Param("dataFim") Date dataFim);

    @Query("select count(v) from Contrato c inner join c.veiculos v "
            + "where c.cobranca is null "
            + "and c.situacao != 'ERRO' "
            + "and c.ufRegistro = :uf "
            + "and c.situacaoFinanceira = 'NAO_PAGO' "
            + "and c.financeira in (:financeiras) "
            + "and (c.ufRegistro != 'CE' or (c.situacao = 'ATIVO')) "
            + "and (c.ufRegistro != 'AP' or (c.situacao = 'ATIVO')) "
            + "and (c.ufRegistro != 'MS' or (c.situacao = 'ATIVO')) "
            + "and (c.ufRegistro != 'PR' or (c.tipoContrato != 'CESSAO_DIREITO_DEVEDOR')) "
            + "and c.dataConclusaoDETRAN between :dataInicio and :dataFim")
    Long countVeiculosByFinanceirasUfRegistro(@Param("financeiras") List<Financeira> financeiras, @Param("uf") Uf uf, @Param("dataInicio") Date dataInicio, @Param("dataFim") Date dataFim);

    @Query("select distinct f from Contrato c "
            + "inner join c.financeira f "
            + "inner join c.veiculos v "
            + "where c.cobranca is null "
            + "and c.situacao != 'ERRO' "
            + "and c.ufRegistro = :uf "
            + "and c.situacaoFinanceira = 'NAO_PAGO' "
            + "and (c.ufRegistro != 'CE' or (c.situacao = 'ATIVO')) "
            + "and (c.ufRegistro != 'AP' or (c.situacao = 'ATIVO')) "
            + "and (c.ufRegistro != 'MS' or (c.situacao = 'ATIVO')) "
            + "and (c.ufRegistro != 'PR' or (c.tipoContrato != 'CESSAO_DIREITO_DEVEDOR')) "
            + "and c.dataConclusaoDETRAN between :dataInicio and :dataFim ")
    List<Financeira> findByFinanceirasCobrancaAberta(@Param("uf") Uf uf, @Param("dataInicio") Date dataInicio, @Param("dataFim") Date dataFim);

    @Query("select distinct c.ufRegistro from Contrato c "
            + "inner join c.financeira f "
            + "inner join c.veiculos v "
            + "where c.cobranca is null "
            + "and c.situacao != 'ERRO' "
            + "and c.financeira = :financeira "
            + "and c.situacaoFinanceira = 'NAO_PAGO' "
            + "and (c.ufRegistro != 'CE' or (c.situacao = 'ATIVO')) "
            + "and (c.ufRegistro != 'AP' or (c.situacao = 'ATIVO')) "
            + "and (c.ufRegistro != 'MS' or (c.situacao = 'ATIVO')) "
            + "and (c.ufRegistro != 'PR' or (c.tipoContrato != 'CESSAO_DIREITO_DEVEDOR')) "
            + "and c.dataConclusaoDETRAN between :dataInicio and :dataFim")
    List<Uf> findUfsCobrancaAbertaPorFinanceira(@Param("financeira") Financeira financeira, @Param("dataInicio") Date dataInicio, @Param("dataFim") Date dataFim);

    @Modifying
    @Query("update Contrato c set c.cobranca = null, c.situacaoFinanceira = 'NAO_PAGO' where c.cobranca.id = :id")
    int setCobrancaContratoCancelada(@Param("id") Long id);


    @Query("select cob.estado, cast(cob.dataGeracao as date), sum(cob.valorCobranca), sum(cob.valorDetran), sum(cob.valorCredenciada) "
            + "from Cobranca cob inner join cob.financeira f "
            + "where exists (select 1 from Contrato c where c.financeira = f "
            + "and (:uf is null or c.ufRegistro = :uf)) "
            + "and f in (:financeiras) "
            + "and cob.dataGeracao between :dataInicio and :dataFim "
            + "group by cob.estado, cast(cob.dataGeracao as date) "
            + "order by cob.estado, cast(cob.dataGeracao as date)")
    List<Object[]> findPagasByPeriodo(@Param("financeiras") List<Financeira> financeiras,
                                      @Param("uf") Uf uf,
                                      @Param("dataInicio") Date dataInicio,
                                      @Param("dataFim") Date dataFim);

    @Query("select cob.estado, cast(cob.dataGeracao as date), sum(cob.quantidadeRegistros) "
            + "from Cobranca cob inner join cob.financeira f "
            + "where exists (select 1 from Contrato c where c.financeira = f "
            + "and (:uf is null or c.ufRegistro = :uf)) "
            + "and f in (:financeiras) "
            + "and cob.dataGeracao between :dataInicio and :dataFim "
            + "group by cob.estado, cast(cob.dataGeracao as date) "
            + "order by cob.estado, cast(cob.dataGeracao as date)")
    List<Object[]> findRegistrosByPeriodo(@Param("financeiras") List<Financeira> financeiras,
                                          @Param("uf") Uf uf,
                                          @Param("dataInicio") Date dataInicio,
                                          @Param("dataFim") Date dataFim);

    @Query("select cob.estado, cast(cob.dataGeracao as date), count(con.id) "
            + "from Cobranca cob inner join cob.financeira f inner join cob.contratos con "
            + "where exists (select 1 from Contrato c where c.financeira = f "
            + "and (:uf is null or c.ufRegistro = :uf)) "
            + "and f in (:financeiras) "
            + "and cob.dataGeracao between :dataInicio and :dataFim "
            + "group by cob.estado, cast(cob.dataGeracao as date) "
            + "order by cob.estado, cast(cob.dataGeracao as date)")
    List<Object[]> findContratosByPeriodo(@Param("financeiras") List<Financeira> financeiras,
                                          @Param("uf") Uf uf,
                                          @Param("dataInicio") Date dataInicio,
                                          @Param("dataFim") Date dataFim);

    @Query("select sum(c.valorCobranca) "
            + "from Cobranca c inner join c.financeira f "
            + "where (:uf is null or c.estado = :uf) "
            + "and f in (:financeiras) "
            + "and c.situacaoCobranca = 'ENVIADA' ")
    BigDecimal getValorCobrancasAberto(@Param("financeiras") List<Financeira> financeiras, @Param("uf") Uf uf);

    @Query("from ConteudoArquivoCobranca c join fetch c.arquivo a "
            + "where c.valida = 'S' and c.dataVencimento is not null "
            + "and c.numeroLote is not null and c.linhaDigitavel is not null "
            + "and a.dataInicioCobranca >= :dataInicio and a.dataFimCobranca <= :dataFim "
            + "and c.cnpjAgenteFinanceiro = :cnpj and a.uf = :uf")
    ConteudoArquivoCobranca findValoresCobranca(@Param("cnpj") String cnpj, @Param("dataInicio") Date dataInicio, @Param("dataFim") Date dataFim,
                                                @Param("uf") Uf uf);

    @Query("select c "
            + "from Cobranca c "
            + "inner join c.financeira f "
            + "where c.situacaoCobranca = 'GERADA' "
            + "and f.documento like CONCAT('%',:cnpj,'%') "
            + "and c.estado = :uf "
            + "and c.quantidadeRegistros = :qtdRegistros "
            + "and (c.valorCredenciada - c.valorDesconto) = :valor ")
    Cobranca findByFinanceiraQtdRegistrosEstado(@Param("cnpj") String cnpj,
                                                @Param("uf") Uf uf,
                                                @Param("qtdRegistros") Long qtdRegistros,
                                                @Param("valor") BigDecimal valor);

    @Query("select c from Cobranca c "
            + "inner join fetch c.financeira f "
            + "where c.id = (select max(o.id) from Cobranca o inner join o.financeira n where n.documento = :cnpj and o.estado = :uf) ")
    Cobranca findByFinanceiraEstado(@Param("cnpj") String documento, @Param("uf") Uf pr);


    @Query("select c from Cobranca c "
            + "inner join fetch c.financeira f "
            + "where c.id = (select max(o.id) from Cobranca o inner join o.financeira n where n.documento = :cnpj and o.estado = :uf and o.dataInicio = :dataInicio) ")
    Optional<Cobranca> findByFinanceiraEstadoData(@Param("cnpj") String documento, @Param("uf") Uf uf, @Param("dataInicio") Date dataInicio);

    @Query("select count(v) from Contrato c inner join c.veiculos v "
            + "where c.cobranca is null "
            + "and c.situacao != 'ERRO' "
            + "and v.tipo in ('MOTOCICLETA', 'MOTONETA', 'CICLOMOTOR') "
            + "and (c.ufRegistro != 'PR' or (c.tipoContrato != 'CESSAO_DIREITO_DEVEDOR'))"
            + "and c.ufRegistro = :uf "
            + "and c.dataConclusaoDETRAN between :dataInicio and :dataFim")
    Long countVeiculos2RodasByUfRegistro(@Param("uf") Uf uf, @Param("dataInicio") Date dataInicio, @Param("dataFim") Date dataFim);

    @Query("select count(v) from Contrato c inner join c.veiculos v "
            + "where c.cobranca is null "
            + "and c.situacao != 'ERRO' "
            + "and v.tipo not in ('MOTOCICLETA', 'MOTONETA', 'CICLOMOTOR') "
            + "and (c.ufRegistro != 'PR' or (c.tipoContrato != 'CESSAO_DIREITO_DEVEDOR'))"
            + "and c.ufRegistro = :uf "
            + "and c.dataConclusaoDETRAN between :dataInicio and :dataFim")
    Long countVeiculos4RodasByUfRegistro(@Param("uf") Uf uf, @Param("dataInicio") Date dataInicio, @Param("dataFim") Date dataFim);

    @Query("select count(v.id) from Contrato c "
            + "inner join c.financeira f "
            + "inner join c.veiculos v "
            + "where c.cobranca is null "
            + "and c.situacao != 'ERRO' "
            + "and v.tipo in ('MOTOCICLETA', 'MOTONETA', 'CICLOMOTOR') "
            + "and c.ufRegistro = :uf "
            + "and c.financeira = :financeira "
            + "and (c.ufRegistro != 'CE' or (c.situacao = 'ATIVO')) "
            + "and c.dataConclusaoDETRAN between :dataInicio and :dataFim")
    Long countByFinanceirasVeiculos2Rodas(@Param("financeira") Financeira financeira, @Param("uf") Uf uf, @Param("dataInicio") Date dataInicio, @Param("dataFim") Date dataFim);

    @Query("select count(v.id) from Contrato c "
            + "inner join c.financeira f "
            + "inner join c.veiculos v "
            + "where c.cobranca is null "
            + "and c.situacao != 'ERRO' "
            + "and v.tipo not in ('MOTOCICLETA', 'MOTONETA', 'CICLOMOTOR') "
            + "and c.ufRegistro = :uf "
            + "and c.financeira = :financeira "
            + "and (c.ufRegistro != 'CE' or (c.situacao = 'ATIVO')) "
            + "and c.dataConclusaoDETRAN between :dataInicio and :dataFim")
    Long countByFinanceirasVeiculos4Rodas(@Param("financeira") Financeira financeira, @Param("uf") Uf uf, @Param("dataInicio") Date dataInicio, @Param("dataFim") Date dataFim);

    @Query("select count(v.id) from Contrato c "
            + "inner join c.financeira f "
            + "inner join c.veiculos v "
            + "where c.situacao != 'ERRO' "
            + "and (c.ufRegistro != 'CE' or (c.situacao = 'ATIVO')) "
            + "and v.tipo not in ('MOTOCICLETA', 'MOTONETA', 'CICLOMOTOR') and c.cobranca = :cobranca")
    Long count4RodasByCobranca(@Param("cobranca") Cobranca cobranca);

    @Query("select count(v.id) from Contrato c "
            + "inner join c.financeira f "
            + "inner join c.veiculos v "
            + "where c.situacao != 'ERRO' "
            + "and (c.ufRegistro != 'CE' or (c.situacao = 'ATIVO')) "
            + "and v.tipo in ('MOTOCICLETA', 'MOTONETA', 'CICLOMOTOR') and c.cobranca = :cobranca")
    Long count2RodasByCobranca(@Param("cobranca") Cobranca cobranca);

    @Query("select count(v.id) from Contrato c "
            + "inner join c.financeira f "
            + "inner join c.veiculos v "
            + "where c.situacao != 'ERRO' "
            + "and c.ufRegistro = :uf "
            + "and (c.ufRegistro != 'CE' or (c.situacao = 'ATIVO')) "
            + "and v.tipo not in ('MOTOCICLETA', 'MOTONETA', 'CICLOMOTOR') and c.cobranca = :cobranca")
    Long count4RodasByCobrancaAndUf(@Param("cobranca") Cobranca cobranca, @Param("uf") Uf uf);

    @Query("select count(v.id) from Contrato c "
            + "inner join c.financeira f "
            + "inner join c.veiculos v "
            + "where c.situacao != 'ERRO' "
            + "and c.ufRegistro = :uf "
            + "and (c.ufRegistro != 'CE' or (c.situacao = 'ATIVO')) "
            + "and v.tipo in ('MOTOCICLETA', 'MOTONETA', 'CICLOMOTOR') and c.cobranca = :cobranca")
    Long count2RodasByCobrancaAndUf(@Param("cobranca") Cobranca cobranca, @Param("uf") Uf uf);

    @Query("select count(c) from Cobranca c" +
            " where c.dataInicio = :dataInicio" +
            " and c.dataFim = :dataFim" +
            " and c.estado = :uf")
    Long countCobrancasByPeriodoAndUf(@Param("dataInicio") Date dataInicio, @Param("dataFim") Date dataFim, @Param("uf") Uf uf);

    @Query("SELECT c FROM Contrato c WHERE c.cobranca = :cobranca")
    List<Contrato> findContratosByCobranca(@Param("cobranca") Cobranca cobranca);

    @Query("SELECT distinct c.ufRegistro FROM Contrato c WHERE c.cobranca = :cobranca")
    List<Uf> findUfsDeContratosByCobranca(@Param("cobranca") Cobranca cobranca);

    @Query("select distinct f.nome, c.ufRegistro, f.id " +
            "from Contrato c inner join c.financeira f " +
            "where c.dataConclusaoDETRAN between :primeiroDiaDoMesAnterior and :ultimoDiaDoMesAnterior " +
            "and c.situacaoFinanceira = 'NAO_PAGO' " +
            "and c.cobranca is null " +
            "and c.situacao != 'ERRO' " +
            "and (c.ufRegistro != 'CE'or (c.situacao = 'ATIVO'))" +
            "and (c.ufRegistro != 'AP' or (c.situacao = 'ATIVO'))" +
            "and (c.ufRegistro != 'MS' or (c.situacao = 'ATIVO'))" +
            "and (c.ufRegistro != 'PR' or (c.situacao != 'CESSAO_DIREITO_DEVEDOR'))" +
            "and (f.unificaCobrancas is null or f.unificaCobrancas = 'N')"
    )
    List<Object[]> findByContratosSemCobrancaUnificada(
            @Param("primeiroDiaDoMesAnterior") Date primeiroDiaDoMesAnterior,
            @Param("ultimoDiaDoMesAnterior") Date ultimoDiaDoMesAnterior
    );


    @Query("select distinct f.nome, f.id " +
            "from Contrato c inner join c.financeira f " +
            "where c.dataConclusaoDETRAN between :primeiroDiaDoMesAnterior and :ultimoDiaDoMesAnterior " +
            "and c.situacaoFinanceira = 'NAO_PAGO' " +
            "and c.cobranca is null " +
            "and c.situacao != 'ERRO' " +
            "and (c.ufRegistro != 'CE' or (c.situacao = 'ATIVO'))" +
            "and (c.ufRegistro != 'AP' or (c.situacao = 'ATIVO'))" +
            "and (c.ufRegistro != 'MS' or (c.situacao = 'ATIVO'))" +
            "and (c.ufRegistro != 'PR' or (c.situacao != 'CESSAO_DIREITO_DEVEDOR'))" +
            "and (f.unificaCobrancas = 'S')"
    )
    List<Object[]> findByContratosComCobrancaUnificada(
            @Param("primeiroDiaDoMesAnterior") Date primeiroDiaDoMesAnterior,
            @Param("ultimoDiaDoMesAnterior") Date ultimoDiaDoMesAnterior
    );

    Optional<Cobranca> findCobrancaByEstadoAndDataInicioEqualsAndDataFimEqualsAndFinanceiraAndSituacaoCobrancaIn
            (@Param("estado") Uf estado,
             @Param("dataInicio") Date dataInicio,
             @Param("dataFim") Date dataFim,
             @Param("financeira") Financeira financeira,
             @Param("situacaoCobranca") List<SituacaoCobranca> situacaoCobranca);

    @Query("select count(c) from Contrato c " +
            "where c.financeira.documento = :documento " +
            "and c.cobranca = :cobranca " +
            "and c.situacao != 'ERRO' " +
            "and c.ufRegistro = :uf " +
            "and c.situacaoFinanceira = 'COBRADO' " +
            "and (c.ufRegistro != 'CE' or (c.situacao = 'ATIVO')) " +
            "and (c.ufRegistro != 'AP' or (c.situacao = 'ATIVO')) " +
            "and (c.ufRegistro != 'MS' or (c.situacao = 'ATIVO')) " +
            "and (c.ufRegistro != 'PR' or (c.tipoContrato != 'CESSAO_DIREITO_DEVEDOR')) " +
            "and c.dataConclusaoDETRAN between :dataInicio and :dataFim")
    Long countContratosByFinanceiraAndUfRegistroAndDocumento(
            @Param("cobranca") Cobranca cobranca,
            @Param("uf") Uf uf,
            @Param("dataInicio") Date dataInicio,
            @Param("dataFim") Date dataFim,
            @Param("documento") String documento);

    @Query("select count(v) from ContratoRsng c inner join c.veiculos v " +
            "where c.financeira = :financeira " +
            "and c.cobranca is null " +
            "and c.situacao != 'ERRO' " +
            "and c.ufRegistro = :uf " +
            "and c.situacaoFinanceira = 'NAO_PAGO' " +
            "and c.dataConclusaoRsng between :dataInicio and :dataFim")
    Long countVeiculosRnsgByFinanceiraAndUfRegistro(
            @Param("financeira") Financeira financeira,
            @Param("uf") Uf uf,
            @Param("dataInicio") Date dataInicio,
            @Param("dataFim") Date dataFim);

    @Query("select count(v) from ContratoRsng c inner join c.veiculos v " +
            "where c.financeira = :financeira " +
            "and c.cobranca is null " +
            "and c.situacaoBaixa = 'BAIXADO' " +
            "and c.ufRegistro = :uf " +
            "and c.situacaoFinanceira = 'NAO_PAGO' " +
            "and c.dataBaixaB3 between :dataInicio and :dataFim")
    Long countVeiculosBaixaSngByFinanceiraAndUfRegistro(
            @Param("financeira") Financeira financeira,
            @Param("uf") Uf uf,
            @Param("dataInicio") Date dataInicio,
            @Param("dataFim") Date dataFim);

    @Query("select count(v) from Contrato c inner join c.veiculos v " +
            "where c.financeira = :financeira " +
            "and c.cobranca is null " +
            "and c.situacaoBaixa = 'BAIXADO' " +
            "and c.ufRegistro = :uf " +
            "and c.situacaoFinanceira = 'NAO_PAGO' " +
            "and c.dataBaixaB3 between :dataInicio and :dataFim")
    Long countVeiculosBaixaByFinanceiraAndUfRegistro(
            @Param("financeira") Financeira financeira,
            @Param("uf") Uf uf,
            @Param("dataInicio") Date dataInicio,
            @Param("dataFim") Date dataFim);

    @Query("select count(v) from ContratoRsng c inner join c.veiculos v " +
            "where c.financeira = :financeira " +
            "and c.cobranca is null " +
            "and c.situacaoBaixa = 'CANCELADO' " +
            "and c.ufRegistro = :uf " +
            "and c.situacaoFinanceira = 'NAO_PAGO' " +
            "and c.dataCancelamentoBaixaB3 between :dataInicio and :dataFim")
    Long countVeiculosCancelamentoBaixaSngByFinanceiraAndUfRegistro(
            @Param("financeira") Financeira financeira,
            @Param("uf") Uf uf,
            @Param("dataInicio") Date dataInicio,
            @Param("dataFim") Date dataFim);

    @Query("select count(v) from Contrato c inner join c.veiculos v " +
            "where c.financeira = :financeira " +
            "and c.cobranca is null " +
            "and c.situacaoBaixa = 'CANCELADO' " +
            "and c.ufRegistro = :uf " +
            "and c.situacaoFinanceira = 'NAO_PAGO' " +
            "and c.dataCancelamentoBaixaB3 between :dataInicio and :dataFim")
    Long countVeiculosCancelamentoBaixaByFinanceiraAndUfRegistro(
            @Param("financeira") Financeira financeira,
            @Param("uf") Uf uf,
            @Param("dataInicio") Date dataInicio,
            @Param("dataFim") Date dataFim);
}


