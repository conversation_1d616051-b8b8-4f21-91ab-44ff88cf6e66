package com.registrocontrato.seguranca.repository;

import com.registrocontrato.infra.entity.*;
import com.registrocontrato.infra.service.BaseRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

@Repository
public interface FinanceiraRepository extends BaseRepository<Financeira> {

	@Query("select f from Financeira f LEFT JOIN FETCH f.integradora i where f.documento = :doc")
	Financeira findByDocumento(@Param("doc") String documento);

	List<Financeira> findByAtivo(SimNao s);

	@Query("select f from Financeira f inner join f.situacoesFinanceiraEstado s where f.ativo = 'S' and s.situacaoFinanceira = 'S' and s.uf = :uf ")
	List<Financeira> findByAtivoAndUfEndereco(@Param("uf") Uf uf);

	//TODO: verificar se não precisa validar com a data também
	@Query("select f from Financeira f inner join f.situacoesFinanceiraEstado s where f.ativo = 'S' and s.situacaoFinanceira = 'S' and f.id = :id and s.uf = :uf ")
	Financeira findByIdAndCredenciamentoAtivo(@Param("id") Long id, @Param("uf") Uf uf);

	@Query("from SituacaoFinanceiraEstado s where s.uf = :uf and s.financeira = :financeira "
			+ "and s.id = (select max(x.id) from SituacaoFinanceiraEstado x where x.uf = :uf and x.financeira = :financeira)")
	SituacaoFinanceiraEstado findSituacaoFinanceiraByEstadoAndFinanceira(@Param("uf")Uf uf, @Param("financeira")Financeira financeira);

	@Query(value = "SELECT f.* FROM registro.contrato c join registro.veiculo v on c.id = v.contrato_id join registro.financeira f on c.financeira_id = f.id WHERE v.numero_chassi = :numeroChassi", nativeQuery = true)
	Financeira findFinanceiraByNumeroChassi(@Param("numeroChassi") String numeroChassi);

	Financeira findFirstByDocumento(@Param("documento") String documento);

	@Query("SELECT sfe FROM SituacaoFinanceiraEstado sfe where sfe.financeira = :financeira")
    List<SituacaoFinanceiraEstado> findSituacaoFinanceiraEstadoByFinanceira(@Param("financeira") Financeira financeira);

	@Query("select f from Financeira f" +
			" join fetch f.situacoesFinanceiraEstado" +
			" where f.enviarEmailSemFaturamento='S'" +
			" and f.ativo = 'S'")
	List<Financeira> findFinanceirasEnviarAvisoSemFaturamento();

	@Query("select distinct f from Financeira f" +
			" join fetch f.situacoesFinanceiraEstado" +
			" where f.enviarEmailSemFaturamento='S'" +
			" and f.ativo = 'S'" +
			" and exists (select 1 from SituacaoFinanceiraEstado sfe " +
			" where sfe.financeira = f" +
			" and sfe.uf = :uf" +
			" and :dataInicio < sfe.dataFimCredenciamentoDetran and" +
			" :dataInicio >= sfe.dataInicioCredenciamentoDetran and" +
			" :dataFim > sfe.dataInicioCredenciamentoDetran and" +
			" :dataFim <= sfe.dataFimCredenciamentoDetran)")
	List<Financeira> findFinanceirasEnviarAvisoSemFaturamentoECredenciamentoAtivo(@Param("uf") Uf uf, @Param("dataInicio") Date dataInicio, @Param("dataFim") Date dataFim);

	@Query("select sfe from SituacaoFinanceiraEstado sfe" +
			" where sfe.financeira = :financeira and" +
			" now() between sfe.dataInicioCredenciamentoDetran and sfe.dataFimCredenciamentoDetran")
	List<SituacaoFinanceiraEstado> findSituacoesFinanceiraEstadoAtivas(@Param("financeira") Financeira financeira);

	@Query("select sfe from SituacaoFinanceiraEstado sfe" +
			" where sfe.financeira = :financeira and" +
			" :dataInicio < sfe.dataFimCredenciamentoDetran and" +
			" :dataInicio >= sfe.dataInicioCredenciamentoDetran and" +
			" :dataFim > sfe.dataInicioCredenciamentoDetran and" +
			" :dataFim <= sfe.dataFimCredenciamentoDetran")
	List<SituacaoFinanceiraEstado> findSituacoesFinanceiraEstadoAtivas(@Param("financeira") Financeira financeira, @Param("dataInicio") Date dataInicio, @Param("dataFim") Date dataFim);

	@Query("SELECT sfe FROM SituacaoFinanceiraEstado sfe " +
			"join fetch sfe.financeira f " +
			"where f.ativo='S' " +
			"and sfe.reembolsoLinhaDigitavel='S'")
    List<SituacaoFinanceiraEstado> findSituacoesByAtivoAndReembolso();

	@Query("SELECT sfe FROM SituacaoFinanceiraEstado sfe " +
			"join fetch sfe.financeira f " +
			"where f.ativo='S' " +
			"and sfe.reembolsoLinhaDigitavel='S' " +
			"and sfe.uf = :uf")
    List<SituacaoFinanceiraEstado> findSituacoesByAtivoAndReembolsoAndUf(@Param("uf") Uf uf);

	@Query(value = "select distinct f.* from registro.financeira f " +
			"inner join financeiro.cupom_desconto c on f.id = c.financeira_id " +
			"where financeira_id is not null", nativeQuery = true)
	List<Financeira> findFinanceiraComDesconto();

	@Query("SELECT f FROM Financeira f " +
			"WHERE f.grupoFinanceira = (SElECT fin.grupoFinanceira FROM Financeira fin where fin.documento = :documento)")
	List<Financeira> findAllBySameGrupo(@Param("documento") String documento);

	@Query(value = "select f from Financeira f " +
			"where f.dataFimPlace <= current_date - :dias")
	List<Financeira> findVencimentoDoContrato(@Param("dias") Integer dias);

	@Query(value = "select distinct f.* " +
            "from registro.financeira f " +
            "inner join registro.contrato c on f.id = c.financeira_id " +
            "where (c.data_cadastro > cast(:diaOperacao as DATE) " +
			"and c.data_cadastro < (select current_date - interval '1 years'))", nativeQuery = true)
	List<Financeira> findOperacaoAnual(@Param("diaOperacao") String diaOperacao);

	List<Financeira> findAllByGrupoFinanceira(GrupoFinanceira grupoFinanceira);
}
