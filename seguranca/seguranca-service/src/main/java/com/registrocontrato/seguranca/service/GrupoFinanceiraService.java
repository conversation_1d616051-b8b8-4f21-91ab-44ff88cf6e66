package com.registrocontrato.seguranca.service;

import java.util.ArrayList;
import java.util.List;

import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.data.domain.Sort.Direction;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.data.repository.PagingAndSortingRepository;
import org.springframework.stereotype.Service;

import com.registrocontrato.infra.entity.GrupoFinanceira;
import com.registrocontrato.infra.exception.ServiceException;
import com.registrocontrato.infra.service.BaseService;
import com.registrocontrato.seguranca.repository.GrupoFinanceiraRepository;
import com.registrocontrato.seguranca.service.dto.GrupoFinanceiraDTO;

@Service
public class GrupoFinanceiraService extends BaseService<GrupoFinanceira, GrupoFinanceiraDTO> {

	private static final long serialVersionUID = 1L;

	@Autowired
	private GrupoFinanceiraRepository grupoFinanceiraRepository;

	@Override
	public Page<GrupoFinanceira> findAll(int first, int pageSize, GrupoFinanceiraDTO filter) {
		Specification<GrupoFinanceira> contratoSpec = new Specification<GrupoFinanceira>() {

			@Override
			public Predicate toPredicate(Root<GrupoFinanceira> root, CriteriaQuery<?> cq, CriteriaBuilder cb) {
				cq.distinct(true);
				List<Predicate> predicates = new ArrayList<>();

				if (StringUtils.isNotEmpty(filter.getNome())) {
					predicates.add(cb.like(cb.lower(root.<String>get("nome")), "%" + filter.getNome().toLowerCase() + "%"));
				}

				return andTogether(predicates, cb);
			}

			private Predicate andTogether(List<Predicate> predicates, CriteriaBuilder cb) {
				return cb.and(predicates.toArray(new Predicate[0]));
			}
		};
		return grupoFinanceiraRepository.findAll(contratoSpec, new PageRequest(first / pageSize, pageSize, new Sort(Direction.ASC, "nome")));
	}

	@Override
	public void save(GrupoFinanceira entity) throws ServiceException {
		GrupoFinanceira duplicado = grupoFinanceiraRepository.findByNomeIgnoreCase(entity.getNome());
		if (duplicado != null && !duplicado.getId().equals(entity.getId())) {
			throw new ServiceException(String.format("Já existe um Grupo de Financeira cadastrada com este nome %s.", entity.getNome()));
		}
		
		super.save(entity);
	}

	public List<GrupoFinanceira> findGruposFinanceiras() {
		return grupoFinanceiraRepository.findAll();
	}

	@Override
	protected PagingAndSortingRepository<GrupoFinanceira, Long> getRepository() {
		return grupoFinanceiraRepository;
	}

}
