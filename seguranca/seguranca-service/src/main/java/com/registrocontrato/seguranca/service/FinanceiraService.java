package com.registrocontrato.seguranca.service;

import com.registrocontrato.infra.entity.*;
import com.registrocontrato.infra.exception.ServiceException;
import com.registrocontrato.infra.service.BaseService;
import com.registrocontrato.infra.util.PlaceconUtil;
import com.registrocontrato.seguranca.entity.Perfil;
import com.registrocontrato.seguranca.entity.Usuario;
import com.registrocontrato.seguranca.repository.FinanceiraRepository;
import com.registrocontrato.seguranca.service.dto.FinanceiraDTO;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.Hibernate;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.data.domain.Sort.Direction;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.data.repository.PagingAndSortingRepository;
import org.springframework.stereotype.Service;

import javax.mail.internet.AddressException;
import javax.mail.internet.InternetAddress;
import javax.persistence.criteria.*;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class FinanceiraService extends BaseService<Financeira, FinanceiraDTO> {

	private static final long serialVersionUID = 1L;

	private final FinanceiraRepository financeiraRepository;

	private final UsuarioService usuarioService;

	public FinanceiraService(FinanceiraRepository financeiraRepository, UsuarioService usuarioService) {
		super();
		this.financeiraRepository = financeiraRepository;
		this.usuarioService = usuarioService;
	}

	@Value("${file-financeira.dir:null}")
	private String FILE_DIR;

	public List<Financeira> buscarFinanceirasDoGrupo(String documento) {
		return financeiraRepository.findAllBySameGrupo(documento);
	}

	public List<Financeira> buscarFinanceirasDoGrupo(GrupoFinanceira grupoFinanceira) {
		return financeiraRepository.findAllByGrupoFinanceira(grupoFinanceira);
	}

	@Override
	public Page<Financeira> findAll(int first, int pageSize, FinanceiraDTO filter) {
		Specification<Financeira> contratoSpec = new Specification<Financeira>() {

			@Override
			public Predicate toPredicate(Root<Financeira> root, CriteriaQuery<?> cq, CriteriaBuilder cb) {
				cq.distinct(true);
				List<Predicate> predicates = new ArrayList<>();

				if (StringUtils.isNotEmpty(filter.getNome())) {
					predicates.add(cb.like(cb.lower(root.<String>get("nome")), "%" + filter.getNome().toLowerCase() + "%"));
				}
				if (StringUtils.isNotEmpty(filter.getDocumento())) {
					predicates.add(cb.like(cb.lower(root.<String>get("documento")), "%" + PlaceconUtil.retiraFormatacao(filter.getDocumento().toLowerCase()) + "%"));
				}
				if (filter.getAtivo() != null) {
					predicates.add(cb.equal(root.<SimNao>get("ativo"), filter.getAtivo()));
				}

				if (filter.getUfAtivos() != null){
					Join<Financeira, SituacaoFinanceiraEstado> joinEstados = root.join("situacoesFinanceiraEstado");
					List<Uf> values = new ArrayList<Uf>();
					values.add(filter.getUfAtivos());

					Predicate estadoDaFincanceira = joinEstados.<Uf>get("uf").in(values);

					predicates.add(estadoDaFincanceira);

				}

				// se o usuario eh financeira filtrar somente os usuario da financeira
				Usuario usuarioLogado = usuarioService.findByCpfFinanceiras(filter.getUsuario());
				if (usuarioLogado.getPerfil() == Perfil.FINANCEIRA || !usuarioLogado.getFinanceiras().isEmpty()) {
					List<Long> values = new ArrayList<Long>();
					usuarioLogado.getFinanceiras().forEach(f -> values.add(f.getId()));
					predicates.add(root.<Long>get("id").in(values));
				}
				return andTogether(predicates, cb);
			}

			private Predicate andTogether(List<Predicate> predicates, CriteriaBuilder cb) {
				return cb.and(predicates.toArray(new Predicate[0]));
			}
		};
		return financeiraRepository.findAll(contratoSpec, new PageRequest(first / pageSize, pageSize, new Sort(Direction.ASC, "nome")));
	}


	public SituacaoFinanceiraEstado findSituacaoFinanceiraEstado(Uf uf, Financeira financeira) {
		return financeiraRepository.findSituacaoFinanceiraByEstadoAndFinanceira(uf, financeira);
	}

	public List<SituacaoFinanceiraEstado> findSituacoesFinanceiraEstado(Financeira financeira) {
		return financeiraRepository.findSituacaoFinanceiraEstadoByFinanceira(financeira);
	}

	public List<Financeira> findFinanceirasAvisoFaturamentoAtivoECredenciamentoAtivo(Uf uf, Date dataInicio, Date dataFim) {
		return financeiraRepository.findFinanceirasEnviarAvisoSemFaturamentoECredenciamentoAtivo(uf, dataInicio, dataFim);
	}

	@Override
	public void save(Financeira entity) throws ServiceException {
		Financeira duplicado = financeiraRepository.findByDocumento(entity.getDocumento());
		if (duplicado != null && !duplicado.getId().equals(entity.getId())) {
			throw new ServiceException(String.format("Já existe uma Financeira cadastrada com o CNPJ %s.", entity.getDocumento()));
		}
		validaCredenciamento(entity);
		verifyDadosMatriz(entity);
		salvarArquivo(entity);
		super.save(entity);
	}

	public void validaCredenciamento(Financeira entity) throws ServiceException{
		if (entity.getSituacoesFinanceiraEstado() == null) return;
		for (SituacaoFinanceiraEstado sfe : entity.getSituacoesFinanceiraEstado()) {
			if (sfe.getDataInicioCredenciamentoDetran().after(sfe.getDataFimCredenciamentoDetran())) {
				throw new ServiceException("A data de início do credenciamento não pode ser depois da data de fim");
			}
		}
	}

	public void verifyDadosMatriz(Financeira entity) {
		if (entity.getPossuiMatriz() != SimNao.S) {
			entity.setCepMatriz(null);
			entity.setNomeMatriz(null);
			entity.setBairroMatriz(null);
			entity.setComplementoMatriz(null);
			entity.setDocumentoMatriz(null);
			entity.setEnderecoMatriz(null);
			entity.setMunicipioMatriz(null);
			entity.setNomeMatriz(null);
			entity.setNumeroMatriz(null);
		}
	}

	public List<String> recuperaEmailsPendencia(Financeira financeira) {
		if (financeira.getEmailPendenciaAnexo() != null
				&& !financeira.getEmailPendenciaAnexo().trim().equals("")) {
			return Arrays.asList(financeira.getEmailPendenciaAnexo().split(";"));
		}
		return Collections.emptyList();
	}

	public List<InternetAddress> criaListaDeEmailsPendenciaAnexo(Financeira financeira) throws AddressException {
		List<String> listaEmails = recuperaEmailsPendencia(financeira);
		if (listaEmails.isEmpty()) {
			return Collections.singletonList(new InternetAddress(financeira.getEmailRepresentante()));
		}
		return listaEmails.stream()
				.map(email -> {
					InternetAddress endereco = new InternetAddress();
					endereco.setAddress(email);
					return endereco;
				})
				.collect(Collectors.toList());
	}

	private void salvarArquivo(Financeira entity) throws ServiceException {
		if (!PlaceconUtil.isListaVaziaOuNula(entity.getAnexos())) {
			for (AnexoFinanceira a : entity.getAnexos()) {
				InputStream file = a.getFile();
				if (file != null) {
					try {
						String referenciaArquivo = PlaceconUtil.formataData(new Date()) + "_" + RandomStringUtils.randomAlphanumeric(5);
						a.setReferenciaArquivo(referenciaArquivo);
						File targetFile = new File(FILE_DIR, referenciaArquivo);
						FileUtils.copyInputStreamToFile(file, targetFile);
					} catch (IOException e) {
						e.printStackTrace();
						throw new ServiceException("Erro ao salvar o arquivo");
					}
				}
			}
		}
	}

	public List<Financeira> getFinanceirasObservatorioUsuario(Usuario usuario) {
		List<Financeira> financeirasUsuario = getFinanceirasUsuario(usuario);
		return financeirasUsuario.stream().filter(fin -> fin.getObservatorioAtivo() == SimNao.S).collect(Collectors.toList());
	}

	public List<Financeira> getFinanceirasUsuario(Usuario usuario) {
		if (usuario.getPerfil() == Perfil.FINANCEIRA || !usuario.getFinanceiras().isEmpty()) {
			return usuario.getFinanceiras();
		} else if (usuario.getPerfil() == Perfil.DETRAN) {
			return Collections.emptyList();
		}
		return findAtivos();
	}

	@Override
	public Financeira findOne(Long id) {
		Financeira financeira = super.findOne(id);
		Hibernate.initialize(financeira.getAnexos());
		Hibernate.initialize(financeira.getIntegradora());
		return financeira;
	}

	@Override
	protected PagingAndSortingRepository<Financeira, Long> getRepository() {
		return financeiraRepository;
	}

	public List<Financeira> findAtivos() {
		return financeiraRepository.findByAtivo(SimNao.S);
	}

	public List<Financeira> findAtivosByUf(Uf uf) {
		return financeiraRepository.findByAtivoAndUfEndereco(uf);
	}

    public List<SituacaoFinanceiraEstado> findSituacoesByAtivoAndReembolso() {
		return financeiraRepository.findSituacoesByAtivoAndReembolso();
	}

	public List<SituacaoFinanceiraEstado> findSituacoesByAtivoAndReembolsoAndUf(Uf uf) {
		return financeiraRepository.findSituacoesByAtivoAndReembolsoAndUf(uf);
	}

	public List<Financeira> findFinanceiraComDesconto() {
		List<Financeira> financeiraComDesconto = financeiraRepository.findFinanceiraComDesconto();
		return financeiraComDesconto;
	}

	public Financeira findByDocumento(String cnpj) {
		Financeira financeira = financeiraRepository.findByDocumento(cnpj);
		if (Objects.isNull(financeira))
			return null;
		return financeira;
	}
}
