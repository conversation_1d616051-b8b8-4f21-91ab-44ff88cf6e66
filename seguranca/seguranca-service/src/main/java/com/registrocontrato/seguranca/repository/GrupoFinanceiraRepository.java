package com.registrocontrato.seguranca.repository;

import org.springframework.stereotype.Repository;

import com.registrocontrato.infra.entity.GrupoFinanceira;
import com.registrocontrato.infra.service.BaseRepository;

import java.util.List;

@Repository
public interface GrupoFinanceiraRepository extends BaseRepository<GrupoFinanceira> {

	GrupoFinanceira findByNomeIgnoreCase(String nome);

	GrupoFinanceira findById(Long id);

	List<GrupoFinanceira> findAll();

}
