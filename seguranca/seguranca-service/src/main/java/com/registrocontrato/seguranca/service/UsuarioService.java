package com.registrocontrato.seguranca.service;

import java.security.NoSuchAlgorithmException;
import java.util.*;
import java.util.stream.Collectors;

import javax.mail.internet.InternetAddress;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.Join;
import javax.persistence.criteria.JoinType;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;

import com.registrocontrato.seguranca.entity.*;
import com.registrocontrato.seguranca.repository.CookiesRepository;
import org.apache.commons.lang.RandomStringUtils;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.Hibernate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.data.domain.Sort.Direction;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.data.repository.PagingAndSortingRepository;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.stereotype.Service;

import com.registrocontrato.infra.email.Email;
import com.registrocontrato.infra.email.EnviaEmail;
import com.registrocontrato.infra.entity.Financeira;
import com.registrocontrato.infra.entity.SimNao;
import com.registrocontrato.infra.exception.ServiceException;
import com.registrocontrato.infra.service.BaseService;
import com.registrocontrato.seguranca.repository.UsuarioRepository;
import com.registrocontrato.seguranca.service.dto.UsuarioDTO;


@Service
public class UsuarioService extends BaseService<Usuario, UsuarioDTO> {

	private static final long serialVersionUID = 1L;

	@Autowired
	private UsuarioRepository usuarioRepository;

	@Autowired
	private CookiesRepository cookiesRepository;

	@Autowired
	private EnviaEmail enviaEmail;

	@Value("${jsf.PROJECT_STAGE:null}")
	private String ambiente;

	@Value("${cas.url.ambiente.boasvindas:https://placecon.com.br}")
	private String urlPlacecon;

	@Override
	public Page<Usuario> findAll(int first, int pageSize, UsuarioDTO filter) {

		Specification<Usuario> usuarioSpect = new Specification<Usuario>() {

			@Override
			public Predicate toPredicate(Root<Usuario> root, CriteriaQuery<?> cq, CriteriaBuilder cb) {
				cq.distinct(true);
				List<Predicate> predicates = new ArrayList<>();

				if (StringUtils.isNotEmpty(filter.getNome())) {
					predicates.add(cb.like(cb.lower(root.<String>get("nome")), "%" + filter.getNome().toLowerCase() + "%"));
				}
				if (StringUtils.isNotEmpty(filter.getCpf())) {
					predicates.add(cb.like(cb.lower(root.<String>get("cpf")), "%" + filter.getCpf().toLowerCase() + "%"));
				}
				if (StringUtils.isNotEmpty(filter.getEmail())) {
					predicates.add(cb.like(cb.lower(root.<String>get("email")), "%" + filter.getEmail().toLowerCase() + "%"));
				}
				if (filter.getAtivo() != null) {
					predicates.add(cb.equal(root.<SimNao>get("ativo"), filter.getAtivo()));
				}
				if (filter.getGrupo() != null && filter.getPermissao() == null) {
					Join<Usuario, Grupo> joinGrupos = root.join("grupos");
					List<Long> values = new ArrayList<Long>();
					values.add(filter.getGrupo().getId());
					predicates.add(joinGrupos.<Long>get("id").in(values));
				}
				if(filter.getPermissao() != null && filter.getGrupo() != null){
					Join<Usuario, Grupo> joinGrupos = root.join("grupos");
					List<Long> values = new ArrayList<Long>();
					values.add(filter.getGrupo().getId());

					Join<Grupo, Permissao> joinGrupoPermissoes = joinGrupos.join("permissoes", JoinType.LEFT);
					List<Long> valuesPermissoes = new ArrayList<Long>();
					valuesPermissoes.add(filter.getPermissao().getId());

					Join<Usuario, Permissao> joinPermissoes = root.join("permissoes", JoinType.LEFT);
					List<Long> userPerm = new ArrayList<Long>();
					userPerm.add(filter.getPermissao().getId());

					Predicate grupoPermissao =joinGrupoPermissoes.<Long>get("id").in(valuesPermissoes);
					Predicate userPermissao = joinPermissoes.<Long>get("id").in(userPerm);
					Predicate userDoGrupo = joinGrupos.<Long>get("id").in(values);

					predicates.add(cb.and(userDoGrupo,cb.or(userPermissao, grupoPermissao)));
				}
				if (filter.getPermissao() != null && filter.getGrupo() == null) {
					Join<Usuario, Permissao> joinPermissoes = root.join("permissoes", JoinType.LEFT);
					List<Long> values = new ArrayList<Long>();
					values.add(filter.getPermissao().getId());

					Join<Usuario, Grupo> joinGrupos = root.join("grupos", JoinType.LEFT);
					Join<Grupo, Permissao> joinGrupoPermissoes = joinGrupos.join("permissoes", JoinType.LEFT);
					List<Long> valuesPermissoes = new ArrayList<Long>();
					valuesPermissoes.add(filter.getPermissao().getId());

					Predicate grupoPermissao =joinGrupoPermissoes.<Long>get("id").in(valuesPermissoes);
					predicates.add(cb.or(joinPermissoes.<Long>get("id").in(values), grupoPermissao));
				}

				// usuários do suporte não podem visualizar os usuários ADM do sistema
				if (filter.getPerfil().equals(Perfil.SUPORTE_PLACE))
					predicates.add(cb.notEqual(root.<Perfil>get("perfil"), Perfil.ADMINISTRADOR));

				// se o usuario eh financeira filtrar somente os usuario da financeira
				Usuario usuarioLogado = findByCpfFinanceiras(filter.getUsuarioLogado());
				Join<Usuario, Financeira> joinFinanceira = null;

				if (usuarioLogado.getPerfil() == Perfil.FINANCEIRA || !usuarioLogado.getFinanceiras().isEmpty()) {
					joinFinanceira = root.join("financeiras");

					List<Long> values = usuarioLogado.getFinanceiras().stream().map(Financeira::getId).collect(Collectors.toList());
					predicates.add(joinFinanceira.<Long>get("id").in(values));
					predicates.add(cb.equal(root.<Perfil>get("perfil"), Perfil.FINANCEIRA));
				}

				if (filter.getFinanceira() != null) {
					if (joinFinanceira == null) {
						joinFinanceira = root.join("financeiras");
					}
					List<Long> values = new ArrayList<Long>();
					values.add(filter.getFinanceira().getId());
					predicates.add(joinFinanceira.<Long>get("id").in(values));
				}

				if (filter.getConsultaRapida() == Boolean.TRUE){
					if (!StringUtils.isBlank(filter.getValor())){
						predicates.add(
								cb.or(
										cb.like(cb.lower(root.<String>get("nome")), "%" + filter.getValor().toLowerCase() + "%"),
										cb.like(cb.lower(root.<String>get("cpf")), "%" + filter.getValor().toLowerCase() + "%")
								)
						);
					}

					filter.setConsultaRapida(null);
					filter.setValor(null);
				}

				return andTogether(predicates, cb);
			}

			private Predicate andTogether(List<Predicate> predicates, CriteriaBuilder cb) {
				return cb.and(predicates.toArray(new Predicate[0]));
			}
		};
		return usuarioRepository.findAll(usuarioSpect, new PageRequest(first / pageSize, pageSize, new Sort(Direction.DESC, "id")));
	}

	@Override
	public Usuario findOne(Long id) {
		Usuario usuario = super.findOne(id);
		Hibernate.initialize(usuario.getGrupos());
		Hibernate.initialize(usuario.getPermissoes());
		Hibernate.initialize(usuario.getFinanceiras());
		return usuario;
	}

	@Override
	public void save(Usuario entity) throws ServiceException {
		boolean newUser = false;
		String newPassword = null;
		if (entity.getId() == null) {
			newUser = true;
			try {
				newPassword = RandomStringUtils.randomAlphanumeric(7).toLowerCase();
				entity.setPassword(newPassword);
			} catch (NoSuchAlgorithmException e) {
				throw new ServiceException("Não foi possível gerar a senha do usuário");
			}


			Usuario usuario = usuarioRepository.findByCpf(entity.getCpf());
			if (usuario != null) {
				throw new ServiceException("Usuário já cadastrado.");
			}
		}
		List<Usuario> usuariosEmail = usuarioRepository.findByEmail(entity.getEmail());
		if (((newUser && !usuariosEmail.isEmpty()) || (!newUser && usuariosEmail.size() > 1)) && !ambiente.equals("Development")) {
			throw new ServiceException("O email deste usuário já está cadastrado em outro usuário. Insira um novo email.");
		}
		super.save(entity);
		if (newUser){
			sendMailBoasVindas(entity, newPassword);
		}
	}

	public void saveCookies(Usuario entity, Cookies cookies) throws ServiceException {
		cookiesRepository.save(cookies);
		entity.setCookies(cookies);
		this.save(entity);
	}

	public String buscarEmailByCpf(String cpf) {
		Usuario usuario = usuarioRepository.findByCpf(cpf);
		return Objects.isNull(usuario) ? null : usuario.getEmail();
	}

	private void sendMailBoasVindas(Usuario user, String password){
		try{
			 HashMap<Character, List<InternetAddress>>hash= new HashMap<>();
			 List<InternetAddress>emails = new ArrayList<>();
			 emails.add(new InternetAddress(user.getEmail()));
			 hash.put(Email.TIPO_PARA, emails);
			 Map<String, String> params = new HashMap<String, String>();
			 params.put("LOGIN", user.getCpf());
			 params.put("NOME", user.getNome());
			 params.put("SENHA", password);
			 params.put("URL", urlPlacecon);
			 Email email = new Email(enviaEmail);
			 email.enviarEmail("PLACECON :: Boas Vindas", params, hash, "/email/boasvindas.xhtml");
		} catch(Exception e){
			e.printStackTrace();
		}
	}

	@Override
	protected PagingAndSortingRepository<Usuario, Long> getRepository() {
		return usuarioRepository;
	}

	public Usuario findByCpf(String login) {
		Usuario usuario = usuarioRepository.findByCpf(login);
		return initializeUsuario(usuario);
	}

	public Usuario findByCpfApi(String login) {
		Usuario usuario = usuarioRepository.findByCpfAPI(login);
		return initializeUsuario(usuario);
	}

	private Usuario initializeUsuario(Usuario usuario) {
		if (usuario != null) {
			Hibernate.initialize(usuario.getGrupos());
			Hibernate.initialize(usuario.getPermissoes());
			Hibernate.initialize(usuario.getFinanceiras());
		}
		return usuario;
	}

	public Usuario findByCpfFinanceiras(String login) {
		return usuarioRepository.findByCpfFinanceiras(login);
	}

	public Usuario findByCpfApiFinanceiras(String login) {
		return usuarioRepository.findByCpfApiFinanceiras(login);
	}

	public List<Permissao> findPermissoesByUsuario(Long id){
		return usuarioRepository.findPermissao(id);
	}

	public List<Permissao> findPermissoesGrupoAndUsuario(Long id) { return  usuarioRepository.findPermissaoGPS(id); }

	public List<Grupo> finGruposByUsuario(Long id) { return  usuarioRepository.findGrupoDoUsuario(id); }

	public List<Usuario> findByPermissao(String permissao) {
		return usuarioRepository.findByPermissao(permissao);
	}

	public void updateFoto(Long id, byte[] foto) {
		Usuario usuario = usuarioRepository.findOne(id);
		usuario.setFoto(foto);
		usuarioRepository.save(usuario);
	}

	public String criarCpfApi(){
		String user = RandomStringUtils.randomAlphanumeric(10).toLowerCase();
		return user;
	}
	public String criarPasswordApi(){
		String password = RandomStringUtils.randomAlphanumeric(25).toLowerCase();
		return password;
	}
	public String encoderPasswordApi(String password){
		String encode = new BCryptPasswordEncoder().encode(password);
		return encode;
	}

	public List<Usuario> findByFinanceiraAndPermissao(Financeira financeira, Permissao permissao) {
		return usuarioRepository.findByPermissoesContainingAndFinanceirasContaining(permissao, financeira);
	}

	public List<Usuario> findByFinanceiraAndGrupo(Financeira financeira, Grupo grupo) {
		return usuarioRepository.findByGruposContainingAndFinanceirasContaining(grupo, financeira);
	}
}
