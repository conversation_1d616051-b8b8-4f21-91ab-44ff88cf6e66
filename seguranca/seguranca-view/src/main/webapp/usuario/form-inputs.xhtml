<ui:composition xmlns="http://www.w3.org/1999/xhtml"
				xmlns:f="http://xmlns.jcp.org/jsf/core" xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
				xmlns:p="http://primefaces.org/ui" xmlns:jsf="http://xmlns.jcp.org/jsf"
                xmlns:pe="http://primefaces.org/ui/extensions"
				xmlns:sec="http://www.springframework.org/security/tags">

    <div class="row">
        <div class="col-lg-4">
            <div class="form-group form-group-default required">
                <label>CPF</label>
                <input jsf:id="cpf" type="text" maxlength="255" jsf:value="#{usuarioBean.entity.cpf}"
                       required="true" jsf:required="true" jsf:readonly="#{usuarioBean.entity.id != null}"
                       jsf:label="CPF" class="form-control cpf" jsf:converter="cpfConverter"
                       jsf:validator="cpfValidator" disabled="#{disabled}"/>
            </div>
        </div>
        <div class="col-lg-#{helperSessionBean.usuario.perfil == 'FINANCEIRA' ? '6' : '4'}">
            <div class="form-group form-group-default required">
                <label>Nome Completo</label>
                <input jsf:id="nome" type="text" maxlength="255" jsf:value="#{usuarioBean.entity.nome}"
                       required="true" jsf:required="true" jsf:label="Nome Completo" class="form-control"
                       disabled="#{disabled}"/>
            </div>
        </div>
        <div class="col-lg-2" jsf:rendered="#{helperSessionBean.usuario.perfil == 'ADMINISTRADOR' || helperSessionBean.usuario.perfil == 'SUPORTE_PLACE'}">
            <div class="form-group form-group-default form-group-default-select2 required">
                <label>Perfil</label>
                <select jsf:id="perfil_adm" jsf:value="#{usuarioBean.entity.perfil}"
                        class="form-control full-width select2" required="true" jsf:required="true" size="1"
                        disabled="#{disabled}">
                    <f:selectItem itemLabel="Selecione"/>
                    <f:selectItems value="#{helperBean.perfis}" var="i" itemLabel="#{i.descricao}"/>
                    <f:ajax render="divEstado divFinanceira" execute="@this"
                            onevent="function(data) { $.masks(); $('.uf').focus();}"/>
                </select>
            </div>
        </div>
        <div class="col-lg-2" jsf:id="divEstado">
            <div class="form-group form-group-default form-group-default-select2 #{usuarioBean.entity.perfil == 'DETRAN' ? 'required' : ''}">
                <label>Estado</label>
                <select jsf:id="estado" jsf:value="#{usuarioBean.entity.uf}"
                        class="form-control full-width uf select2"
                        required="#{usuarioBean.entity.perfil == 'DETRAN' ? 'required' : null}"
                        jsf:required="#{usuarioBean.entity.perfil == 'DETRAN'}" size="1"
                        disabled="#{disabled == 'disabled' || helperSessionBean.usuario.perfil != 'ADMINISTRADOR' ? 'disabled' : null}">
                    <f:selectItem itemLabel="Selecione"/>
                    <f:selectItems value="#{helperBean.ufs}" var="i" itemLabel="#{i}"/>
                </select>
            </div>
        </div>
    </div>
    <div class="row">
        <div class="col-lg-4">
            <div class="form-group form-group-default required">
                <label>E-mail</label>
                <input jsf:id="email" type="email" maxlength="255"
                       jsf:value="#{usuarioBean.entity.email}" required="true" jsf:required="true"
                       jsf:label="Email" class="form-control" disabled="#{disabled}"/>
            </div>
        </div>
        <div class="col-lg-4">
            <div class="form-group form-group-default form-group-default-select2 required">
                <label>Ativo</label>
                <select jsf:id="ativo" jsf:value="#{usuarioBean.entity.ativo}"
                        class="form-control full-width select2" required="true" jsf:required="true" size="1"
                        disabled="#{disabled == 'disabled' ||
						usuarioBean.perfilAdmOuSuporte() ? 'disabled' : null}">
                    <f:selectItem itemLabel="Selecione"/>
                    <f:selectItems value="#{helperBean.simNao}" var="i" itemLabel="#{i.descricao}"/>
                </select>
            </div>
        </div>
        <div class="col-lg-4">
            <div class="form-group form-group-default form-group-default-select2">
                <label>Agente</label>
                <select jsf:id="agente" jsf:value="#{usuarioBean.entity.agente}"
                        class="form-control full-width select2" size="1"
                        disabled="#{disabled == 'disabled' ||
						helperSessionBean.usuario.perfil != 'ADMINISTRADOR' ? 'disabled' : null}">
                    <f:selectItem itemLabel="Selecione"/>
                    <f:selectItems value="#{helperSessionBean.agentes}" var="i" itemValue="#{i}" itemLabel="#{i.nome}"/>
                    <f:converter converterId="agenteConverter"/>
                </select>
            </div>
        </div>
    </div>

    <sec:authorize ifAnyGranted="VISUALIZAR_CREDENCIAIS_API">
        <div class="row">
            <div class="tab-pane col-lg-12">
                <div class="panel panel-default panel-fill">
                    <div class="panel-heading">
                        <h3 class="panel-title">Credenciais API</h3>
                    </div>
                    <div class="panel-body">
                        <div class="row">
                            <div class="col-lg-4">
                                <div class="form-group form-group-default form-group-default-select2">
                                    <label>Nome Usuário</label>
                                    <input jsf:id="cpfapi" type="text" maxlength="255"
                                           jsf:value="#{usuarioBean.entity.cpfAPI}"
                                           jsf:rendered="true"
                                           class="form-control"
                                           disabled="true"/>
                                </div>
                            </div>
                            <div class="col-lg-4">
                                <div class="form-group form-group-default form-group-default-select2">
                                    <label>Senha API</label>
                                    <input jsf:id="passwordapi" type="text" maxlength="255"
                                           jsf:value="#{usuarioBean.passwordApi}"
                                           jsf:rendered="true"
                                           class="form-control"
                                           disabled="true"/>
                                </div>
                            </div>
                            <div class="col-lg-4">
                                <div class="form-group form-group-default form-group-default-select2">
                                    <label>URL da API</label>
                                    <input jsf:id="urlApi" type="text" maxlength="255"
                                           jsf:value="https://placecon.com.br/registro-ws/"
                                           jsf:rendered="true"
                                           class="form-control"
                                           disabled="true"/>
                                </div>
                            </div>
                        </div>
                        <div class="row text-center">
                            <div class="col-lg-12" jsf:id="cred_buttons">
                                <p:commandLink value="Criar Credencial" class="btn btn-primary btn-cons"
                                               rendered="#{usuarioBean.entity.cpfAPI == null and disabled == null}">
                                    <f:ajax render="cred_buttons cpfapi passwordapi"
                                            execute="@this"
                                            listener="#{usuarioBean.criarCredencialApi()}"/>
                                </p:commandLink>

                                <p:commandLink value="Atualizar Senha" class="btn btn-primary btn-cons"
                                               rendered="#{usuarioBean.entity.cpfAPI != null and disabled == null}">
                                    <f:ajax render="cred_buttons cpfapi passwordapi"
                                            execute="@this"
                                            listener="#{usuarioBean.atualizarCredencialApi()}"/>
                                </p:commandLink>

                                <p:commandLink value="Deletar Credencial" class="btn btn-primary btn-cons"
                                               rendered="#{usuarioBean.entity.cpfAPI != null and disabled == null}">
                                    <f:ajax render=" cred_buttons cpfapi passwordapi"
                                            execute="@this"
                                            listener="#{usuarioBean.deletarCredencialApi()}"/>
                                </p:commandLink>
                                <p:commandLink id="btnAjaxCopy" value="Copiar" class="btn btn-primary btn-cons"
                                               rendered="#{usuarioBean.entity.cpfAPI != null and disabled == null}"/>
                                <pe:clipboard id="clipAjax" trigger="btnAjaxCopy" action="copy"
                                              text="#{usuarioBean.copiarCredenciais()}"/>

                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </sec:authorize>


    <div class="row">
        <div class="tab-pane col-lg-12" id="profile-b1">
            <!-- Personal-Information -->
            <div class="panel panel-default panel-fill">
                <div class="panel-heading">
                    <h3 class="panel-title">Grupos</h3>
                </div>
                <div class="panel-body">
                    <p:pickList id="grupos" value="#{usuarioBean.grupos}" var="o" itemValue="#{o}"
                                itemLabel="#{o.nome}" showCheckbox="true" responsive="true" showSourceFilter="true"
                                showTargetFilter="true" filterMatchMode="contains" converter="grupoConverter"
                                disabled="#{disabled eq 'disabled'
								|| usuarioBean.perfilAdmOuSuporte() ? true : null}">
                        <f:facet name="sourceCaption">Disponível</f:facet>
                        <f:facet name="targetCaption">Selecionado</f:facet>
                        <p:column style="width:90%;">
                            #{o.nome}
                        </p:column>
                    </p:pickList>
                </div>
            </div>
        </div>
    </div>
    <div class="row">
        <div class="tab-pane col-lg-12" id="profile-b1">
            <!-- Personal-Information -->
            <div class="panel panel-default panel-fill">
                <div class="panel-heading">
                    <h3 class="panel-title">Permissões</h3>
                </div>
                <div class="panel-body">
                    <p:pickList id="permissoes" value="#{usuarioBean.permissoes}" var="o" itemValue="#{o}"
                                itemLabel="#{o.sistema} - #{o.nome}" showCheckbox="true" responsive="true"
                                showSourceFilter="true" showTargetFilter="true" filterMatchMode="contains"
                                converter="permissaoConverter"
                                disabled="#{
							disabled eq 'disabled' || helperSessionBean.usuario.perfil != 'ADMINISTRADOR' ? true :
							null}">
                        >

                        <f:facet name="sourceCaption">Disponível</f:facet>
                        <f:facet name="targetCaption">Selecionado</f:facet>

                        <p:column style="width:90%;">
                            #{o.sistema} - #{o.nome}
                        </p:column>
                    </p:pickList>
                </div>
            </div>
        </div>
    </div>
    <div class="row">
        <div class="tab-pane col-lg-12" id="profile-b1">
            <!-- Personal-Information -->
            <div class="panel panel-default panel-fill">
                <div class="panel-heading">
                    <h3 class="panel-title">Financeiras</h3>
                </div>
                <div class="panel-body" jsf:id="divFinanceira">
                    <p:pickList id="financeiras" value="#{usuarioBean.financeiras}" var="o"
                                itemValue="#{o}" itemLabel="#{o.nome}" showCheckbox="true" responsive="true"
                                showSourceFilter="true" showTargetFilter="true" filterMatchMode="contains"
                                label="Financeiras" required="#{usuarioBean.entity.perfil == 'FINANCEIRA'}"
                                converter="financeiraConverter"
                                disabled="#{disabled == 'disabled' || (helperSessionBean.usuario.perfil == 'ADMINISTRADOR' || helperSessionBean.usuario.perfil == 'SUPORTE_PLACE') ? null : 'disabled'}">
                    <f:facet name="sourceCaption">Disponível</f:facet>
                        <f:facet name="targetCaption">Selecionado</f:facet>

                        <p:column style="width:90%;">
                            #{o.nome}
                        </p:column>
                    </p:pickList>
                </div>
            </div>
        </div>
    </div>
</ui:composition>
