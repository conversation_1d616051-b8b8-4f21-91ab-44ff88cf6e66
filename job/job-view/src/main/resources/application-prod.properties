#datasource
spring.datasource.jndi-name=java:jboss/datasources/RegistroDS
#jpa
spring.jpa.database-platform=org.hibernate.dialect.PostgreSQLDialect
spring.jpa.hibernate.ddl-auto=update
spring.jpa.properties.hibernate.id.new_generator_mappings=true
spring.jpa.properties.org.hibernate.envers.store_data_at_delete=true
spring.jpa.show-sql=false
spring.jpa.format-sql=true
#cas
cas.server=https://placecon.com.br/auth
cas.local.login=https://placecon.com.br/job/login/cas
cas.url.ambiente.boasvindas=https://placecon.com.br/

enderecos.migracao=https://m.placecon.com.br/registro-app-ws/api/v1/migracao

#jsf
jsf.PROJECT_STAGE=Production
server.servlet.session.timeout=30
#log
logging.level=INFO
logging.config=classpath:/log4j2.xml
file.dir=/storage/contratos/
file.dir-read=/storage/contratos/
file.mg.dir=/storage/mg/
file.dir.image-flow=/storage/documentoArrecadacao/image/
file.dir.contrato-sini=/data/contratos-sini/
# email

spring.mail.host=smtp.mailgun.org
spring.mail.port=587
spring.mail.username=<EMAIL>
spring.mail.password=**************************************************
spring.mail.testConnection=false
spring.mail.properties.mail.smtp.auth=true
spring.mail.properties.mail.smtp.starttls.enable=true

spring.mail.mailgun.domain=placecon.com.br
spring.mail.mailgun.ws-auth=Basic ************************************************************************
spring.mail.contato=<EMAIL>
spring.mail.ambiente=prod
spring.mail.destinarios.baixa=<EMAIL>
spring.mail.destinarios.suporte=<EMAIL>

#boleto
boleto.url=https://app.boletocloud.com/api
boleto.token=api-key_pPEocFcZcgdIOYZcNgmfzNYNiansbo_MzFG6UoAgaMc=
boleto.token.conta.itau=api-key_ibUAA5f_k_Rw7SRgvU705hZqhcZEZhFi4KLPsfCaaWo=
boleto.versao=v1
boleto.conta=24255-7
boleto.agencia=6630
boleto.banco=341
boleto.carteira=109
boleto.beneficiario.nome=PLACE TECNOLOGIA E INOVACAO S/A
boleto.beneficiario.cprf=06.032.507/0001-03
boleto.beneficiario.endereco.cep=04542-001
boleto.beneficiario.endereco.uf=SP
boleto.beneficiario.endereco.localidade=Sao Paulo
boleto.beneficiario.endereco.bairro=Itaim Bibi
boleto.beneficiario.endereco.logradouro=Rua Leopoldo Couto de Magalhaes Junior
boleto.beneficiario.endereco.numero=1098
boleto.beneficiario.endereco.complemento=Sala 91
boleto.titulo=CC
boleto.instrucao1=
boleto.instrucao2=
boleto.instrucao3=

#service DETRAN AL

detran.al.default.uri=https://registros.detran.al.gov.br
detran.al.context.uri=/rdc-app-rest/rest/v2
detran.al.gerar.token=/auth
detran.al.consulta=/contrato/chassi/{chassi}
detran.al.registro=/contrato
detran.al.envio.imagem=/arquivo/chassi/{chassi}

#service DETRAN CE
detran.ce.default.uri=https://gravamews.detran.ce.gov.br/veiculows
detran.ce.cliente.autenticado=/gravametw/api/autenticar/clienteAutenticado
detran.ce.registro.buscar.apontamento=/gravametw/contrato/api/apontamento
detran.ce.registro.buscar.apontamentopendente=/gravametw/contrato/api/apontamentoPendente
detran.ce.registro.buscar.apontamentospendentes=/gravametw/contrato/api/apontamentosPendentes
detran.ce.registro.buscar.contrato=/gravametw/contrato/api/contrato
detran.ce.registro.buscar.contratos=/gravametw/contrato/api/contratos
detran.ce.registro.buscar.contratosveiculo=/gravametw/contrato/api/contratosVeiculo
detran.ce.registro.buscar.municipiosce=/gravametw/contrato/api/municipios/ce
detran.ce.registro.cancelar=/gravametw/contrato/api/cancelar
detran.ce.registro.download.contratodigitalizado=/gravametw/contrato/api/downloadContratoDigitalizado
detran.ce.registro.enviar.contratodigitalizado=/gravametw/contrato/api/enviarContratoDigitalizado
detran.ce.registro.registrar=/gravametw/contrato/api/registrar
detran.ce.daes.buscar.dae=/daewstw/api/dae
detran.ce.daes.buscar.daes=/daewstw/api/daes
detran.ce.daes.gerar.dae=/daewstw/api/dae/gerar
detran.ce.daes.extrato.dae=/daewstw/api/dae/pdf
detran.ce.daes.buscar.servicos=/daewstw/api/servicos
detran.ce.daes.estoquedaes=/daewstw/api/dae/estoque

#service DETRAN SE
detran.se.default.uri=https://api.registrocontrato.detran.se.gov.br
detran.se.gera.token=/api/v1/login
detran.se.refresh.token=/api/v1/refresh
detran.se.profile=/api/v1/profile
detran.se.criar.aditivo=/api/v1/creditor/additive
detran.se.consultar.contrato=/api/v1/creditor/contract/{uuid}
detran.se.criar.registrar.contrato=/api/v1/creditor/contract/create/and/register
detran.se.criar.contrato=/api/v1/creditor/contract
detran.se.registrar.contrato=/api/v1/creditor/contract/{contract}/register
detran.se.atualizar.contrato=/api/v1/creditor/contract/{uuid}
detran.se.salvar.imagem=/api/v1/creditor/contract/save_image
detran.se.cancelar.contrato=/api/v1/creditor/contract/{contract}/canceled
detran.se.baixar.contrato=/api/v1/manager/contract/write-off
detran.se.cancelar.contrato.detran=/api/v1/manager/contract/cancel
detran.se.cnpjresponsible=06032507000103
detran.se.tokenvalidation=8a8836f6ffc560fd950f86f8e8d9052b33bc3337148c297ca8a3a265c7900e9c

# service DETRAN PR
detran.pr.default.uri.auth=https://auth-cs.identidadedigital.pr.gov.br/centralautenticacao/api/v1/token
detran.pr.default.uri=https://www.registrodecontrato.detran.pr.gov.br/detran-regcon/api
detran.pr.usuario=e8c0653fea13f91bf3c48159f7c24f78
detran.pr.senha=G9F8jX5apHgkAmWF
# service DETRAN SC
detran.sc.default.uri=http://webservicesp.detrannet.sc.gov.br/RegistroContrato/RegistroContrato.asmx
detran.sc.context.path=com.registrocontrato.registro.service.detran.sc.client
detran.sc.registrarcontrato=RegistrarContrato
detran.sc.consultarsequencial=ConsultarSequencialContrato
detran.sc.usuario=placeti
detran.sc.senha=#0603250ProD7000103@
# service DETRAN MG
detran.mg.default.uri=http://webservice.detran.mg.gov.br/sircof/soap/contratos_financeiros/wsdl
detran.mg.context.path=com.registrocontrato.registro.service.detran.mg.client
detran.mg.usuario=06032507000103
detran.mg.senha=2262df343111ee477711fce38808f3412e584804
# service DETRAN BA
detran.ba.default.uri=http://200.187.13.78/wsdetrancontrato/wsdetrancontrato.asmx
detran.ba.context.path=com.registrocontrato.registro.service.detran.ba.client
detran.ba.usuario=104407
detran.ba.senha=NBVkLj
# service DETRAN MT
detran.mt.default.uri=https://ws.detrannet.mt.gov.br/wsRegistroContrato/wsRegistroContrato.asmx
detran.mt.default.endpoint=http://ws.detrannet.mt.gov.br
detran.mt.context.path=com.registrocontrato.registro.service.detran.mt.client
detran.mt.url.imagem=GravaImagem2

# service DETRAN RS
detran.rs.default.uri=https://www.vei.detran.rs.gov.br/sng/ContratoIncSoap
detran.rs.default.uri.imagem=https://www.vei.detran.rs.gov.br/sng/rest/contratossl/uploadContrato
detran.rs.context.path=com.registrocontrato.registro.service.detran.rs.client

# service DETRAN PI
detran.pi.default.uri-boleto=https://www.pi.getran.com.br/financeiro/api/registro/gerarBoleto
detran.pi.default.uri=https://www.pi.getran.com.br/registro-contrato
detran.pi.context.path=com.registrocontrato.registro.service.detran.pi.client
detran.pi.usuario=06032507000103
detran.pi.senha=d(89@^1Y,]j1
detran.pi.rest.token=eyJhbGciOiJIUzI1NiJ9.eyJzdWIiOiIwNjAzMjUwNzAwMDEwMyIsImF1ZCI6WyJSRUdJU1RSQURPUkEiXSwiaXNzIjoiZmluYW5jZWlyby1hcGkiLCJpYXQiOjE3NDg1NDc0ODcsImp0aSI6Ijc5MzkzY2ExLTdlMmQtNDgyOC1iNDMxLWM2YTM0NDUyNDNmYSIsIm5hbWUiOiJQbGFjZSBUZWNub2xvZ2lhIGUgSW5vdmHDp8OjbyBTLiBBLiJ9.XUhO7xSpM9zqcAIgovQ0DxJh4eVVcuTkBdDBei9Iv1E
# service DETRAN MS
detran.ms.default.uri=https://web2.detran.ms.gov.br/s56/rc-api
detran.ms.url.cadastrar=/solicitacao/solicitar-registro
detran.ms.url.autenticacao=/usuario/authenticate
detran.ms.url.desbloquear.contrato=/solicitacao/desbloquear-contrato
detran.ms.url.envio.imagem=/solicitacao/enviar-imagem
detran.ms.url.corrigir.imagem=/solicitacao/corrigir-imagem
detran.ms.url.busca.andamento=/buscar/andamento
detran.ms.url.consulta.boleto=/buscar/guia-pagamento
detran.ms.url.consulta.boleto.byte=/buscar/guia-pagamento/byte
detran.ms.usuario=060325070000103
detran.ms.senha=yg525h<~D?
# service DETRAN PE
detran.pe.default.uri=http://************:51075/WebApiRegistroContratoGravame/RegistraContrato
detran.pe.default.cobranca.uri=https://************:52675/Estabelecimento/Obter/MovimentacaoFinanceira
#Banco Pan Service
bancopan.default.uri=https://api.bancopan.com.br/transacional/apipanveiculos
bancopan.obter-token=/token
bancopan.autenticar-token=/oauth/token
bancopan.contrato-digitalizado=/GrvEnvioDocContrato/ContratoDigitalizado
bancopan.130_usuario_senha=MTMwOlVTUl9SRUdfUExBQ0U6d0kySGk3Y1JlNCM=
#Tabela FIPE
fipe.default.uri=http://fipeapi.appspot.com/api/1/
fipe.connectionTimeout=10000
placecon.cripto.key=/opt/chaves/prod.key
sistema=JOB
detran.certificados=/storage/certificados/

#API DO BANCO DO BRASIL
bancodobrasil.oauth2.uri=https://oauth.bb.com.br/oauth/token
bancodobrasil.default.uri=https://api-ip.bb.com.br/pagamentos-lote/v1
bancodobrasil.url.cadastrar.boleto=/lotes-boletos
bancodobrasil.url.confirmar.pagamento.boleto=/boletos/{codigoIdentificadorPagamento}
bancodobrasil.url.cadastrar.gru=/pagamentos-gru
bancodobrasil.url.confirmar.pagamento.gru=/gru/{codigoIdentificadorPagamento}
bancodobrasil.client.secret=eyJpZCI6ImVmNjFmOTAtMjE5NS0iLCJjb2RpZ29QdWJsaWNhZG9yIjowLCJjb2RpZ29Tb2Z0d2FyZSI6MjIwMDksInNlcXVlbmNpYWxJbnN0YWxhY2FvIjoxLCJzZXF1ZW5jaWFsQ3JlZGVuY2lhbCI6MSwiYW1iaWVudGUiOiJwcm9kdWNhbyIsImlhdCI6MTY0MDk4NDgzMzg2OX0
bancodobrasil.client.id=eyJpZCI6IjBiMjc3YWItNDI0OC00MSIsImNvZGlnb1B1YmxpY2Fkb3IiOjAsImNvZGlnb1NvZnR3YXJlIjoyMjAwOSwic2VxdWVuY2lhbEluc3RhbGFjYW8iOjF9
bancodobrasil.app.key=f6f5ebb404bbef8013463489f0050056b961a5ba
bancodobrasil.scopes=grant_type=client_credentials&scope=pagamentos-lote.pagamentos-info pagamentos-lote.lotes-requisicao pagamentos-lote.guias-codigo-barras-requisicao pagamentos-lote.guias-codigo-barras-info pagamentos-lote.boletos-requisicao pagamentos-lote.lotes-info pagamentos-lote.pagamentos-guias-sem-codigo-barras-info pagamentos-lote.pagamentos-codigo-barras-info pagamentos-lote.boletos-info
bancodobrasil.payment_contract=290472
bancodobrasil.debit_branch_office=1004
bancodobrasil.debit_current_account=242557
bancodobrasil.check_digits_debit_current_account=2
certificado.place.senha=032507

#arquivos das portarias de credenciamento
portarias.detran.path=/storage/portarias

# MENSAGERIA - KAFKA
spring.kafka.producer.bootstrap-servers=10.40.160.4:9092
spring.kafka.producer.key-serializer=org.apache.kafka.common.serialization.StringSerializer
spring.kafka.producer.value-serializer=org.apache.kafka.common.serialization.StringSerializer
spring.kafka.producer.client-id=group-id

spring.kafka.consumer.bootstrap-servers=10.40.160.4:9092
spring.kafka.consumer.group-id=group-id
spring.kafka.consumer.auto-offset-reset=earliest
spring.kafka.consumer.key-deserializer=org.apache.kafka.common.serialization.StringDeserializer
spring.kafka.consumer.value-deserializer=org.apache.kafka.common.serialization.StringDeserializer

auto.create.topics.enable=true

google.chat.monitoramento=https://chat.googleapis.com/v1/spaces/AAAAJMO4F0s/messages
google.chat.monitoramento.key=AIzaSyDdI0hCZtE6vySjMm-WEfRq3CPzqKqqsHI
google.chat.monitoramento.token=r66EqMH5lp7wQ6FFqj5grmqP_s1L5gCRWsgSYSJqd1c
