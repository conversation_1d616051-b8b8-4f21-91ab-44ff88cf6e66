package com.registrocontrato.job.entity;

public enum CurrentJob {
	BOLETO_CLOUD
	,BOLETO_PR
	,BOLETO_MS
	,BOLETO_PE
	,COBRANCA
	,CREDEN<PERSON>AMENTO
	,DOC<PERSON><PERSON><PERSON>_RS
	,<PERSON><PERSON><PERSON><PERSON><PERSON>_SC
	,D<PERSON><PERSON><PERSON><PERSON>_MG
	,D<PERSON><PERSON><PERSON><PERSON>_MS
	,D<PERSON><PERSON><PERSON><PERSON>_MT
	,<PERSON><PERSON><PERSON><PERSON><PERSON>_PR
	,DOCUMENTO_CE
	,DOCUMENTO_SE
	,DOCUMENTO_AL
	,FALHAS_NAO_TRATADAS
	,EXPIRAR_ACESSO_ALERTA
	,EXPIRAR_ACESSO
	,CONTRATOS_SEM_ANEXO
	,FTP_BOLETO_PE
	,FTP_BOLETO_SC
	,NOTIFICACAO_ECOMM
	,PROJECAO_RELATORIO
	,R<PERSON>ES<PERSON>_CHASSI_INATIVO
	,ROTINA_ENVIO_BA
	,ROTINA_FINANCEIRA_BA_BAIXA
	,ROTINA_FINANCEIRA_CE_BAIXA
	,ROTINA_FINANCEIRA_BA_ENVIO_CODIGO_PAGAMENTO
	,ROTINA_FINANCEIRA_GO_ENVIO_CODIGO_PAGAMENTO
	,ROTINA_FINANCEIRA_BA_LEMBRETE_NAO_PAGOS
	,ROTINA_FINANCEIRA_RS_BAIXA
	,ROTINA_FINANCEIRA_RS_ENVIO_CODIGO_PAGAMENTO
	,ROTINA_FINANCEIRA_RS_LEMBRETE_NAO_PAGOS
	,ROTINA_FINANCEIRA_GO_LEMBRETE_NAO_PAGOS
	,TABELA_FIPE_CARRO
	,TABELA_FIPE_MOTO
	,TABELA_FIPE_CAMINHAO
	,VALIDAR_CONTRATO_MS
	,VALIDAR_ARQUIVOS_MS
	,NOTIFICACAO_NAO_PAGAMENTO
	,NOTIFICARCAO_BOLETO_VENCIDO
	,NOTIFICACAO_CONTRATO_PENDENTE
	,NOTIFICAR_CONTRATOS_PENDENTES_PAGAMENTO_SEM_TAXA
	,NOTIFICACAO_CONFIRMACAO_PAGAMENTO
	,ROTINA_NOTIFICACAO_BA
	,BUSCA_TIPOVEICULO_DETRAN_CE
	,DOWNLOAD_CONTRATOS_PAN
	,IMAGENS_SINI
	,APONTAMENTO_CE
	,FINANCEIRO_CADASTRA_PAGAMENTO_BANCO
	,FINANCEIRO_CONFIRMA_PAGAMENTO_BANCO
	,ENVIAR_RELATORIO_DETRAN_RJ, ENVIAR_RELATORIO_DETRAN_AC
	,NOTIFICACAO_CREDENCIAMENTO_FINANCEIRA
	,ROTINA_FINANCEIRA_RS
	,TABELA_FIPE
	,NOTIFICACAO_CONTRATO_PENDENTE_PAGAMENTO
	,NOTIFICACAO_CONTRATO_SEM_ANEXO_INICIO_MES_SICOOB
	,NOTIFICACAO_CONTRATO_SEM_ANEXO_COM_RELATORIO_ADEMICON
	,NOTIFICAR_CONTRATO_PENDENTES_EFETIVACAO
	,NOTIFICACAO_INCLUSAO_ANEXO
	,ROTINA_VERFICACAO_CONTRATO
	,NOTIFICACAO_CONTRATO_SEM_ANEXO_COM_RELATORIO
	,FINANCEIRA
	,NOTIFICACAO_CONTRATO_SEM_ANEXO_INICIO_MES
	,ALERTA_VENCIMENTO_ECNPJ
	,ALERTA_COBRANCA_UNIFICADA
	,BOLETO_INADIMPLENCIA_PE
	,REGISTRO_DETRAN_RSNG
}
