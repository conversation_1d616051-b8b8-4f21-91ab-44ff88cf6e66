package com.registrocontrato.job.task.detran;

import com.registrocontrato.infra.email.EnviaEmail;
import com.registrocontrato.infra.entity.Uf;
import com.registrocontrato.job.entity.SituacaoExecucaoJob;
import com.registrocontrato.job.service.JobService;
import com.registrocontrato.job.task.PlaceTask;
import com.registrocontrato.registro.entity.MensagensNaoTratadas;
import com.registrocontrato.registro.service.MensagensNaoTratadasService;

import java.time.LocalDateTime;
import java.util.List;

public abstract class EnvioImagensImpl extends PlaceTask {

    private final MensagensNaoTratadasService mensagensService;

    protected EnvioImagensImpl(EnviaEmail enviaEmail, JobService jobService, MensagensNaoTratadasService mensagensService) {
        super(enviaEmail, jobService);
        this.mensagensService = mensagensService;
    }

    protected void armazenarMensagensDeErro(List<MensagensNaoTratadas> mensagensErro, LocalDateTime l1) {
        log.info("Armazenando mensagens de erro - " + mensagensErro.size());
        if (!mensagensErro.isEmpty()) {
            StringBuilder logErro = new StringBuilder();

            mensagensErro.forEach(erro -> {
                log.warn(erro.getMensagem());
                logErro.append(erro.getMensagem());
                mensagensService.save(erro);
            });
            jobService.registrarLogJob(getJob(), l1, LocalDateTime.now(), SituacaoExecucaoJob.ERRO, logErro.toString());
        }
    }

    protected void registrarLogSemEnvios(LocalDateTime l1) {
        jobService.registrarLogJob(getJob(), l1, LocalDateTime.now(), SituacaoExecucaoJob.SUCESSO, "Nenhum contrato pendente!");
    }

    protected abstract void notificarSucesso(StringBuilder sucesso, LocalDateTime l1);

    protected abstract Uf getUf();
}
