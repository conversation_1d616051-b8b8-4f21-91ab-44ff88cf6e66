package com.registrocontrato.job.task.detran.rs;

import com.registrocontrato.infra.email.EnviaEmail;
import com.registrocontrato.infra.entity.SimNao;
import com.registrocontrato.infra.entity.Uf;
import com.registrocontrato.job.entity.CurrentJob;
import com.registrocontrato.job.entity.SituacaoExecucaoJob;
import com.registrocontrato.job.service.JobService;
import com.registrocontrato.job.task.PlaceTask;
import com.registrocontrato.registro.entity.Contrato;
import com.registrocontrato.registro.entity.DocumentoArrecadacao;
import com.registrocontrato.registro.repository.DocumentoArrecadacaoRepository;
import com.registrocontrato.registro.service.ContratoService;
import com.registrocontrato.registro.service.detran.rs.soap.WsDetranRS;
import com.registrocontrato.registro.service.dto.RetornoStatusTaxaVeiculoDTO;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

@Component
public class FinanceiroDentranRSBaixarDocumentoArrecadacaoTask extends PlaceTask {

    private final WsDetranRS wsDetranRsSoapClient;

    private final DocumentoArrecadacaoRepository documentoArrecadacaoRepository;

    private final ContratoService contratoService;

    public FinanceiroDentranRSBaixarDocumentoArrecadacaoTask(EnviaEmail enviaEmail,
                                                             JobService jobService,
                                                             WsDetranRS wsDetranRsSoapClient,
                                                             DocumentoArrecadacaoRepository documentoArrecadacaoRepository, ContratoService contratoService) {
        super(enviaEmail, jobService);
        this.wsDetranRsSoapClient = wsDetranRsSoapClient;
        this.documentoArrecadacaoRepository = documentoArrecadacaoRepository;
        this.contratoService = contratoService;
    }

    @Scheduled(cron = "0 0/10 6-23 * * *", zone = TIME_ZONE)
    @Async("jobExecutor")
    public void run() {
        start();
    }

    @Override
    public void execute() {
        LocalDateTime l1 = LocalDateTime.now();
        List<DocumentoArrecadacao> docs = documentoArrecadacaoRepository.recuperaDocumentosNaoPagoAndNotificado(Uf.RS, Calendar.getInstance().getTime());

        for (DocumentoArrecadacao d : docs) {
            log.info("Consultando status do boleto do chassi: " + d.getVeiculo().getNumeroChassi());
            RetornoStatusTaxaVeiculoDTO consultaStatusTaxasVeiculo = wsDetranRsSoapClient.consultaStatusTaxasVeiculo(d.getVeiculo().getContrato(), d.getVeiculo().getNumeroChassi());
            if (consultaStatusTaxasVeiculo != null && consultaStatusTaxasVeiculo.getTaxaReservaContrato() == SimNao.S) {
                Contrato contrato  = contratoService.findOne(d.getVeiculo().getContrato().getId());
                contratoService.ativar(contrato);
                d.setDataPagamento(new Date());
                documentoArrecadacaoRepository.save(d);
            }
        }

        LocalDateTime l2 = LocalDateTime.now();
        getJobService().registrarLogJob(getJob(), l1, l2, SituacaoExecucaoJob.SUCESSO, "Baixa de Pagamento");
    }


    @Override
    public CurrentJob getJob() {
        return CurrentJob.ROTINA_FINANCEIRA_RS_BAIXA;

    }

}
