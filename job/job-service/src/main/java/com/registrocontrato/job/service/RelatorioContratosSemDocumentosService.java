package com.registrocontrato.job.service;

import com.registrocontrato.infra.email.Email;
import com.registrocontrato.infra.email.EnviaEmail;
import com.registrocontrato.infra.entity.Financeira;
import com.registrocontrato.infra.exception.ServiceException;
import com.registrocontrato.infra.util.PlaceconUtil;
import com.registrocontrato.job.entity.CurrentJob;
import com.registrocontrato.job.entity.SituacaoExecucaoJob;
import com.registrocontrato.registro.dto.ContratoSemAnexoDTO;
import org.springframework.stereotype.Service;

import javax.mail.internet.AddressException;
import javax.mail.internet.InternetAddress;
import java.io.IOException;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class RelatorioContratosSemDocumentosService extends ExcelService {

    private final JobService service;
    private final EnviaEmail enviaEmail;

    public RelatorioContratosSemDocumentosService(
            JobService service,
            EnviaEmail enviaEmail) {
        super(service);
        this.service = service;
        this.enviaEmail = enviaEmail;
    }

    public void findContratosAndMontarEmail(Financeira financeira, CurrentJob currentJob) {
        LocalDateTime l1 = LocalDateTime.now();
        List<ContratoSemAnexoDTO> semAnexo = service.findContratosSemAnexoByFinanceira(financeira);
        String registroLog = "";
        if (!PlaceconUtil.isListaVaziaOuNula(semAnexo)) {
            HashMap<Long, List<ContratoSemAnexoDTO>> grupamentos = agruparFinanceiras(semAnexo);
            for (Map.Entry<Long, List<ContratoSemAnexoDTO>> ufSemAnexo : grupamentos.entrySet()) {
                StringBuffer sb = new StringBuffer();
                ContratoSemAnexoDTO semAnexoTotal = new ContratoSemAnexoDTO();
                for (ContratoSemAnexoDTO c : ufSemAnexo.getValue()) {
                    semAnexoTotal.setTotal(semAnexoTotal.getTotal() + c.getTotal());
                    if (semAnexoTotal.getFinanceira() == null) {
                        semAnexoTotal.setFinanceira(c.getFinanceira());
                    }
                    if (!sb.toString().isEmpty()) {
                        sb.append("<br/>");
                    }
                    sb.append(" - " + c.getUf().name() + ": <b>" + c.getTotal() + " registro");
                    if (c.getTotal() > 1) {
                        sb.append("(s)");
                    }
                    sb.append("</b>\n");
                }
                try {
                    registroLog = enviarNotificacaoContratoSemAnexoPorUf(sb, semAnexoTotal, currentJob);
                } catch (AddressException | IOException e) {
                    throw new ServiceException(e.getMessage());
                }
            }
        }
        LocalDateTime l2 = LocalDateTime.now();
        service.registrarLogJob(currentJob, l1, l2, SituacaoExecucaoJob.SUCESSO, registroLog);
    }

    public Email instEmail(EnviaEmail enviaEmail) {
        return new Email(enviaEmail);
    }

    public String enviarNotificacaoContratoSemAnexoPorUf(StringBuffer ufSemAnexos,
                                                          ContratoSemAnexoDTO semAnexoTotal,
                                                          CurrentJob currentJob) throws AddressException, IOException {
        String titulo = "Quantitativo de Contratos sem documentos";
        String totalRegistros = String.valueOf(semAnexoTotal.getTotal());
        HashMap<Character, List<InternetAddress>> hash = new HashMap<Character, List<InternetAddress>>();
        List<InternetAddress> emails = recuperaEmailPendencia(semAnexoTotal.getFinanceira());
        hash.put(Email.TIPO_PARA, emails);
        Map<String, String> params = new HashMap<String, String>();
        params.put("TOTAL", totalRegistros);
        params.put("LISTAUF", ufSemAnexos.toString());
        String financeira = semAnexoTotal.getFinanceira().getNome();
        params.put("FINANCEIRA", financeira);
        String nomeRepresentante = semAnexoTotal.getFinanceira().getNomeRepresentante();
        params.put("REPRESENTANTE", nomeRepresentante);
        Email email = instEmail(enviaEmail);
        String excel = excel(semAnexoTotal.getFinanceira(), currentJob);
        email.addAnexo(excel);
        email.enviarEmail(titulo, params, hash, "/email/contratosSemDocumentos.xhtml");
        String mensagem = String.format("<b>Sr(a) %s,</b><br/>"
                        + "<p>O Agente Financeiro <b>%s</b> possui <b>%s registro(s) de contrato(s) sem os documentos digitalizados.</b>"
                        + "Pedimos sua atenção para a regularização desse item, evitando futuros transtornos.</p>"
                        + "<p>Conte com nosso time no auxílio dessa regularização.</p>",
                nomeRepresentante, financeira, totalRegistros);

        service.saveNotificacao(LocalDate.now(), LocalDate.now().plusDays(6),
                semAnexoTotal.getFinanceira(), titulo, mensagem);
        return email.getTexto();
    }

    private HashMap<Long, List<ContratoSemAnexoDTO>> agruparFinanceiras(
            List<ContratoSemAnexoDTO> semAnexo) {
        HashMap<Long, List<ContratoSemAnexoDTO>> grupamento = new HashMap<>();
        for (ContratoSemAnexoDTO c : semAnexo) {
            List<ContratoSemAnexoDTO> contratos = grupamento.get(c.getFinanceira().getId());
            if (contratos == null) {
                contratos = new ArrayList<>();
                grupamento.put(c.getFinanceira().getId(), contratos);
            }
            contratos.add(c);
        }
        return grupamento;
    }

    public List<InternetAddress> recuperaEmailPendencia(Financeira financeira) throws AddressException {
        List<InternetAddress> emails = new ArrayList<>();

        if (financeira.getEmailPendenciaAnexo() != null && !financeira.getEmailPendenciaAnexo().trim().equals("")) {
            for (String string : financeira.getEmailPendenciaAnexo().split(";")) {
                if (!string.isEmpty()) {
                    try {
                        emails.add(new InternetAddress(string.trim()));
                    } catch (Exception e) {
                        logger.error(e.getMessage());
                    }
                }
            }
            return emails;
        }
        emails.add(new InternetAddress(financeira.getEmailRepresentante()));
        return emails;
    }

}
