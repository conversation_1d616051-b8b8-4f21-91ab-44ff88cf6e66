#datasource 
spring.datasource.jndi-name=java:jboss/datasources/financeiroDS

#jpa
spring.jpa.hibernate.dialect=PostgreSQLDialect
spring.jpa.hibernate.ddl-auto=update
spring.jpa.properties.hibernate.id.new_generator_mappings=true
spring.jpa.properties.org.hibernate.envers.store_data_at_delete=true
spring.jpa.show-sql=false
spring.jpa.format-sql=false

#cas
cas.server=https://placecon.com.br/auth
cas.local.login=https://hot.placecon.com.br/registro/login/cas
cas.url.ambiente.boasvindas=https://placecon.com.br/

#jsf
jsf.PROJECT_STAGE=Production

#log
logging.level=INFO
logging.config=classpath:/log4j2.xml

file.dir=/data/contratos/
#file.dir-read=/storage/contratos/
file.dir-read=/data/contratos/

#diretorio para anexos de financeira
file-financeira.dir=/data/financeira/
#file-financeira.dir-read=/storage/financeira/
file-financeira.dir-read=/data/financeira/

#diretorio para boletos
file-boleto.dir=/data/boletos/
#file-boleto.dir-read=/storage/boletos/
file-boleto.dir-read=/data/boletos/

#diretorio para nf de financeiro
file-nf.dir=/data/notasFiscais/
#file-nf.dir-read=/storage/notasFiscais/
file-nf.dir-read=/data/notasFiscais/

# remessa
file.remessa.dir=/data/remessas/
#file.remessa.dir-read=/storage/remessas/
file.remessa.dir-read=/data/remessas/

# remessa chassi
file.remessa.chassi.dir=/data/remessaschassi/
#file.remessa.chassi.dir-read=/storage/remessaschassi/
file.remessa.chassi.dir-read=/data/remessaschassi/

#boleto
boleto.url=https://app.boletocloud.com/api
boleto.token=api-key_pPEocFcZcgdIOYZcNgmfzNYNiansbo_MzFG6UoAgaMc=
boleto.token.conta.itau=api-key_ibUAA5f_k_Rw7SRgvU705hZqhcZEZhFi4KLPsfCaaWo=
boleto.versao=v1
boleto.conta=24255-7
boleto.agencia=6630
boleto.banco=341
boleto.carteira=109
boleto.beneficiario.nome=PLACE TECNOLOGIA E INOVACAO S/A
boleto.beneficiario.cprf=06.032.507/0001-03
boleto.beneficiario.endereco.cep=04548-040
boleto.beneficiario.endereco.uf=SP
boleto.beneficiario.endereco.localidade=SAO PAULO
boleto.beneficiario.endereco.bairro=Vila Olimpia
boleto.beneficiario.endereco.logradouro=Rua Tenerife
boleto.beneficiario.endereco.numero=31
boleto.beneficiario.endereco.complemento=4o Andar Sala S18
boleto.titulo=CC
boleto.instrucao1=
boleto.instrucao2=
boleto.instrucao3=		
boleto.b3=<EMAIL>


# service DETRAN SP
detran.sp.default.uri=http://10.200.47.78:80/eaigever/SircofService
detran.sp.context.path=com.registrocontrato.registro.service.detran.sp.client

# service DETRAN MS
detran.ms.default.uri=https://web2.detran.ms.gov.br/s56/rc-api
detran.ms.url.autenticacao=/usuario/authenticate
detran.ms.url.consulta.boleto=/buscar/guia-pagamento

# service DETRAN PR
detran.pr.default.uri.auth=https://auth-cs.identidadedigital.pr.gov.br/centralautenticacao/api/v1/token
detran.pr.default.uri=https://www.registrodecontrato.detran.pr.gov.br/detran-regcon/api
detran.pr.usuario=e8c0653fea13f91bf3c48159f7c24f78
detran.pr.senha=G9F8jX5apHgkAmWF

# service DETRAN AP
detran.ap.default.uri.auth=https://www.ap.getran.com.br/integracao-api-rest/api/autenticacao
detran.ap.default.uri=https://www.ap.getran.com.br/integracao-api-rest
detran.ap.url.cadastrar=/api/areaRestrita/registroContrato/registrar
detran.ap.url.consultar=/api/areaRestrita/registroContrato/consultar

# service DETRAN RJ
detran.rj.default.uri=http://10.200.180.71:8080/wsstack/services/REG-CONT
detran.rj.context.path=com.registrocontrato.registro.service.detran.rj.client

# service DETRAN PI
detran.pi.default.uri-boleto=https://www.pi.getran.com.br/financeiro/api/registro/gerarBoleto
detran.pi.default.uri=https://www.pi.getran.com.br/registro-contrato
detran.pi.context.path=com.registrocontrato.registro.service.detran.pi.client
detran.pi.usuario=06032507000103
detran.pi.senha=d(89@^1Y,]j1
detran.pi.rest.token=eyJhbGciOiJIUzI1NiJ9.eyJzdWIiOiIwNjAzMjUwNzAwMDEwMyIsImF1ZCI6WyJSRUdJU1RSQURPUkEiXSwiaXNzIjoiZmluYW5jZWlyby1hcGkiLCJpYXQiOjE3NDg1NDc0ODcsImp0aSI6Ijc5MzkzY2ExLTdlMmQtNDgyOC1iNDMxLWM2YTM0NDUyNDNmYSIsIm5hbWUiOiJQbGFjZSBUZWNub2xvZ2lhIGUgSW5vdmHDp8OjbyBTLiBBLiJ9.XUhO7xSpM9zqcAIgovQ0DxJh4eVVcuTkBdDBei9Iv1E

# service DETRAN RR
detran.rr.default.uri=https://www.rr.getran.com.br
detran.rr.url.cadastrar=/getranServicos/rest/registroContrato/registrarContrato
detran.rr.url.consultarcontrato=/getranServicos/rest/registroContrato/consultarContrato

# service DETRAN MG
detran.mg.default.uri=http://webservice.detran.mg.gov.br/sircof/soap/contratos_financeiros/service
detran.mg.context.path=com.registrocontrato.registro.service.detran.mg.client

# service DETRAN SC
detran.sc.default.uri=http://webservicesp.detrannet.sc.gov.br/RegistroContrato/RegistroContrato.asmx
detran.sc.context.path=com.registrocontrato.registro.service.detran.sc.client
detran.sc.registrarcontrato=RegistrarContrato
detran.sc.consultarsequencial=ConsultarSequencialContrato

# service DETRAN MT
detran.mt.default.uri=http://ws.detrannet.mt.gov.br/wsRegistroContrato/wsRegistroContrato.asmx
detran.mt.default.endpoint=http://ws.detrannet.mt.gov.br
detran.mt.context.path=com.registrocontrato.registro.service.detran.mt.client
detran.mt.url.cadastrar=RegistraContrato
detran.mt.url.consultar=ConsultaContrato

# service DETRAN AC
detran.ac.default.uri=https://www.ac.getran.com.br/getranServicos/rest/registroContrato/
detran.ac.context.path=com.registrocontrato.registro.service.detran.ac.client
detran.ac.url.cadastrar=registrarContrato
detran.ac.url.consultar=consultarContrato

# service DETRAN BA
detran.ba.default.uri=http://200.187.13.78/wsdetrancontrato/wsdetrancontrato.asmx
detran.ba.context.path=com.registrocontrato.registro.service.detran.ba.client

# service DETRAN PE
detran.pe.default.uri=http://200.238.67.2:51075/WebApiRegistroContratoGravame/RegistraContrato

#CONTA AZUL
contaazul.id=Fjo0MPHXyfWSpp3KlXtn02VWf9NIairR
contaazul.secret=pbItMwvyW39tgdb26D4UvjajFYam8ew0
contaazul.url=https://api.contaazul.com/auth/authorize
contaazul.redirect=https://hot.placecon.com.br/registro/contaazul/confirma-venda.xhtml
contaazul.url-token=https://api.contaazul.com/oauth2/token
contaazul.url-servicos=https://api.contaazul.com/v1/services/
contaazul.url-clientes=https://api.contaazul.com/v1/customers/
contaazul.url-vendas=https://api.contaazul.com/v1/sales


qrcode.validate.url=https://hot.placecon.com.br/registro/public/validate.xhtml
sign.url.return=https://hot.placecon.com.br

# email
spring.mail.mailgun.domain=placecon.com.br
spring.mail.mailgun.ws-auth=Basic ************************************************************************
spring.mail.contato=<EMAIL>
spring.mail.ambiente=prod
spring.mail.destinarios.baixa=<EMAIL>
spring.mail.destinarios.suporte=<EMAIL>

# timeout detran
detran.readTimeout=20000
detran.connectionTimeout=30000

decoder.url=https://api.procob.com/veiculos/v2/V0001?decodificadorChassi=SIM&chassi=
decoder.usuario=<EMAIL>
decoder.senha=MGtKMBmUXmBAMn5

placecon.cripto.key=/opt/chaves/prod.key

#ocr
tesseract.datapath=/usr/share/tesseract/4/tessdata

sistema=financeiro

#dados place
place.telefone1=+55 (11) 4210-1220
place.telefone2=0800 591 0915
place.site=https://placeti.com.br/

#NOTAS REEMBOLSO
ufesp.valor=2,572
ufesp.artigo=artigo 15, item II da portaria 465/2016
ufesp.portariapagamento=465/2016
duda.rj.codigoreceita=031 - 0
duda.rj.portaria=5639 de 31 de maio de 2019

# MENSAGERIA - KAFKA
spring.kafka.producer.bootstrap-servers=***********:9092
spring.kafka.producer.key-serializer=org.apache.kafka.common.serialization.StringSerializer
spring.kafka.producer.value-serializer=org.apache.kafka.common.serialization.StringSerializer
spring.kafka.producer.client-id=group-id

spring.kafka.consumer.bootstrap-servers=***********:9092
spring.kafka.consumer.group-id=group-id
spring.kafka.consumer.auto-offset-reset=earliest
spring.kafka.consumer.key-deserializer=org.apache.kafka.common.serialization.StringDeserializer
spring.kafka.consumer.value-deserializer=org.apache.kafka.common.serialization.StringDeserializer

auto.create.topics.enable=true
