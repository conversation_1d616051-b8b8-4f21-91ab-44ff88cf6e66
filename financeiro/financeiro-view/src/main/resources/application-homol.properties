#datasource 
spring.datasource.jndi-name=java:jboss/datasources/FinanceiroDS

#jpa
spring.jpa.hibernate.dialect=PostgreSQLDialect
spring.jpa.hibernate.ddl-auto=update
spring.jpa.properties.hibernate.id.new_generator_mappings=true
spring.jpa.properties.org.hibernate.envers.store_data_at_delete=true
spring.jpa.show-sql=false
spring.jpa.format-sql=false

#cas
cas.server=${CAS_AMBIENTE}/auth
cas.local.login=${CAS_AMBIENTE}/financeiro/login/cas
cas.url.ambiente.boasvindas=https://homol.placecon.com.br/

#jsf
jsf.PROJECT_STAGE=Development

#log
logging.level=INFO
logging.config=classpath:/log4j2.xml


#diretorio para anexos de financeira
file-financeira.dir=/data/financeira/
#file-financeira.dir-read=/storage/financeira/
file-financeira.dir-read=/data/financeira/

#diretorio para boletos
file-boleto.dir=/data/boletos/
#file-boleto.dir-read=/storage/boletos/
file-boleto.dir-read=/data/boletos/

#diretorio para nf de financeiro
file-nf.dir=/data/notasFiscais/
#file-nf.dir-read=/storage/notasFiscais/
file-nf.dir-read=/data/notasFiscais/

# remessa
file.remessa.dir=/data/remessas/
#file.remessa.dir-read=/storage/remessas/
file.remessa.dir-read=/data/remessas/

# remessa chassi
file.remessa.chassi.dir=/data/remessaschassi/
#file.remessa.chassi.dir-read=/storage/remessaschassi/
file.remessa.chassi.dir-read=/data/remessaschassi/

#boleto
boleto.url=https://sandbox.boletocloud.com/api
boleto.token=api-key_TiW3A-rbaoQo2PJgn-CxGCR3aVxe4gN5htnQmrtbNQM=
boleto.token.conta.itau=api-key_NVyd_7-DAeLGXuhkVJ0vCnMCwCa2lnR3SL90f2Hpa4c=
boleto.versao=v1
boleto.conta=24255-7
boleto.agencia=6630
boleto.banco=341
boleto.carteira=109
boleto.beneficiario.nome=PLACE TECNOLOGIA E INOVACAO S/A
boleto.beneficiario.cprf=06.032.507/0001-03
boleto.beneficiario.endereco.cep=04548-040
boleto.beneficiario.endereco.uf=SP
boleto.beneficiario.endereco.localidade=SAO PAULO
boleto.beneficiario.endereco.bairro=Vila Olimpia
boleto.beneficiario.endereco.logradouro=Rua Tenerife
boleto.beneficiario.endereco.numero=31
boleto.beneficiario.endereco.complemento=4o Andar Sala S18
boleto.titulo=CC
boleto.instrucao1=Nao receber, ambiente de desenvolvimento
boleto.instrucao2=
boleto.instrucao3=		
boleto.b3=<EMAIL>


# service DETRAN SP
detran.sp.default.uri=http://10.200.240.158/eaigever/SircofService
detran.sp.context.path=com.registrocontrato.registro.service.detran.sp.client
detran.sp.usuario=06032507000103
detran.sp.senha=PLACE@

# service DETRAN PR
detran.pr.default.uri.auth=https://auth-cs-hml.identidadedigital.pr.gov.br/centralautenticacao/api/v1/token
detran.pr.default.uri=https://homolog.registrodecontrato.detran.pr.gov.br/detran-regcon/api
detran.pr.usuario=22ac3c5a5bf0b520d281c122d1490650
detran.pr.senha=pl4c3t1

# service DETRAN AP
detran.ap.default.uri.auth=https://loiwh3.searchtecnologia.com.br/integracao-api-rest/api/autenticacao
detran.ap.default.uri=https://loiwh3.searchtecnologia.com.br/integracao-api-rest
detran.ap.url.cadastrar=/api/areaRestrita/registroContrato/registrar
detran.ap.url.consultar=/api/areaRestrita/registroContrato/consultar
detran.ap.usuario=PLACETI
detran.ap.senha=PLACETI2019

# service DETRAN RJ
detran.rj.default.uri=http://*************:8080/wsstack/services/REG-CONT
detran.rj.context.path=com.registrocontrato.registro.service.detran.rj.client
detran.rj.usuario=COCTPTIN
detran.rj.senha=SENHA01

# service DETRAN PI
detran.pi.default.uri-boleto=https://www.pi.getran.com.br/financeiro/api/registro/gerarBoleto
detran.pi.default.uri=http://localhost:8080/ws-detran/registroContratoWS
detran.pi.context.path=com.registrocontrato.registro.service.detran.pi.client
detran.pi.usuario=06032507000103
detran.pi.senha=p@c3TecIn0V

# service DETRAN RR
detran.rr.default.uri=https://loiwh3.searchtecnologia.com.br
detran.rr.url.cadastrar=/getranServicos/rest/registroContrato/registrarContrato
detran.rr.url.consultarcontrato=/getranServicos/rest/registroContrato/consultarContrato
detran.rr.usuario=PLACETI
detran.rr.senha=ksokv_58azisplgjjazzmb.

# service DETRAN MG
detran.mg.default.uri=http://webservicehomologa.detran.mg.gov.br/sircof/soap/contratos_financeiros/service
detran.mg.context.path=com.registrocontrato.registro.service.detran.mg.client
detran.mg.usuario=06032507000103
detran.mg.senha=9f87fc5fea1f0c56cfa4c54844221c8923aec3ef

# service DETRAN SC   
detran.sc.default.uri=https://dividaativa.ciasc.gov.br/homologacao/detran/RegistroContrato/RegistroContrato.asmx
detran.sc.context.path=com.registrocontrato.registro.service.detran.sc.client
detran.sc.usuario=placeti
detran.sc.senha=@20_placeti_19#
detran.sc.registrarcontrato=RegistrarContrato
detran.sc.consultarsequencial=ConsultarSequencialContrato

# service DETRAN AC
detran.ac.default.uri=
detran.ac.context.path=com.registrocontrato.registro.service.detran.ac.client
detran.ac.url.cadastrar=registrarContrato
detran.ac.url.consultar=consultarContrato
detran.ac.usuario=
detran.ac.senha=

# service DETRAN MT
detran.mt.default.uri=http://wbs2.homologa.detrannet.mt.gov.br/wsRegistroContrato/wsRegistroContrato.asmx
detran.mt.default.endpoint=http://wbs2.homologa.detrannet.mt.gov.br
detran.mt.context.path=com.registrocontrato.registro.service.detran.mt.client
detran.mt.url.cadastrar=RegistraContrato
detran.mt.url.consultar=ConsultaContrato

# service DETRAN BA
detran.ba.default.uri=http://200.187.13.80/wsdetrancontrato/wsdetrancontrato.asmx
detran.ba.context.path=com.registrocontrato.registro.service.detran.ba.client
detran.ba.usuario=100403
detran.ba.senha=PlaceT

# service DETRAN PB
detran.pb.default.uri=http://prxcodata.pb.gov.br/CallWSDT/CallWSDT
detran.pb.context.path=com.registrocontrato.registro.service.detran.pb.client
detran.pb.usuario=0603250
detran.pb.senha=DET123
detran.pb.codigo=000955
detran.pb.sigla.transacao=PL
detran.pb.sigla.transmissao=PL

# service DETRAN PE
detran.pe.default.uri=http://200.238.67.2:41075/WebApiRegistroContratoGravame/RegistraContrato

qrcode.validate.url=https://homol.placecon.com.br/registro/public/validate.xhtml
sign.url.return=https://homol.placecon.com.br

#CONTA AZUL
contaazul.id=r1uEQXN7TmT7Bp7wZ92E6zTWd53bbCf0
contaazul.secret=FTtKixHyZsxz1dnscMuIBAEXCNZTHqAn
contaazul.url=https://api.contaazul.com/auth/authorize
contaazul.redirect=https://homol.placecon.com.br/registro/contaazul/confirma-venda.xhtml
contaazul.url-token=https://api.contaazul.com/oauth2/token
contaazul.url-servicos=https://api.contaazul.com/v1/services/
contaazul.url-clientes=https://api.contaazul.com/v1/customers/
contaazul.url-vendas=https://api.contaazul.com/v1/sales

# email
spring.mail.mailgun.domain=placecon.com.br
spring.mail.mailgun.ws-auth=Basic ************************************************************************
spring.mail.contato=<EMAIL>
spring.mail.ambiente=desenv
spring.mail.destinarios.baixa=<EMAIL>
spring.mail.destinarios.suporte=<EMAIL>

# timeout detran
detran.readTimeout=20000
detran.connectionTimeout=20000

decoder.url=https://api.procob.com/veiculos/v2/V0001?decodificadorChassi=SIM&chassi=
decoder.usuario=<EMAIL>
decoder.senha=MGtKMBmUXmBAMn5

#ocr
tesseract.datapath=/usr/share/tesseract/4/tessdata

#NOTAS REEMBOLSO
ufesp.valor=2,572
ufesp.artigo=artigo 15, item II da portaria 465/2016
ufesp.portariapagamento=465/2016
duda.rj.codigoreceita=031 - 0
duda.rj.portaria=5639 de 31 de maio de 2019

sistema=FINANCEIRO

#dados place
place.telefone1=+55 (11) 4210-1220
place.telefone2=0800 591 0915
place.site=https://placeti.com.br/
