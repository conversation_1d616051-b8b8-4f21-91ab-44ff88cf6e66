package com.registrocontrato.financeiro.service;

import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.data.domain.Sort.Direction;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.data.repository.PagingAndSortingRepository;
import org.springframework.stereotype.Service;

import com.registrocontrato.finaceiro.entity.Imposto;
import com.registrocontrato.finaceiro.entity.RecorrenciaImposto;
import com.registrocontrato.finaceiro.entity.TipoImposto;
import com.registrocontrato.finaceiro.entity.dto.ImpostoDTO;
import com.registrocontrato.financeiro.repository.ImpostoRepository;
import com.registrocontrato.infra.exception.ServiceException;
import com.registrocontrato.infra.service.BaseService;

@Service
public class ImpostoService extends BaseService<Imposto, ImpostoDTO>{
	
	private static final long serialVersionUID = 1L;

	@Autowired
	private ImpostoRepository repository;

	@Override
	public Page<Imposto> findAll(int first, int pageSize, ImpostoDTO filter) {

		Specification<Imposto> spec = new Specification<Imposto>() {

			@Override
			public Predicate toPredicate(Root<Imposto> root, CriteriaQuery<?> cq, CriteriaBuilder cb) {
				cq.distinct(true);
				List<Predicate> predicates = new ArrayList<>();
				
				if (filter.getRecorrencia() != null) {
					predicates.add(cb.equal(root.<RecorrenciaImposto>get("recorrencia"), filter.getRecorrencia()));
				}
				if (filter.getTipo() != null) {
					predicates.add(cb.equal(root.<TipoImposto>get("tipoImposto"), filter.getTipo()));
				}
				
				if (filter.getDataInicio() != null) {
					Date d = Date.from(filter.getDataInicio().toInstant().atZone(ZoneId.systemDefault()).toLocalDate().atStartOfDay().atZone(ZoneId.systemDefault()).toInstant());
					predicates.add(cb.greaterThanOrEqualTo(root.<Date>get("dataInicio"), d));
				}
				
				if (filter.getDataFim() != null) {
					Date d = Date.from(filter.getDataFim().toInstant().atZone(ZoneId.systemDefault()).toLocalDate().atStartOfDay().atZone(ZoneId.systemDefault()).toInstant());
					predicates.add(cb.or(cb.isNull(root.<Date>get("dataFim")), 
							cb.lessThanOrEqualTo(root.<Date>get("dataFim"), d)));
				}

				if (filter.getDataValida() != null) {
					Date d = Date.from(filter.getDataValida().toInstant().atZone(ZoneId.systemDefault()).toLocalDate().atStartOfDay().atZone(ZoneId.systemDefault()).toInstant());
					predicates.add(cb.lessThanOrEqualTo(root.<Date>get("dataInicio"), d));
					predicates.add(cb.or(cb.isNull(root.<Date>get("dataFim")),
							cb.greaterThanOrEqualTo(root.<Date>get("dataFim"), d)));
				}
				

				return andTogether(predicates, cb);
			}

			private Predicate andTogether(List<Predicate> predicates, CriteriaBuilder cb) {
				return cb.and(predicates.toArray(new Predicate[0]));
			}
		};
		
		return repository.findAll(spec, new PageRequest(first / pageSize, pageSize, new Sort(Direction.ASC, "dataInicio")));
	
	}
	
	
	public void save(Imposto imposto) throws ServiceException {
		if (imposto.getTipoImposto() == null || imposto.getDataInicio() == null || imposto.getPercentualImposto() == null || 
				imposto.getRecorrencia() == null) {
			throw new ServiceException("Informe os dados obrigatórios do imposto: tipo, recorrência, data inicial e percentual");
		}
		if (imposto.getDataFim() == null && repository.isImpostoVigente(imposto)) {
			throw new ServiceException("Imposto já registrado");
		}
		if (repository.possuiRegistroNoPeriodo(imposto)) {
			throw new ServiceException("Já existe um imposto registrado no período informado");
		}
		repository.save(imposto);
	}

	@Override
	protected PagingAndSortingRepository<Imposto, Long> getRepository() {
		return repository;
	}

}
